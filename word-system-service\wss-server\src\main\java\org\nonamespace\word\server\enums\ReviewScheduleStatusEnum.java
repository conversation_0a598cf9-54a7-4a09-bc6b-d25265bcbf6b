package org.nonamespace.word.server.enums;

/**
 * 复习计划状态枚举类
 * <p>
 * 该枚举类用于表示复习计划的不同状态，包括待开始、进行中、已完成和已跳过。
 * </p>
 */
public enum ReviewScheduleStatusEnum {
    //待开始, 进行中, 已完成, 已跳过
    WAIT_START("待开始"),
    IN_PROGRESS("进行中"),
    COMPLETED("已完成"),
    SKIPPED("已跳过");
    private final String value;
    ReviewScheduleStatusEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static ReviewScheduleStatusEnum getByDefault(String value, ReviewScheduleStatusEnum defaultValue) {
        for (ReviewScheduleStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return defaultValue;
    }

}
