package org.nonamespace.word.common.utils;

import cn.hutool.core.thread.ThreadFactoryBuilder;

import java.util.concurrent.*;

public class WordEnrichThreadPoolUtil {

    private final static ThreadPoolExecutor executorService;

    static {
        executorService = (ThreadPoolExecutor) Executors.newFixedThreadPool(64, ThreadFactoryBuilder.create().setNamePrefix("word-enrich-pool-").build());
    }


    /**
     * 提交一个Callable任务到线程池并返回Future结果
     * @param task 需要执行的任务
     * @param <T> 任务返回值类型
     * @return Future对象，包含任务执行结果
     */
    public static <T> Future<T> submitTask(Callable<T> task) {
        if (task == null) throw new NullPointerException();
        RunnableFuture<T> ftask = newTaskFor(task);
        executorService.execute(ftask);
        return ftask;
    }

    /**
     * 提交一个Runnable任务到线程池
     * @param task 需要执行的任务
     */
    public static void executeTask(Runnable task) {
        if (task == null) throw new NullPointerException();
        executorService.execute(task);
    }

    private static <T> RunnableFuture<T> newTaskFor(Callable<T> callable) {
        return new FutureTask<>(callable);
    }

    private static RunnableFuture<Void> newTaskFor(Runnable runnable) {
        return new FutureTask<>(runnable, null);
    }

    // 关闭线程池
    public static void shutdown() {
        if (executorService != null) {
            executorService.shutdown();
        }
    }

    // 立即关闭线程池
    public static void shutdownNow() {
        if (executorService != null) {
            executorService.shutdownNow();
        }
    }

    // 检查线程池是否关闭
    public static boolean isShutdown() {
        return executorService == null || executorService.isShutdown();
    }

    // 检查线程池是否终止
    public static boolean isTerminated() {
        return executorService == null || executorService.isTerminated();
    }
}
