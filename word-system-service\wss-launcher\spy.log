【P6SPY-SQL】耗时: 34ms | statement | 连接ID: 0%n--- 完整SQL:%nSELECT version()%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 1%n--- 完整SQL:%nSELECT version()%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | statement | 连接ID: 2%n--- 完整SQL:%nSELECT version()%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 3%n--- 完整SQL:%nSELECT version()%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT version()%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 23ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT    config_id,config_name,config_key,config_value,config_type,create_by,create_time,update_by,update_time,remark    FROM  sys_config%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 24ms | statement | 连接ID: 4%n--- 完整SQL:%nselect dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark  		from sys_dict_data       		 WHERE  status = ?  		order by dict_sort asc%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | statement | 连接ID: 4%n--- 完整SQL:%nselect job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, remark 		from sys_job%n--- 调用来源:%n%(stackTrace)
