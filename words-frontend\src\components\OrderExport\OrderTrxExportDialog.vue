<template>
  <el-dialog
    title="导出交易流水"
    :visible.sync="visible"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="exportForm"
      :model="exportForm"
      :rules="exportRules"
      label-width="120px"
      size="small"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="订单号">
            <el-input v-model="exportForm.orderNo" placeholder="请输入订单号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="商户流水号">
            <el-input v-model="exportForm.cusTrxSeq" placeholder="请输入商户流水号" clearable />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="交易类型">
            <el-select v-model="exportForm.trxType" placeholder="请选择交易类型" clearable>
              <el-option label="支付" value="pay" />
              <el-option label="退款" value="refund" />
              <el-option label="撤销" value="cancel" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="交易状态">
            <el-select v-model="exportForm.trxStatus" placeholder="请选择交易状态" clearable>
              <el-option label="未付款" value="未付款" />
              <el-option label="已付款" value="已付款" />
              <el-option label="已退款" value="已退款" />
              <el-option label="已取消" value="已取消" />
              <el-option label="处理中" value="处理中" />
              <el-option label="失败" value="失败" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="支付类型">
            <el-select v-model="exportForm.payType" placeholder="请选择支付类型" clearable>
              <el-option label="支付宝" value="alipay" />
              <el-option label="微信支付" value="wechat" />
              <el-option label="银联支付" value="unionpay" />
              <el-option label="现金" value="cash" />
              <el-option label="转账" value="transfer" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="交易索引">
            <el-input-number
              v-model="exportForm.trxIdx"
              :min="1"
              placeholder="交易索引"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="学生姓名">
            <el-input v-model="exportForm.studentName" placeholder="请输入学生姓名" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="学生手机号">
            <el-input v-model="exportForm.studentPhone" placeholder="请输入学生手机号" clearable />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="销售员姓名">
            <el-input v-model="exportForm.salerName" placeholder="请输入销售员姓名" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="平台交易ID">
            <el-input-number
              v-model="exportForm.trxId"
              :min="1"
              placeholder="平台交易ID"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="交易时间">
            <el-date-picker
              v-model="createTimeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="handleCreateTimeChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="最小金额(元)">
            <el-input-number
              v-model="minAmount"
              :min="0"
              :precision="2"
              placeholder="最小交易金额"
              style="width: 100%"
              @change="handleMinAmountChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最大金额(元)">
            <el-input-number
              v-model="maxAmount"
              :min="0"
              :precision="2"
              placeholder="最大交易金额"
              style="width: 100%"
              @change="handleMaxAmountChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="最大导出数量" prop="maxExportCount">
            <el-input-number
              v-model="exportForm.maxExportCount"
              :min="1"
              :max="10000"
              placeholder="最大导出数量"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="包含订单详情">
            <el-switch v-model="exportForm.includeOrderDetails" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="包含学生信息">
            <el-switch v-model="exportForm.includeStudentInfo" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="排序字段">
            <el-select v-model="exportForm.orderBy" placeholder="请选择排序字段">
              <el-option label="交易时间" value="create_time" />
              <el-option label="更新时间" value="update_time" />
              <el-option label="交易金额" value="trx_amt" />
              <el-option label="交易索引" value="trx_idx" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序方向">
            <el-select v-model="exportForm.orderDirection" placeholder="请选择排序方向">
              <el-option label="降序" value="DESC" />
              <el-option label="升序" value="ASC" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" :loading="exporting" @click="handleExport">
        {{ exporting ? '导出中...' : '确认导出' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { exportOrderTrxApi } from '@/api/management/order-trx'
import { managerExportOrderTrxApi } from '@/api/management/order-manager'
import { exportExcel, formatExportParams, validateExportParams, generateExportFileName } from '@/utils/download'

export default {
  name: 'OrderTrxExportDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isManager: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      exportForm: {
        orderNo: '',
        cusTrxSeq: '',
        trxType: '',
        trxStatus: '',
        payType: '',
        trxIdx: null,
        trxId: null,
        studentName: '',
        studentPhone: '',
        salerName: '',
        createTimeStart: '',
        createTimeEnd: '',
        minTrxAmt: null,
        maxTrxAmt: null,
        maxExportCount: 1000,
        includeOrderDetails: false,
        includeStudentInfo: false,
        orderBy: 'create_time',
        orderDirection: 'DESC',
        exportFormat: 'excel'
      },
      exportRules: {
        maxExportCount: [
          { required: true, message: '请输入最大导出数量', trigger: 'blur' },
          { type: 'number', min: 1, max: 10000, message: '导出数量必须在1-10000之间', trigger: 'blur' }
        ]
      },
      createTimeRange: [],
      minAmount: null,
      maxAmount: null,
      exporting: false
    }
  },
  methods: {
    handleCreateTimeChange(value) {
      if (value && value.length === 2) {
        this.exportForm.createTimeStart = value[0]
        this.exportForm.createTimeEnd = value[1]
      } else {
        this.exportForm.createTimeStart = ''
        this.exportForm.createTimeEnd = ''
      }
    },
    handleMinAmountChange(value) {
      this.exportForm.minTrxAmt = value ? Math.round(value * 100) : null
    },
    handleMaxAmountChange(value) {
      this.exportForm.maxTrxAmt = value ? Math.round(value * 100) : null
    },
    handleExport() {
      this.$refs.exportForm.validate((valid) => {
        if (valid) {
          this.doExport()
        }
      })
    },
    async doExport() {
      try {
        this.exporting = true
        
        // 格式化导出参数
        const params = formatExportParams(this.exportForm)
        
        // 验证导出参数
        const validation = validateExportParams(params)
        if (!validation.valid) {
          this.$message.error(validation.message)
          return
        }
        
        // 选择导出API
        const exportApi = this.isManager ? managerExportOrderTrxApi : exportOrderTrxApi
        
        // 生成文件名
        const fileName = generateExportFileName('交易流水', params)
        
        // 执行导出
        await exportExcel(
          exportApi,
          params,
          fileName,
          (fileName) => {
            this.$message.success(`导出成功: ${fileName}`)
            this.handleClose()
          },
          (error) => {
            this.$message.error('导出失败: ' + (error.message || '未知错误'))
          }
        )
      } catch (error) {
        this.$message.error('导出失败: ' + (error.message || '未知错误'))
      } finally {
        this.exporting = false
      }
    },
    handleClose() {
      this.$emit('update:visible', false)
      this.resetForm()
    },
    resetForm() {
      this.$refs.exportForm.resetFields()
      this.createTimeRange = []
      this.minAmount = null
      this.maxAmount = null
      this.exporting = false
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
