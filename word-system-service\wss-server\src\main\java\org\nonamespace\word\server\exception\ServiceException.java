package org.nonamespace.word.server.exception;

/**
 * 业务异常类
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
public class ServiceException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private String code;

    /**
     * 错误消息
     */
    private String message;

    public ServiceException() {
        super();
    }

    public ServiceException(String message) {
        super(message);
        this.message = message;
    }

    public ServiceException(String code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public ServiceException(String message, Throwable cause) {
        super(message, cause);
        this.message = message;
    }

    public ServiceException(String code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * 创建参数验证异常
     */
    public static ServiceException paramError(String message) {
        return new ServiceException("PARAM_ERROR", message);
    }

    /**
     * 创建数据不存在异常
     */
    public static ServiceException dataNotFound(String message) {
        return new ServiceException("DATA_NOT_FOUND", message);
    }

    /**
     * 创建业务逻辑异常
     */
    public static ServiceException businessError(String message) {
        return new ServiceException("BUSINESS_ERROR", message);
    }

    /**
     * 创建系统异常
     */
    public static ServiceException systemError(String message) {
        return new ServiceException("SYSTEM_ERROR", message);
    }

    /**
     * 创建系统异常
     */
    public static ServiceException systemError(String message, Throwable cause) {
        return new ServiceException("SYSTEM_ERROR", message, cause);
    }
}
