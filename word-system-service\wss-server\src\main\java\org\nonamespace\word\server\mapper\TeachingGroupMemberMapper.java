package org.nonamespace.word.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.nonamespace.word.server.domain.TeachingGroupMember;
import org.nonamespace.word.server.dto.management.teachinggroup.TeacherDto;
import org.nonamespace.word.server.dto.management.teachinggroup.TeachingGroupDto;

import java.util.List;

/**
 * 教学组成员Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Mapper
public interface TeachingGroupMemberMapper extends BaseMapper<TeachingGroupMember> {

    /**
     * 分页查询教学组教师列表
     * 
     * @param page 分页参数
     * @param req 查询条件
     * @return 教师列表
     */
    IPage<TeacherDto.BasicResp> selectGroupTeachersPage(Page<TeacherDto.BasicResp> page, @Param("req") TeachingGroupDto.GetGroupTeachersReq req);

    /**
     * 查询教学组所有教师
     * 
     * @param groupId 教学组ID
     * @return 教师列表
     */
    List<TeacherDto.BasicResp> selectGroupTeachers(@Param("groupId") String groupId);

    /**
     * 批量添加教师到教学组
     * 
     * @param members 成员列表
     * @return 影响行数
     */
    int insertBatch(@Param("members") List<TeachingGroupMember> members);

    /**
     * 批量移除教师从教学组
     * 
     * @param groupId 教学组ID
     * @param teacherIds 教师ID列表
     * @return 影响行数
     */
    int deleteBatchByGroupIdAndTeacherIds(@Param("groupId") String groupId, @Param("teacherIds") List<String> teacherIds);

    /**
     * 根据教学组ID删除所有成员
     * 
     * @param groupId 教学组ID
     * @return 影响行数
     */
    int deleteByGroupId(@Param("groupId") String groupId);

    /**
     * 检查教师是否已在其他教学组
     * 
     * @param teacherIds 教师ID列表
     * @return 已分配的教师ID列表
     */
    List<String> selectAssignedTeachers(@Param("teacherIds") List<String> teacherIds);

    /**
     * 获取教师的教学组信息
     * 
     * @param teacherId 教师ID
     * @return 教学组信息
     */
    TeachingGroupDto.Resp selectTeacherGroup(@Param("teacherId") String teacherId);
}
