package org.nonamespace.word.server.constants;

public interface OrderConstants {

    interface TrxType {
        String PAY = "支付";
        String REFUND = "退款";
        String CANCEL = "撤销";
    }

    interface Source {
        String SYSTEM = "系统创建";
        String DOUDIAN = "抖店";
        String XINGCHENG = "星CRM";
    }
    /**
     * 订单交易状态
     */
    interface OrderStatus {
        String UNPAID = "未付款";
        String PAID = "已付款";
        String FULL_PAID = "已全额支付";
        String PART_PAID = "已部分支付";
        String REFUND = "已退款";
        String CANCEL = "已取消";
    }

    /**
     * 交易流水状态
     */
    interface TrxStatus {
        String UNPAID = "未付款";
        String PAID = "已付款";
        String REFUNDED = "已退款";
        String CANCELLED = "已取消";
        String PROCESSING = "处理中";
        String FAILED = "失败";
    }


    /**
     * 签署状态
     */
    interface SignStatus {
        String NO_NEED = "无需签署";
        String UN_SIGN = "未签署";
        String SIGNED = "已签署";
    }

}
