package org.nonamespace.word.server.constants;

public interface OrderConstants {

    interface TrxType {
        String PAY = "pay";
        String REFUND = "refund";
        String CANCEL = "cancel";
    }

    interface Source {
        String SYSTEM = "系统创建";
        String DOUDIAN = "抖店";
        String XINGCHENG = "星CRM";
    }
    /**
     * 订单交易状态
     */
    interface OrderStatus {
        String UNPAID = "未付款";
        String PAID = "已付款";
        String FULL_PAID = "已全额支付";
        String PART_PAID = "已部分支付";
        String REFUND = "已退款";
        String CANCEL = "已取消";
    }


    /**
     * 签署状态
     */
    interface SignStatus {
        String NO_NEED = "无需签署";
        String UN_SIGN = "未签署";
        String SIGNED = "已签署";
    }

}
