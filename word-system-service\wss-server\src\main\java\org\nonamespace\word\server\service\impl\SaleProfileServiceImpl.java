package org.nonamespace.word.server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.nonamespace.word.server.domain.SaleProfile;
import org.nonamespace.word.server.mapper.SaleProfileMapper;
import org.nonamespace.word.server.service.ISaleProfileService;
import org.springframework.stereotype.Service;

/**
 * 销售人员属性数据层服务实现
 * 
 * <AUTHOR>
 * @date 2025-06-29
 */
@Service
public class SaleProfileServiceImpl extends ServiceImpl<SaleProfileMapper, SaleProfile> implements ISaleProfileService {
    // 纯数据层服务实现
}
