package org.nonamespace.word.server.dto.management.teacher;

import lombok.Data;

import java.util.List;

@Data
public class TeacherListSelectDto {
    @Data
    public static class Req {
        private String keyword;
    }

    @Data
    public static class Resp {
        Integer total;
        List<User> rows;
        Integer pageNum = 1;
        Integer pageSize = 20;
    }

    @Data
    public static class User {
        String id;
        String name;
    }
}
