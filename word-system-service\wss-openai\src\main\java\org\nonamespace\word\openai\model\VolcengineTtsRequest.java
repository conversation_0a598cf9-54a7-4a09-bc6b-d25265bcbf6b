package org.nonamespace.word.openai.model;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * 火山模型 -- tts语音合成请求
 *
 * <AUTHOR>
 * @date 2025/5/20 9:37
 */
@Data
public class VolcengineTtsRequest {

    private App app = new App();
    private User user = new User();
    private Audio audio = new Audio();
    private Request request = new Request();

    private VolcengineTtsRequest() {
    }

    public VolcengineTtsRequest(String appid, String token, String cluster, String voiceType, String userUid) {
        app.setAppid(appid);
        app.setToken(token);
        app.setCluster(cluster);

        if(StrUtil.isBlankIfStr(userUid)){
            userUid = IdUtil.fastSimpleUUID();
        }
        user.setUid(userUid);

        audio.setVoice_type(voiceType);
    }

    public VolcengineTtsRequest text(String text){
        request.setText(text);
        return this;
    }


    @Getter
    @Setter
    class App {
        private String appid;
        private String token;
        private String cluster;
    }


    @Getter
    @Setter
    class User {
        private String uid;
    }


    @Getter
    @Setter
    class Audio {
        private String voice_type;
        private String encoding = "mp3";
        private float speed_ratio = 1.0f;
        private float volume_ratio = 10;
        private float pitch_ratio = 10;
        private String emotion = "happy";
    }


    @Getter
    @Setter
    class Request {
        private String reqid = IdUtil.fastSimpleUUID();
        private String text;
        private String text_type = "plain";
        private String operation = "query";
    }

}
