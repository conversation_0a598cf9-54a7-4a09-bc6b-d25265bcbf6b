package org.nonamespace.word.rest.controller.order;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.dto.order.RefundRecordDto;
import org.nonamespace.word.server.service.order.IOrderRefundRecordService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 退款记录管理Controller（管理员版本）
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Slf4j
@RestController
@RequestMapping("/refund-records-manager")
@RequiredArgsConstructor
@Validated
public class RefundRecordManagerController extends BaseController {

    private final IOrderRefundRecordService refundRecordService;

    /**
     * 分页查询退款记录（管理员权限）
     */
    @PreAuthorize("@ss.hasPermi('refund:records:manager:query')")
    @GetMapping("/list")
    public TableDataInfo list(RefundRecordDto.QueryReq req) {
        log.info("管理员查询退款记录: req={}", req);

        IPage<RefundRecordDto.Resp> page = refundRecordService.selectRefundRecordsByParam(req);

        TableDataInfo dataTable = getDataTable(page.getRecords());
        log.info("管理员查询退款记录成功: total={}", page.getTotal());

        return dataTable;
    }

    /**
     * 查询退款记录详情（管理员权限）
     */
    @PreAuthorize("@ss.hasPermi('refund:records:manager:detail')")
    @GetMapping("/{refundRecordId}")
    public AjaxResult getDetail(@PathVariable String refundRecordId) {
        try {
            log.info("管理员查询退款记录详情: refundRecordId={}", refundRecordId);
            
            RefundRecordDto.DetailResp detail = refundRecordService.getRefundRecordDetail(refundRecordId);
            
            if (detail == null) {
                return AjaxResult.error("退款记录不存在");
            }
            
            log.info("管理员查询退款记录详情成功: refundRecordId={}", refundRecordId);
            return AjaxResult.success(detail);
            
        } catch (Exception e) {
            log.error("管理员查询退款记录详情失败: refundRecordId={}", refundRecordId, e);
            return AjaxResult.error("查询退款记录详情失败: " + e.getMessage());
        }
    }

//    /**
//     * 批量审批退款记录（管理员权限）
//     */
//    @PreAuthorize("@ss.hasPermi('refund:records:manager:approve')")
//    @Log(title = "批量退款记录审批", businessType = BusinessType.UPDATE)
//    @PostMapping("/batch-approve")
//    public AjaxResult batchApprove(@RequestBody List<RefundRecordDto.ApprovalReq> reqList) {
//        try {
//            log.info("批量审批退款记录: count={}", reqList.size());
//
//            String approverId = getUserId().toString();
//            String approverName = getUsername();
//
//            for (RefundRecordDto.ApprovalReq req : reqList) {
//                refundRecordService.approveRefundRecord(
//                        req.getRefundRecordId(),
//                        req.getApprovalResult(),
//                        req.getApprovalRemark(),
//                        approverId,
//                        approverName
//                );
//            }
//
//            log.info("批量审批退款记录成功: count={}", reqList.size());
//            return AjaxResult.success("批量审批成功");
//
//        } catch (Exception e) {
//            log.error("批量审批退款记录失败", e);
//            return AjaxResult.error("批量审批失败: " + e.getMessage());
//        }
//    }

    /**
     * 查询退款统计数据（管理员权限）
     */
    @PreAuthorize("@ss.hasPermi('refund:records:manager:statistics')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics(@RequestParam(required = false) String startDate,
                                  @RequestParam(required = false) String endDate) {
        try {
            log.info("管理员查询退款统计数据: startDate={}, endDate={}", startDate, endDate);
            
            RefundRecordDto.StatisticsResp statistics = refundRecordService.getRefundStatistics(startDate, endDate);
            
            log.info("管理员查询退款统计数据成功");
            return AjaxResult.success(statistics);
            
        } catch (Exception e) {
            log.error("管理员查询退款统计数据失败", e);
            return AjaxResult.error("查询统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 导出退款记录（管理员权限）
     */
    @PreAuthorize("@ss.hasPermi('refund:records:manager:export')")
    @Log(title = "管理员退款记录导出", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RefundRecordDto.QueryReq req) {
        try {
            log.info("管理员开始导出退款记录: req={}", req);
            
            // 管理员可以导出更多数据
            req.setMaxExportCount(50000);
            
            List<RefundRecordDto.Resp> list = refundRecordService.exportRefundRecords(req);
            
            ExcelUtil<RefundRecordDto.Resp> util = new ExcelUtil<>(RefundRecordDto.Resp.class);
            util.exportExcel(response, list, "退款记录数据_管理员");
            
            log.info("管理员导出退款记录成功: count={}", list.size());
            
        } catch (Exception e) {
            log.error("管理员导出退款记录失败", e);
        }
    }

    /**
     * 查询全部退款记录概览（管理员权限）
     */
    @PreAuthorize("@ss.hasPermi('refund:records:manager:overview')")
    @GetMapping("/overview")
    public AjaxResult getOverview() {
        try {
            log.info("管理员查询退款记录概览");
            
            // 查询今日统计
            RefundRecordDto.StatisticsResp todayStats = refundRecordService.getRefundStatistics(
                    java.time.LocalDate.now().toString(), 
                    java.time.LocalDate.now().toString()
            );
            
            // 查询本月统计
            String monthStart = java.time.LocalDate.now().withDayOfMonth(1).toString();
            String monthEnd = java.time.LocalDate.now().toString();
            RefundRecordDto.StatisticsResp monthStats = refundRecordService.getRefundStatistics(monthStart, monthEnd);
            
//            // 查询待审批数量
//            Integer pendingCount = refundRecordService.getPendingApprovalCount();
            
            // 构建概览数据
            java.util.Map<String, Object> overview = new java.util.HashMap<>();
            overview.put("todayStats", todayStats);
            overview.put("monthStats", monthStats);
//            overview.put("pendingApprovalCount", pendingCount);
            
            log.info("管理员查询退款记录概览成功");
            return AjaxResult.success(overview);
            
        } catch (Exception e) {
            log.error("管理员查询退款记录概览失败", e);
            return AjaxResult.error("查询概览失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新退款记录状态（管理员权限）
     */
    @PreAuthorize("@ss.hasPermi('refund:records:manager:update')")
    @Log(title = "批量更新退款记录状态", businessType = BusinessType.UPDATE)
    @PostMapping("/batch-update-status")
    public AjaxResult batchUpdateStatus(@RequestParam List<String> refundRecordIds,
                                      @RequestParam String refundStatus) {
        try {
            log.info("批量更新退款记录状态: ids={}, status={}", refundRecordIds, refundStatus);
            
            refundRecordService.batchUpdateRefundStatus(refundRecordIds, refundStatus);
            
            log.info("批量更新退款记录状态成功: count={}", refundRecordIds.size());
            return AjaxResult.success("批量更新成功");
            
        } catch (Exception e) {
            log.error("批量更新退款记录状态失败", e);
            return AjaxResult.error("批量更新失败: " + e.getMessage());
        }
    }
}
