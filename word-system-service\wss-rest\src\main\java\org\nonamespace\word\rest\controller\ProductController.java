package org.nonamespace.word.rest.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.dto.product.ProductDto;
import org.nonamespace.word.server.service.IProductService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

/**
 * 产品管理Controller
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Slf4j
@RestController
@RequestMapping("/management/products")
@RequiredArgsConstructor
@Validated
public class ProductController extends BaseController {

    private final IProductService productService;

    /**
     * 分页查询产品列表
     */
    @PreAuthorize("@ss.hasPermi('management:products:list')")
    @GetMapping
    public AjaxResult getProducts(ProductDto.GetListReq req) {
        try {
            IPage<ProductDto.BasicResp> page = productService.getProductPage(req);
            return AjaxResult.success(page);
        } catch (Exception e) {
            log.error("获取产品列表失败", e);
            return error("获取产品列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询产品详情
     */
    @PreAuthorize("@ss.hasPermi('management:products:query')")
    @GetMapping("/{id}")
    public AjaxResult getProduct(@PathVariable String id) {
        try {
            ProductDto.DetailResp product = productService.getProductDetail(id);
            if (product == null) {
                return error("产品不存在");
            }
            return AjaxResult.success(product);
        } catch (Exception e) {
            log.error("获取产品详情失败", e);
            return error("获取产品详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建产品
     */
    @PreAuthorize("@ss.hasPermi('management:products:add')")
    @Log(title = "产品管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult createProduct(@Valid @RequestBody ProductDto.CreateReq req) {
        try {
            String productId = productService.createProduct(req);
            return AjaxResult.success("创建成功", productId);
        } catch (Exception e) {
            log.error("创建产品失败", e);
            return error("创建产品失败: " + e.getMessage());
        }
    }

    /**
     * 更新产品信息
     */
    @PreAuthorize("@ss.hasPermi('management:products:edit')")
    @Log(title = "产品管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult updateProduct(@Valid @RequestBody ProductDto.UpdateReq req) {
        try {
            boolean success = productService.updateProduct(req);
            return success ? AjaxResult.success() : error("更新失败");
        } catch (Exception e) {
            log.error("更新产品失败", e);
            return error("更新产品失败: " + e.getMessage());
        }
    }

    /**
     * 删除产品
     */
    @PreAuthorize("@ss.hasPermi('management:products:remove')")
    @Log(title = "产品管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult deleteProducts(@PathVariable String ids) {
        try {
            List<String> productIds = Arrays.asList(ids.split(","));
            boolean success = productService.deleteProducts(productIds);
            return success ? AjaxResult.success() : error("删除失败");
        } catch (Exception e) {
            log.error("删除产品失败", e);
            return error("删除产品失败: " + e.getMessage());
        }
    }

    /**
     * 上架产品
     */
    @PreAuthorize("@ss.hasPermi('management:products:edit')")
    @Log(title = "产品管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/enable")
    public AjaxResult enableProduct(@PathVariable String id) {
        try {
            boolean success = productService.enableProduct(id);
            return success ? AjaxResult.success("上架成功") : error("上架失败");
        } catch (Exception e) {
            log.error("上架产品失败", e);
            return error("上架产品失败: " + e.getMessage());
        }
    }

    /**
     * 下架产品
     */
    @PreAuthorize("@ss.hasPermi('management:products:edit')")
    @Log(title = "产品管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/disable")
    public AjaxResult disableProduct(@PathVariable String id) {
        try {
            boolean success = productService.disableProduct(id);
            return success ? AjaxResult.success("下架成功") : error("下架失败");
        } catch (Exception e) {
            log.error("下架产品失败", e);
            return error("下架产品失败: " + e.getMessage());
        }
    }

    /**
     * 查询所有上架的产品列表（用于下单选择）
     */
    @GetMapping("/available")
    public AjaxResult getAvailableProducts() {
        try {
            List<ProductDto.BasicResp> products = productService.getAvailableProducts();
            return AjaxResult.success(products);
        } catch (Exception e) {
            log.error("获取上架产品列表失败", e);
            return error("获取上架产品列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据类型查询产品列表
     */
    @GetMapping("/type/{type}")
    public AjaxResult getProductsByType(@PathVariable String type) {
        try {
            List<ProductDto.BasicResp> products = productService.getProductsByType(type);
            return AjaxResult.success(products);
        } catch (Exception e) {
            log.error("根据类型查询产品列表失败", e);
            return error("根据类型查询产品列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据学科查询产品列表
     */
    @GetMapping("/subject/{subject}")
    public AjaxResult getProductsBySubject(@PathVariable String subject) {
        try {
            List<ProductDto.BasicResp> products = productService.getProductsBySubject(subject);
            return AjaxResult.success(products);
        } catch (Exception e) {
            log.error("根据学科查询产品列表失败", e);
            return error("根据学科查询产品列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据标签查询产品列表
     */
    @GetMapping("/tag/{tag}")
    public AjaxResult getProductsByTag(@PathVariable String tag) {
        try {
            List<ProductDto.BasicResp> products = productService.getProductsByTag(tag);
            return AjaxResult.success(products);
        } catch (Exception e) {
            log.error("根据标签查询产品列表失败", e);
            return error("根据标签查询产品列表失败: " + e.getMessage());
        }
    }

    /**
     * 查询热门产品列表
     */
    @GetMapping("/hot")
    public AjaxResult getHotProducts(@RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<ProductDto.BasicResp> products = productService.getHotProducts(limit);
            return AjaxResult.success(products);
        } catch (Exception e) {
            log.error("查询热门产品列表失败", e);
            return error("查询热门产品列表失败: " + e.getMessage());
        }
    }


    /**
     * 更新产品库存
     */
    @PreAuthorize("@ss.hasPermi('management:products:edit')")
    @Log(title = "产品管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/stock")
    public AjaxResult updateStock(@PathVariable String id, @RequestParam Integer quantity) {
        try {
            boolean success = productService.updateStock(id, quantity);
            return success ? AjaxResult.success("库存更新成功") : error("库存更新失败");
        } catch (Exception e) {
            log.error("更新产品库存失败", e);
            return error("更新产品库存失败: " + e.getMessage());
        }
    }

    /**
     * 检查产品库存
     */
    @GetMapping("/{id}/stock/check")
    public AjaxResult checkStock(@PathVariable String id, @RequestParam Integer quantity) {
        try {
            boolean sufficient = productService.checkStock(id, quantity);
            return AjaxResult.success("库存检查完成", sufficient);
        } catch (Exception e) {
            log.error("检查产品库存失败", e);
            return error("检查产品库存失败: " + e.getMessage());
        }
    }
}
