package org.nonamespace.word.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.nonamespace.word.server.domain.CourseSectionStep;
import org.nonamespace.word.server.dto.course.CourseReportDataDto;

import java.util.List;

/**
 * 课程学习步骤Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Mapper
public interface CourseSectionStepMapper extends BaseMapper<CourseSectionStep> {

    List<CourseReportDataDto> statisticCourseReportData(@Param("courseId") String courseId);

}
