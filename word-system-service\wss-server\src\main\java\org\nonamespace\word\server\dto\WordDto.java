package org.nonamespace.word.server.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> <PERSON> on 2025/05/13 01:12
 */
@Data
@Accessors(chain = true)
public class WordDto {
    private String id;
    private String word;
    private String syllables;
    private String phoneticUk;
    private String phoneticUs;
    private String audioUkUrl;
    private String audioUsUrl;
    private String meanings;
    private String sentences;
    private String tags;
    private Integer difficulty;
    private String videoUrl;
    private Boolean flagPracticeWord;
    private Boolean flagPracticeTranslate;
    private Boolean flagPracticeOrder;
    private String version;
}
