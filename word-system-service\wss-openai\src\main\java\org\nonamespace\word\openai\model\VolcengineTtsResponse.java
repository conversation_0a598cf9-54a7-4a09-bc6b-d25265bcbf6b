package org.nonamespace.word.openai.model;

import lombok.Data;

/**
 * 火山模型 -- tts语音合成响应
 *
 * <AUTHOR>
 * @date 2025/5/20 9:38
 */
@Data
public class VolcengineTtsResponse {

    // 与 VolcengineTtsRequest 中的reqid对应
    private String reqid;

    private int code;
    private String message;

    // 负数表示合成完毕
    private int sequence;

    // 返回的音频数据，base64 编码
    private String data;

    // 返回音频的长度，单位ms
    private String duration;

    // 包含字级别和音素级别的时间戳信息
    private String frontend;
}
