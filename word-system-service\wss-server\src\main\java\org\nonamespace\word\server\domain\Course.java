package org.nonamespace.word.server.domain;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.nonamespace.word.server.entity.DataEntity;
import org.nonamespace.word.server.misc.mybatis.ListStringTypeHandler;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 课程对象 course
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@TableName(value = "course", autoResultMap = true)
public class Course extends DataEntity
{
    private static final long serialVersionUID = 1L;
    /** 教师ID */
    @Excel(name = "教师ID")
    private String teacherId;

    /** 学生ID */
    @Excel(name = "学生ID")
    private String studentId;

    /** 课堂类型 (学习课、复习课) */
    @Excel(name = "课堂类型 (学习课、复习课)")
    private String type;

    /** 课程类型 (正式课、试听课) */
    @Excel(name = "课程类型 (正式课、试听课)")
    @TableField("course_type")
    private String courseType;

//    /** 是否使用系统 */
//    @Excel(name = "是否使用系统")
//    @TableField("use_system")
    @TableField(exist = false)
    private Boolean useSystem;

    /** 计划开始时间 */
    @Excel(name = "计划开始时间")
    private Date scheduledStartTime;

    /** 计划结束时间 */
    @Excel(name = "计划结束时间")
    private Date scheduledEndTime;

    /** 实际开始时间 */
    @Excel(name = "实际开始时间")
    private Date actualStartTime;

    /** 实际结束时间 */
    @Excel(name = "实际结束时间")
    private Date actualEndTime;

    /** 课堂时长（分钟） */
    @Excel(name = "课堂时长", readConverterExp = "分=钟")
    private Long durationMinutes;

    /** 课程状态 (计划中, 进行中, 完成, 取消) */
    @Excel(name = "课程状态 (计划中, 进行中, 完成, 取消)")
    private String courseStatus;

    /** 外部课程系统ID */
    @Excel(name = "外部课程系统ID")
    private String externalCourseId;

    /** 课堂总结笔记 */
    @Excel(name = "课堂总结笔记")
    private String courceSummaryNote;

    /** 总单词数 */
    @Excel(name = "总单词数")
    private Long statWordCnt;

    /** 学习单词数 */
    @Excel(name = "学习单词数")
    private Long statWordLearnCnt;

    /** 错误单词数 */
    @Excel(name = "错误单词数")
    private Long statWordMistakesCnt;

    /** 复习单词数 */
    @Excel(name = "复习单词数")
    private Long statWordReviewCnt;

        /** 当前环节在课程所有环节中的索引 (从0开始) */
    @Excel(name = "当前环节索引")
    private Long currentSectionIndex;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONObject content;

    private String wordPdfUrl;
    private String practicesPdfUrl;
    private String practicesPdfUrl1;

    /** 错词讲义PDF下载地址 */
    private String errorHandoutPdfUrl;

    /** 错题练习PDF下载地址 */
    private String errorExercisePdfUrl;

    private String cancelType;
    private String cancelReason;

    /** 消课方式 (系统消课、人工消课) */
    private String consumptionMethod;

    /** 消课信息 (JSON格式，存储人工消课的详细信息) */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONObject consumptionInfo;

    private String subject;
    private String specification;
    private String scheduleId;

    /** 录屏链接 */
//    @Excel(name = "录屏链接")
//    @TableField("recording_url")
    @TableField(exist = false)
    private String recordingUrl;

    /** 备注 */
//    @Excel(name = "备注")
//    @TableField("remark")
    @TableField(exist = false)
    private String remark;

    @TableField(typeHandler = ListStringTypeHandler.class)
    private List<String> exceptionTypes;



    public void addExceptionType(String exceptionType) {
        if (this.exceptionTypes == null) {
            this.exceptionTypes = new ArrayList<>();
        }
        if (!this.exceptionTypes.contains(exceptionType)) {
            this.exceptionTypes.add(exceptionType);
        }
    }
}
