//package org.nonamespace.word.rest.controller;
//
//import java.util.List;
//
//import jakarta.servlet.http.HttpServletResponse;
//import org.nonamespace.word.server.service.impl.TextbookItemService;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.PutMapping;
//import org.springframework.web.bind.annotation.DeleteMapping;
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//import com.ruoyi.common.annotation.Log;
//import com.ruoyi.common.core.controller.BaseController;
//import com.ruoyi.common.core.domain.AjaxResult;
//import com.ruoyi.common.enums.BusinessType;
//import org.nonamespace.word.server.domain.TextbookItem;
//
///**
// * 词词汇关联: 关联词和具体单词，定义词汇级别等Controller
// *
// * <AUTHOR>
// * @date 2025-05-13
// */
//@RestController
//@RequestMapping("/word/item")
//public class TextbookItemController extends BaseController
//{
//    @Autowired
//    private TextbookItemService textbookItemService;
//
//    /**
//     * 获取词词汇关联: 关联词和具体单词，定义词汇级别等详细信息
//     */
//    @PreAuthorize("@ss.hasPermi('word:item:query')")
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") String id)
//    {
//        return success(textbookItemService.selectTextbookItemById(id));
//    }
//
//    /**
//     * 新增词词汇关联: 关联词和具体单词，定义词汇级别等
//     */
//    @PreAuthorize("@ss.hasPermi('word:item:add')")
//    @Log(title = "词词汇关联: 关联词和具体单词，定义词汇级别等", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody TextbookItem textbookItem)
//    {
//        return toAjax(textbookItemService.insertTextbookItem(textbookItem));
//    }
//
//    /**
//     * 修改词词汇关联: 关联词和具体单词，定义词汇级别等
//     */
//    @PreAuthorize("@ss.hasPermi('word:item:edit')")
//    @Log(title = "词词汇关联: 关联词和具体单词，定义词汇级别等", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody TextbookItem textbookItem)
//    {
//        return toAjax(textbookItemService.updateTextbookItem(textbookItem));
//    }
//
//    /**
//     * 删除词词汇关联: 关联词和具体单词，定义词汇级别等
//     */
//    @PreAuthorize("@ss.hasPermi('word:item:remove')")
//    @Log(title = "词词汇关联: 关联词和具体单词，定义词汇级别等", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable String[] ids)
//    {
//        return toAjax(textbookItemService.deleteTextbookItemByIds(ids));
//    }
//}
