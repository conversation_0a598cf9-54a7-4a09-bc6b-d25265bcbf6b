package org.nonamespace.word.server.dto.course;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CourseSectionStepDto {
    private String id;
    private String type;
    private String status;
    private String result;
    private Date startTime;
    private Date endTime;
    private List<String> sentenceOrder;
    private List<String> options;
    private String studentAnswer;
    private String answer;
    private String textbookItemId;  // 添加教材项ID字段

}