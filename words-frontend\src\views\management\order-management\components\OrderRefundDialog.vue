<template>
  <el-dialog
    title="订单退款"
    v-model="visible"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="orderInfo" class="refund-content">
      <!-- 订单信息 -->
      <el-card class="order-info-card" style="margin-bottom: 20px;">
        <template #header>
          <span>订单信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ orderInfo.no }}</el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getStatusType(orderInfo.orderStatus)">{{ orderInfo.orderStatus }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="订单总金额">¥{{ (orderInfo.totalAmt / 100).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="已支付金额">¥{{ (orderInfo.amtPaid / 100).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="未支付金额">¥{{ (orderInfo.amtUnpaid / 100).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="学生姓名">{{ orderInfo.studentName }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 退款表单 -->
      <el-form
        ref="refundForm"
        :model="refundForm"
        :rules="refundRules"
        label-width="120px"
      >
        <el-form-item label="退款类型">
          <el-radio-group v-model="refundForm.refundType" @change="handleRefundTypeChange">
            <el-radio label="partial">部分退款</el-radio>
            <el-radio label="full">全额退款</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item 
          v-if="refundForm.refundType === 'partial'" 
          label="退款金额" 
          prop="refundAmount"
        >
          <el-input-number
            v-model="refundForm.refundAmount"
            :min="0"
            :max="maxRefundAmount"
            :precision="2"
            :step="0.01"
            style="width: 200px"
          />
          <span style="margin-left: 10px; color: #666;">
            最大可退款金额: ¥{{ maxRefundAmount.toFixed(2) }}
          </span>
        </el-form-item>

        <el-form-item label="退款原因" prop="refundReason">
          <el-input
            v-model="refundForm.refundReason"
            type="textarea"
            :rows="4"
            placeholder="请输入退款原因"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" :loading="submitting" @click="handleSubmit">
        {{ submitting ? '处理中...' : '确认退款' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { refundOrderApi, fullRefundOrderApi } from '@/api/management/order'

export default {
  name: 'OrderRefundDialog',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    orderInfo: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      visible: this.value,
      submitting: false,
      refundForm: {
        refundType: 'partial',
        refundAmount: 0,
        refundReason: ''
      },
      refundRules: {
        refundAmount: [
          { required: true, message: '请输入退款金额', trigger: 'blur' },
          { type: 'number', min: 0, message: '退款金额必须大于0', trigger: 'blur' }
        ],
        refundReason: [
          { required: true, message: '请输入退款原因', trigger: 'blur' },
          { min: 5, max: 200, message: '退款原因长度在5到200个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    maxRefundAmount() {
      return this.orderInfo ? (this.orderInfo.amtPaid / 100) : 0.01
    }
  },
  watch: {
    value(val) {
      this.visible = val
      if (val && this.orderInfo) {
        this.resetForm()
      }
    },
    visible(val) {
      this.$emit('input', val)
    }
  },
  methods: {
    getStatusType(status) {
      const statusMap = {
        '未付款': 'warning',
        '已付款': 'success',
        '已全额支付': 'success',
        '已部分支付': 'primary',
        '已取消': 'danger',
        '已退款': 'info'
      }
      return statusMap[status] || 'info'
    },
    handleRefundTypeChange(value) {
      if (value === 'full') {
        this.refundForm.refundAmount = this.maxRefundAmount
      } else {
        this.refundForm.refundAmount = 0
      }
    },
    resetForm() {
      this.refundForm = {
        refundType: 'partial',
        refundAmount: 0,
        refundReason: ''
      }
      if (this.$refs.refundForm) {
        this.$refs.refundForm.resetFields()
      }
    },
    handleSubmit() {
      this.$refs.refundForm.validate(async (valid) => {
        if (valid) {
          await this.doRefund()
        }
      })
    },
    async doRefund() {
      try {
        this.submitting = true
        
        const refundData = {
          orderId: this.orderInfo.id,
          refundReason: this.refundForm.refundReason
        }

        if (this.refundForm.refundType === 'full') {
          // 全额退款
          await fullRefundOrderApi(this.orderInfo.id, refundData)
        } else {
          // 部分退款
          refundData.refundAmount = Math.round(this.refundForm.refundAmount * 100) // 转换为分
          await refundOrderApi(this.orderInfo.id, refundData)
        }

        this.$message.success('退款申请提交成功')
        this.$emit('success')
        this.handleClose()
        
      } catch (error) {
        console.error('退款失败:', error)
        this.$message.error('退款失败: ' + (error.message || '未知错误'))
      } finally {
        this.submitting = false
      }
    },
    handleClose() {
      this.visible = false
      this.resetForm()
    }
  }
}
</script>

<style scoped>
.refund-content {
  padding: 10px 0;
}

.order-info-card {
  border-radius: 8px;
}

.dialog-footer {
  text-align: right;
}

.el-input-number {
  width: 200px;
}
</style>
