package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.*;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.JdbcType;
import org.nonamespace.word.server.dto.course.CourseSectionDto;
import org.nonamespace.word.server.dto.course.CourseSectionWordDto;
import org.nonamespace.word.server.entity.DataEntity;
import org.nonamespace.word.server.misc.mybatis.ListStringTypeHandler;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> Created on 2025/06/02 16:35
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "student_word_test", autoResultMap = true)
public class StudentWordTest extends DataEntity {

    private String studentId;

    private Integer testedWordNum;

    private Integer successWordNum;

    private String successRate;

    private String result;

    private Integer estimatedWordNum;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private CourseSectionDto content;

    private String status;

    private String courseId;

    private String suggestions;

    private String textbookId;
    private String consumTime;


    @TableField(typeHandler = ListStringTypeHandler.class,jdbcType = JdbcType.ARRAY)
    private List<String> correctWord = new ArrayList<>();

    @TableField(typeHandler = ListStringTypeHandler.class,jdbcType = JdbcType.ARRAY)
    private List<String> wrongWord = new ArrayList<>();

    private String teacherId;
    @TableField(typeHandler = ListStringTypeHandler.class,jdbcType = JdbcType.ARRAY)
    private List<String> lastSemesterWord = new ArrayList<>();

    @TableField(typeHandler = ListStringTypeHandler.class,jdbcType = JdbcType.ARRAY)
    private List<String> specialWord = new ArrayList<>();

    /** 错词讲义PDF下载地址 */
    private String errorHandoutPdfUrl;

    /** 错题练习PDF下载地址 */
    private String errorExercisePdfUrl;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private LastGradeInfo lastGradeInfo;

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LastGradeInfo {
        private String stage;
        private String publisher;
        private Integer lastGrade;
        private Integer lastSemester;
        private String textbookId;
        private String textbookName;
    }

    @TableField(typeHandler = JacksonTypeHandler.class)
    private TestDetailInfo testDetailInfo;

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TestDetailInfo {
        //上学期单词掌握率
        private String lastSemesterWordCollectRate;
        //上学期单词掌握率建议
        private String lastSemesterSuggestions;
        //特殊单词掌握率
        private String specialWordCollectRate;
        //特殊单词掌握率建议
        private String specialWordSuggestions;
        //建议教材
        private String suggestTextbookName;
        //课题单词学习计划
        private String suggestLearnTime;

        //特色词汇学习建议
        private String specialWordLearnSuggestions;
    }
}
