# e签宝接口对接文档

## 概述

本文档记录了e签宝相关接口的对接实现，包括获取签署页面链接和下载已签署文件及附属材料两个接口。

## 已实现接口

### 1. 获取签署页面链接接口

**接口地址**: `https://open.esign.cn/doc/opendoc/pdf-sign3/pvfkwd`

**功能描述**: 发起签署时，开发者可指定签署方签署通知的方式（短信通知或邮件通知），签署人访问签署链接以进行相关操作；当开发者需要在业务系统中访问签署链接或自行发送签署通知时，可通过此接口获取合同的签署/预览链接。

**实现文件**:
- 输入参数模型: `GetSignUrlInput.java`
- 输出参数模型: `GetSignUrlOutput.java`
- 服务接口: `IESignService.getSignUrl()`
- 服务实现: `ESignServiceImpl.getSignUrl()`
- 测试类: `GetSignUrlTest.java`

**主要参数**:
- `signFlowId`: 签署流程ID（必填）
- `needLogin`: 是否需要登录（可选）
- `urlType`: 链接类型，0-签署链接，1-预览链接（可选）
- `operator`: 签署操作人信息（可选）
- `organization`: 机构签署方信息（可选）
- `redirectConfig`: 重定向配置项（可选）
- `clientType`: 客户端类型（可选）
- `appScheme`: AppScheme（可选）

### 2. 下载已签署文件及附属材料接口

**接口地址**: `https://open.esign.cn/doc/opendoc/pdf-sign3/kczf8g`

**功能描述**: 流程结束后，获取签署完成的文件以及相关附属材料的下载链接。未签署完成的流程，无法下载相关文件，否则会报错："流程非签署完成状态，不允许下载文档"。

**实现文件**:
- 输入参数模型: `GetFileDownloadUrlInput.java`
- 输出参数模型: `GetFileDownloadUrlOutput.java`
- 服务接口: `IESignService.getFileDownloadUrl()`
- 服务实现: `ESignServiceImpl.getFileDownloadUrl()`
- 测试类: `GetFileDownloadUrlTest.java`

**主要参数**:
- `signFlowId`: 已完成状态的签署流程ID（必填）

**返回数据**:
- `files`: 签署文件信息列表
- `attachments`: 附属材料信息列表
- `certificateDownloadUrl`: 海外签证书报告下载地址

## 技术实现特点

### 1. 安全性保障
- 完善的参数校验机制
- 统一的异常处理
- 签名验证和请求头设置

### 2. 代码质量
- 遵循单一职责原则
- 合理的分层架构（Controller-Service-Repository）
- 完善的日志记录
- 统一的响应格式

### 3. 幂等性控制
- 基于签署流程ID的幂等性保证
- 防止重复处理机制

### 4. 异常处理
- 统一的异常体系（ESignException）
- 详细的错误信息记录
- 分类处理不同类型异常

## 使用示例

### 获取签署页面链接

```java
// 创建请求参数
GetSignUrlInput input = new GetSignUrlInput();
input.setSignFlowId("your-sign-flow-id");
input.setNeedLogin(false);
input.setUrlType(0); // 0-签署链接，1-预览链接

// 设置操作人信息（可选）
GetSignUrlInput.Operator operator = new GetSignUrlInput.Operator();
operator.setPsnAccount("<EMAIL>");
input.setOperator(operator);

// 设置重定向配置（可选）
GetSignUrlInput.RedirectConfig redirectConfig = new GetSignUrlInput.RedirectConfig();
redirectConfig.setRedirectUrl("https://example.com/callback");
redirectConfig.setRedirectDelayTime(3);
input.setRedirectConfig(redirectConfig);

// 调用接口
GetSignUrlOutput output = eSignService.getSignUrl(input);

// 获取结果
if (output.isSuccess()) {
    String shortUrl = output.getData().getShortUrl();
    String longUrl = output.getData().getUrl();
    // 处理签署链接
}
```

### 下载已签署文件及附属材料

```java
// 创建请求参数
GetFileDownloadUrlInput input = new GetFileDownloadUrlInput("completed-sign-flow-id");

// 调用接口
GetFileDownloadUrlOutput output = eSignService.getFileDownloadUrl(input);

// 获取结果
if (output.isSuccess()) {
    // 处理签署文件
    List<GetFileDownloadUrlOutput.FileInfo> files = output.getData().getFiles();
    for (GetFileDownloadUrlOutput.FileInfo file : files) {
        String downloadUrl = file.getDownloadUrl();
        String fileName = file.getFileName();
        // 下载文件
    }
    
    // 处理附属材料
    List<GetFileDownloadUrlOutput.AttachmentInfo> attachments = output.getData().getAttachments();
    for (GetFileDownloadUrlOutput.AttachmentInfo attachment : attachments) {
        String downloadUrl = attachment.getDownloadUrl();
        String fileName = attachment.getFileName();
        // 下载附件
    }
}
```

## 注意事项

1. **下载链接有效期**: 文件下载链接有效期为60分钟，过期后需要重新调用接口获取新的下载地址。

2. **流程状态要求**: 下载文件接口只能用于已完成签署的流程，未完成的流程会返回错误。

3. **重定向域名**: 使用重定向功能时，域名需要在e签宝提前放行，否则会报安全风险错误。

4. **参数校验**: 所有接口都有完善的参数校验，确保传入参数的有效性。

5. **异常处理**: 建议在业务代码中捕获并处理ESignException异常，根据异常类型进行相应处理。

## 常量定义

相关API路径常量已在 `ESignConstants.ApiPath` 中定义：
- `GET_SIGN_URL`: 获取签署页面链接
- `GET_FILE_DOWNLOAD_URL`: 下载已签署文件及附属材料

## 测试

每个接口都提供了对应的单元测试类，包括：
- 正常功能测试
- 参数校验测试
- 异常情况测试

测试类位于 `src/test/java/org/nonamespace/word/thirdpart/esign/service/` 目录下。