package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nonamespace.word.server.entity.DataEntity;

import java.time.LocalTime;

/**
 * 教师可上课时间实体类
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "teacher_time_slot", autoResultMap = true)
public class TeacherTimeSlot extends DataEntity {

    /**
     * 教师ID
     */
    @TableField("teacher_id")
    private String teacherId;

    /**
     * 星期几 (1-7, 1为周一, 7为周日，与Java DayOfWeek一致)
     */
    @TableField("weekday")
    private Integer weekday;

    /**
     * 开始时间 (如: 09:15)
     */
    @TableField("start_time")
    private LocalTime startTime;

    /**
     * 结束时间 (如: 10:55)
     */
    @TableField("end_time")
    private LocalTime endTime;

    /**
     * 状态 (available: 可上课, scheduled: 已排课)
     */
    @TableField("status")
    private String status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}
