package org.nonamespace.word.server.dto;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.StrPool;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.web.multipart.MultipartFile;

/**
 * 词定义 (统一教材与词): 定义各种词对象 textbook
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@Accessors(chain = true)
public class TextbookDto
{

    /** 词表ID */
    private String id;

    /** 词表名称 (例如: "高频315词表", "牛津版三年级上册U1") */
    private String name;

    /** 学生id */
    private String studentId;

    /**
     * 封面
     */
    private MultipartFile coverFile;

    /** 类型 (学校教材,特色词表,学生词表) */
    private String type;

    /** 词表结构 */
    private String wordList;

    /** 标签，如["类型:学校教材", "年级:一年级", "版本:牛津版"] */
//    private String[] tags;


    private String publisher;
    private Integer grade;
    private Integer semester;
    private String required;
    /**
     * 阶段 ("小学", "初中", "高中")
     */
    private String stage;

    public String getCoverName(String middle){
        if(coverFile != null){
            return "textbook" + StrPool.SLASH + middle + StrPool.SLASH + "symbols/cover" + StrPool.DOT + FileUtil.getSuffix(coverFile.getOriginalFilename());
        }
        return null;
    }
}
