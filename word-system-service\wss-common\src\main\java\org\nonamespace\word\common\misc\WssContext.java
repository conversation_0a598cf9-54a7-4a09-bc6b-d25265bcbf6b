package org.nonamespace.word.common.misc;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;

import java.util.Date;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

public class WssContext {

    private static ThreadLocal<ConcurrentMap<String, Object>> local = new ThreadLocal<>();

    public static void init(){
        ConcurrentMap<String, Object> map = new ConcurrentHashMap<>();
        map.put("_NOW_", new Date());
        local.set(map);
    }


    public static void release(){
        try {
            local.get().clear();
            local.remove();
        } catch (Exception e) {
            // ignore
        }
    }

    public static <T> T get(String key){
        return (T) local.get().get(key);
    }

    public static <T> void set(String key, T value){
        local.get().put(key, value);
    }

    public static Date now(){
        return get("_NOW_");
    }

    public static String userId(){
        try {
            return SecurityUtils.getUserId().toString();
        } catch (Exception e) {
            return "System";
        }
    }

    public static SysUser currentUser() {
        try {
            return SecurityUtils.getLoginUser().getUser();
        } catch (Exception e) {
            throw new RuntimeException("当前用户未登录或无法获取用户信息", e);
        }
    }

    public static boolean isAdmin(){
        try {
            return SecurityUtils.isAdmin(SecurityUtils.getUserId());
        } catch (Exception e) {
            return false;
        }
    }





}
