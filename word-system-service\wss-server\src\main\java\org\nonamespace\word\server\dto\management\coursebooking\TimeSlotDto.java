package org.nonamespace.word.server.dto.management.coursebooking;

import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

/**
 * 时间段DTO
 *
 * <AUTHOR> Assistant
 * @date 2025-01-01
 */
@Data
public class TimeSlotDto {

    /**
     * 星期几 (1-7, 1=周一, 7=周日)
     */
    @NotNull(message = "星期几不能为空")
    @Min(value = 1, message = "星期几必须在1-7之间")
    @Max(value = 7, message = "星期几必须在1-7之间")
    private Integer dayOfWeek;

    /**
     * 开始时间 (小时, 0-23)
     */
    @NotNull(message = "开始时间不能为空")
    @Min(value = 0, message = "开始时间必须在0-23之间")
    @Max(value = 23, message = "开始时间必须在0-23之间")
    private Integer startHour;

    /**
     * 结束时间 (小时, 0-23)
     */
    @NotNull(message = "结束时间不能为空")
    @Min(value = 0, message = "结束时间必须在0-23之间")
    @Max(value = 23, message = "结束时间必须在0-23之间")
    private Integer endHour;

    /**
     * 星期几文本
     */
    private String dayOfWeekText;

    /**
     * 时间段文本
     */
    private String timeSlotText;

    /**
     * 获取星期几文本
     */
    public String getDayOfWeekText() {
        if (dayOfWeek == null) {
            return "";
        }
        String[] days = {"", "周一", "周二", "周三", "周四", "周五", "周六", "周日"};
        return days[dayOfWeek];
    }

    /**
     * 获取时间段文本
     */
    public String getTimeSlotText() {
        if (startHour == null || endHour == null) {
            return "";
        }
        return String.format("%02d:00-%02d:00", startHour, endHour);
    }
}
