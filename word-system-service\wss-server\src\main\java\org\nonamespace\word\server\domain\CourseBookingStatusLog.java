package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nonamespace.word.server.entity.DataEntity;

import java.util.Date;

/**
 * 预约课申请状态变更记录实体
 *
 * <AUTHOR> Assistant
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("course_booking_status_log")
public class CourseBookingStatusLog extends DataEntity {

    /**
     * 申请ID
     */
    @TableField("application_id")
    private String applicationId;

    /**
     * 原状态
     */
    @TableField("old_status")
    private String oldStatus;

    /**
     * 新状态
     */
    @TableField("new_status")
    private String newStatus;

    /**
     * 操作人ID
     */
    @TableField("operator_id")
    private String operatorId;

    /**
     * 操作人姓名
     */
    @TableField("operator_name")
    private String operatorName;

    /**
     * 操作原因
     */
    @TableField("operation_reason")
    private String operationReason;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
}
