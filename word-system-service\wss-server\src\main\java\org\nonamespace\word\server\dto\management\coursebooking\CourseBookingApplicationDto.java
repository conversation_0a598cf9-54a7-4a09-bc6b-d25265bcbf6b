package org.nonamespace.word.server.dto.management.coursebooking;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * 预约课申请DTO
 *
 * <AUTHOR> Assistant
 * @date 2025-01-01
 */
public class CourseBookingApplicationDto {

    /**
     * 申请请求
     */
    @Data
    public static class ApplyReq {
        
        @NotBlank(message = "学生ID不能为空")
        private String studentId;
        
        @NotBlank(message = "学科不能为空")
        private String subject = "英语";
        
        @NotBlank(message = "规格不能为空")
        private String specification = "单词课";
        
        @NotBlank(message = "性质不能为空")
        private String nature = "试听课";
        
        private String grade;
        
        @NotEmpty(message = "候选老师不能为空")
        @Size(min = 1, max = 2, message = "请选择1-2个候选老师")
        private List<String> teacherCandidates;
        
        @NotEmpty(message = "偏好时间不能为空")
        private List<TimeSlotDto> preferredTimeSlots;
        
        private String applyReason;
    }

    /**
     * 查询请求
     */
    @Data
    public static class QueryReq {
        
        private String status;
        
        private String subject;
        
        private String salesGroupId;
        
        private String teachingGroupId;
        
        private String studentName;
        
        private String salesName;
        
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date applyTimeStart;

        @DateTimeFormat(pattern = "yyyy-MM-dd")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date applyTimeEnd;
        
        private Integer pageNum = 1;
        
        private Integer pageSize = 20;
    }

    /**
     * 列表响应
     */
    @Data
    public static class ListResp {
        
        private String id;
        
        private String studentId;
        
        private String studentName;
        
        private String salesId;

        private String salesName;

        private String salesGroupId;

        private String salesGroupName;
        
        private String subject;
        
        private String specification;
        
        private String nature;
        
        private String grade;

        private String teachingGroupId;

        private String teachingGroupName;
        
        private List<TeacherCandidateDto> teacherCandidates;
        
        private String status;
        
        private String statusText;
        
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date applyTime;

        private String confirmTeacherName;

        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date confirmTime;

        private String confirmByName;

        private Integer remindCount;

        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date lastRemindTime;
        
        private Boolean canRemind;
    }

    /**
     * 详情响应
     */
    @Data
    public static class DetailResp {
        
        private String id;
        
        private String studentId;
        
        private String studentName;
        
        private String salesId;
        
        private String salesName;
        
        private String salesGroupId;
        
        private String salesGroupName;
        
        private String subject;
        
        private String specification;
        
        private String nature;
        
        private String grade;
        
        private List<TimeSlotDto> preferredTimeSlots;
        
        private List<TeacherCandidateDto> teacherCandidates;
        
        private String teachingGroupId;
        
        private String teachingGroupName;
        
        private String status;
        
        private String statusText;
        
        private String applyReason;
        
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date applyTime;

        private String confirmTeacherId;

        private String confirmTeacherName;

        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date confirmTime;

        private String confirmBy;

        private String confirmByName;

        private String rejectReason;

        private Integer remindCount;

        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date lastRemindTime;
        
        private Boolean canRemind;
        
        private String autoAssignedCourseHourId;
    }

    /**
     * 确认请求
     */
    @Data
    public static class ConfirmReq {
        
        @NotBlank(message = "确认的老师ID不能为空")
        private String confirmTeacherId;
        
        private String confirmReason;
    }

    /**
     * 拒绝请求
     */
    @Data
    public static class RejectReq {
        
        @NotBlank(message = "拒绝原因不能为空")
        private String rejectReason;
    }

    /**
     * 催促请求
     */
    @Data
    public static class RemindReq {
        
        private String remindMessage;
    }
}
