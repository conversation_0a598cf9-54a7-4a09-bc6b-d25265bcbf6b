import request from '@/utils/request'

// 获取老师看板数据
export function getTeacherDashboardData(data) {
  return request({
    url: '/course-consumption-role-dashboard/teacher/data',
    method: 'post',
    data: data
  })
}

// 获取组长看板数据
export function getGroupLeaderDashboardData(data) {
  return request({
    url: '/course-consumption-role-dashboard/group-leader/data',
    method: 'post',
    data: data
  })
}

// 获取管理看板数据
export function getAdminDashboardData(data) {
  return request({
    url: '/course-consumption-role-dashboard/admin/data',
    method: 'post',
    data: data
  })
}

// 获取老师选择器选项 (组长使用)
export function getTeacherSelectorOptions() {
  return request({
    url: '/course-consumption-role-dashboard/teacher-selector',
    method: 'get'
  })
}

// 获取组选择器选项 (管理使用)
export function getGroupSelectorOptions() {
  return request({
    url: '/course-consumption-role-dashboard/group-selector',
    method: 'get'
  })
}

// 获取指定组的老师选择器选项 (管理使用)
export function getTeacherSelectorOptionsByGroup(groupId) {
  return request({
    url: `/course-consumption-role-dashboard/group/${groupId}/teachers`,
    method: 'get'
  })
}
