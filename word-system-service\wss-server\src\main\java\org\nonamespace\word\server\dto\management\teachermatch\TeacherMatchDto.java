package org.nonamespace.word.server.dto.management.teachermatch;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 教师匹配相关DTO
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
public class TeacherMatchDto {

    /**
     * 学生时间段
     */
    @Data
    public static class StudentTimeSlot {
        /**
         * 星期几 (1-7, 1为周一, 7为周日，与Java DayOfWeek一致)
         */
        @NotNull(message = "星期几不能为空")
        private Integer weekday;

        /**
         * 开始时间 (如: 09:15)
         */
        @NotBlank(message = "开始时间不能为空")
        private String startTime;

        /**
         * 结束时间 (如: 10:55)
         */
        @NotBlank(message = "结束时间不能为空")
        private String endTime;
    }

    /**
     * 匹配老师请求
     */
    @Data
    public static class MatchTeachersReq {
        /**
         * 学生ID
         */
        @NotBlank(message = "学生ID不能为空")
        private String studentId;

        /**
         * 开始日期
         */
        @NotBlank(message = "开始日期不能为空")
        private String startDate;

        /**
         * 学生可上课时间段（可选）
         */
        private List<StudentTimeSlot> timeSlots;

        /**
         * 试听课时间（可选，用于试听课时间匹配）
         */
        private TrialClassTime trialClassTime;

        /**
         * 搜索关键词（可选）
         */
        private String keyword;

        /**
         * 教学组ID列表（可选）
         */
        private List<String> groupIds;

        // ========== 基础信息筛选 ==========
        /**
         * 性别筛选（可选）
         */
        private String gender;

        /**
         * 年龄范围筛选（可选）
         */
        private Integer minAge;
        private Integer maxAge;

        /**
         * 兼职/全职筛选（可选）
         */
        private String employmentType;

        /**
         * 目前状态筛选（可选）
         */
        private String currentStatus;

        // ========== 教育背景筛选 ==========
        /**
         * 学历筛选（可选）
         */
        private List<String> education;

        /**
         * 大学属性筛选（可选）
         */
        private List<String> universityType;

        /**
         * 是否师范类筛选（可选）
         */
        private Boolean isNormalUniversity;

        /**
         * 是否留学筛选（可选）
         */
        private Boolean studyAbroad;

        // ========== 教学资质筛选 ==========
        /**
         * 教资级别筛选（可选）
         */
        private List<String> teachingCertificateLevel;

        /**
         * 教授学科筛选（可选）
         */
        private List<String> subjects;

        /**
         * 已通过培训科目筛选（可选）
         */
        private List<String> trainingSubjects;

        /**
         * 英语资质筛选（可选）
         */
        private List<String> englishQualification;

        /**
         * 普通话资质筛选（可选）
         */
        private List<String> mandarinQualification;

        /**
         * 沟通能力筛选（可选）
         */
        private List<String> communicationAbility;

        /**
         * 英语发音筛选（可选）
         */
        private List<String> englishPronunciation;

        /**
         * 教龄范围筛选（可选）
         */
        private Integer minTeachingYears;
        private Integer maxTeachingYears;

        // ========== 教学经历和风格筛选 ==========
        /**
         * 教过课程筛选（可选）
         */
        private List<String> taughtCourses;

        /**
         * 上课风格筛选（可选）
         */
        private List<String> teachingStyle;

        /**
         * 适合学生年级筛选（可选）
         */
        private List<String> suitableGrades;

        /**
         * 适合学生程度筛选（可选）
         */
        private List<String> suitableLevels;

        /**
         * 适合学生性格筛选（可选）
         */
        private String suitablePersonality;

        /**
         * 暑期课上课时间筛选（可选）
         * full: 全满档, golden: 黄金档, other: 其他档
         */
        private String summerScheduleType;

        /**
         * 课点更新天数筛选（可选）
         * 表示老师可排课时间最后更新的时间距离当前时间的天数
         * 默认3天，最小1天
         */
        private Integer timeSlotUpdateDays;
    }

    /**
     * 教师可用时间段
     */
    @Data
    public static class TeacherAvailableTimeSlot {
        /**
         * 星期几 (1-7, 1为周一, 7为周日，与Java DayOfWeek一致)
         */
        private Integer weekday;

        /**
         * 开始时间 (如: 09:15)
         */
        private String startTime;

        /**
         * 结束时间 (如: 10:55)
         */
        private String endTime;

        /**
         * 状态 (available: 可排课, occupied: 已占用)
         */
        private String status;

        @Override
        public boolean equals(Object o) {
            if (o == null || getClass() != o.getClass()) return false;
            TeacherAvailableTimeSlot that = (TeacherAvailableTimeSlot) o;
            return Objects.equals(weekday, that.weekday) && Objects.equals(startTime, that.startTime) && Objects.equals(endTime, that.endTime) && Objects.equals(status, that.status);
        }

        @Override
        public int hashCode() {
            return Objects.hash(weekday, startTime, endTime, status);
        }
    }

    /**
     * 匹配的教师信息
     */
    @Data
    public static class MatchedTeacher {
        /**
         * 教师ID
         */
        private String teacherId;

        /**
         * 教师姓名
         */
        private String teacherName;

        /**
         * 教师手机号
         */
        private String teacherPhone;

        /**
         * 教学组名称
         */
        private String groupName;

        // ========== 基础信息 ==========
        /**
         * 性别
         */
        private String gender;

        /**
         * 年龄
         */
        private Integer age;

        /**
         * 兼职/全职
         */
        private String employmentType;

        /**
         * 目前状态
         */
        private String currentStatus;

        // ========== 教育背景 ==========
        /**
         * 学历
         */
        private String education;

        /**
         * 毕业院校
         */
        private String graduateSchool;

        /**
         * 大学属性
         */
        private String universityType;

        /**
         * 是否师范类
         */
        private Boolean isNormalUniversity;

        /**
         * 是否留学
         */
        private Boolean studyAbroad;

        /**
         * 留学国家
         */
        private String studyAbroadCountry;

        // ========== 教学资质 ==========
        /**
         * 教资级别
         */
        private String teachingCertificateLevel;

        /**
         * 教授学科
         */
        private List<String> subjects;

        /**
         * 已通过培训科目
         */
        private List<String> trainingSubjects;

        /**
         * 英语资质
         */
        private String englishQualification;

        /**
         * 普通话资质
         */
        private String mandarinQualification;

        /**
         * 沟通能力
         */
        private String communicationAbility;

        /**
         * 英语发音
         */
        private String englishPronunciation;

        /**
         * 教学经验年数
         */
        private Integer teachingYears;

        // ========== 教学经历和风格 ==========
        /**
         * 教过课程
         */
        private List<String> taughtCourses;

        /**
         * 上课风格
         */
        private List<String> teachingStyle;

        /**
         * 适合学生年级
         */
        private List<String> suitableGrades;

        /**
         * 适合学生程度
         */
        private List<String> suitableLevels;

        /**
         * 适合学生性格
         */
        private String suitablePersonality;

        /**
         * 当前学生数
         */
        private Integer currentStudents;

        /**
         * 匹配的时间段数量（已废弃，保留兼容性）
         */
        @Deprecated
        private Integer matchedTimeSlots;

        /**
         * 匹配度百分比（已废弃，保留兼容性）
         */
        @Deprecated
        private Double matchPercentage;

        /**
         * 教师可用时间段（用于周视图显示）
         */
        private List<TeacherAvailableTimeSlot> availableTimeSlots;

        /**
         * 教师状态
         */
        private String status;
    }

    /**
     * 匹配老师响应
     */
    @Data
    public static class MatchTeachersResp {
        /**
         * 匹配的教师列表
         */
        private List<MatchedTeacher> teachers;

        /**
         * 总匹配教师数
         */
        private Integer totalCount;

        /**
         * 学生信息
         */
        private StudentInfo studentInfo;
    }

    /**
     * 学生信息
     */
    @Data
    public static class StudentInfo {
        /**
         * 学生ID
         */
        private String studentId;

        /**
         * 学生姓名
         */
        private String studentName;

        /**
         * 学生手机号
         */
        private String studentPhone;

        /**
         * 年级
         */
        private String grade;

        /**
         * 学校
         */
        private String school;
    }

    /**
     * 教师详细时间安排（用于周视图）
     */
    @Data
    public static class TeacherWeeklySchedule {
        /**
         * 教师ID
         */
        private String teacherId;

        /**
         * 教师姓名
         */
        private String teacherName;

        /**
         * 开始日期
         */
        private String startDate;

        /**
         * 结束日期
         */
        private String endDate;

        /**
         * 每天的时间安排
         */
        private List<DailySchedule> dailySchedules;
    }

    /**
     * 每日时间安排
     */
    @Data
    public static class DailySchedule {
        /**
         * 日期
         */
        private String date;

        /**
         * 星期几 (1-7, 1为周一, 7为周日，与Java DayOfWeek一致)
         */
        private Integer weekday;

        /**
         * 时间段列表
         */
        private List<TeacherAvailableTimeSlot> timeSlots;
    }

    /**
     * 试听课时间DTO
     */
    @Data
    public static class TrialClassTime {
        private Date date;        // 试听课日期
        private String startTime; // 开始时间 (HH:mm格式)
        private String endTime;   // 结束时间 (HH:mm格式)
    }
}
