package org.nonamespace.word.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.domain.Course;
import org.nonamespace.word.server.util.PerformanceMonitor;
import org.nonamespace.word.server.domain.UserStudentExt;
import org.nonamespace.word.server.dto.management.teachermatch.TeacherMatchDto;
import org.nonamespace.word.server.dto.management.teachinggroup.TeacherDto;
import org.nonamespace.word.server.service.ICourseService;
import org.nonamespace.word.server.service.ITeacherManagementService;
import org.nonamespace.word.server.service.ITeacherMatchService;
import org.nonamespace.word.server.service.UserStudentExtService;
import org.nonamespace.word.server.util.SystemDataQueryUtil;
import org.nonamespace.word.server.util.TeacherMatchDebugUtil;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 教师匹配服务实现类
 *
 * 主要功能：
 * 1. 根据学生时间需求查找可用的教师
 * 2. 考虑教师已排课程，计算真实可用时间
 * 3. 批量查询优化，避免N+1查询问题
 * 4. 提供教师周课表查询功能
 *
 * 注意：不计算匹配度，只返回能排进课程的教师
 *
 * <AUTHOR>
 * @since 2025-06-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TeacherMatchServiceImpl implements ITeacherMatchService {

    private final ITeacherManagementService teacherManagementService;
    private final ICourseService courseService;
    private final UserStudentExtService userStudentExtService;
    private final PerformanceMonitor performanceMonitor;
    private final TeacherMatchDebugUtil debugUtil;
    private final SystemDataQueryUtil systemDataQueryUtil;

    /**
     * 匹配教师
     *
     * 算法流程：
     * 1. 获取学生信息和所有符合条件的教师
     * 2. 批量查询教师已排课程（未来一个月）
     * 3. 批量查询教师可用时间段
     * 4. 批量查询教师带教信息和详细信息
     * 5. 在内存中计算每个教师的实际空余时间（排除已排课程）
     * 6. 检查教师是否能同时满足学生的所有时间需求
     * 7. 按当前学生数排序返回可用教师
     *
     * 匹配规则：
     * - 教师必须能同时满足学生的所有时间段需求
     * - 每个学生时间段必须完全包含在教师的某个可用时间段内
     * - 已排课程的时间段会被排除，不参与匹配
     *
     * @param request 匹配请求参数
     * @return 匹配结果
     */
    @Override
    public TeacherMatchDto.MatchTeachersResp matchTeachers(TeacherMatchDto.MatchTeachersReq request) {
        return performanceMonitor.monitor("matchTeachers", () -> {
            log.info("开始匹配教师: studentId={}, timeSlots={}, trialClassTime={}",
                request.getStudentId(), request.getTimeSlots().size(),
                request.getTrialClassTime() != null ? "有试听课时间" : "无试听课时间");

            // 验证学生时间段格式
//            if (!debugUtil.validateStudentTimeSlots(request.getTimeSlots())) {
//                throw new RuntimeException("学生时间段格式验证失败");
//            }

            // 打印学生时间需求详情
//            for (int i = 0; i < request.getTimeSlots().size(); i++) {
//                TeacherMatchDto.StudentTimeSlot slot = request.getTimeSlots().get(i);
//                log.info("学生时间需求[{}]: 星期{} {}:{}", i, slot.getWeekday(), slot.getStartTime(), slot.getEndTime());
//            }

            try {
                // 1. 获取学生信息
                TeacherMatchDto.StudentInfo studentInfo = performanceMonitor.monitor("getStudentInfo",
                    () -> getStudentInfo(request.getStudentId()));

                // 2. 获取所有符合条件的教师
                List<TeacherDto.BasicResp> allTeachers = performanceMonitor.monitor("getActiveTeachers",
                    () -> getActiveTeachers(request));
                if (allTeachers.isEmpty()) {
                    log.warn("没有找到符合条件的教师");
                    return createEmptyResponse(studentInfo);
                }

                List<String> teacherIds = allTeachers.stream()
                    .map(TeacherDto.BasicResp::getId)
                    .collect(Collectors.toList());

                log.info("找到{}个符合条件的教师", allTeachers.size());

                // 如果有试听课时间要求，先过滤出在试听课时间段有空闲的教师
                if (request.getTrialClassTime() != null) {
                    TeacherMatchDto.TrialClassTime trialTime = request.getTrialClassTime();
                    log.info("开始检查试听课时间可用性: date={}, startTime={}, endTime={}",
                        trialTime.getDate(), trialTime.getStartTime(), trialTime.getEndTime());

                    Map<String, Boolean> trialTimeAvailability = batchCheckTrialTimeAvailability(
                        teacherIds, trialTime.getDate(), trialTime.getStartTime(), trialTime.getEndTime());

                    // 过滤出在试听课时间段有空闲的教师
                    List<TeacherDto.BasicResp> availableForTrialTeachers = allTeachers.stream()
                        .filter(teacher -> trialTimeAvailability.getOrDefault(teacher.getId(), false))
                        .collect(Collectors.toList());

                    log.info("试听课时间过滤后剩余{}个教师", availableForTrialTeachers.size());
                    allTeachers = availableForTrialTeachers;

                    // 更新教师ID列表
                    teacherIds = allTeachers.stream()
                        .map(TeacherDto.BasicResp::getId)
                        .collect(Collectors.toList());
                }


                List<TeacherMatchDto.MatchedTeacher> availableTeachers = new ArrayList<>();


                // 3. 批量查询教师带教信息
                Map<String, TeacherDto.TeachingInfoResp> teacherTeachingInfoMap = getTeacherTeachingInfoMap(teacherIds);

                // 4. 批量查询教师详细信息
                Map<String, TeacherDto.DetailResp> teacherDetailMap = getTeacherDetailMap(teacherIds);

                if(CollUtil.isEmpty(request.getTimeSlots())){
                    allTeachers.stream().map(teacher-> buildMatchedTeacher(teacher, teacherTeachingInfoMap.get(teacher.getId()),
                            teacherDetailMap.get(teacher.getId()), new ArrayList<>())).forEach(availableTeachers::add);

                }else{
                    // 5. 批量查询教师已排课程（未来一个月）
                    Map<String, List<Course>> teacherScheduledCourses = getTeacherScheduledCourses(teacherIds, request.getStartDate());

                    // 6. 批量查询教师可用时间段
                    Map<String, List<TeacherDto.TimeSlotResp>> teacherAvailableSlots = getTeacherAvailableSlots(teacherIds);

                    // 7. 检查每个教师是否能满足学生时间需求
                    for (TeacherDto.BasicResp teacher : allTeachers) {
                        log.info("检查教师{} ({})的可用性", teacher.getName(), teacher.getId());

                        TeacherMatchDto.MatchedTeacher availableTeacher = checkTeacherAvailability(
                                teacher,
                                request.getTimeSlots(),
                                teacherScheduledCourses.getOrDefault(teacher.getId(), new ArrayList<>()),
                                teacherAvailableSlots.getOrDefault(teacher.getId(), new ArrayList<>()),
                                teacherTeachingInfoMap.get(teacher.getId()),
                                teacherDetailMap.get(teacher.getId())
                        );

                        // 只有教师能同时满足所有时间段才算可用
                        if (availableTeacher != null) {
                            log.info("教师{} ({})匹配成功", teacher.getName(), teacher.getId());
                            availableTeachers.add(availableTeacher);
                        } else {
                            log.info("教师{} ({})匹配失败", teacher.getName(), teacher.getId());
                        }
                    }
                }

            // 8. 按当前学生数排序（学生少的优先）
            availableTeachers.sort(Comparator.comparingInt(TeacherMatchDto.MatchedTeacher::getCurrentStudents));
            processTeacherVisibility(availableTeachers);

            // 9. 构建响应
            TeacherMatchDto.MatchTeachersResp response = new TeacherMatchDto.MatchTeachersResp();
            response.setStudentInfo(studentInfo);
            response.setTeachers(availableTeachers);
            response.setTotalCount(availableTeachers.size());

                log.info("匹配完成: 找到{}个可用教师", availableTeachers.size());
                return response;

            } catch (Exception e) {
                log.error("匹配教师失败", e);
                throw e;
            }
        });
    }

    /**
     * 获取教师周课表
     *
     * @param teacherId 教师ID
     * @param startDate 开始日期
     * @return 周课表信息
     */
    @Override
    public TeacherMatchDto.TeacherWeeklySchedule getTeacherWeeklySchedule(String teacherId, String startDate) {
        log.info("获取教师周课表: teacherId={}, startDate={}", teacherId, startDate);

        try {
            // 获取教师详细信息
            TeacherDto.DetailResp teacher = teacherManagementService.getTeacherDetail(teacherId);
            if (teacher == null) {
                throw new RuntimeException("教师不存在");
            }

            // 获取教师可用时间段
            List<TeacherDto.TimeSlotResp> timeSlots = teacherManagementService.getTeacherTimeSlots(teacherId);
            log.info("教师{}的时间段总数: {}", teacherId, timeSlots.size());
            timeSlots.forEach(slot -> {
                log.info("教师{}时间段: 星期{} {}:{}-{}, 状态: {}", teacherId, slot.getWeekday(), slot.getWeekday(), slot.getStartTime(), slot.getEndTime(), slot.getStatus());
            });

            // 获取教师已排课程
            Map<String, List<Course>> scheduledCourses = getTeacherScheduledCourses(
                Collections.singletonList(teacherId), startDate);

            // 构建响应
            TeacherMatchDto.TeacherWeeklySchedule response = new TeacherMatchDto.TeacherWeeklySchedule();
            response.setTeacherId(teacherId);
            response.setTeacherName(teacher.getName());
            response.setStartDate(startDate);

            // 计算结束日期（一周后）
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = start.plusDays(6);
            response.setEndDate(end.toString());

            // 构建每日时间安排（按照周一开始的顺序）
            List<TeacherMatchDto.DailySchedule> dailySchedules = new ArrayList<>();

            // 找到本周的周一作为起始日期
            LocalDate weekStart = start;
            while (weekStart.getDayOfWeek().getValue() != 1) { // 1 = 周一
                weekStart = weekStart.minusDays(1);
            }

            // 按照周一开始的顺序构建7天的时间安排
            for (int i = 0; i < 7; i++) {
                LocalDate currentDate = weekStart.plusDays(i);
                // 数据库weekday现在与Java DayOfWeek格式一致：1=周一, 2=周二, ..., 7=周日
                int weekday = currentDate.getDayOfWeek().getValue();

                TeacherMatchDto.DailySchedule dailySchedule = new TeacherMatchDto.DailySchedule();
                dailySchedule.setDate(currentDate.toString());
                dailySchedule.setWeekday(weekday);

                // 计算当天的完整时间段（包含可用和占用）
                List<TeacherMatchDto.TeacherAvailableTimeSlot> daySlots = calculateDailyTimeSlots(
                    weekday,
                    timeSlots,
                    scheduledCourses.getOrDefault(teacherId, new ArrayList<>()),
                    currentDate
                );
                dailySchedule.setTimeSlots(daySlots);

                dailySchedules.add(dailySchedule);
            }

            // 去重时间段（如果有重复的时间段）
            dailySchedules.forEach(ds -> ds.setTimeSlots(
                ds.getTimeSlots().stream().distinct().collect(Collectors.toList())
            ));

            response.setDailySchedules(dailySchedules);

            return response;

        } catch (Exception e) {
            log.error("获取教师周课表失败: teacherId={}", teacherId, e);
            throw new RuntimeException("获取教师周课表失败: " + e.getMessage());
        }
    }

    /**
     * 根据不同角色处理老师可见字段
     */
    private void processTeacherVisibility(List<TeacherMatchDto.MatchedTeacher> teachers) {
        if(systemDataQueryUtil.isSalesSystemUser()) {
            // 销售屏蔽年龄、专业
            for (TeacherMatchDto.MatchedTeacher teacher : teachers) {
                teacher.setAge(null);
            }
        }
    }

    /**
     * 获取学生信息
     *
     * @param studentId 学生ID
     * @return 学生信息
     */
    private TeacherMatchDto.StudentInfo getStudentInfo(String studentId) {

        UserStudentExt student = userStudentExtService.lambdaQuery().eq(UserStudentExt::getStudentId, studentId)
                .one();
        if(student == null) {
            throw new RuntimeException("学生信息不存在");
        }

        TeacherMatchDto.StudentInfo studentInfo = new TeacherMatchDto.StudentInfo();
        studentInfo.setStudentId(studentId);
        studentInfo.setStudentName(student.getName());
        studentInfo.setStudentPhone(student.getPhone());
        studentInfo.setGrade(student.getGrade());
        studentInfo.setSchool(student.getSchool());

        return studentInfo;
    }

    /**
     * 获取活跃教师列表（支持完整筛选条件）- 高性能版本
     *
     * 优化策略：
     * 1. 使用数据库层面的筛选，避免查询所有教师数据
     * 2. 只查询基本信息，减少数据传输量
     * 3. 支持分页和限制，避免内存溢出
     *
     * @param request 匹配请求（包含所有筛选条件）
     * @return 教师列表
     */
    private List<TeacherDto.BasicResp> getActiveTeachers(TeacherMatchDto.MatchTeachersReq request) {
        log.info("开始获取活跃教师列表，筛选条件: {}", request);
        long startTime = System.currentTimeMillis();

        try {
            // 使用优化的数据库查询，直接在数据库层面进行筛选
            List<TeacherDto.BasicResp> teachers = teacherManagementService.getFilteredActiveTeachers(request);

            long endTime = System.currentTimeMillis();
            log.info("获取活跃教师列表完成，耗时: {}ms, 教师数量: {}", endTime - startTime, teachers.size());

            return teachers;
        } catch (Exception e) {
            log.error("获取活跃教师列表失败", e);
            throw new RuntimeException("获取活跃教师列表失败: " + e.getMessage());
        }
    }



    /**
     * 创建空响应
     *
     * @param studentInfo 学生信息
     * @return 空响应
     */
    private TeacherMatchDto.MatchTeachersResp createEmptyResponse(TeacherMatchDto.StudentInfo studentInfo) {
        TeacherMatchDto.MatchTeachersResp response = new TeacherMatchDto.MatchTeachersResp();
        response.setStudentInfo(studentInfo);
        response.setTeachers(new ArrayList<>());
        response.setTotalCount(0);
        return response;
    }

    /**
     * 批量查询教师已排课程
     * 
     * 查询指定时间范围内（从开始日期到一个月后）所有教师的已排课程，
     * 包括待开始、进行中、已完成状态的课程
     *
     * @param teacherIds 教师ID列表
     * @param startDate 开始日期
     * @return 教师已排课程映射表
     */
    private Map<String, List<Course>> getTeacherScheduledCourses(List<String> teacherIds, String startDate) {
        if (teacherIds.isEmpty()) {
            return new HashMap<>();
        }

        // 计算查询时间范围：从开始日期到一个月后
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = start.plusMonths(1);

        // 批量查询所有教师的已排课程
        List<Course> allCourses = courseService.lambdaQuery()
            // 查出除content字段外的所有字段
            .select(Course.class, c -> !c.getColumn().equals("content"))
            .in(Course::getTeacherId, teacherIds)
            .in(Course::getCourseStatus, Arrays.asList("待开始", "进行中", "已完成"))
            .ge(Course::getScheduledStartTime, start.atStartOfDay())
            .le(Course::getScheduledStartTime, end.atTime(23, 59, 59))
            .eq(Course::getDeleted, false)
            .list();

        log.debug("查询到{}个教师的{}个已排课程，时间范围：{} 到 {}",
            teacherIds.size(), allCourses.size(), start, end);

        // 按教师ID分组
        return allCourses.stream()
            .collect(Collectors.groupingBy(Course::getTeacherId));
    }

    /**
     * 批量查询教师可用时间段
     *
     * @param teacherIds 教师ID列表
     * @return 教师可用时间段映射表
     */
    private Map<String, List<TeacherDto.TimeSlotResp>> getTeacherAvailableSlots(List<String> teacherIds) {
        if (teacherIds.isEmpty()) {
            return new HashMap<>();
        }

        // 使用批量查询，一次性获取所有教师的可用时间段
        return teacherManagementService.getTeacherTimeSlotsMap(teacherIds);
    }

    /**
     * 批量查询教师带教信息
     *
     * @param teacherIds 教师ID列表
     * @return 教师带教信息映射表
     */
    private Map<String, TeacherDto.TeachingInfoResp> getTeacherTeachingInfoMap(List<String> teacherIds) {
        if (teacherIds.isEmpty()) {
            return new HashMap<>();
        }

        // 使用批量查询，一次性获取所有教师的带教信息
        List<TeacherDto.TeachingInfoResp> teachingInfoList = teacherManagementService.getTeachingInfoBatch(teacherIds);
        return teachingInfoList.stream()
            .collect(Collectors.toMap(TeacherDto.TeachingInfoResp::getTeacherId, info -> info));
    }

    /**
     * 批量查询教师详细信息
     *
     * @param teacherIds 教师ID列表
     * @return 教师详细信息映射表
     */
    private Map<String, TeacherDto.DetailResp> getTeacherDetailMap(List<String> teacherIds) {
        if (teacherIds.isEmpty()) {
            return new HashMap<>();
        }

        // 使用批量查询，一次性获取所有教师的详细信息
        List<TeacherDto.DetailResp> teacherDetailList = teacherManagementService.getTeacherDetailBatch(teacherIds);
        return teacherDetailList.stream()
            .collect(Collectors.toMap(TeacherDto.DetailResp::getId, detail -> detail));
    }

    /**
     * 检查教师是否可用
     *
     * 核心算法：
     * 1. 根据已排课程计算教师的实际空余时间
     * 2. 检查教师是否能同时满足学生的所有时间需求
     * 3. 每个学生时间段都必须完全包含在教师的某个可用时间段内
     * 4. 如果能满足所有需求则构建教师信息，否则返回null
     *
     * @param teacher 教师基本信息
     * @param studentTimeSlots 学生时间需求
     * @param scheduledCourses 教师已排课程
     * @param availableSlots 教师可用时间段
     * @param teachingInfo 教师带教信息
     * @param teacherDetail 教师详细信息
     * @return 可用教师信息，如果不可用则返回null
     */
    private TeacherMatchDto.MatchedTeacher checkTeacherAvailability(
        TeacherDto.BasicResp teacher,
        List<TeacherMatchDto.StudentTimeSlot> studentTimeSlots,
        List<Course> scheduledCourses,
        List<TeacherDto.TimeSlotResp> availableSlots,
        TeacherDto.TeachingInfoResp teachingInfo,
        TeacherDto.DetailResp teacherDetail) {

        log.info("检查教师{}的时间匹配: 原始可用时间段{}个, 已排课程{}个",
            teacher.getName(), availableSlots.size(), scheduledCourses.size());

        // 打印教师原始可用时间段
//        for (TeacherDto.TimeSlotResp slot : availableSlots) {
//            log.info("  原始时间段: 星期{} {}:{} (状态: {})",
//                slot.getWeekday(), slot.getStartTime(), slot.getEndTime(), slot.getStatus());
//        }

        // 打印教师已排课程
//        for (Course course : scheduledCourses) {
//            log.info("  已排课程: {} 星期{} {}:{}",
//                course.getId(),
//                course.getScheduledStartTime().toInstant().atZone(java.time.ZoneId.systemDefault()).getDayOfWeek().getValue(),
//                course.getScheduledStartTime().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalTime(),
//                course.getScheduledEndTime().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalTime());
//        }

        // 1. 计算教师的实际空余时间（排除已排课程）
        List<TeacherMatchDto.TeacherAvailableTimeSlot> actualAvailableSlots =
            calculateActualAvailableSlots(availableSlots, scheduledCourses);

        log.info("教师{}计算后的实际可用时间段{}个:", teacher.getName(), actualAvailableSlots.size());
//        for (TeacherMatchDto.TeacherAvailableTimeSlot slot : actualAvailableSlots) {
//            log.info("  实际时间段: 星期{} {}:{} (状态: {})",
//                slot.getWeekday(), slot.getStartTime(), slot.getEndTime(), slot.getStatus());
//        }

        // 2. 检查是否能同时满足学生的所有时间需求
        boolean canScheduleAll = true;
        int matchedSlots = 0;

        for (TeacherMatchDto.StudentTimeSlot studentSlot : studentTimeSlots) {
//            log.info("检查学生时间需求: 星期{} {}:{}",
//                studentSlot.getWeekday(), studentSlot.getStartTime(), studentSlot.getEndTime());

            boolean foundMatch = false;
            for (TeacherMatchDto.TeacherAvailableTimeSlot teacherSlot : actualAvailableSlots) {
                // 只考虑可用的时间段，排除已占用的时间段
                if ("available".equals(teacherSlot.getStatus()) && isTimeSlotMatch(studentSlot, teacherSlot)) {
//                    log.info("  找到匹配的教师时间段: 星期{} {}:{}",
//                        teacherSlot.getWeekday(), teacherSlot.getStartTime(), teacherSlot.getEndTime());
                    foundMatch = true;
                    matchedSlots++;
                    break;
                }
            }
            if (!foundMatch) {
                log.info("  未找到匹配的教师时间段");
                canScheduleAll = false;
                break; // 有一个时间段无法满足，直接退出
            }
        }

        log.info("教师{}匹配结果: 需要匹配{}个时间段，实际匹配{}个，能否满足所有需求: {}",
            teacher.getName(), studentTimeSlots.size(), matchedSlots, canScheduleAll);

        // 3. 如果不能排课，返回null
        if (!canScheduleAll) {
            // 如果教师没有任何可用时间段，给出更明确的提示
            if (actualAvailableSlots.isEmpty()) {
                log.info("教师{}没有设置任何可用时间段", teacher.getName());
            } else {
                log.info("教师{}的可用时间段无法满足学生的时间需求", teacher.getName());
            }
            return null;
        }

        // 4. 构建可用教师信息
        return buildMatchedTeacher(teacher, teachingInfo, teacherDetail, actualAvailableSlots);
    }

    private static TeacherMatchDto.MatchedTeacher buildMatchedTeacher(TeacherDto.BasicResp teacher, TeacherDto.TeachingInfoResp teachingInfo, TeacherDto.DetailResp teacherDetail, List<TeacherMatchDto.TeacherAvailableTimeSlot> actualAvailableSlots) {
        TeacherMatchDto.MatchedTeacher availableTeacher = new TeacherMatchDto.MatchedTeacher();
        availableTeacher.setTeacherId(teacher.getId());
        availableTeacher.setTeacherName(teacher.getName());
        availableTeacher.setTeacherPhone(teacher.getPhone());
        availableTeacher.setGroupName(teacher.getGroupName());

        // 设置教师详细信息
        if (teacherDetail != null) {
            // 基础信息
            availableTeacher.setGender(teacherDetail.getGender());
            availableTeacher.setAge(teacherDetail.getAge());
            availableTeacher.setEmploymentType(teacherDetail.getEmploymentType());
            availableTeacher.setCurrentStatus(teacherDetail.getCurrentStatus());

            // 教育背景
            availableTeacher.setEducation(teacherDetail.getEducation());
            availableTeacher.setGraduateSchool(teacherDetail.getGraduateSchool());
            availableTeacher.setUniversityType(teacherDetail.getUniversityType());
            availableTeacher.setIsNormalUniversity(teacherDetail.getIsNormalUniversity());
            availableTeacher.setStudyAbroad(teacherDetail.getStudyAbroad());
            availableTeacher.setStudyAbroadCountry(teacherDetail.getStudyAbroadCountry());

            // 教学资质
            availableTeacher.setTeachingCertificateLevel(teacherDetail.getTeachingCertificateLevel());
            availableTeacher.setSubjects(teacherDetail.getSubjects() != null ? teacherDetail.getSubjects() : new ArrayList<>());
            availableTeacher.setTrainingSubjects(teacherDetail.getTrainingSubjects() != null ? teacherDetail.getTrainingSubjects() : new ArrayList<>());
            availableTeacher.setEnglishQualification(teacherDetail.getEnglishQualification());
            availableTeacher.setMandarinQualification(teacherDetail.getMandarinQualification());
            availableTeacher.setCommunicationAbility(teacherDetail.getCommunicationAbility());
            availableTeacher.setEnglishPronunciation(teacherDetail.getEnglishPronunciation());
            availableTeacher.setTeachingYears(teacherDetail.getTeachingYears() != null ? teacherDetail.getTeachingYears() : 0);

            // 教学经历和风格
            availableTeacher.setTaughtCourses(teacherDetail.getTaughtCourses() != null ? teacherDetail.getTaughtCourses() : new ArrayList<>());
            availableTeacher.setTeachingStyle(teacherDetail.getTeachingStyle() != null ? teacherDetail.getTeachingStyle() : new ArrayList<>());
            availableTeacher.setSuitableGrades(teacherDetail.getSuitableGrades() != null ? teacherDetail.getSuitableGrades() : new ArrayList<>());
            availableTeacher.setSuitableLevels(teacherDetail.getSuitableLevels() != null ? teacherDetail.getSuitableLevels() : new ArrayList<>());
            availableTeacher.setSuitablePersonality(teacherDetail.getSuitablePersonality());
        } else {
            // 设置默认值
            availableTeacher.setSubjects(new ArrayList<>());
            availableTeacher.setTrainingSubjects(new ArrayList<>());
            availableTeacher.setTeachingYears(0);
            availableTeacher.setTaughtCourses(new ArrayList<>());
            availableTeacher.setTeachingStyle(new ArrayList<>());
            availableTeacher.setSuitableGrades(new ArrayList<>());
            availableTeacher.setSuitableLevels(new ArrayList<>());
        }

        // 设置当前学生数
        int currentStudents = teachingInfo != null ? teachingInfo.getCurrentStudents() : 0;
        availableTeacher.setCurrentStudents(currentStudents);

        // 设置可用时间段
        availableTeacher.setAvailableTimeSlots(actualAvailableSlots);
        availableTeacher.setStatus(teacher.getStatus());
        return availableTeacher;
    }

    /**
     * 计算教师的实际空余时间
     *
     * 算法说明：
     * 1. 遍历教师的每个可用时间段
     * 2. 检查该时间段内是否有已排课程
     * 3. 如果有冲突课程，则将时间段分割为多个空余时间段
     * 4. 如果无冲突，则整个时间段都可用
     *
     * @param availableSlots 教师可用时间段
     * @param scheduledCourses 教师已排课程
     * @return 实际空余时间段列表
     */
    private List<TeacherMatchDto.TeacherAvailableTimeSlot> calculateActualAvailableSlots(
        List<TeacherDto.TimeSlotResp> availableSlots,
        List<Course> scheduledCourses) {

        List<TeacherMatchDto.TeacherAvailableTimeSlot> actualSlots = new ArrayList<>();

        // 首先过滤出状态为 "available" 的时间段
        List<TeacherDto.TimeSlotResp> validSlots = availableSlots.stream()
            .filter(slot -> "available".equals(slot.getStatus()))
            .collect(Collectors.toList());

        log.info("过滤后的可用时间段: 原始{}个, 过滤后{}个", availableSlots.size(), validSlots.size());

        for (TeacherDto.TimeSlotResp slot : validSlots) {
            // 将教师可用时间段根据已排课程进行智能分割
            List<TeacherMatchDto.TeacherAvailableTimeSlot> slotParts =
                splitSlotByScheduledCourses(slot, scheduledCourses);
            actualSlots.addAll(slotParts);
        }

        return actualSlots;
    }

    /**
     * 计算指定日期的完整时间段（包含可用和占用）
     *
     * 算法说明：
     * 1. 获取教师在指定星期几的所有可用时间段
     * 2. 获取该日期的所有已排课程
     * 3. 将可用时间段和占用时间段合并，按时间顺序排列
     * 4. 返回完整的时间段列表，包含状态信息
     *
     * @param weekday 星期几 (1-7, 1为周一, 7为周日)
     * @param allTimeSlots 教师所有时间段
     * @param scheduledCourses 已排课程列表
     * @param currentDate 当前日期
     * @return 当天的完整时间段列表
     */
    private List<TeacherMatchDto.TeacherAvailableTimeSlot> calculateDailyTimeSlots(
        int weekday,
        List<TeacherDto.TimeSlotResp> allTimeSlots,
        List<Course> scheduledCourses,
        LocalDate currentDate) {

        List<TeacherMatchDto.TeacherAvailableTimeSlot> result = new ArrayList<>();

        // 1. 获取该星期几的可用时间段
        List<TeacherDto.TimeSlotResp> allWeekdaySlots = allTimeSlots.stream()
            .filter(slot -> slot.getWeekday().equals(weekday))
            .collect(Collectors.toList());

        List<TeacherDto.TimeSlotResp> weekdaySlots = allWeekdaySlots.stream()
            .filter(slot -> "available".equals(slot.getStatus()))
            .collect(Collectors.toList());

        log.info("星期{}的所有时间段数量: {}, 可用时间段数量: {}", weekday, allWeekdaySlots.size(), weekdaySlots.size());
        if (allWeekdaySlots.size() > 0) {
            allWeekdaySlots.forEach(slot -> {
                log.info("星期{}时间段: {}:{}-{}, 状态: {}", weekday, slot.getWeekday(), slot.getStartTime(), slot.getEndTime(), slot.getStatus());
            });
        }

        // 2. 获取未来一个月内该星期几的所有已排课程
        List<Course> weekdayCourses = scheduledCourses.stream()
            .filter(course -> {
                // 检查课程的星期几是否匹配
                int courseWeekday = course.getScheduledStartTime().toInstant()
                    .atZone(java.time.ZoneId.systemDefault()).getDayOfWeek().getValue();
                return courseWeekday == weekday;
            })
            .sorted(Comparator.comparing(Course::getScheduledStartTime))
            .collect(Collectors.toList());

        log.debug("星期{}的可用时间段数量: {}, 已排课程数量: {}", weekday, weekdaySlots.size(), weekdayCourses.size());

        // 3. 为每个可用时间段计算可用和占用的部分
        for (TeacherDto.TimeSlotResp slot : weekdaySlots) {
            List<TeacherMatchDto.TeacherAvailableTimeSlot> slotParts =
                calculateSlotWithOccupiedTime(slot, weekdayCourses);
            result.addAll(slotParts);
        }

        // 4. 如果该星期几没有可用时间段，但有已排课程，也要显示占用的时间段
        if (weekdaySlots.isEmpty() && !weekdayCourses.isEmpty()) {
            for (Course course : weekdayCourses) {
                TeacherMatchDto.TeacherAvailableTimeSlot occupiedSlot = new TeacherMatchDto.TeacherAvailableTimeSlot();
                occupiedSlot.setWeekday(weekday);
                LocalTime courseStart = course.getScheduledStartTime().toInstant()
                    .atZone(java.time.ZoneId.systemDefault()).toLocalTime();
                LocalTime courseEnd = course.getScheduledEndTime().toInstant()
                    .atZone(java.time.ZoneId.systemDefault()).toLocalTime();
                occupiedSlot.setStartTime(courseStart.toString());
                occupiedSlot.setEndTime(courseEnd.toString());
                occupiedSlot.setStatus("occupied");
                result.add(occupiedSlot);
            }
        }

        // 5. 按开始时间排序
        result.sort((a, b) -> LocalTime.parse(a.getStartTime()).compareTo(LocalTime.parse(b.getStartTime())));

        return result;
    }

    /**
     * 计算时间段的可用和占用部分
     *
     * 示例：
     * 原时间段：9:00-12:00
     * 已排课程：10:00-11:00
     * 结果：
     * - 9:00-10:00 (available)
     * - 10:00-11:00 (occupied)
     * - 11:00-12:00 (available)
     *
     * @param slot 原始时间段
     * @param weekdayCourses 该星期几的所有已排课程
     * @return 分割后的时间段列表（包含可用和占用）
     */
    private List<TeacherMatchDto.TeacherAvailableTimeSlot> calculateSlotWithOccupiedTime(
        TeacherDto.TimeSlotResp slot,
        List<Course> weekdayCourses) {

        List<TeacherMatchDto.TeacherAvailableTimeSlot> result = new ArrayList<>();

        // 获取与该时间段在时间上冲突的课程（只检查时间，不检查星期几，因为已经过滤过了）
        List<Course> conflictCourses = weekdayCourses.stream()
            .filter(course -> isTimeOverlap(slot, course))
            .sorted(Comparator.comparing(Course::getScheduledStartTime))
            .collect(Collectors.toList());

        if (conflictCourses.isEmpty()) {
            // 没有冲突，整个时间段都可用
            result.add(convertToAvailableTimeSlot(slot));
            return result;
        }

        // 有冲突，需要分割时间段
        LocalTime slotStart = LocalTime.parse(slot.getStartTime());
        LocalTime slotEnd = LocalTime.parse(slot.getEndTime());
        LocalTime currentTime = slotStart;

        for (Course course : conflictCourses) {
            LocalTime courseStart = course.getScheduledStartTime().toInstant()
                .atZone(java.time.ZoneId.systemDefault()).toLocalTime();
            LocalTime courseEnd = course.getScheduledEndTime().toInstant()
                .atZone(java.time.ZoneId.systemDefault()).toLocalTime();

            // 添加课程前的可用时间段
            if (currentTime.isBefore(courseStart)) {
                TeacherMatchDto.TeacherAvailableTimeSlot availableSlot = new TeacherMatchDto.TeacherAvailableTimeSlot();
                availableSlot.setWeekday(slot.getWeekday());
                availableSlot.setStartTime(currentTime.toString());
                availableSlot.setEndTime(courseStart.toString());
                availableSlot.setStatus("available");
                result.add(availableSlot);
            }

            // 添加占用的时间段
            TeacherMatchDto.TeacherAvailableTimeSlot occupiedSlot = new TeacherMatchDto.TeacherAvailableTimeSlot();
            occupiedSlot.setWeekday(slot.getWeekday());
            occupiedSlot.setStartTime(courseStart.toString());
            occupiedSlot.setEndTime(courseEnd.toString());
            occupiedSlot.setStatus("occupied");
            result.add(occupiedSlot);

            // 更新当前时间
            currentTime = courseEnd.isAfter(currentTime) ? courseEnd : currentTime;
        }

        // 添加最后的可用时间段
        if (currentTime.isBefore(slotEnd)) {
            TeacherMatchDto.TeacherAvailableTimeSlot availableSlot = new TeacherMatchDto.TeacherAvailableTimeSlot();
            availableSlot.setWeekday(slot.getWeekday());
            availableSlot.setStartTime(currentTime.toString());
            availableSlot.setEndTime(slotEnd.toString());
            availableSlot.setStatus("available");
            result.add(availableSlot);
        }

        return result;
    }

    /**
     * 根据已排课程智能分割时间段
     *
     * 示例：
     * 原时间段：周一 9:00-12:00
     * 已排课程：周一 10:00-11:00
     * 分割结果：周一 9:00-10:00, 周一 11:00-12:00
     *
     * @param slot 原始时间段
     * @param scheduledCourses 已排课程列表
     * @return 分割后的时间段列表
     */
    private List<TeacherMatchDto.TeacherAvailableTimeSlot> splitSlotByScheduledCourses(
        TeacherDto.TimeSlotResp slot,
        List<Course> scheduledCourses) {

        List<TeacherMatchDto.TeacherAvailableTimeSlot> result = new ArrayList<>();

        // 获取该时间段内的所有冲突课程，并按开始时间排序
        List<Course> conflictCourses = scheduledCourses.stream()
            .filter(course -> isTimeConflict(slot, course))
            .sorted(Comparator.comparing(Course::getScheduledStartTime))
            .collect(Collectors.toList());

        if (conflictCourses.isEmpty()) {
            // 没有冲突，整个时间段都可用
            result.add(convertToAvailableTimeSlot(slot));
            return result;
        }

        // 有冲突，需要智能分割时间段
        LocalTime slotStart = LocalTime.parse(slot.getStartTime());
        LocalTime slotEnd = LocalTime.parse(slot.getEndTime());
        LocalTime currentStart = slotStart;

        for (Course course : conflictCourses) {
            LocalTime courseStart = course.getScheduledStartTime().toInstant()
                .atZone(java.time.ZoneId.systemDefault()).toLocalTime();
            LocalTime courseEnd = course.getScheduledEndTime().toInstant()
                .atZone(java.time.ZoneId.systemDefault()).toLocalTime();

            // 如果当前开始时间在课程开始时间之前，添加空余时间段
            if (currentStart.isBefore(courseStart)) {
                TeacherMatchDto.TeacherAvailableTimeSlot availableSlot = new TeacherMatchDto.TeacherAvailableTimeSlot();
                availableSlot.setWeekday(slot.getWeekday());
                availableSlot.setStartTime(currentStart.toString());
                availableSlot.setEndTime(courseStart.toString());
                availableSlot.setStatus("available");
                result.add(availableSlot);
            }

            // 更新当前开始时间为课程结束时间
            currentStart = courseEnd.isAfter(currentStart) ? courseEnd : currentStart;
        }

        // 如果最后还有剩余时间，添加最后一个空余时间段
        if (currentStart.isBefore(slotEnd)) {
            TeacherMatchDto.TeacherAvailableTimeSlot availableSlot = new TeacherMatchDto.TeacherAvailableTimeSlot();
            availableSlot.setWeekday(slot.getWeekday());
            availableSlot.setStartTime(currentStart.toString());
            availableSlot.setEndTime(slotEnd.toString());
            availableSlot.setStatus("available");
            result.add(availableSlot);
        }

        return result;
    }

    /**
     * 判断时间段与课程是否冲突
     *
     * @param slot 时间段
     * @param course 课程
     * @return 是否冲突
     */
    private boolean isTimeConflict(TeacherDto.TimeSlotResp slot, Course course) {
        // 检查星期几是否匹配
        // 数据库weekday现在与Java DayOfWeek格式一致：1=周一, 2=周二, ..., 7=周日
        int courseWeekday = course.getScheduledStartTime().toInstant()
            .atZone(java.time.ZoneId.systemDefault()).getDayOfWeek().getValue();

        if (!slot.getWeekday().equals(courseWeekday)) {
            log.debug("星期几不匹配: slot.weekday={}, course.weekday={}",
                slot.getWeekday(), courseWeekday);
            return false;
        }

        // 检查时间是否重叠
        LocalTime slotStart = LocalTime.parse(slot.getStartTime());
        LocalTime slotEnd = LocalTime.parse(slot.getEndTime());
        LocalTime courseStart = course.getScheduledStartTime().toInstant()
            .atZone(java.time.ZoneId.systemDefault()).toLocalTime();
        LocalTime courseEnd = course.getScheduledEndTime().toInstant()
            .atZone(java.time.ZoneId.systemDefault()).toLocalTime();

        boolean isConflict = slotStart.isBefore(courseEnd) && slotEnd.isAfter(courseStart);
        log.debug("时间冲突检测: slot[{}:{}] vs course[{}:{}] = {}",
            slotStart, slotEnd, courseStart, courseEnd, isConflict);

        return isConflict;
    }

    /**
     * 判断时间段与课程在时间上是否重叠（不检查星期几）
     *
     * @param slot 时间段
     * @param course 课程
     * @return 是否重叠
     */
    private boolean isTimeOverlap(TeacherDto.TimeSlotResp slot, Course course) {
        // 只检查时间是否重叠，不检查星期几
        LocalTime slotStart = LocalTime.parse(slot.getStartTime());
        LocalTime slotEnd = LocalTime.parse(slot.getEndTime());
        LocalTime courseStart = course.getScheduledStartTime().toInstant()
            .atZone(java.time.ZoneId.systemDefault()).toLocalTime();
        LocalTime courseEnd = course.getScheduledEndTime().toInstant()
            .atZone(java.time.ZoneId.systemDefault()).toLocalTime();

        boolean isOverlap = slotStart.isBefore(courseEnd) && slotEnd.isAfter(courseStart);
        log.debug("时间重叠检测: slot[{}:{}] vs course[{}:{}] = {}",
            slotStart, slotEnd, courseStart, courseEnd, isOverlap);

        return isOverlap;
    }

    /**
     * 判断学生时间需求与教师时间段是否匹配
     *
     * 匹配条件：
     * 1. 星期几必须相同
     * 2. 学生的时间段必须完全包含在教师的可用时间段内
     *
     * @param studentSlot 学生时间需求
     * @param teacherSlot 教师时间段
     * @return 是否匹配
     */
    private boolean isTimeSlotMatch(
        TeacherMatchDto.StudentTimeSlot studentSlot,
        TeacherMatchDto.TeacherAvailableTimeSlot teacherSlot) {

        // 星期几必须相同
        if (!studentSlot.getWeekday().equals(teacherSlot.getWeekday())) {
            log.debug("星期几不匹配: 学生星期{} vs 教师星期{}", studentSlot.getWeekday(), teacherSlot.getWeekday());
            return false;
        }

        // 学生时间段必须完全包含在教师可用时间段内
        LocalTime studentStart = LocalTime.parse(studentSlot.getStartTime());
        LocalTime studentEnd = LocalTime.parse(studentSlot.getEndTime());
        LocalTime teacherStart = LocalTime.parse(teacherSlot.getStartTime());
        LocalTime teacherEnd = LocalTime.parse(teacherSlot.getEndTime());

        boolean isFullyContained = !studentStart.isBefore(teacherStart) && !studentEnd.isAfter(teacherEnd);

        log.debug("时间段匹配检查: 学生[星期{} {}:{}] vs 教师[星期{} {}:{}] = {} (学生开始>=教师开始: {}, 学生结束<=教师结束: {})",
            studentSlot.getWeekday(), studentStart, studentEnd,
            teacherSlot.getWeekday(), teacherStart, teacherEnd,
            isFullyContained,
            !studentStart.isBefore(teacherStart),
            !studentEnd.isAfter(teacherEnd));

        return isFullyContained;
    }

    /**
     * 转换为可用时间段格式
     *
     * @param slot 原始时间段
     * @return 可用时间段
     */
    private TeacherMatchDto.TeacherAvailableTimeSlot convertToAvailableTimeSlot(TeacherDto.TimeSlotResp slot) {
        TeacherMatchDto.TeacherAvailableTimeSlot availableSlot = new TeacherMatchDto.TeacherAvailableTimeSlot();
        availableSlot.setWeekday(slot.getWeekday());
        availableSlot.setStartTime(slot.getStartTime());
        availableSlot.setEndTime(slot.getEndTime());
        availableSlot.setStatus(slot.getStatus());
        return availableSlot;
    }

    /**
     * 检查教师在指定日期时间段是否有1小时空闲时间（数据层操作）
     *
     * @param teacherId 教师ID
     * @param date 日期
     * @param startTime 开始时间 (HH:mm格式)
     * @param endTime 结束时间 (HH:mm格式)
     * @return 是否有空闲时间
     */
    @Override
    public boolean hasOneHourFreeTime(String teacherId, java.util.Date date, String startTime, String endTime) {
        try {
            log.info("检查教师{}在{}的{}:{}是否有空闲时间", teacherId, date, startTime, endTime);

            // 1. 获取指定日期的星期几
            LocalDate localDate = date.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
            int weekday = localDate.getDayOfWeek().getValue(); // 1=周一, 7=周日

            // 2. 获取教师在该星期几的可用时间段
            List<TeacherDto.TimeSlotResp> teacherTimeSlots = teacherManagementService.getTeacherTimeSlots(teacherId);
            List<TeacherDto.TimeSlotResp> weekdaySlots = teacherTimeSlots.stream()
                    .filter(slot -> slot.getWeekday().equals(weekday) && "available".equals(slot.getStatus()))
                    .collect(Collectors.toList());

            if (weekdaySlots.isEmpty()) {
                log.info("教师{}在星期{}没有可用时间段", teacherId, weekday);
                return false;
            }

            // 3. 获取该日期的已排课程
            LocalDate endDate = localDate.plusDays(1);
            List<Course> scheduledCourses = courseService.lambdaQuery()
                    // 查出除content字段外的所有字段
                    .select(Course.class, c -> !c.getColumn().equals("content"))
                    .eq(Course::getTeacherId, teacherId)
                    .in(Course::getCourseStatus, Arrays.asList("待开始", "进行中"))
                    .ge(Course::getScheduledStartTime, localDate.atStartOfDay())
                    .lt(Course::getScheduledStartTime, endDate.atStartOfDay())
                    .eq(Course::getDeleted, false)
                    .list();

            // 4. 检查是否有足够的连续空闲时间
            LocalTime trialStart = LocalTime.parse(startTime);
            LocalTime trialEnd = LocalTime.parse(endTime);

            boolean hasEnoughTime = hasEnoughContinuousFreeTime(weekdaySlots, scheduledCourses, trialStart, trialEnd);

            if (hasEnoughTime) {
                log.info("教师{}在{}的{}:{}有足够的空闲时间", teacherId, date, startTime, endTime);
                return true;
            }

            log.info("教师{}在{}的{}:{}没有空闲时间", teacherId, date, startTime, endTime);
            return false;

        } catch (Exception e) {
            log.error("检查教师空闲时间失败: teacherId={}, date={}", teacherId, date, e);
            return false;
        }
    }

    /**
     * 批量检查教师试听课时间可用性（数据层操作）
     *
     * @param teacherIds 教师ID列表
     * @param date 试听课日期
     * @param startTime 开始时间 (HH:mm格式)
     * @param endTime 结束时间 (HH:mm格式)
     * @return 教师ID -> 是否可用的映射
     */
    @Override
    public java.util.Map<String, Boolean> batchCheckTrialTimeAvailability(java.util.List<String> teacherIds,
                                                                          java.util.Date date,
                                                                          String startTime,
                                                                          String endTime) {
        Map<String, Boolean> result = new HashMap<>();

        if (teacherIds.isEmpty()) {
            return result;
        }

        try {
            log.info("批量检查{}个教师在{}的{}:{}试听课时间可用性", teacherIds.size(), date, startTime, endTime);

            // 1. 获取指定日期的星期几
            LocalDate localDate = date.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
            int weekday = localDate.getDayOfWeek().getValue(); // 1=周一, 7=周日

            // 2. 批量获取所有教师的时间段（修复N+1查询问题）
            Map<String, List<TeacherDto.TimeSlotResp>> teacherTimeSlotsMap =
                    teacherManagementService.getTeacherTimeSlotsMap(teacherIds);

            // 3. 批量获取该日期的所有教师已排课程
            LocalDate endDate = localDate.plusDays(1);
            List<Course> allScheduledCourses = courseService.lambdaQuery()
                    // 查出除content字段外的所有字段
                    .select(Course.class, c -> !c.getColumn().equals("content"))
                    .in(Course::getTeacherId, teacherIds)
                    .in(Course::getCourseStatus, Arrays.asList("待开始", "进行中"))
                    .ge(Course::getScheduledStartTime, localDate.atStartOfDay())
                    .lt(Course::getScheduledStartTime, endDate.atStartOfDay())
                    .eq(Course::getDeleted, false)
                    .list();

            // 按教师ID分组
            Map<String, List<Course>> teacherCoursesMap = allScheduledCourses.stream()
                    .collect(Collectors.groupingBy(Course::getTeacherId));

            // 4. 逐个检查每个教师的可用性
            LocalTime trialStart = LocalTime.parse(startTime);
            LocalTime trialEnd = LocalTime.parse(endTime);

            for (String teacherId : teacherIds) {
                boolean isAvailable = false;

                // 获取该教师在指定星期几的可用时间段
                List<TeacherDto.TimeSlotResp> teacherTimeSlots =
                        teacherTimeSlotsMap.getOrDefault(teacherId, new ArrayList<>());
                List<TeacherDto.TimeSlotResp> weekdaySlots = teacherTimeSlots.stream()
                        .filter(slot -> slot.getWeekday().equals(weekday) && "available".equals(slot.getStatus()))
                        .collect(Collectors.toList());

                if (!weekdaySlots.isEmpty()) {
                    // 获取该教师的已排课程
                    List<Course> teacherCourses = teacherCoursesMap.getOrDefault(teacherId, new ArrayList<>());

                    // 检查是否有足够的连续空闲时间
                    isAvailable = hasEnoughContinuousFreeTime(weekdaySlots, teacherCourses, trialStart, trialEnd);
                }

                result.put(teacherId, isAvailable);
            }

            long availableCount = result.values().stream().mapToLong(available -> available ? 1 : 0).sum();
            log.info("批量检查完成: {}个教师中有{}个可用", teacherIds.size(), availableCount);

            return result;

        } catch (Exception e) {
            log.error("批量检查教师试听课时间可用性失败: teacherIds={}, date={}", teacherIds, date, e);
            // 发生错误时，返回所有教师都不可用
            for (String teacherId : teacherIds) {
                result.put(teacherId, false);
            }
            return result;
        }
    }

    /**
     * 检查是否有足够的连续空闲时间
     *
     * @param availableSlots 教师可用时间段
     * @param scheduledCourses 已排课程
     * @param trialStart 试听课开始时间
     * @param trialEnd 试听课结束时间
     * @return 是否有足够的连续空闲时间（≥60分钟）
     */
    private boolean hasEnoughContinuousFreeTime(List<TeacherDto.TimeSlotResp> availableSlots,
                                               List<Course> scheduledCourses,
                                               LocalTime trialStart,
                                               LocalTime trialEnd) {

        // 计算试听课需要的时间长度（分钟）
        long trialDurationMinutes = java.time.Duration.between(trialStart, trialEnd).toMinutes();

        log.debug("试听课时间: {}-{}, 需要时长: {}分钟", trialStart, trialEnd, trialDurationMinutes);

        // 遍历每个可用时间段
        for (TeacherDto.TimeSlotResp slot : availableSlots) {
            LocalTime slotStart = LocalTime.parse(slot.getStartTime());
            LocalTime slotEnd = LocalTime.parse(slot.getEndTime());

            log.debug("检查可用时间段: {}-{}", slotStart, slotEnd);

            // 1. 计算该时间段内的实际空闲子时间段（排除已排课程）
            List<TimeSegment> freeSegments = calculateFreeTimeSegments(slotStart, slotEnd, scheduledCourses);

            // 2. 检查每个空闲子时间段与试听课时间的重叠
            for (TimeSegment freeSegment : freeSegments) {
                // 计算重叠时间段
                LocalTime overlapStart = freeSegment.start.isAfter(trialStart) ? freeSegment.start : trialStart;
                LocalTime overlapEnd = freeSegment.end.isBefore(trialEnd) ? freeSegment.end : trialEnd;

                // 检查是否有重叠
                if (!overlapStart.isBefore(overlapEnd)) {
                    continue; // 没有重叠
                }

                // 计算重叠时间长度
                long overlapMinutes = java.time.Duration.between(overlapStart, overlapEnd).toMinutes();

                log.debug("空闲时间段: {}-{}, 重叠时间: {}-{}, 重叠时长: {}分钟",
                    freeSegment.start, freeSegment.end, overlapStart, overlapEnd, overlapMinutes);

                // 如果重叠时间≥60分钟，则认为可用
                if (overlapMinutes >= 60) {
                    log.debug("找到足够的连续空闲时间: {}分钟", overlapMinutes);
                    return true;
                }
            }
        }

        log.debug("没有找到足够的连续空闲时间");
        return false;
    }

    /**
     * 计算时间段内的实际空闲子时间段（排除已排课程）
     *
     * @param slotStart 时间段开始时间
     * @param slotEnd 时间段结束时间
     * @param scheduledCourses 已排课程
     * @return 空闲子时间段列表
     */
    private List<TimeSegment> calculateFreeTimeSegments(LocalTime slotStart, LocalTime slotEnd,
                                                       List<Course> scheduledCourses) {
        List<TimeSegment> freeSegments = new ArrayList<>();

        // 获取该时间段内的所有已排课程，并按开始时间排序
        List<TimeSegment> occupiedSegments = scheduledCourses.stream()
            .map(course -> {
                LocalTime courseStart = course.getScheduledStartTime().toInstant()
                    .atZone(java.time.ZoneId.systemDefault()).toLocalTime();
                LocalTime courseEnd = course.getScheduledEndTime().toInstant()
                    .atZone(java.time.ZoneId.systemDefault()).toLocalTime();
                return new TimeSegment(courseStart, courseEnd);
            })
            .filter(segment -> {
                // 只保留与当前时间段有重叠的课程
                return segment.start.isBefore(slotEnd) && segment.end.isAfter(slotStart);
            })
            .sorted((a, b) -> a.start.compareTo(b.start))
            .collect(Collectors.toList());

        log.debug("时间段 {}-{} 内有 {} 个已排课程", slotStart, slotEnd, occupiedSegments.size());

        // 如果没有已排课程，整个时间段都是空闲的
        if (occupiedSegments.isEmpty()) {
            freeSegments.add(new TimeSegment(slotStart, slotEnd));
            return freeSegments;
        }

        // 计算空闲时间段
        LocalTime currentTime = slotStart;

        for (TimeSegment occupied : occupiedSegments) {
            // 调整占用时间段的边界，确保在当前时间段范围内
            LocalTime occupiedStart = occupied.start.isBefore(slotStart) ? slotStart : occupied.start;
            LocalTime occupiedEnd = occupied.end.isAfter(slotEnd) ? slotEnd : occupied.end;

            // 如果当前时间在占用时间段开始之前，则有空闲时间
            if (currentTime.isBefore(occupiedStart)) {
                freeSegments.add(new TimeSegment(currentTime, occupiedStart));
                log.debug("添加空闲时间段: {}-{}", currentTime, occupiedStart);
            }

            // 更新当前时间为占用时间段结束时间
            currentTime = occupiedEnd.isAfter(currentTime) ? occupiedEnd : currentTime;
        }

        // 检查最后一个占用时间段之后是否还有空闲时间
        if (currentTime.isBefore(slotEnd)) {
            freeSegments.add(new TimeSegment(currentTime, slotEnd));
            log.debug("添加最后空闲时间段: {}-{}", currentTime, slotEnd);
        }

        return freeSegments;
    }

    /**
     * 时间段内部类
     */
    private static class TimeSegment {
        final LocalTime start;
        final LocalTime end;

        TimeSegment(LocalTime start, LocalTime end) {
            this.start = start;
            this.end = end;
        }

        @Override
        public String toString() {
            return start + "-" + end;
        }
    }
}
