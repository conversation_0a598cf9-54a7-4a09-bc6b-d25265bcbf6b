package org.nonamespace.word.server.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 交易流水导出相关DTO
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
public class OrderTrxExportDto {

    /**
     * 交易流水导出查询请求
     */
    @Data
    public static class ExportReq {
        
        /**
         * 订单号
         */
        private String orderNo;
        
        /**
         * 订单ID
         */
        private String orderId;
        
        /**
         * 商户交易流水号
         */
        private String cusTrxSeq;
        
        /**
         * 交易类型（pay, refund, cancel）
         */
        private String trxType;
        
        /**
         * 交易状态
         */
        private String trxStatus;
        
        /**
         * 支付类型
         */
        private String payType;
        
        /**
         * 学生姓名
         */
        private String studentName;
        
        /**
         * 学生手机号
         */
        private String studentPhone;
        
        /**
         * 销售员姓名
         */
        private String salerName;
        
        /**
         * 交易创建开始时间
         */
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date createTimeStart;
        
        /**
         * 交易创建结束时间
         */
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date createTimeEnd;
        
        /**
         * 最小交易金额（分）
         */
        private Long minTrxAmt;
        
        /**
         * 最大交易金额（分）
         */
        private Long maxTrxAmt;
        
        /**
         * 交易索引
         */
        private Integer trxIdx;
        
        /**
         * 平台交易ID
         */
        private Integer trxId;
        
        /**
         * 导出字段列表（如果为空则导出所有字段）
         */
        private List<String> exportFields;
        
        /**
         * 导出格式（excel, csv）
         */
        private String exportFormat = "excel";
        
        /**
         * 是否包含订单详情
         */
        private Boolean includeOrderDetails = false;
        
        /**
         * 是否包含学生信息
         */
        private Boolean includeStudentInfo = false;
        
        /**
         * 最大导出数量限制
         */
        private Integer maxExportCount = 10000;
        
        /**
         * 排序字段
         */
        private String orderBy = "create_time";
        
        /**
         * 排序方向（ASC, DESC）
         */
        private String orderDirection = "DESC";
    }

    /**
     * 交易流水导出数据响应
     */
    @Data
    public static class ExportResp {
        
        /**
         * 交易流水ID
         */
        private String trxId;
        
        /**
         * 订单号
         */
        private String orderNo;
        
        /**
         * 订单ID
         */
        private String orderId;
        
        /**
         * 商户交易流水号
         */
        private String cusTrxSeq;
        
        /**
         * 交易索引
         */
        private Integer trxIdx;
        
        /**
         * 交易类型
         */
        private String trxType;
        
        /**
         * 交易类型描述
         */
        private String trxTypeDesc;
        
        /**
         * 交易状态
         */
        private String trxStatus;
        
        /**
         * 交易状态描述
         */
        private String trxStatusDesc;
        
        /**
         * 支付类型
         */
        private String payType;
        
        /**
         * 支付类型描述
         */
        private String payTypeDesc;
        
        /**
         * 交易金额（元）
         */
        private String trxAmtYuan;
        
        /**
         * 平台交易ID
         */
        private Integer platformTrxId;
        
        /**
         * 学生姓名
         */
        private String studentName;
        
        /**
         * 学生手机号
         */
        private String studentPhone;
        
        /**
         * 销售员姓名
         */
        private String salerName;
        
        /**
         * 销售员手机号
         */
        private String salerPhone;
        
        /**
         * 订单标题
         */
        private String orderBody;
        
        /**
         * 订单状态
         */
        private String orderStatus;
        
        /**
         * 订单总金额（元）
         */
        private String orderTotalAmtYuan;
        
        /**
         * 产品名称
         */
        private String productName;
        
        /**
         * 学科
         */
        private String subject;
        
        /**
         * 课型
         */
        private String courseType;
        
        /**
         * 交易创建时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date createTime;
        
        /**
         * 交易更新时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date updateTime;
        
        /**
         * 创建人
         */
        private String createBy;
        
        /**
         * 更新人
         */
        private String updateBy;
        
        /**
         * 备注
         */
        private String remark;
    }

    /**
     * 导出结果响应
     */
    @Data
    public static class ExportResultResp {
        
        /**
         * 导出文件名
         */
        private String fileName;
        
        /**
         * 导出记录数
         */
        private Integer exportCount;
        
        /**
         * 文件大小（字节）
         */
        private Long fileSize;
        
        /**
         * 导出时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date exportTime;
        
        /**
         * 导出用户
         */
        private String exportUser;
        
        /**
         * 导出条件摘要
         */
        private String exportConditions;
        
        /**
         * 下载链接（如果是异步导出）
         */
        private String downloadUrl;
        
        /**
         * 导出状态（processing, completed, failed）
         */
        private String status;
        
        /**
         * 错误信息（如果导出失败）
         */
        private String errorMessage;
        
        /**
         * 交易类型统计
         */
        private String trxTypeStats;
        
        /**
         * 交易状态统计
         */
        private String trxStatusStats;
        
        /**
         * 总交易金额（元）
         */
        private String totalTrxAmtYuan;
    }
}
