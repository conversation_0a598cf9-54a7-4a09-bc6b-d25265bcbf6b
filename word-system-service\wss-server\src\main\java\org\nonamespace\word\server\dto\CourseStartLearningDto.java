package org.nonamespace.word.server.dto;


import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.nonamespace.word.server.dto.course.CourseSectionDto;

import java.util.List;

@Data
public class CourseStartLearningDto {


    @Getter @Setter
    public static class Req {
        private String textbookId;
        private String unitId;

        private List<String> wordIds;
        private List<String> textbookItemIds;
    }


    @Getter @Setter
    public static class Resp extends CourseSectionDto {
    }

}
