package org.nonamespace.word.server.service;

public interface IYoudaoVoiceService {

    /**
     * 下载单词音频
     *
     * @param word 单词
     * @param type 音频类型（0：美式发音，1：英式发音）
     * @return 音频文件路径
     */
    String downloadWordAudio(String word, int type);

    /**
     * 下载单词的美式和英式发音
     *
     * @param word 单词
     * @return 音频文件路径数组 [美式发音路径, 英式发音路径]
     */
    String[] downloadWordAudios(String word);


    /**
     * 下载单词的音频
     * @param word
     * @param type
     * @return
     */
    byte[] downloadWordAudioByte(String word, int type);
}
