package org.nonamespace.word.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.nonamespace.word.server.domain.UserStudentExt;

import java.util.List;
import java.util.Map;

public interface UserStudentExtService extends IService<UserStudentExt> {

    /**
     * 批量查询学生对应的销售ID
     *
     * @param studentIds 学生ID列表
     * @return 学生ID -> 销售ID的映射
     */
    Map<String, String> getSalesIdsByStudentIds(List<String> studentIds);
}
