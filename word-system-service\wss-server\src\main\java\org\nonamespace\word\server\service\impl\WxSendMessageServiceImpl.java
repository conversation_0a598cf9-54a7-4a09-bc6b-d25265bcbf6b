package org.nonamespace.word.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.SetUtils;
import org.nonamespace.word.common.config.WxMpProperties;
import org.nonamespace.word.server.domain.*;
import org.nonamespace.word.server.dto.course.CourseReportDataDto;
import org.nonamespace.word.server.dto.course.CourseSectionMessageWordsDto;
import org.nonamespace.word.server.dto.course.CourseSendMessageDto;
import org.nonamespace.word.server.mapper.CourseMapper;
import org.nonamespace.word.server.mapper.WxSendMessageMapper;
import org.nonamespace.word.server.service.*;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
public class WxSendMessageServiceImpl extends ServiceImpl<WxSendMessageMapper, WxSendMessage> implements IWxSendMessageService {


    private final WxMpProperties wxMpProperties;
    private final IUserService userService;
    private final ISysUserOpenidService sysUserOpenidService;
    private final ICourseSectionStepService courseSectionStepService;
    private final ICourseSectionService courseSectionService;
    private final ITextbookItemService textbookItemService;
    private final IWordService wordService;

    private final CourseMapper courseMapper;

    private final StringRedisTemplate redisTemplate;


    private static final String REDIS_KEY_END_COURSE = "course:remind:END_COURSE:";
    private static final String REDIS_KEY_START_COURSE = "course:remind:START_COURSE:";

    @Override
    public void generalEndCourseWxMessage(Course course) {
        String courseId = course.getId();
        // 设置缓存， 兜底30s后过期
        Boolean flag = redisTemplate.opsForValue().setIfAbsent(REDIS_KEY_END_COURSE + courseId, "true", 30, TimeUnit.SECONDS);
        if(flag != null && !flag) {
            log.warn("当前课程ID: {}，消息推送任务正在处理中...", courseId);
            return ;
        }

        try {
            // 获取学生信息
            List<SysUser> sysUsers = userService.lambdaQuery()
                    .select(SysUser::getUserId, SysUser::getNickName)
                    .in(SysUser::getUserId, List.of(course.getStudentId(), course.getTeacherId()))
                    .list();
            if (CollUtil.isEmpty(sysUsers)) {
                log.warn("学生ID: {}，教师ID：{}，均未找到用户信息", course.getStudentId(), course.getTeacherId());
                return;
            }
            Map<String, String> sysUserMap = sysUsers.stream().collect(Collectors.toMap(k -> String.valueOf(k.getUserId()), SysUser::getNickName));
            // 根据学生id获取对应openid
            List<SysUserOpenid> sysUserOpenids = sysUserOpenidService.lambdaQuery()
                    .in(SysUserOpenid::getUserId, List.of(course.getStudentId(), course.getTeacherId())).list();
            if (CollUtil.isEmpty(sysUserOpenids)) {
                log.warn("学生ID: {}，教师ID：{}，均未找到绑定微信openid", course.getStudentId(), course.getTeacherId());
                return;
            }

            WxMpProperties.MpConfig mpConfig = getFirstMpConfig();

            // 获取课程学习单词数量，复习单词数量，以及正确率
            List<CourseSection> courseSections = courseSectionService.lambdaQuery()
                    .select(CourseSection::getWords, CourseSection::getId, CourseSection::getType,
                            CourseSection::getTitle, CourseSection::getStatLearnedWords, CourseSection::getStatCorrectWords)
                    .eq(CourseSection::getCourseId, courseId).list();
            if(CollUtil.isEmpty(courseSections)) {
                log.warn("课程ID: {}，未找到课程章节信息", courseId);
                return ;
            }

            // 组装课堂总结信息
            WxSendMessage.ReportData reportData = buildCourseReportData(course, courseSections);

            // 结果入库
            List<WxSendMessage> sendMessages = CollUtil.newArrayList();
            sysUserOpenids.forEach(sysUserOpenid -> {
                String msgId = IdUtil.getSnowflakeNextIdStr();
                // 用户姓名。不管推给老师还是学生，名称都显示学生
                String userName = sysUserMap.getOrDefault(course.getStudentId(), sysUserOpenid.getPhonenumber());
                WxSendMessage.WxTemplateMessage messageDto = WxSendMessage.WxTemplateMessage.builder()
                        .toUser(sysUserOpenid.getOpenid())
                        .templateId(mpConfig.getLearningReportTemplateId())
                        .url(String.format(wxMpProperties.getLearningReportDetailUrl(), msgId))
                        .first(String.format("【%s】课程已完成", userName))
                        .keyword1(course.getSubject() + StrPool.C_SPACE + course.getSpecification() + StrPool.C_SPACE + course.getType())
                        .keyword2(DateUtil.formatDate(course.getActualEndTime()))
                        .keyword3(course.getCourseStatus())
                        .keyword4(String.format("新学%d词，复习%d词，拓展阅读%d词。", reportData != null ? reportData.getLearningWordCnt() : 0,
                                reportData != null ? reportData.getReviewWordCnt() : 0,
                                reportData != null ? reportData.getSentencesWordCnt() : 0))
                        .remark("点击查看详情")
                        .build();

                // 组装入库对象
                WxSendMessage sendMessage = WxSendMessage.builder()
                        .openid(messageDto.getToUser())
                        .unionid(sysUserOpenid.getUnionid())
                        .status(Boolean.FALSE)
                        .tryTimes(0)
                        .templateId(mpConfig.getLearningReportTemplateId())
                        .content(messageDto)
                        .appid(mpConfig.getAppId())
                        .trigger("下课通知")
                        .messageType("文本消息").remark("[学习课]下课")
                        .relationId(courseId)
                        .userId(String.valueOf(sysUserOpenid.getUserId()))
                        .userName(userName)
                        .reportData(reportData)
                        .build();
                sendMessage.setId(msgId);
                sendMessages.add(sendMessage);
            });

            if(CollUtil.isNotEmpty(sendMessages)){
                saveBatch(sendMessages);
                log.info("课程ID={}, 微信模板消息记录保存成功", courseId);
            }
        } catch (Exception e) {
            log.error("处理微信消息异常", e);
        } finally {
            redisTemplate.delete(REDIS_KEY_END_COURSE + courseId);
        }
    }

    @Override
    public void generalEndReviewWxMessage(Course course) {
        log.info("复习课ID={}下课，开始组装消息推送内容。", course.getId());
        String courseId = course.getId();
        // 设置缓存， 兜底30s后过期
        Boolean flag = redisTemplate.opsForValue().setIfAbsent(REDIS_KEY_END_COURSE + courseId, "true", 30, TimeUnit.SECONDS);
        if(flag != null && !flag) {
            log.warn("[复习课] - 当前课程ID: {}，消息推送任务正在处理中...", courseId);
            return ;
        }
        try {
            // 获取学生信息
            List<SysUser> sysUsers = userService.lambdaQuery()
                    .select(SysUser::getUserId, SysUser::getNickName)
                    .in(SysUser::getUserId, List.of(course.getStudentId(), course.getTeacherId()))
                    .list();
            if (CollUtil.isEmpty(sysUsers)) {
                log.warn("学生ID: {}，教师ID：{}，均未找到用户信息", course.getStudentId(), course.getTeacherId());
                return;
            }
            Map<String, String> sysUserMap = sysUsers.stream().collect(Collectors.toMap(k -> String.valueOf(k.getUserId()), SysUser::getNickName));
            // 根据学生id获取对应openid
            List<SysUserOpenid> sysUserOpenids = sysUserOpenidService.lambdaQuery()
                    .in(SysUserOpenid::getUserId, List.of(course.getStudentId(), course.getTeacherId()))
                    .list();
            if (CollUtil.isEmpty(sysUserOpenids)) {
                log.warn("学生ID: {}，教师ID：{}，均未找到绑定微信openid", course.getStudentId(), course.getTeacherId());
                return;
            }

            WxMpProperties.MpConfig mpConfig = getFirstMpConfig();

            // 获取课程学习单词数量，复习单词数量，以及正确率
            List<CourseSection> courseSections = courseSectionService.lambdaQuery()
                    .select(CourseSection::getWords, CourseSection::getId, CourseSection::getType,
                            CourseSection::getTitle, CourseSection::getStatLearnedWords, CourseSection::getStatCorrectWords)
                    .eq(CourseSection::getCourseId, courseId).list();
            if(CollUtil.isEmpty(courseSections)) {
                log.warn("[复习课] - 课程ID: {}，未找到课程章节信息", courseId);
                return ;
            }

            // 组装课堂总结信息
            WxSendMessage.ReportData reportData = buildReviewReportData(course, courseSections);

            // 结果入库
            List<WxSendMessage> sendMessages = CollUtil.newArrayList();
            sysUserOpenids.forEach(sysUserOpenid -> {
                // 用户姓名。不管推给老师还是学生，名称都显示学生
                String userName = sysUserMap.getOrDefault(course.getStudentId(), sysUserOpenid.getPhonenumber());
                String msgId = IdUtil.getSnowflakeNextIdStr();
                WxSendMessage.WxTemplateMessage messageDto = WxSendMessage.WxTemplateMessage.builder()
                        .toUser(sysUserOpenid.getOpenid())
                        .templateId(mpConfig.getLearningReportTemplateId())
                        .url(String.format(wxMpProperties.getReviewReportDetailUrl(), msgId))
                        .first(String.format("【%s】课程已完成", userName))
                        .keyword1(course.getSubject() + StrPool.C_SPACE + course.getSpecification() + StrPool.C_SPACE + course.getType())
                        .keyword2(DateUtil.formatDate(course.getActualEndTime()))
                        .keyword3(course.getCourseStatus())
                        .keyword4(String.format("本次复习%d词，正确率%s。", reportData != null ? reportData.getReviewWordCnt() : 0,
                                reportData != null ? reportData.getReviewCorrectRateStr() : "0%"))
                        .remark("点击查看详情")
                        .build();

                // 组装入库对象
                WxSendMessage sendMessage = WxSendMessage.builder()
                        .openid(messageDto.getToUser())
                        .unionid(sysUserOpenid.getUnionid())
                        .status(Boolean.FALSE)
                        .tryTimes(0)
                        .templateId(mpConfig.getLearningReportTemplateId())
                        .content(messageDto)
                        .appid(mpConfig.getAppId())
                        .trigger("下课通知")
                        .messageType("文本消息").remark("[复习课]下课")
                        .relationId(courseId)
                        .userId(String.valueOf(sysUserOpenid.getUserId()))
                        .userName(userName)
                        .reportData(reportData)
                        .build();
                sendMessage.setId(msgId);
                sendMessages.add(sendMessage);
            });

            if(CollUtil.isNotEmpty(sendMessages)){
                saveBatch(sendMessages);
                log.info("[复习课] - 课程ID={}, 微信模板消息记录保存成功", courseId);
            }
        } catch (Exception e) {
            log.error("[复习课] - 处理微信消息异常", e);
        } finally {
            redisTemplate.delete(REDIS_KEY_END_COURSE + courseId);
        }
    }

    @Override
    public void buildTomorrowCourseWxMessage() {
        List<Course> tomorrowCourseList = courseMapper.getTomorrowCourseList();
        if(CollUtil.isEmpty(tomorrowCourseList)) {
            log.warn("[buildTomorrowCourseWxMessage] - 第二天暂无上课安排，无需发送微信消息");
            return ;
        }
        List<CourseSendMessageDto> messageDtos = buildWxMessageDto(tomorrowCourseList, "[B]上课通知");
        buildStartCourseMessage(messageDtos, "批量第二天课程推送");
    }



    @Override
    public void buildToday12MinCourseList() {
        List<Course> tomorrowCourseList = courseMapper.getToday12MinCourseList();
        if(CollUtil.isEmpty(tomorrowCourseList)) {
            log.warn("[buildToday12MinCourseList] - 今天暂无上课安排，无需发送微信消息");
            return ;
        }
        List<CourseSendMessageDto> messageDtos = buildWxMessageDto(tomorrowCourseList, "[S]上课通知");
        buildStartCourseMessage(messageDtos, "课前10分钟推送");
    }

    @Override
    public void buildAfter20ModifyCourseList() {
        List<Course> tomorrowCourseList = courseMapper.getAfter20ModifyCourseList();
        if(CollUtil.isEmpty(tomorrowCourseList)) {
            log.warn("[buildAfter20ModifyCourseList] - 今天暂无调课，无需发送微信消息");
            return ;
        }
        List<CourseSendMessageDto> messageDtos = buildWxMessageDto(tomorrowCourseList, "[B]上课通知");
        buildStartCourseMessage(messageDtos, "20点后实时推送");
    }

    @Override
    public void buildRightNowCourseList(List<Course> list) {
        if(CollUtil.isEmpty(list)) {
            log.warn("[buildRightNowCourseList] - 课程信息为空，无需发送微信消息");
            return ;
        }
        List<CourseSendMessageDto> messageDtos = buildWxMessageDto(list, "[B]上课通知");
        buildStartCourseMessage(messageDtos, "20点后实时推送");
    }

    /**
     * 课程信息转成雕铣dto
     * @param courseList
     */
    private List<CourseSendMessageDto> buildWxMessageDto(List<Course> courseList, String trigger) {
        /**
         * 将这里的studentId和teacherId合并，只获取有绑定openid的数据
         */
        Set<String> studentIds = courseList.stream().map(Course::getStudentId).collect(Collectors.toSet());
        Set<String> teacherIds = courseList.stream().map(Course::getTeacherId).collect(Collectors.toSet());
        SetUtils.SetView<String> userIds = SetUtils.union(studentIds, teacherIds);
        // 获取openid
        List<SysUserOpenid> userOpenids = sysUserOpenidService.lambdaQuery()
                .in(SysUserOpenid::getUserId, userIds).list();
        Map<String, List<SysUserOpenid>> userOpenidMap = new HashMap<>();
        if(CollUtil.isNotEmpty(userOpenids)) {
            userOpenidMap = userOpenids.stream().collect(Collectors.groupingBy(SysUserOpenid::getUserId));
        }
        Map<String, List<SysUserOpenid>> finalUserOpenidMap = userOpenidMap;
        List<CourseSendMessageDto> messageDtos = new ArrayList<>();
        courseList.forEach(course -> {
            if(StrUtil.isNotEmpty(course.getTeacherId()) && finalUserOpenidMap.containsKey(course.getTeacherId())) {
                List<SysUserOpenid> sysUserOpenids = finalUserOpenidMap.get(course.getTeacherId());
                sysUserOpenids.forEach(user -> {
                    CourseSendMessageDto messageDto = BeanUtil.copyProperties(course, CourseSendMessageDto.class);
                    messageDto.setTrigger(trigger);
                    messageDto.setCourseId(course.getId());
                    messageDto.setOpenid(user.getOpenid());
                    messageDto.setUserId(course.getTeacherId());
                    messageDto.setUserName(user.getUserName());
                    messageDtos.add(messageDto);
                });
            }
            if(StrUtil.isNotEmpty(course.getStudentId()) && finalUserOpenidMap.containsKey(course.getStudentId())) {
                List<SysUserOpenid> sysUserOpenids = finalUserOpenidMap.get(course.getStudentId());
                sysUserOpenids.forEach(user -> {
                    CourseSendMessageDto messageDto = BeanUtil.copyProperties(course, CourseSendMessageDto.class);
                    messageDto.setTrigger(trigger);
                    messageDto.setCourseId(course.getId());
                    messageDto.setOpenid(user.getOpenid());
                    messageDto.setUserId(course.getStudentId());
                    messageDto.setUserName(user.getUserName());
                    messageDtos.add(messageDto);
                });
            }
        });
        return messageDtos;
    }

    private void buildStartCourseMessage(List<CourseSendMessageDto> courseList, String remark) {
        WxMpProperties.MpConfig mpConfig = getFirstMpConfig();
        // 用openid，courseId，trigger过滤一下已经入库的数据
        courseList = courseList.stream().filter(c -> !this.isExistsWxMessage(c.getUserId(), c.getOpenid(), c.getCourseId(), c.getTrigger())).toList();
        // 结果入库
        List<WxSendMessage> sendMessages = CollUtil.newArrayList();
        courseList.forEach(course -> {
            String redisKey = REDIS_KEY_START_COURSE + course.getCourseId() + StrPool.COLON + course.getOpenid() + StrPool.COLON + course.getUserId() + StrPool.COLON + course.getTrigger();
            try {
                // 如果当前数据已入库的，就不需要重复入了，同一个课程id，同一个openid，就不需要重复入库了
                String flag = redisTemplate.opsForValue().get(redisKey);
                if (StrUtil.isNotBlank(flag)) {
                    log.warn("当前课程ID: {}，OPENID={} 消息推送任务正在处理中...", course.getCourseId(), course.getOpenid());
                    return;
                }
                redisTemplate.opsForValue().set(redisKey, "true", 1, TimeUnit.MINUTES);
                String msgId = IdUtil.getSnowflakeNextIdStr();
                WxSendMessage.WxTemplateMessage messageDto = WxSendMessage.WxTemplateMessage.builder()
                        .toUser(course.getOpenid())
                        .templateId(mpConfig.getStartCourseTemplateId())
                        .first(String.format("【%s】上课提醒", course.getUserName()))
                        .keyword1(course.getSubject() + StrPool.C_SPACE + course.getSpecification() + StrPool.C_SPACE + course.getType())
                        .keyword2(DateUtil.format(course.getScheduledStartTime(), "yyyy-MM-dd HH:mm:ss"))
                        .remark("点击查看详情")
                        .build();

                // 组装入库对象
                WxSendMessage sendMessage = WxSendMessage.builder()
                        .openid(messageDto.getToUser())
                        .status(Boolean.FALSE)
                        .tryTimes(0)
                        .templateId(mpConfig.getStartCourseTemplateId())
                        .content(messageDto)
                        .appid(mpConfig.getAppId())
                        .trigger(course.getTrigger())
                        .messageType("文本消息")
                        .remark(remark)
                        .relationId(course.getCourseId())
                        .userId(course.getUserId())
                        .userName(course.getUserName())
                        .build();
                sendMessage.setId(msgId);
                sendMessages.add(sendMessage);
            } finally {
                redisTemplate.delete(redisKey);
            }
        });

        if(CollUtil.isNotEmpty(sendMessages)){
            saveBatch(sendMessages);
            log.info("上课通知  微信模板消息记录保存成功，数量={}", sendMessages.size());
        }
    }

    private boolean isExistsWxMessage(String userId, String openid, String courseId, String trigger){
        return this.lambdaQuery().eq(WxSendMessage::getOpenid, openid)
                .eq(WxSendMessage::getRelationId, courseId)
                .eq(WxSendMessage::getUserId, userId)
                .eq(WxSendMessage::getTrigger, trigger).count() > 0;
    }


    private WxMpProperties.MpConfig getFirstMpConfig() {
        Optional<WxMpProperties.MpConfig> configOptional = wxMpProperties.getConfigs().stream().findFirst();
        if(configOptional.isEmpty()) {
            throw new IllegalArgumentException("微信配置信息为空，请确认");
        }
        return configOptional.get();
    }


    /**
     * 组装课堂总结数据
     * @param course
     * @return
     */
    private WxSendMessage.ReportData buildCourseReportData(Course course, List<CourseSection> courseSections) {
        List<CourseReportDataDto> courseReportDataDtos = courseSectionStepService.statisticCourseReportData(course.getId());
        if(CollUtil.isEmpty(courseReportDataDtos)) {
            return null;
        }

        // wordCountMap
        final String[] studyType = { "新课程学习" };
        final String[] reviewType = { "抗遗忘复习" };
        Map<String, List<WxSendMessage.ReportData.CountData>> wordCountMap = new HashMap<>(){{
            put("学习词汇", CollUtil.newArrayList());
            put("复习词汇", CollUtil.newArrayList());
        }};
        courseReportDataDtos.forEach(dto -> {
            if(Arrays.asList(studyType).contains(dto.getSectionType())) {
                wordCountMap.computeIfPresent("学习词汇", (k, v) -> {
                    v.add(new WxSendMessage.ReportData.CountData(dto.getTextbookName(), dto.getWordCnt()));
                    return v;
                });
            }
            if(Arrays.asList(reviewType).contains(dto.getSectionType())) {
                wordCountMap.computeIfPresent("复习词汇", (k, v) -> {
                    v.add(new WxSendMessage.ReportData.CountData(dto.getTextbookName(), dto.getWordCnt()));
                    return v;
                });
            }
        });

        long learningWordCnt = wordCountMap.get("学习词汇").stream().mapToLong(WxSendMessage.ReportData.CountData::getCount).sum();
        long reviewWordCnt = wordCountMap.get("复习词汇").stream().mapToLong(WxSendMessage.ReportData.CountData::getCount).sum();

        Set<String> studySectionIds = courseSections.stream().filter(section -> section.getType().equalsIgnoreCase("新课程学习")).map(CourseSection::getId).collect(Collectors.toSet());


        String beforeCorrectRateStr = "0%", reviewCorrectRateStr = null;
        if(CollUtil.isNotEmpty(studySectionIds)) {
            // 增加：  未学习前：知识掌握率XX% （第1、第3环节的总正确率，单词和句子的英翻中题目正确率，）学习后：知识掌握率XX%（下课前复习的正确率）
            List<CourseSectionStep> steps = courseSectionStepService.lambdaQuery().eq(CourseSectionStep::getCourseId, course.getId())
                    .in(CourseSectionStep::getSectionId, studySectionIds)
                    .list();
            if(CollUtil.isNotEmpty(steps)) {
                // 取出  单词和句子的英翻中题目
                Map<String, List<CourseSectionStep>> stepTypeMap = steps.stream().collect(Collectors.groupingBy(CourseSectionStep::getType));
                //
                List<CourseSectionStep> beforeSteps = new ArrayList<>(stepTypeMap.getOrDefault("单词测试", List.of()));
                beforeSteps.addAll(stepTypeMap.getOrDefault("句子翻译", List.of()));

                // 统计出 题目正确率
                if(CollUtil.isNotEmpty(beforeSteps)){
                    long correctCount = beforeSteps.stream().filter(step -> step.getResult() != null && step.getResult().equalsIgnoreCase("正确")).count();
                    double beforeCorrectRate = (double) (correctCount * 100) / beforeSteps.size();
                    beforeCorrectRateStr = (int) beforeCorrectRate + "%";
                }
            }

        }

        // 知识掌握率XX%（下课前复习的正确率）
        Set<String> reviewStepIds = courseSections.stream().filter(section -> section.getType().equalsIgnoreCase("下课复习")).map(CourseSection::getId).collect(Collectors.toSet());

        if(CollUtil.isNotEmpty(reviewStepIds)) {
            List<CourseSectionStep> reviewSteps = courseSectionStepService.lambdaQuery().eq(CourseSectionStep::getCourseId, course.getId())
                    .in(CourseSectionStep::getSectionId, reviewStepIds)
                    .list();
            long correctCount = reviewSteps.stream().filter(step -> step.getResult() != null && step.getResult().equalsIgnoreCase("正确")).count();
            double correctRate = (double) (correctCount * 100) / reviewSteps.size();
            reviewCorrectRateStr = (int) correctRate + "%";
        }

        // 总词汇数 / 句型数
        long wordTotalCnt = courseReportDataDtos.stream().mapToLong(CourseReportDataDto::getWordCnt).sum();
        // 总阅读量，每个单词对应例句的单词数。 从course的content计算出来
        Set<String> wordsSet = new HashSet<>();
        courseSections.forEach(section -> {
            CourseSectionMessageWordsDto messageWordsDto = JSONUtil.toBean(section.getWords(), CourseSectionMessageWordsDto.class);
            if(messageWordsDto == null || CollUtil.isEmpty(messageWordsDto.getWordInfos())) {
                log.info("环节ID={} 不存在wordInfos或为空", section);
                return ;
            }
            messageWordsDto.getWordInfos().forEach(word -> {
                List<String> splitWords = StrUtil.split(word.getSentences().getSentenceEn(), StrPool.C_SPACE);
                wordsSet.addAll(splitWords);
            });
        });

        // （2）如果正课有增加抗遗忘复习，要把复习数据加入。
        // 第X次课的第几次复习：复习XX词，正确率XX
        // 第X次课的第几次复习：复习XX词，正确率XX
        List<WxSendMessage.ReportData.ReviewWordData> reviewWordDataList = new ArrayList<>();
        courseSections.stream().filter(section -> section.getType().equalsIgnoreCase("抗遗忘复习")).forEach(section -> {
            // 计算正确率
            String correctRateStr = "0%";
            if(section.getStatLearnedWords() > 0) {
                double correctRate = (double) (section.getStatCorrectWords() * 100) / section.getStatLearnedWords();
                correctRateStr = (int) correctRate + "%";

            }
            reviewWordDataList.add(new WxSendMessage.ReportData.ReviewWordData(section.getTitle(), section.getStatLearnedWords(), correctRateStr));
        });

        // 获取本次课程中所有出现错误的单词列表
        List<String> errorWordList = new ArrayList<>();
        try {
            errorWordList = getCourseErrorWords(course.getId());
        } catch (Exception e) {
            log.warn("获取课程错误单词列表失败, courseId: {}, error: {}", course.getId(), e.getMessage());
            // 继续执行，不影响原有功能
        }

        // 实现目标进度
        return WxSendMessage.ReportData.builder()
                .courseType(course.getType())
                .beforeCorrectRateStr(beforeCorrectRateStr)
                .reviewCorrectRateStr(reviewCorrectRateStr)
                .learningWordCnt(learningWordCnt)
                .reviewWordCnt(reviewWordCnt)
                // listenerTrainingTime，每个单词1分钟
                .listenerTrainingTime(wordTotalCnt)
                .courseTotalTime(DateUtil.between(course.getActualStartTime(), course.getActualEndTime(), DateUnit.MINUTE))
                .wordTotalCnt(wordTotalCnt)
                .sentencesCnt(wordTotalCnt)
                .sentencesWordCnt(CollUtil.isNotEmpty(wordsSet) ? wordsSet.size() : 0L)
                .goalsRate(BigDecimal.valueOf(100))
                .wordCountMap(wordCountMap)
                .reviewWordDataList(reviewWordDataList)
                .errorWordList(errorWordList != null ? errorWordList : new ArrayList<>())
                .build();
    }

    /**
     * 复习课 课堂总结数据
     * @param courseSections
     * @return
     */
    private WxSendMessage.ReportData buildReviewReportData(Course course, List<CourseSection> courseSections) {
        List<WxSendMessage.ReportData.ReviewWordData> reviewWordDataList = new ArrayList<>();
        Set<String> wordsSet = new HashSet<>();
        AtomicLong totalWordsCnt = new AtomicLong();
        AtomicLong correctWordsCnt = new AtomicLong();
        courseSections.stream().filter(section -> section.getType().equalsIgnoreCase("抗遗忘复习")).forEach(section -> {
            // 计算每次复习的正确率
            String correctRateStr = "0%";
            if(section.getStatLearnedWords() > 0) {
                double correctRate = (double) (section.getStatCorrectWords() * 100) / section.getStatLearnedWords();
                correctRateStr = (int) correctRate + "%";

            }
            reviewWordDataList.add(new WxSendMessage.ReportData.ReviewWordData(section.getTitle(), section.getStatLearnedWords(), correctRateStr));

            // 计算复习单词数量
            totalWordsCnt.addAndGet(section.getStatLearnedWords());
            // 正确单词数量
            correctWordsCnt.addAndGet(section.getStatCorrectWords());

            // 复习单词数量。为什么这里要重新统计？ 因为这里要去重。正确率计算是不能去重的
            CourseSectionMessageWordsDto messageWordsDto = JSONUtil.toBean(section.getWords(), CourseSectionMessageWordsDto.class);
            if(messageWordsDto == null || CollUtil.isEmpty(messageWordsDto.getWordInfos())) {
                log.info("环节ID={} 不存在wordInfos或为空", section);
                return ;
            }
            messageWordsDto.getWordInfos().forEach(word -> {
                wordsSet.add(word.getWord());
            });
        });

        // 复习总的正确率
        String correctRateStr = "0%";
        if(totalWordsCnt.get() > 0){
            double correctRate = (double) (correctWordsCnt.get() * 100) / totalWordsCnt.get();
            correctRateStr = (int) correctRate + "%";
        }


        // 获取本次课程中所有出现错误的单词列表
        List<String> errorWordList = new ArrayList<>();
        try {
            errorWordList = getCourseErrorWords(course.getId());
        } catch (Exception e) {
            log.warn("获取复习课程错误单词列表失败, courseId: {}, error: {}", course.getId(), e.getMessage());
            // 继续执行，不影响原有功能
        }

        // 计算总的正确率，和总的复习单词
        return WxSendMessage.ReportData.builder()
                .goalsRate(BigDecimal.valueOf(100))
                .courseType(course.getType())
                .reviewWordCnt(wordsSet.size())
                .reviewCorrectRateStr(correctRateStr)
                .reviewWordDataList(reviewWordDataList)
                .errorWordList(errorWordList != null ? errorWordList : new ArrayList<>())
                .build();
    }

    /**
     * 查询本次课程中所有出现错误的单词列表
     *
     * @param courseId 课程ID
     * @return 错误单词列表
     */
    private List<String> getCourseErrorWords(String courseId) {
        try {
            if (StrUtil.isBlank(courseId)) {
                log.warn("课程ID为空，无法获取错误单词列表");
                return new ArrayList<>();
            }

            // 1. 查询本次课程中所有出现错误的教材项ID
            List<String> errorTextbookItemIds = courseSectionStepService.lambdaQuery()
                    .select(CourseSectionStep::getTextbookItemId)
                    .eq(CourseSectionStep::getCourseId, courseId)
                    .eq(CourseSectionStep::getResult, "错误")
                    .list().stream()
                    .map(CourseSectionStep::getTextbookItemId)
                    .filter(Objects::nonNull)
                    .filter(StrUtil::isNotBlank)
                    .distinct()
                    .toList();

            if (CollUtil.isEmpty(errorTextbookItemIds)) {
                log.debug("课程{}没有错误的教材项", courseId);
                return new ArrayList<>();
            }

            // 2. 根据教材项ID查询对应的单词ID
            List<String> wordIds = textbookItemService.lambdaQuery()
                    .select(TextbookItem::getWordId)
                    .in(TextbookItem::getId, errorTextbookItemIds)
                    .list().stream()
                    .map(TextbookItem::getWordId)
                    .filter(Objects::nonNull)
                    .filter(StrUtil::isNotBlank)
                    .distinct()
                    .toList();

            if (CollUtil.isEmpty(wordIds)) {
                log.debug("课程{}的错误教材项没有对应的单词ID", courseId);
                return new ArrayList<>();
            }

            // 3. 根据单词ID查询单词文本
            List<String> errorWords = wordService.lambdaQuery()
                    .select(Word::getWord)
                    .in(Word::getId, wordIds)
                    .list().stream()
                    .map(Word::getWord)
                    .filter(Objects::nonNull)
                    .filter(StrUtil::isNotBlank)
                    .distinct()
                    .toList();

            log.debug("课程{}获取到{}个错误单词", courseId, errorWords.size());
            return errorWords;

        } catch (Exception e) {
            log.error("获取课程错误单词列表异常, courseId: {}", courseId, e);
            return new ArrayList<>();
        }
    }
}
