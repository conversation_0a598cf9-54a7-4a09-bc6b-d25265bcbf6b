package org.nonamespace.word.server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.domain.SalesGroup;
import org.nonamespace.word.server.mapper.SalesGroupMapper;
import org.nonamespace.word.server.service.ISalesGroupService;
import org.springframework.stereotype.Service;

/**
 * 销售组数据层服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
@Slf4j
@Service
public class SalesGroupServiceImpl extends ServiceImpl<SalesGroupMapper, SalesGroup> implements ISalesGroupService {
    // 纯数据层服务，只提供基础的CRUD操作
    // 业务逻辑已移至SalesGroupFacade
}
