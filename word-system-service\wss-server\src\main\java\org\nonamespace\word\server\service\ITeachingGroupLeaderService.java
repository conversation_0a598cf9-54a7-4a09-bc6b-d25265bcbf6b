package org.nonamespace.word.server.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.nonamespace.word.server.dto.management.booking.CourseBookingDto;
import org.nonamespace.word.server.dto.management.teachingleader.TeachingGroupLeaderDto;

import java.util.List;

/**
 * 教学组长服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ITeachingGroupLeaderService {

    /**
     * 根据组长用户ID获取教学组ID
     * 
     * @param leaderId 组长用户ID
     * @return 教学组ID
     */
    String getTeachingGroupIdByLeader(String leaderId);

    /**
     * 获取教学组的待审核申请列表
     *
     * @param teachingGroupId 教学组ID
     * @param req 查询请求参数
     * @return 分页结果
     */
    IPage<CourseBookingDto.BasicResp> getPendingApplicationsForGroup(String teachingGroupId, TeachingGroupLeaderDto.GetPendingApplicationsReq req);

    /**
     * 获取所有待审核申请列表（Admin和HR使用）
     *
     * @param req 查询请求参数
     * @return 分页结果
     */
    IPage<CourseBookingDto.BasicResp> getAllPendingApplications(TeachingGroupLeaderDto.GetPendingApplicationsReq req);

    /**
     * 根据用户角色获取预约课申请列表（统一查询方法）
     * 不同角色的区别只是数据访问范围的约束：
     * - Admin/HR: 查看所有申请
     * - 教学组长: 查看自己组内被预约的老师的申请
     * - 销售总监: 查看所有申请
     * - 销售组长: 查看自己和组内成员提交的申请
     * - 销售: 查看自己提交的申请
     *
     * @param currentUserId 当前用户ID
     * @param req 查询请求参数
     * @return 分页结果
     */
    IPage<CourseBookingDto.BasicResp> getApplicationsWithRoleConstraints(String currentUserId, TeachingGroupLeaderDto.GetPendingApplicationsReq req);

    /**
     * 检查用户是否有权限审核指定申请
     * 
     * @param userId 用户ID
     * @param applicationId 申请ID
     * @return 是否有权限
     */
    boolean hasReviewPermission(String userId, String applicationId);

    /**
     * 审核申请
     * 
     * @param req 审核请求参数
     * @param reviewerId 审核人ID
     * @return 是否成功
     */
    boolean reviewApplication(TeachingGroupLeaderDto.ReviewApplicationReq req, String reviewerId);

    /**
     * 批量审核申请
     * 
     * @param req 批量审核请求参数
     * @param reviewerId 审核人ID
     * @return 批量审核结果
     */
    TeachingGroupLeaderDto.BatchReviewResp batchReviewApplications(TeachingGroupLeaderDto.BatchReviewReq req, String reviewerId);

    /**
     * 获取教学组的审核统计信息
     *
     * @param teachingGroupId 教学组ID
     * @return 统计信息
     */
    TeachingGroupLeaderDto.ReviewStatsResp getReviewStatsForGroup(String teachingGroupId);

    /**
     * 获取所有审核统计信息（Admin和HR使用）
     *
     * @return 统计信息
     */
    TeachingGroupLeaderDto.ReviewStatsResp getAllReviewStats();

    /**
     * 获取教学组下的教师列表
     *
     * @param groupId 教学组ID
     * @return 教师列表
     */
    List<TeachingGroupLeaderDto.GroupTeacherResp> getGroupTeachers(String groupId);

    /**
     * 获取指定申请中本组内的候选教师列表（用于通过申请页面）
     *
     * @param applicationId 申请记录ID
     * @param groupId 教学组ID
     * @return 指定申请中本组内的候选教师列表
     */
    List<TeachingGroupLeaderDto.GroupTeacherResp> getAppliedTeachers(String applicationId, String groupId);

    /**
     * 获取教师在指定申请试听课时间的可选时间段
     *
     * @param teacherId 教师ID
     * @param applicationId 申请ID
     * @return 可选的试听课时间段列表
     */
    List<TeachingGroupLeaderDto.TrialTimeSlotResp> getTeacherAvailableSlots(String teacherId, String applicationId);

    /**
     * 分配教师到申请
     * 
     * @param req 分配请求参数
     * @param assignerId 分配人ID
     * @return 是否成功
     */
    boolean assignTeacherToApplication(TeachingGroupLeaderDto.AssignTeacherReq req, String assignerId);

    /**
     * 获取审核历史记录
     * 
     * @param teachingGroupId 教学组ID
     * @param req 查询请求参数
     * @return 分页结果
     */
    IPage<TeachingGroupLeaderDto.ReviewHistoryResp> getReviewHistory(String teachingGroupId, TeachingGroupLeaderDto.GetReviewHistoryReq req);

    /**
     * 导出审核报告
     * 
     * @param teachingGroupId 教学组ID
     * @param req 导出请求参数
     * @return 文件URL
     */
    String exportReviewReport(String teachingGroupId, TeachingGroupLeaderDto.ExportReviewReportReq req);

    /**
     * 根据组长用户ID获取教学组信息
     * 
     * @param leaderId 组长用户ID
     * @return 教学组信息
     */
    TeachingGroupLeaderDto.TeachingGroupInfoResp getTeachingGroupInfoByLeader(String leaderId);

    /**
     * 设置审核规则
     * 
     * @param teachingGroupId 教学组ID
     * @param req 设置请求参数
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean setReviewRules(String teachingGroupId, TeachingGroupLeaderDto.SetReviewRulesReq req, String operatorId);

    /**
     * 获取审核规则
     * 
     * @param teachingGroupId 教学组ID
     * @return 审核规则
     */
    TeachingGroupLeaderDto.ReviewRulesResp getReviewRules(String teachingGroupId);
}
