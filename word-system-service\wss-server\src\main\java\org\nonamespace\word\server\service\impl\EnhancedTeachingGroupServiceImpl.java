package org.nonamespace.word.server.service.impl;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.domain.TeachingGroup;
import org.nonamespace.word.server.dto.management.teachinggroup.TeacherDto;
import org.nonamespace.word.server.dto.management.teachinggroup.TeachingGroupDto;
import org.nonamespace.word.server.service.IEnhancedTeachingGroupService;
import org.nonamespace.word.server.service.ITeachingGroupMemberService;
import org.nonamespace.word.server.service.ITeachingGroupService;
import org.nonamespace.word.server.util.SystemDataQueryUtil;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 增强的教学组管理Service实现类
 * 包含部门同步和角色管理功能
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EnhancedTeachingGroupServiceImpl implements IEnhancedTeachingGroupService {

    private final ITeachingGroupService teachingGroupService;
    private final ITeachingGroupMemberService teachingGroupMemberService;
    private final ISysDeptService sysDeptService;
    private final ISysUserService sysUserService;
    private final SystemDataQueryUtil systemDataQueryUtil;
    private final RoleServiceImpl roleService;
    private final UserServiceImpl userService;

    @Override
        public boolean createTeachingGroupWithDept(TeachingGroupDto.CreateReq req) {
        try {
            log.info("开始创建教学组和部门: groupName={}, originalDeptId={}", req.getName(), req.getDeptId());

            // 1. 获取教学中心部门
            SysDept teachingCenter = systemDataQueryUtil.getTeachingCenterDept();
            log.info("获取到教学中心部门: deptId={}, deptName={}", teachingCenter.getDeptId(), teachingCenter.getDeptName());

            // 2. 创建对应的部门
            SysDept dept = new SysDept();
            dept.setDeptName(req.getName());
            dept.setParentId(teachingCenter.getDeptId());
            dept.setAncestors(teachingCenter.getAncestors() + "," + teachingCenter.getDeptId());
            dept.setOrderNum(99); // 默认排序
            dept.setStatus("0"); // 正常状态
            dept.setDelFlag("0");
            dept.setCreateTime(new Date());

            log.info("准备创建部门: deptName={}, parentId={}, ancestors={}",
                    dept.getDeptName(), dept.getParentId(), dept.getAncestors());

            int deptResult = sysDeptService.insertDept(dept);
            if (deptResult <= 0) {
                throw new RuntimeException("创建部门失败");
            }

            // 检查部门ID是否正确生成
            Long newDeptId = dept.getDeptId();
            log.info("insertDept后的部门ID: deptId={}, deptName={}", newDeptId, dept.getDeptName());

            // 如果insertDept没有设置ID，尝试通过查询获取
            if (newDeptId == null) {
                log.warn("insertDept没有返回部门ID，尝试通过查询获取");

                // 通过父部门ID和部门名称查询刚创建的部门
                Optional<SysDept> createdDept = systemDataQueryUtil.findDeptByParentAndName(
                    teachingCenter.getDeptId(), req.getName());

                if (createdDept.isPresent()) {
                    newDeptId = createdDept.get().getDeptId();
                    log.info("通过查询获取到部门ID: deptId={}, deptName={}", newDeptId, req.getName());
                } else {
                    log.error("无法通过查询找到刚创建的部门: parentId={}, deptName={}",
                            teachingCenter.getDeptId(), req.getName());
                    throw new RuntimeException("部门创建失败，未获取到部门ID");
                }
            }

            if (newDeptId == null) {
                log.error("最终仍无法获取部门ID");
                throw new RuntimeException("部门创建失败，未获取到部门ID");
            }

            // 3. 创建教学组，关联部门ID
            Long originalDeptId = req.getDeptId();
            req.setDeptId(newDeptId);

            // 验证设置是否成功
            Long setDeptId = req.getDeptId();
            log.info("设置教学组部门ID: groupName={}, originalDeptId={}, newDeptId={}, setDeptId={}",
                    req.getName(), originalDeptId, newDeptId, setDeptId);

            if (setDeptId == null || !setDeptId.equals(newDeptId)) {
                log.error("设置部门ID失败，期望: {}, 实际: {}", newDeptId, setDeptId);
                throw new RuntimeException("设置部门ID失败");
            }

            boolean groupResult = teachingGroupService.createTeachingGroup(req);
            if (!groupResult) {
                throw new RuntimeException("创建教学组失败");
            }

            // 4. 分配组长和教务
            if (StrUtil.isNotEmpty(req.getLeaderId())) {
                ensureUserHasRole(Long.valueOf(req.getLeaderId()), systemDataQueryUtil.getTeacherGroupLeaderRole().getRoleId());
            }
            if (StrUtil.isNotEmpty(req.getAdminId())) {
                ensureUserHasRole(Long.valueOf(req.getAdminId()), systemDataQueryUtil.getTeacherGroupAdminRole().getRoleId());
            }

            log.info("教学组和部门创建完成: groupName={}, deptId={}", req.getName(), req.getDeptId());
            return true;
        } catch (Exception e) {
            log.error("创建教学组和部门失败", e);
            throw new RuntimeException("创建教学组和部门失败: " + e.getMessage());
        }
    }

    @Override
        public boolean updateTeachingGroupWithDept(TeachingGroupDto.UpdateReq req) {
        try {
            // 1. 获取教学组信息
            TeachingGroup group = teachingGroupService.getById(req.getId());
            if (group == null) {
                throw new RuntimeException("教学组不存在");
            }
            
            // 2. 如果名称发生变化，同步更新部门名称
            if (!req.getName().equals(group.getName()) && group.getDeptId() != null) {
                SysDept dept = sysDeptService.selectDeptById(group.getDeptId());
                if (dept != null) {
                    dept.setDeptName(req.getName());
                    dept.setUpdateTime(new Date());
                    sysDeptService.updateDept(dept);
                }
            }
            
            // 3. 更新教学组
            boolean result = teachingGroupService.updateTeachingGroup(req);
            if (!result) {
                throw new RuntimeException("更新教学组失败");
            }

            // 4. 确保组长和教务有对应的角色
            if (StrUtil.isNotEmpty(req.getLeaderId())) {
                ensureUserHasRole(Long.valueOf(req.getLeaderId()), systemDataQueryUtil.getTeacherGroupLeaderRole().getRoleId());
            }
            if (StrUtil.isNotEmpty(req.getAdminId()) ) {
                ensureUserHasRole(Long.valueOf(req.getAdminId()), systemDataQueryUtil.getTeacherGroupAdminRole().getRoleId());
            }
            
            return true;
        } catch (Exception e) {
            log.error("更新教学组和部门失败", e);
            throw new RuntimeException("更新教学组和部门失败: " + e.getMessage());
        }
    }

    @Override
    public boolean deleteTeachingGroupWithDept(String groupId) {
        try {
            // 1. 获取教学组信息
            TeachingGroup group = teachingGroupService.getById(groupId);
            if (group == null) {
                throw new RuntimeException("教学组不存在");
            }

            // 2. 获取教学组下的所有教师
            List<TeacherDto.BasicResp> groupTeachers = teachingGroupMemberService.selectGroupTeachers(groupId);

            // 3. 获取"老师"角色，确保删除教学组时教师仍然保持教师角色
            SysRole teacherRole = systemDataQueryUtil.getTeacherRole();

            // 4. 清空这些教师的部门信息并确保保持教师角色
            for (TeacherDto.BasicResp teacher : groupTeachers) {
                userService.lambdaUpdate()
                        .set(SysUser::getDeptId, systemDataQueryUtil.getTeachingCenterDept().getDeptId())
                        .eq(SysUser::getUserId, Long.valueOf(teacher.getId()));
            }

            // 5. 删除教学组的所有成员关系
            boolean memberRemoveResult = teachingGroupMemberService.removeAllMembersByGroupId(groupId);
            if (!memberRemoveResult) {
                log.warn("删除教学组成员关系失败: groupId={}", groupId);
            } else {
                log.info("已删除教学组所有成员关系: groupId={}", groupId);
            }

            // 6. 删除对应的部门
            if (group.getDeptId() != null) {
                sysDeptService.deleteDeptById(group.getDeptId());
                log.info("已删除教学组对应的部门: deptId={}", group.getDeptId());
            }

            // 7. 删除教学组
            boolean result = teachingGroupService.deleteTeachingGroup(groupId);
            if (!result) {
                throw new RuntimeException("删除教学组失败");
            }

            log.info("成功删除教学组: groupId={}, groupName={}, 已清空 {} 名教师的部门信息",
                    groupId, group.getName(), groupTeachers.size());
            return true;
        } catch (Exception e) {
            log.error("删除教学组和部门失败", e);
            throw new RuntimeException("删除教学组和部门失败: " + e.getMessage());
        }
    }

    @Override
    public boolean assignTeachersWithRoleCheck(TeachingGroupDto.AssignTeachersReq req) {
        try {
            // 1. 获取教学组信息
            TeachingGroup group = teachingGroupService.getById(req.getGroupId());
            if (group == null) {
                throw new RuntimeException("教学组不存在");
            }
            
            // 2. 确保所有教师都有"老师"角色
            SysRole teacherRole = systemDataQueryUtil.getTeacherRole();
            for (String teacherId : req.getTeacherIds()) {
                ensureUserHasRole(Long.valueOf(teacherId), teacherRole.getRoleId());

                // 3. 将教师添加到对应的部门
                if (group.getDeptId() != null) {
                    userService.lambdaUpdate()
                            .set(SysUser::getDeptId, group.getDeptId())
                            .eq(SysUser::getUserId, Long.valueOf(teacherId));
                }
            }
            
            // 4. 分配教师到教学组
            boolean result = teachingGroupService.assignTeachers(req);
            if (!result) {
                throw new RuntimeException("分配教师失败");
            }
            
            return true;
        } catch (Exception e) {
            log.error("分配教师失败", e);
            throw new RuntimeException("分配教师失败: " + e.getMessage());
        }
    }

    @Override
    public boolean setGroupLeaderWithRole(String groupId, String teacherId) {
        try {
            // 1. 确保教师有"教学组长"角色
            SysRole leaderRole = systemDataQueryUtil.getTeacherGroupLeaderRole();
            ensureUserHasRole(Long.valueOf(teacherId), leaderRole.getRoleId());
            
            // 2. 更新教学组的组长
            TeachingGroupDto.UpdateReq req = new TeachingGroupDto.UpdateReq();
            req.setId(groupId);
            req.setLeaderId(teacherId);
            
            boolean result = teachingGroupService.updateTeachingGroup(req);
            if (!result) {
                throw new RuntimeException("设置组长失败");
            }
            
            return true;
        } catch (Exception e) {
            log.error("设置组长失败", e);
            throw new RuntimeException("设置组长失败: " + e.getMessage());
        }
    }

    @Override
    public boolean setGroupAdminWithRole(String groupId, String teacherId) {
        try {
            // 1. 确保教师有"教务"角色
            SysRole adminRole = systemDataQueryUtil.getTeacherGroupAdminRole();
            ensureUserHasRole(Long.valueOf(teacherId), adminRole.getRoleId());
            
            // 2. 更新教学组的教务
            TeachingGroupDto.UpdateReq req = new TeachingGroupDto.UpdateReq();
            req.setId(groupId);
            req.setAdminId(teacherId);
            
            boolean result = teachingGroupService.updateTeachingGroup(req);
            if (!result) {
                throw new RuntimeException("设置教务失败");
            }
            
            return true;
        } catch (Exception e) {
            log.error("设置教务失败", e);
            throw new RuntimeException("设置教务失败: " + e.getMessage());
        }
    }

    @Override
    public boolean removeTeachersFromDept(TeachingGroupDto.RemoveTeachersReq req) {
        try {
            // 1. 从教学组移除教师
            boolean result = teachingGroupService.removeTeachers(req);
            if (!result) {
                throw new RuntimeException("从教学组移除教师失败");
            }

            // 2. 获取"老师"角色，确保移除的教师仍然保持教师角色
            SysRole teacherRole = systemDataQueryUtil.getTeacherRole();

            // 3. 将教师从部门移除（设置为默认部门）并确保保持教师角色
            for (String teacherId : req.getTeacherIds()) {
                userService.lambdaUpdate()
                        .set(SysUser::getDeptId, systemDataQueryUtil.getTeachingCenterDept().getDeptId())
                        .eq(SysUser::getUserId, Long.valueOf(teacherId));
            }

            return true;
        } catch (Exception e) {
            log.error("从教学组和部门移除教师失败", e);
            throw new RuntimeException("从教学组和部门移除教师失败: " + e.getMessage());
        }
    }



    /**
     * 确保用户拥有指定角色
     */
    private void ensureUserHasRole(Long userId, Long roleId) {

        try {
            // 获取用户信息
            SysUser user = sysUserService.selectUserById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在: " + userId);
            }

            // 检查用户是否已有该角色
            boolean hasRole = false;
            if (user.getRoles() != null && !user.getRoles().isEmpty()) {
                hasRole = user.getRoles().stream()
                    .anyMatch(role -> roleId.equals(role.getRoleId()));
            }

            if (!hasRole) {
                // 获取用户当前的所有角色ID
                List<Long> currentRoleIds = new ArrayList<>();
                if (user.getRoles() != null && !user.getRoles().isEmpty()) {
                    currentRoleIds = user.getRoles().stream()
                        .map(role -> role.getRoleId())
                        .collect(Collectors.toList());
                }

                // 添加新角色到现有角色列表中
                currentRoleIds.add(roleId);

                // 使用所有角色ID（包括现有的和新的）重新分配角色
                Long[] allRoleIds = currentRoleIds.toArray(new Long[0]);
                sysUserService.insertUserAuth(userId, allRoleIds);
                log.info("为用户 {} 添加角色 {}，当前角色列表: {}", userId, roleId, currentRoleIds);
            } else {
                log.debug("用户 {} 已拥有角色 {}，无需重复分配", userId, roleId);
            }
        } catch (Exception e) {
            log.error("为用户分配角色失败: userId={}, roleId={}", userId, roleId, e);
            throw new RuntimeException("为用户分配角色失败: " + e.getMessage());
        }
    }
}
