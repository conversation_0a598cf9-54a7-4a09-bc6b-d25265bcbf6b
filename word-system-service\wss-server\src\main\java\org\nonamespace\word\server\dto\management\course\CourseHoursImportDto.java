package org.nonamespace.word.server.dto.management.course;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 课时导入DTO
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
public class CourseHoursImportDto {

    /**
     * 导入请求
     */
    @Data
    public static class ImportRequest {
        // 这里可以添加一些导入参数，比如是否覆盖现有数据等
        private Boolean overrideExisting = false;
    }

    /**
     * Excel行数据
     */
    @Data
    public static class ExcelRowData {

        /**
         * 教学组名称
         */
        private String teachingGroupName;

        /**
         * 老师姓名
         */
//        @NotBlank(message = "老师姓名不能为空")
        private String teacherName;

        /**
         * 老师手机号
         */
//        @NotBlank(message = "老师手机号不能为空")
        private String teacherPhone;

        /**
         * 学生姓名
         */
//        @NotBlank(message = "学生姓名不能为空")
        private String studentName;

        /**
         * 学生手机号
         */
//        @NotBlank(message = "学生手机号不能为空")
        private String studentPhone;

        /**
         * 学科
         */
        private String subject;

        /**
         * 课型
         */
        private String specification;

        /**
         * 课时性质（试听课、正式课）
         */
        private String nature = "正式课";

//        /**
//         * 总课时
//         */
//        @NotNull(message = "总课时不能为空")
//        private BigDecimal totalHours = BigDecimal.ZERO;

//        /**
//         * 剩余课时
//         */
//        @NotNull(message = "剩余课时不能为空")
//        private BigDecimal remainingHours = BigDecimal.ZERO;

        /**
         * 购买课时
         */
        private BigDecimal purchasedTotalHours = BigDecimal.ZERO;
        /**
         * 剩余购买课时
         */
        private BigDecimal purchasedRemainingHours = BigDecimal.ZERO;
        /**
         * 赠送课时
         */
        private BigDecimal giftTotalHours = BigDecimal.ZERO;
        /**
         * 剩余赠送课时
         */
        private BigDecimal giftRemainingHours = BigDecimal.ZERO;




        /**
         * 单价（每课时费用）
         */
        private BigDecimal unitPrice = BigDecimal.ZERO;

        /**
         * 批次号
         */
        private String batchNo;

        /**
         * 计算总课时（购买课时 + 赠送课时）
         */
        public BigDecimal getTotalHours() {
            return purchasedTotalHours.add(giftTotalHours);
        }

        /**
         * 计算剩余课时（剩余购买课时 + 剩余赠送课时）
         */
        public BigDecimal getRemainingHours() {
            return purchasedRemainingHours.add(giftRemainingHours);
        }
    }

    /**
     * 导入结果
     */
    @Data
    public static class ImportResult {
        /**
         * 总行数
         */
        private Integer totalRows;

        /**
         * 成功行数
         */
        private Integer successRows;

        /**
         * 失败行数
         */
        private Integer failedRows;

        /**
         * 创建的老师数量
         */
        private Integer createdTeachers;

        /**
         * 创建的学生数量
         */
        private Integer createdStudents;

        /**
         * 创建的师生关系数量
         */
        private Integer createdRelations;

        /**
         * 创建的课时记录数量
         */
        private Integer createdHours;

        /**
         * 错误信息
         */
        private String errorMessage;

        /**
         * 详细信息
         */
        private String detailMessage;
    }
}
