package org.nonamespace.word.server.facade.impl;

import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.ruoyi.common.utils.SecurityUtils;
import org.nonamespace.word.server.domain.*;
import org.nonamespace.word.server.dto.management.booking.CourseBookingReviewDto;
import org.nonamespace.word.server.facade.ICourseBookingReviewFacade;
import org.nonamespace.word.server.service.*;
import org.nonamespace.word.server.util.SystemDataQueryUtil;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 预约课审核管理Facade实现
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CourseBookingReviewFacadeImpl implements ICourseBookingReviewFacade {

    private final ICourseBookingApplicationService courseBookingApplicationService;
    private final ICourseBookingApplicationReviewService courseBookingApplicationReviewService;
    private final ITeachingGroupService teachingGroupService;
    private final ITeachingGroupMemberService teachingGroupMemberService;
    private final ITeacherProfileService teacherProfileService;
    private final ISaleProfileService saleProfileService;
    private final ISalesGroupService salesGroupService;
    private final UserStudentExtService userStudentExtService;
    private final ITeachingGroupLeaderService teachingGroupLeaderService;
    private final SystemDataQueryUtil systemDataQueryUtil;

    @Override
    public IPage<CourseBookingReviewDto.ListResp> getCourseBookingReviewPage(CourseBookingReviewDto.GetListReq req) {
        log.info("分页查询预约课申请列表: req={}", req);

            // 应用数据权限
            applyDataPermissions(req);

            // 构建分页对象
            Page<CourseBookingApplication> page = new Page<>(req.getPageNum(), req.getPageSize());

            // 构建查询条件
            var queryWrapper = courseBookingApplicationService.lambdaQuery()
                    .eq(CourseBookingApplication::getDeleted, false)
                    .eq(StrUtil.isNotEmpty(req.getSubject()), CourseBookingApplication::getSubject, req.getSubject())
                    .eq(StrUtil.isNotEmpty(req.getSalesGroupId()), CourseBookingApplication::getSalesGroupId, req.getSalesGroupId())
                    .eq(StrUtil.isNotEmpty(req.getSalesId()), CourseBookingApplication::getSalesId, req.getSalesId())
                    .ge(req.getCreateTimeStart() != null, CourseBookingApplication::getCreateTime, req.getCreateTimeStart())
                    .le(req.getCreateTimeEnd() != null, CourseBookingApplication::getCreateTime, req.getCreateTimeEnd());

            // 应用基于教学组视角的状态过滤
            applyTeachingGroupStatusFilter(queryWrapper, req);

            // 应用教学组管理员权限过滤
            applyTeachingGroupManagerFilter(queryWrapper, req);

            // 执行查询
            IPage<CourseBookingApplication> applicationPage = queryWrapper
                    .orderByDesc(CourseBookingApplication::getCreateTime)
                    .page(page);

            // 转换为响应对象
            return convertToListRespPage(applicationPage);


    }

    @Override
    public CourseBookingReviewDto.DetailResp getCourseBookingReviewDetail(String id) {
        log.info("查询预约课申请详情: id={}", id);

            CourseBookingApplication application = courseBookingApplicationService.getById(id);
            if (application == null || application.getDeleted()) {
                throw new RuntimeException("申请不存在");
            }

            // 检查数据权限
            if (!hasViewPermission(application)) {
                throw new RuntimeException("没有权限查看该申请");
            }

            return convertToDetailResp(application);


    }

    @Override
    public boolean reviewCourseBookingApplication(CourseBookingReviewDto.ReviewReq req) {
        log.info("审核预约课申请: req={}", req);

            // 检查是否为教学组长
            if (!hasTeachingGroupLeaderRole()) {
                throw new RuntimeException("只有教学组长可以审核申请");
            }

            // 获取当前用户ID
            String currentUserId = SecurityUtils.getUserId().toString();

            // 检查审核权限
            if (!teachingGroupLeaderService.hasReviewPermission(currentUserId, req.getApplicationId())) {
                throw new RuntimeException("没有权限审核该申请");
            }

            // 这里应该调用现有的教学组长审核服务
            // 暂时返回true，实际实现需要调用teachingGroupLeaderService
            return true;


    }

    @Override
    public CourseBookingReviewDto.BatchReviewResp batchReviewApplications(CourseBookingReviewDto.BatchReviewReq req) {
        log.info("批量审核预约课申请: req={}", req);

            // 检查是否为教学组长
            if (!hasTeachingGroupLeaderRole()) {
                throw new RuntimeException("只有教学组长可以批量审核申请");
            }

            // 暂时返回空结果，实际实现需要调用teachingGroupLeaderService
            CourseBookingReviewDto.BatchReviewResp resp = new CourseBookingReviewDto.BatchReviewResp();
            resp.setTotalCount(req.getApplicationIds().size());
            resp.setSuccessCount(0);
            resp.setFailedCount(req.getApplicationIds().size());
            resp.setFailedApplicationIds(req.getApplicationIds());
            resp.setFailedReasons(new ArrayList<>());

            return resp;


    }

    @Override
    public CourseBookingReviewDto.StatsResp getReviewStats() {
        log.info("获取审核统计信息");

            CourseBookingReviewDto.StatsResp stats = new CourseBookingReviewDto.StatsResp();

            // 根据角色获取不同的统计数据
            if (systemDataQueryUtil.isAdminOrHr()) {
                // Admin和HR可以查看所有统计
                stats = getAllStats();
            } else if (hasTeachingGroupLeaderRole()) {
                // 教学组长只能查看自己教学组的统计
                stats = getTeachingGroupStats();
            } else {
                throw new RuntimeException("没有权限查看统计信息");
            }

            return stats;


    }

    @Override
    public CourseBookingReviewDto.PermissionResp getCurrentUserPermissions() {
        log.info("获取当前用户审核权限信息");

            CourseBookingReviewDto.PermissionResp permissions = new CourseBookingReviewDto.PermissionResp();

            if (systemDataQueryUtil.isAdmin()) {
                permissions.setUserRole("admin");
                permissions.setCanReview(false);
                permissions.setCanViewAll(true);
            } else if (systemDataQueryUtil.isHr()) {
                permissions.setUserRole("hr");
                permissions.setCanReview(false);
                permissions.setCanViewAll(true);
            } else if (hasTeachingGroupLeaderRole()) {
                permissions.setUserRole("teaching_group_leader");
                permissions.setCanReview(true);
                permissions.setCanViewAll(false);

                // 获取管理的教学组信息
                String currentUserId = SecurityUtils.getUserId().toString();
                List<TeachingGroup> managedGroups = teachingGroupService.lambdaQuery()
                        .eq(TeachingGroup::getLeaderId, currentUserId)
                        .eq(TeachingGroup::getDeleted, false)
                        .list();

                List<String> groupIds = managedGroups.stream()
                        .map(TeachingGroup::getId)
                        .collect(Collectors.toList());
                permissions.setManagedGroupIds(groupIds);

                if (!managedGroups.isEmpty()) {
                    TeachingGroup currentGroup = managedGroups.get(0);
                    permissions.setCurrentGroupId(currentGroup.getId());
                    permissions.setCurrentGroupName(currentGroup.getName());
                }
            } else {
                throw new RuntimeException("没有权限访问审核功能");
            }

            return permissions;


    }

    @Override
    public List<CourseBookingReviewDto.AvailableTeacherResp> getAvailableTeachers(String teachingGroupId) {
        log.info("获取可分配教师列表: teachingGroupId={}", teachingGroupId);

            // 检查是否为教学组长
            if (!hasTeachingGroupLeaderRole()) {
                throw new RuntimeException("只有教学组长可以查看可分配教师");
            }

            // 暂时返回空列表，实际实现需要查询教学组成员
            return new ArrayList<>();


    }

    // 私有方法
    private void applyDataPermissions(CourseBookingReviewDto.GetListReq req) {
        if (systemDataQueryUtil.isAdminOrHr()) {
            // Admin和HR可以查看所有申请
            return;
        }

        if (hasTeachingGroupLeaderRole()) {
            // 教学组长只能查看自己管理的教学组的申请
            String currentUserId = SecurityUtils.getUserId().toString();

            List<TeachingGroup> managedGroups = teachingGroupService.lambdaQuery()
                    .eq(TeachingGroup::getLeaderId, currentUserId)
                    .eq(TeachingGroup::getDeleted, false)
                    .list();

            if (managedGroups.isEmpty()) {
                req.setTeachingGroupManagerUserId("NONE");
                return;
            }

            // 设置教学组管理员标记，使用和原来相同的权限过滤逻辑
            req.setTeachingGroupManagerUserId(currentUserId);
            return;
        }

        // 其他角色没有权限
        throw new RuntimeException("没有权限查看预约课申请");
    }

    private boolean hasViewPermission(CourseBookingApplication application) {
        return true;
//        if (systemDataQueryUtil.isAdminOrHr()) {
//            return true;
//        }
//
//        if (hasTeachingGroupLeaderRole()) {
//            String currentUserId = SecurityUtils.getUserId().toString();
//
//            List<TeachingGroup> managedGroups = teachingGroupService.lambdaQuery()
//                    .eq(TeachingGroup::getLeaderId, currentUserId)
//                    .eq(TeachingGroup::getDeleted, false)
//                    .list();
//
//            return managedGroups.stream()
//                    .anyMatch(group -> group.getId().equals(application.getTeachingGroupId()));
//        }
//
//        return false;
    }

    private boolean hasTeachingGroupLeaderRole() {
        // 这里需要实现检查用户是否有教学组长角色的逻辑
        // 暂时使用简单的检查
        try {
            String currentUserId = SecurityUtils.getUserId().toString();
            List<TeachingGroup> managedGroups = teachingGroupService.lambdaQuery()
                    .eq(TeachingGroup::getLeaderId, currentUserId)
                    .eq(TeachingGroup::getDeleted, false)
                    .list();
            return !managedGroups.isEmpty();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 优化的转换方法，使用批量查询避免N+1问题
     */
    private IPage<CourseBookingReviewDto.ListResp> convertToListRespPage(IPage<CourseBookingApplication> applicationPage) {
        Page<CourseBookingReviewDto.ListResp> result = new Page<>(
                applicationPage.getCurrent(),
                applicationPage.getSize(),
                applicationPage.getTotal()
        );

        List<CourseBookingApplication> applications = applicationPage.getRecords();
        if (applications.isEmpty()) {
            result.setRecords(new ArrayList<>());
            return result;
        }

        // 批量查询所有需要的数据
        BatchQueryData batchData = batchQueryRelatedData(applications);

        // 转换为响应对象
        List<CourseBookingReviewDto.ListResp> records = applications.stream()
                .map(app -> convertToListRespWithBatchData(app, batchData))
                .collect(Collectors.toList());

        result.setRecords(records);
        return result;
    }

    /**
     * 批量查询相关数据的内部类
     */
    private static class BatchQueryData {
        private Map<String, UserStudentExt> studentMap = new HashMap<>();
        private Map<String, TeacherProfile> teacherMap = new HashMap<>();
        private Map<String, TeachingGroup> teachingGroupMap = new HashMap<>();
        private Map<String, SaleProfile> saleProfileMap = new HashMap<>();
        private Map<String, SalesGroup> salesGroupMap = new HashMap<>();
    }

    /**
     * 批量查询所有相关数据
     */
    private BatchQueryData batchQueryRelatedData(List<CourseBookingApplication> applications) {
        BatchQueryData batchData = new BatchQueryData();

        // 收集所有需要查询的ID
        Set<String> studentIds = new HashSet<>();
        Set<String> teacherIds = new HashSet<>();
        Set<String> teachingGroupIds = new HashSet<>();
        Set<String> salesIds = new HashSet<>();
        Set<String> salesGroupIds = new HashSet<>();

        for (CourseBookingApplication app : applications) {
            if (StrUtil.isNotEmpty(app.getStudentId())) {
                studentIds.add(app.getStudentId());
            }
            if (app.getPreferredTeachers() != null) {
                teacherIds.addAll(app.getPreferredTeachers());
            }
//            if (StrUtil.isNotEmpty(app.getTeachingGroupId())) {
//                teachingGroupIds.add(app.getTeachingGroupId());
//            }
            if (StrUtil.isNotEmpty(app.getSalesId())) {
                salesIds.add(app.getSalesId());
            }
            if (StrUtil.isNotEmpty(app.getSalesGroupId())) {
                salesGroupIds.add(app.getSalesGroupId());
            }
        }

        // 批量查询学生信息
        if (!studentIds.isEmpty()) {
            List<UserStudentExt> students = userStudentExtService.lambdaQuery()
                    .in(UserStudentExt::getStudentId, studentIds)
                    .eq(UserStudentExt::getDeleted, false)
                    .list();
            batchData.studentMap = students.stream()
                    .collect(Collectors.toMap(UserStudentExt::getStudentId, s -> s));
        }

        // 批量查询教师信息
        if (!teacherIds.isEmpty()) {
            List<TeacherProfile> teachers = teacherProfileService.lambdaQuery()
                    .in(TeacherProfile::getTeacherId, teacherIds)
                    .eq(TeacherProfile::getDeleted, false)
                    .list();
            batchData.teacherMap = teachers.stream()
                    .collect(Collectors.toMap(TeacherProfile::getTeacherId, t -> t));
        }

        // 批量查询教学组信息
        if (!teachingGroupIds.isEmpty()) {
            List<TeachingGroup> groups = teachingGroupService.lambdaQuery()
                    .in(TeachingGroup::getId, teachingGroupIds)
                    .eq(TeachingGroup::getDeleted, false)
                    .list();
            batchData.teachingGroupMap = groups.stream()
                    .collect(Collectors.toMap(TeachingGroup::getId, g -> g));
        }

        // 批量查询销售人员信息
        if (!salesIds.isEmpty()) {
            List<SaleProfile> salesProfiles = saleProfileService.lambdaQuery()
                    .in(SaleProfile::getSalesId, salesIds)
                    .eq(SaleProfile::getDeleted, false)
                    .list();
            batchData.saleProfileMap = salesProfiles.stream()
                    .collect(Collectors.toMap(SaleProfile::getSalesId, s -> s));
        }

        // 批量查询销售组信息
        if (!salesGroupIds.isEmpty()) {
            List<SalesGroup> salesGroups = salesGroupService.lambdaQuery()
                    .in(SalesGroup::getId, salesGroupIds)
                    .eq(SalesGroup::getDeleted, false)
                    .list();
            batchData.salesGroupMap = salesGroups.stream()
                    .collect(Collectors.toMap(SalesGroup::getId, g -> g));
        }

        return batchData;
    }

    /**
     * 使用批量查询数据转换单个申请记录
     */
    private CourseBookingReviewDto.ListResp convertToListRespWithBatchData(
            CourseBookingApplication application, BatchQueryData batchData) {
        CourseBookingReviewDto.ListResp resp = new CourseBookingReviewDto.ListResp();

        resp.setId(application.getId());
        resp.setStudentId(application.getStudentId());
        resp.setSubject(application.getSubject());
        resp.setCourseType(application.getSpecification());
        resp.setStatus(application.getStatus());

        // 设置基于教学组视角的状态文本
        String statusText = getTeachingGroupPerspectiveStatusText(application);
        resp.setStatusText(statusText);
//        resp.setApplyReason(application.getApplyReason());
//        resp.setTeachingGroupId(application.getTeachingGroupId());
        resp.setSalesId(application.getSalesId());
        resp.setSalesGroupId(application.getSalesGroupId());
        resp.setCreateTime(application.getCreateTime());
        resp.setUpdateTime(application.getUpdateTime());

        // 设置学生信息
        if (StrUtil.isNotEmpty(application.getStudentId())) {
            UserStudentExt student = batchData.studentMap.get(application.getStudentId());
            if (student != null) {
                resp.setStudentName(student.getName());
                resp.setStudentPhone(student.getPhone());
            }
        }

        // 设置候选老师信息
        if (application.getPreferredTeachers() != null && !application.getPreferredTeachers().isEmpty()) {
            List<String> teacherIds = application.getPreferredTeachers();
            List<String> teacherNames = teacherIds.stream()
                    .map(id -> batchData.teacherMap.get(id))
                    .filter(Objects::nonNull)
                    .map(TeacherProfile::getRealName)
                    .collect(Collectors.toList());

            resp.setPreferredTeachers(teacherIds);
            resp.setPreferredTeacherNames(teacherNames);
        }

        // 设置教学组信息
//        if (StrUtil.isNotEmpty(application.getTeachingGroupId())) {
//            TeachingGroup group = batchData.teachingGroupMap.get(application.getTeachingGroupId());
//            if (group != null) {
//                resp.setTeachingGroupName(group.getName());
//            }
//        }

        // 设置销售人员信息
        if (StrUtil.isNotEmpty(application.getSalesId())) {
            SaleProfile saleProfile = batchData.saleProfileMap.get(application.getSalesId());
            if (saleProfile != null) {
                resp.setSalesName(saleProfile.getSalesName());
            }
        }

        // 设置销售组信息
        if (StrUtil.isNotEmpty(application.getSalesGroupId())) {
            SalesGroup salesGroup = batchData.salesGroupMap.get(application.getSalesGroupId());
            if (salesGroup != null) {
                resp.setSalesGroupName(salesGroup.getName());
            }
        }

        // 设置权限标识
        resp.setCanReview(hasTeachingGroupLeaderRole() && hasViewPermission(application));

        return resp;
    }



    private CourseBookingReviewDto.DetailResp convertToDetailResp(CourseBookingApplication application) {
        CourseBookingReviewDto.DetailResp resp = new CourseBookingReviewDto.DetailResp();

        resp.setId(application.getId());
        resp.setStudentId(application.getStudentId());
        resp.setSubject(application.getSubject());
        resp.setCourseType(application.getSpecification()); // 使用specification字段
        resp.setStatus(application.getStatus());
        resp.setStatusText(getStatusText(application.getStatus()));
        resp.setApplyReason(application.getApplicationReason()); // 使用applicationReason字段
//        resp.setTeachingGroupId(application.getTeachingGroupId());
        resp.setSalesId(application.getSalesId());
        resp.setCreateTime(application.getCreateTime());
        resp.setUpdateTime(application.getUpdateTime());

        // 设置学生信息
        if (StrUtil.isNotEmpty(application.getStudentId())) {
            try {
                UserStudentExt student = userStudentExtService.lambdaQuery()
                        .eq(UserStudentExt::getStudentId, application.getStudentId())
                        .eq(UserStudentExt::getDeleted, false)
                        .one();
                if (student != null) {
                    resp.setStudentName(student.getName());
                    resp.setStudentPhone(DesensitizedUtil.mobilePhone(student.getPhone()));
                    resp.setStudentGrade(student.getGrade());
                }
            } catch (Exception e) {
                log.warn("获取学生信息失败: studentId={}", application.getStudentId(), e);
            }
        }

        // 设置首选教师信息
        if (application.getPreferredTeachers() != null && !application.getPreferredTeachers().isEmpty()) {
            try {
                List<String> teacherIds = application.getPreferredTeachers();

                List<CourseBookingReviewDto.TeacherInfo> teacherInfos = teacherIds.stream()
                        .map(id -> {
                            CourseBookingReviewDto.TeacherInfo info = new CourseBookingReviewDto.TeacherInfo();
                            ViewUser teacher = systemDataQueryUtil.getUser(id);
                            info.setTeacherId(id);
                            info.setTeacherName(teacher.getRealName());
                            info.setPhone(teacher.getDsensitizedPhone());
                            return info;
                        })
                        .collect(Collectors.toList());

                resp.setPreferredTeachers(teacherInfos);
            } catch (Exception e) {
                log.warn("获取首选教师信息失败: teacherIds={}", application.getPreferredTeachers(), e);
            }
        }

        // 设置首选时间段信息
        if (application.getPreferredTimeSlots() != null && !application.getPreferredTimeSlots().isEmpty()) {
            try {
                List<CourseBookingReviewDto.TimeSlotInfo> timeSlotInfos = application.getPreferredTimeSlots().stream()
                        .map(slot -> {
                            CourseBookingReviewDto.TimeSlotInfo info = new CourseBookingReviewDto.TimeSlotInfo();
                            info.setWeekday(slot.getWeekday());
                            info.setStartTime(slot.getStartTime());
                            info.setEndTime(slot.getEndTime());
                            return info;
                        })
                        .collect(Collectors.toList());

                resp.setPreferredTimeSlots(timeSlotInfos);
            } catch (Exception e) {
                log.warn("获取首选时间段信息失败", e);
            }
        }

        // 设置试听课时间信息
        if (application.getTrialClassDate() != null &&
            application.getTrialClassStartTime() != null &&
            application.getTrialClassEndTime() != null) {
            try {
                CourseBookingReviewDto.TrialClassTimeInfo trialClassTime = new CourseBookingReviewDto.TrialClassTimeInfo();

                // 格式化日期为YYYY-MM-DD
                LocalDate trialDate = application.getTrialClassDate().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDate();
                trialClassTime.setDate(trialDate.toString());

                // 格式化时间为HH:mm
                trialClassTime.setStartTime(application.getTrialClassStartTime().toString().substring(0, 5));
                trialClassTime.setEndTime(application.getTrialClassEndTime().toString().substring(0, 5));

                resp.setTrialClassTime(trialClassTime);
            } catch (Exception e) {
                log.warn("获取试听课时间信息失败", e);
            }
        }

        // 设置教学组信息
//        if (StrUtil.isNotEmpty(application.getTeachingGroupId())) {
//            try {
//                TeachingGroup group = teachingGroupService.getById(application.getTeachingGroupId());
//                if (group != null && !group.getDeleted()) {
//                    resp.setTeachingGroupName(group.getName());
//                }
//            } catch (Exception e) {
//                log.warn("获取教学组信息失败: groupId={}", application.getTeachingGroupId(), e);
//            }
//        }

        // 设置销售信息
        if (StrUtil.isNotEmpty(application.getSalesId())) {
            try {
                TeacherProfile sales = teacherProfileService.lambdaQuery()
                        .eq(TeacherProfile::getTeacherId, application.getSalesId())
                        .eq(TeacherProfile::getDeleted, false)
                        .one();
                if (sales != null) {
                    resp.setSalesName(sales.getRealName());
                }
            } catch (Exception e) {
                log.warn("获取销售信息失败: salesId={}", application.getSalesId(), e);
            }
        }

        // 设置审核信息
        resp.setAssignedTeacherId(application.getApprovedTeacherId());
        resp.setReviewTime(application.getApprovalTime());
        resp.setReviewerId(application.getApprovalBy());
        resp.setRejectionReason(application.getRejectionReason());

        // 设置确认教师信息
        if (StrUtil.isNotEmpty(application.getApprovedTeacherId())) {
            try {
                TeacherProfile teacher = teacherProfileService.lambdaQuery()
                        .eq(TeacherProfile::getTeacherId, application.getApprovedTeacherId())
                        .eq(TeacherProfile::getDeleted, false)
                        .one();
                if (teacher != null) {
                    resp.setAssignedTeacherName(teacher.getRealName());
                }
            } catch (Exception e) {
                log.warn("获取确认教师信息失败: teacherId={}", application.getApprovedTeacherId(), e);
            }
        }

        // 设置审核人信息
        if (StrUtil.isNotEmpty(application.getApprovalBy())) {
            resp.setReviewerName(systemDataQueryUtil.getUser(application.getApprovalBy()).getRealName());
        }

        // 设置作废信息（如果申请状态为已作废）
        if ("已作废".equals(application.getStatus())) {
            resp.setVoidOperatorId(application.getApprovalBy());
            resp.setVoidTime(application.getApprovalTime());
            resp.setVoidReason(application.getRejectionReason());

            // 获取作废操作人员姓名
            if (StrUtil.isNotEmpty(application.getApprovalBy())) {
                resp.setVoidOperatorName(systemDataQueryUtil.getUser(application.getApprovalBy()).getRealName());
            }
        }

        // 设置确认时间段（组长审核时选择的具体时间）
        if ("已通过".equals(application.getStatus())) {
            try {
                // 获取通过的审核记录
                List<CourseBookingApplicationReview> approvedReviews = courseBookingApplicationReviewService.lambdaQuery()
                        .eq(CourseBookingApplicationReview::getApplicationId, application.getId())
                        .eq(CourseBookingApplicationReview::getReviewResult, "已通过")
                        .eq(CourseBookingApplicationReview::getDeleted, false)
                        .list();

                // 查找有分配时间段的审核记录
                for (CourseBookingApplicationReview review : approvedReviews) {
                    if (StrUtil.isNotEmpty(review.getAssignedTimeSlot())) {
                        try {
                            // 解析JSON格式的时间段信息
                            String timeSlotJson = review.getAssignedTimeSlot();
                            log.debug("解析确认时间段JSON: {}", timeSlotJson);

                            // 尝试解析为试听课时间段格式（包含date字段）
                            if (timeSlotJson.contains("\"date\"")) {
                                cn.hutool.json.JSONObject timeSlotObj = cn.hutool.json.JSONUtil.parseObj(timeSlotJson);
                                CourseBookingReviewDto.TrialTimeSlotInfo confirmedTimeSlot = new CourseBookingReviewDto.TrialTimeSlotInfo();
                                confirmedTimeSlot.setDate(timeSlotObj.getStr("date"));
                                confirmedTimeSlot.setStartTime(timeSlotObj.getStr("startTime"));
                                confirmedTimeSlot.setEndTime(timeSlotObj.getStr("endTime"));
                                resp.setConfirmedTimeSlot(confirmedTimeSlot);
                                break;
                            }
                        } catch (Exception e) {
                            log.warn("解析确认时间段失败: timeSlot={}", review.getAssignedTimeSlot(), e);
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("获取确认时间段失败: applicationId={}", application.getId(), e);
            }
        }

        // 设置审核情况
        List<CourseBookingReviewDto.ReviewStatusInfo> reviewStatusList = buildReviewStatusList(application);
        resp.setReviewStatusList(reviewStatusList);

        // 设置权限标识
        resp.setCanReview(hasTeachingGroupLeaderRole() && hasViewPermission(application));

        return resp;
    }

    /**
     * 构建审核状态列表
     */
    private List<CourseBookingReviewDto.ReviewStatusInfo> buildReviewStatusList(CourseBookingApplication application) {
        List<CourseBookingReviewDto.ReviewStatusInfo> reviewStatusList = new ArrayList<>();

        try {
            // 获取所有相关的教学组（从申请的教学组需求中获取）
            Set<String> requiredTeachingGroupIds = getRequiredTeachingGroupIds(application);

            // 获取已有的审核记录
            List<CourseBookingApplicationReview> existingReviews = courseBookingApplicationReviewService.lambdaQuery()
                    .eq(CourseBookingApplicationReview::getApplicationId, application.getId())
                    .eq(CourseBookingApplicationReview::getDeleted, false)
                    .list();

            // 创建审核记录映射
            Map<String, CourseBookingApplicationReview> reviewMap = existingReviews.stream()
                    .collect(Collectors.toMap(
                            CourseBookingApplicationReview::getTeachingGroupId,
                            review -> review,
                            (existing, replacement) -> existing
                    ));

            // 获取教学组信息
            Map<String, TeachingGroup> teachingGroupMap = new HashMap<>();
            if (!requiredTeachingGroupIds.isEmpty()) {
                List<TeachingGroup> teachingGroups = teachingGroupService.lambdaQuery()
                        .in(TeachingGroup::getId, requiredTeachingGroupIds)
                        .eq(TeachingGroup::getDeleted, false)
                        .list();
                teachingGroupMap = teachingGroups.stream()
                        .collect(Collectors.toMap(TeachingGroup::getId, group -> group));
            }

            // 获取审核人员信息
            Set<String> reviewerIds = existingReviews.stream()
                    .map(CourseBookingApplicationReview::getReviewerId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            // 暂时使用用户ID作为显示名称，后续可以扩展获取真实姓名
            Map<String, String> reviewerNameMap = new HashMap<>();
            for (String reviewerId : reviewerIds) {
                reviewerNameMap.put(reviewerId, systemDataQueryUtil.getUser(reviewerId).getRealName());
            }

            // 构建审核状态信息
            int sortOrder = 1;
            for (String groupId : requiredTeachingGroupIds) {
                CourseBookingReviewDto.ReviewStatusInfo statusInfo = new CourseBookingReviewDto.ReviewStatusInfo();
                statusInfo.setTeachingGroupId(groupId);
                statusInfo.setSortOrder(sortOrder++);

                // 设置教学组名称
                TeachingGroup group = teachingGroupMap.get(groupId);
                if (group != null) {
                    statusInfo.setTeachingGroupName(group.getName());
                } else {
                    statusInfo.setTeachingGroupName("未知教学组");
                }

                // 设置审核状态信息
                CourseBookingApplicationReview review = reviewMap.get(groupId);
                if (review != null) {
                    // 已审核
                    statusInfo.setReviewStatus(review.getReviewResult());
                    statusInfo.setReviewTime(formatDateTime(review.getReviewTime()));
                    statusInfo.setReviewerId(review.getReviewerId());
                    statusInfo.setReviewerName(reviewerNameMap.getOrDefault(review.getReviewerId(), "未知用户"));
                    statusInfo.setReviewComment(review.getReviewComment());
                } else {
                    // 未审核
                    statusInfo.setReviewStatus("待审核");
                    statusInfo.setReviewTime(null);
                    statusInfo.setReviewerId(null);
                    statusInfo.setReviewerName(null);
                    statusInfo.setReviewComment(null);
                }

                reviewStatusList.add(statusInfo);
            }

            // 按教学组名称排序
            reviewStatusList.sort(Comparator.comparing(CourseBookingReviewDto.ReviewStatusInfo::getTeachingGroupName));

        } catch (Exception e) {
            log.error("构建审核状态列表失败: applicationId={}", application.getId(), e);
        }

        return reviewStatusList;
    }

    /**
     * 获取申请需要的教学组ID集合
     */
    private Set<String> getRequiredTeachingGroupIds(CourseBookingApplication application) {
        Set<String> groupIds = new HashSet<>();

        try {
            // 从已有的审核记录中获取
            List<CourseBookingApplicationReview> allReviews = courseBookingApplicationReviewService.lambdaQuery()
                    .eq(CourseBookingApplicationReview::getApplicationId, application.getId())
                    .eq(CourseBookingApplicationReview::getDeleted, false)
                    .list();

            groupIds.addAll(allReviews.stream()
                    .map(CourseBookingApplicationReview::getTeachingGroupId)
                    .collect(Collectors.toSet()));

            // 如果没有审核记录，暂时不自动添加教学组
            // 实际业务中，教学组的审核记录应该在申请创建时就建立
            if (groupIds.isEmpty()) {
                log.debug("申请 {} 没有找到相关的教学组审核记录", application.getId());
            }

        } catch (Exception e) {
            log.warn("获取需要审核的教学组失败: applicationId={}", application.getId(), e);
        }

        return groupIds;
    }

    /**
     * 格式化日期时间
     */
    private String formatDateTime(Date dateTime) {
        if (dateTime == null) {
            return null;
        }
        try {
            return dateTime.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } catch (Exception e) {
            log.warn("格式化日期时间失败", e);
            return dateTime.toString();
        }
    }

    private String getStatusText(String status) {
        // 现在状态直接使用中文，但保持兼容性
        switch (status) {
            case "pending": return "待审核";
            case "approved":
            case "confirmed": return "已通过";
            case "rejected": return "已拒绝";
            case "cancelled": return "已取消";
            case "withdrawn": return "已撤回";
            // 新的中文状态直接返回
            case "待审核":
            case "已通过":
            case "已拒绝":
            case "已取消":
            case "已撤回":
                return status;
            default: return status;
        }
    }

    private CourseBookingReviewDto.StatsResp getAllStats() {
        CourseBookingReviewDto.StatsResp stats = new CourseBookingReviewDto.StatsResp();
        
        // 查询所有申请的统计数据
        long totalApplications = courseBookingApplicationService.lambdaQuery()
                .eq(CourseBookingApplication::getDeleted, false)
                .count();
        
        long pendingApplications = courseBookingApplicationService.lambdaQuery()
                .eq(CourseBookingApplication::getDeleted, false)
                .eq(CourseBookingApplication::getStatus, CourseBookingApplication.Status.PENDING.getCode())
                .count();
        
        stats.setTotalApplications(totalApplications);
        stats.setPendingApplications(pendingApplications);
        stats.setApprovedApplications(0L);
        stats.setRejectedApplications(0L);
        stats.setTodayApplications(0L);
        stats.setTodayReviewed(0L);
        stats.setApprovalRate(0.0);
        stats.setOverdueApplications(0L);
        
        return stats;
    }

    private CourseBookingReviewDto.StatsResp getTeachingGroupStats() {
        return new CourseBookingReviewDto.StatsResp();
//        String currentUserId = SecurityUtils.getUserId().toString();
//
//        List<TeachingGroup> managedGroups = teachingGroupService.lambdaQuery()
//                .eq(TeachingGroup::getLeaderId, currentUserId)
//                .eq(TeachingGroup::getDeleted, false)
//                .list();
//
//        if (managedGroups.isEmpty()) {
//            return new CourseBookingReviewDto.StatsResp();
//        }
//
//        String groupId = managedGroups.get(0).getId();
//
//        CourseBookingReviewDto.StatsResp stats = new CourseBookingReviewDto.StatsResp();
//
//        // 查询教学组的申请统计数据
//        long myGroupApplications = courseBookingApplicationService.lambdaQuery()
//                .eq(CourseBookingApplication::getDeleted, false)
//                .eq(CourseBookingApplication::getTeachingGroupId, groupId)
//                .count();
//
//        long myGroupPending = courseBookingApplicationService.lambdaQuery()
//                .eq(CourseBookingApplication::getDeleted, false)
//                .eq(CourseBookingApplication::getTeachingGroupId, groupId)
//                .eq(CourseBookingApplication::getStatus, "pending")
//                .count();
//
//        stats.setMyGroupApplications(myGroupApplications);
//        stats.setMyGroupPending(myGroupPending);
//        stats.setTotalApplications(myGroupApplications);
//        stats.setPendingApplications(myGroupPending);
//        stats.setApprovedApplications(0L);
//        stats.setRejectedApplications(0L);
//        stats.setTodayApplications(0L);
//        stats.setTodayReviewed(0L);
//        stats.setApprovalRate(0.0);
//        stats.setOverdueApplications(0L);
//
//        return stats;
    }

    /**
     * 获取基于教学组视角的状态文本
     */
    private String getTeachingGroupPerspectiveStatusText(CourseBookingApplication application) {
        try {
            String currentUserId = SecurityUtils.getUserId().toString();
            List<String> userTeachingGroupIds = getCurrentUserTeachingGroupIds(currentUserId);

            if (userTeachingGroupIds.isEmpty()) {
                return getStatusText(application.getStatus());
            }

            // 查询当前用户教学组对该申请的审核记录
            List<CourseBookingApplicationReview> userGroupReviews = courseBookingApplicationReviewService.lambdaQuery()
                    .eq(CourseBookingApplicationReview::getApplicationId, application.getId())
                    .in(CourseBookingApplicationReview::getTeachingGroupId, userTeachingGroupIds)
                    .eq(CourseBookingApplicationReview::getDeleted, false)
                    .list();

            log.debug("申请ID: {}, 当前用户教学组: {}, 审核记录数量: {}",
                    application.getId(), userTeachingGroupIds, userGroupReviews.size());

            String globalStatus = application.getStatus();

            if ("待审核".equals(globalStatus)) {
                // 全局状态为待审核
                if (!userGroupReviews.isEmpty()) {
                    // 当前用户教学组已审核
                    CourseBookingApplicationReview userReview = userGroupReviews.get(0);
                    if ("已通过".equals(userReview.getReviewResult())) {
                        return "待审核(本组已通过)";
                    } else if ("已拒绝".equals(userReview.getReviewResult())) {
                        return "待审核(本组已拒绝)";
                    }
                }
                return "待审核";
            } else if ("已通过".equals(globalStatus)) {
                // 全局状态为已通过
                if (!userGroupReviews.isEmpty()) {
                    CourseBookingApplicationReview userReview = userGroupReviews.get(0);
                    if ("已通过".equals(userReview.getReviewResult())) {
                        return "已通过(本组通过)";
                    } else if ("已拒绝".equals(userReview.getReviewResult())) {
                        return "已通过(本组拒绝)";
                    }
                }
                return "已通过";
            } else if ("已拒绝".equals(globalStatus)) {
                // 全局状态为已拒绝
                if (!userGroupReviews.isEmpty()) {
                    CourseBookingApplicationReview userReview = userGroupReviews.get(0);
                    if ("已拒绝".equals(userReview.getReviewResult())) {
                        return "已拒绝(本组拒绝)";
                    }
                }
                return "已拒绝";
            }

            return getStatusText(globalStatus);
        } catch (Exception e) {
            log.warn("获取教学组视角状态文本失败: applicationId={}", application.getId(), e);
            return getStatusText(application.getStatus());
        }
    }

    /**
     * 获取当前用户管理的教学组ID列表
     */
    private List<String> getCurrentUserTeachingGroupIds(String userId) {
        try {
            // 获取该用户管理的所有教学组
            List<TeachingGroup> managedGroups = teachingGroupService.lambdaQuery()
                    .eq(TeachingGroup::getLeaderId, userId)
                    .eq(TeachingGroup::getDeleted, false)
                    .list();

            return managedGroups.stream()
                    .map(TeachingGroup::getId)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("获取用户教学组失败: userId={}", userId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 应用基于教学组视角的状态过滤
     */
    private void applyTeachingGroupStatusFilter(com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper<CourseBookingApplication> queryWrapper,
                                               CourseBookingReviewDto.GetListReq req) {
        if (StrUtil.isEmpty(req.getStatus())) {
            return;
        }

        // 直接按全局状态查询，不做额外过滤
        // 状态显示的差异化通过 getTeachingGroupPerspectiveStatusText 方法处理
        queryWrapper.eq(CourseBookingApplication::getStatus, req.getStatus());
    }

    /**
     * 应用教学组管理员权限过滤
     */
    private void applyTeachingGroupManagerFilter(com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper<CourseBookingApplication> queryWrapper,
                                                CourseBookingReviewDto.GetListReq req) {
        if (StrUtil.isEmpty(req.getTeachingGroupManagerUserId())) {
            return;
        }

        if ("NONE".equals(req.getTeachingGroupManagerUserId())) {
            queryWrapper.eq(CourseBookingApplication::getId, "NONE");
            return;
        }

        try {
            // 获取该教学组管理员管理的所有教学组
            List<TeachingGroup> managedGroups = teachingGroupService.lambdaQuery()
                    .eq(TeachingGroup::getLeaderId, req.getTeachingGroupManagerUserId())
                    .eq(TeachingGroup::getDeleted, false)
                    .list();

            if (managedGroups.isEmpty()) {
                queryWrapper.eq(CourseBookingApplication::getId, "NONE");
                return;
            }

            // 获取所有教学组的老师ID
            List<String> allTeacherIds = new ArrayList<>();
            for (TeachingGroup group : managedGroups) {
                List<String> teacherIds = teachingGroupMemberService.lambdaQuery()
                        .eq(TeachingGroupMember::getGroupId, group.getId())
                        .eq(TeachingGroupMember::getDeleted, false)
                        .eq(TeachingGroupMember::getStatus, "active")
                        .list()
                        .stream()
                        .map(TeachingGroupMember::getTeacherId)
                        .toList();
                allTeacherIds.addAll(teacherIds);
            }

            if (allTeacherIds.isEmpty()) {
                queryWrapper.eq(CourseBookingApplication::getId, "NONE");
                return;
            }

            // 去重
            allTeacherIds = allTeacherIds.stream().distinct().collect(Collectors.toList());

            // 使用PostgreSQL数组操作符过滤申请（候选老师中包含管理的老师）
            String teacherIdsStr = allTeacherIds.stream()
                    .map(id -> "'" + id + "'::varchar")
                    .collect(Collectors.joining(","));

            queryWrapper.apply("preferred_teachers && ARRAY[" + teacherIdsStr + "]::varchar[]");

            log.info("教学组管理员权限过滤: userId={}, managedGroups={}, teacherCount={}",
                    req.getTeachingGroupManagerUserId(), managedGroups.size(), allTeacherIds.size());

        } catch (Exception e) {
            log.error("应用教学组管理员权限过滤失败", e);
            // 出错时不显示任何结果
            queryWrapper.eq(CourseBookingApplication::getId, "NONE");
        }
    }
}
