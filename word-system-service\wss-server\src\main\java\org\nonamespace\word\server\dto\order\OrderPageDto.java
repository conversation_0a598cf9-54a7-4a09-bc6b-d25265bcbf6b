package org.nonamespace.word.server.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;

@Data
public class OrderPageDto {

    @Data
    public static class Req {
        private Integer pageNum = 1;
        private Integer pageSize = 10;

        private String orderNo;
        private String studentName;
        private String salerName;
        private String orderStatus;
        private String trxMethod;
        private String beginTime;
        private String endTime;
    }


    @Data
    public static class Resp {
        private String id;
        private String no;
        private String body;
        private String orderStatus;
        private Long totalAmt;
        private String salerId;
        private String studentId;
        private String remark;
        private Long amtPaid;
        private Long amtUnpaid;
        private Date lastPayTime;

        /**
         * 合同签署状态：无需签署，未签署，已签署
         */
        private String signStatus;
        /**
         * 交易方式：全额支付，分期支付
         */
        private String trxMethod;
        private String productId;

        private String studentName;
        private String salerName;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date createTime;

    }
}
