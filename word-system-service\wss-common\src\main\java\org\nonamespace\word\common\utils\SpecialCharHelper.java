package org.nonamespace.word.common.utils;

public class SpecialCharHelper {

    public static String replaceStr(String input) {
        if (input == null) {
            return null;
        }
        // 使用正则表达式替换指定的多个特殊字符
        return input.replaceAll("[\\-#;$!@&()\\\\<>/:?*\"| ]", "_");
    }

    public static void main(String[] args) {
        String testString = "This-is#a;test\\$string!with@many&special(characters)<>/?:*\"| and spaces.";
        String replacedString = replaceStr(testString);
        System.out.println("Original string: " + testString);
        System.out.println("Replaced string: " + replacedString);
    }
}
