package org.nonamespace.word.launcher.test;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.nonamespace.word.thirdpart.esign.exception.ESignException;
import org.nonamespace.word.thirdpart.esign.model.GetFileDownloadUrlInput;
import org.nonamespace.word.thirdpart.esign.model.GetFileDownloadUrlOutput;
import org.nonamespace.word.thirdpart.esign.service.IESignFlowService;

/**
 * 下载已签署文件及附属材料测试
 * <AUTHOR>
 */
@Slf4j
//@SpringBootTest
//@ActiveProfiles("test")
public class GetFileDownloadUrlTest {

    //@Autowired
    private IESignFlowService eSignService;

    /**
     * 测试下载已签署文件及附属材料
     */
    @Test
    public void testGetFileDownloadUrl() throws ESignException {
        // 构建请求参数（需要替换为实际的已完成签署流程ID）
        GetFileDownloadUrlInput input = new GetFileDownloadUrlInput("test-completed-sign-flow-id");
        
        try {
            // 调用接口
            GetFileDownloadUrlOutput output = eSignService.getFileDownloadUrl(input);
            
            // 验证结果
            log.info("获取已签署文件下载链接成功");
            log.info("响应码: {}", output.getCode());
            log.info("响应消息: {}", output.getMessage());
            
            if (output.getData() != null) {
                // 输出签署文件信息
                if (output.getData().getFiles() != null && !output.getData().getFiles().isEmpty()) {
                    log.info("签署文件数量: {}", output.getData().getFiles().size());
                    for (int i = 0; i < output.getData().getFiles().size(); i++) {
                        GetFileDownloadUrlOutput.FileInfo file = output.getData().getFiles().get(i);
                        log.info("签署文件[{}] - ID: {}, 名称: {}, 下载链接: {}", 
                                i + 1, file.getFileId(), file.getFileName(), file.getDownloadUrl());
                    }
                } else {
                    log.info("无签署文件");
                }
                
                // 输出附属材料信息
                if (output.getData().getAttachments() != null && !output.getData().getAttachments().isEmpty()) {
                    log.info("附属材料数量: {}", output.getData().getAttachments().size());
                    for (int i = 0; i < output.getData().getAttachments().size(); i++) {
                        GetFileDownloadUrlOutput.AttachmentInfo attachment = output.getData().getAttachments().get(i);
                        log.info("附属材料[{}] - ID: {}, 名称: {}, 下载链接: {}", 
                                i + 1, attachment.getFileId(), attachment.getFileName(), attachment.getDownloadUrl());
                    }
                } else {
                    log.info("无附属材料");
                }
                
                // 输出海外签证书报告下载地址
                if (output.getData().getCertificateDownloadUrl() != null) {
                    log.info("海外签证书报告下载地址: {}", output.getData().getCertificateDownloadUrl());
                } else {
                    log.info("无海外签证书报告");
                }
            }
            
        } catch (ESignException e) {
            log.error("获取已签署文件下载链接失败: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 测试下载已签署文件及附属材料 - 使用构造函数
     */
    @Test
    public void testGetFileDownloadUrlWithConstructor() throws ESignException {
        try {
            // 使用构造函数创建请求参数
            GetFileDownloadUrlOutput output = eSignService.getFileDownloadUrl(
                    new GetFileDownloadUrlInput("test-completed-sign-flow-id")
            );
            
            // 验证结果
            log.info("使用构造函数获取已签署文件下载链接成功");
            log.info("响应码: {}", output.getCode());
            log.info("响应消息: {}", output.getMessage());
            
        } catch (ESignException e) {
            log.error("使用构造函数获取已签署文件下载链接失败: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 测试下载已签署文件及附属材料 - 参数校验
     */
    @Test
    public void testGetFileDownloadUrlValidation() {
        try {
            // 测试空参数
            eSignService.getFileDownloadUrl(null);
        } catch (ESignException.ParameterException e) {
            log.info("空参数校验通过: {}", e.getMessage());
        } catch (ESignException e) {
            log.error("意外异常: {}", e.getMessage());
        }
        
        try {
            // 测试空签署流程ID
            GetFileDownloadUrlInput input = new GetFileDownloadUrlInput();
            eSignService.getFileDownloadUrl(input);
        } catch (ESignException.ParameterException e) {
            log.info("空签署流程ID校验通过: {}", e.getMessage());
        } catch (ESignException e) {
            log.error("意外异常: {}", e.getMessage());
        }
        
        try {
            // 测试空白签署流程ID
            GetFileDownloadUrlInput input = new GetFileDownloadUrlInput("");
            eSignService.getFileDownloadUrl(input);
        } catch (ESignException.ParameterException e) {
            log.info("空白签署流程ID校验通过: {}", e.getMessage());
        } catch (ESignException e) {
            log.error("意外异常: {}", e.getMessage());
        }
    }
    
    /**
     * 测试下载已签署文件及附属材料 - 流程未完成状态
     */
    @Test
    public void testGetFileDownloadUrlWithIncompleteFlow() {
        try {
            // 使用未完成的签署流程ID（应该会报错）
            GetFileDownloadUrlInput input = new GetFileDownloadUrlInput("test-incomplete-sign-flow-id");
            eSignService.getFileDownloadUrl(input);
        } catch (ESignException.ProcessingException e) {
            log.info("未完成流程校验通过: {}", e.getMessage());
            // 预期会收到类似 "流程非签署完成状态，不允许下载文档" 的错误信息
        } catch (ESignException e) {
            log.error("意外异常: {}", e.getMessage());
        }
    }
}