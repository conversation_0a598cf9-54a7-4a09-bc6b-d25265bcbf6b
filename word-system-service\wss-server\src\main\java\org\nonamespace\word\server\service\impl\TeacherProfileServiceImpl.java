package org.nonamespace.word.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.domain.*;
import org.nonamespace.word.server.dto.management.teachermatch.TeacherMatchDto;
import org.nonamespace.word.server.dto.management.teachinggroup.TeacherDto;
import org.nonamespace.word.server.enums.GradeEnum;
import org.nonamespace.word.server.exception.ServiceException;
import org.nonamespace.word.server.mapper.TeacherProfileMapper;
import org.nonamespace.word.server.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TeacherProfileServiceImpl extends ServiceImpl<TeacherProfileMapper, TeacherProfile> implements ITeacherProfileService {

    @Value("${app.teacher.time-slot.update-days: 3}")
    private int timeSlotUpdateDays; // 用于控制教师时间段更新的天数

    @Autowired
    private ITeachingGroupMemberService teachingGroupMemberService;

    @Autowired
    @Lazy
    private ITeachingGroupService teachingGroupService;

    @Autowired
    private ICourseService courseService;

    @Autowired
    private ITeacherStudentRelationService teacherStudentRelationService;

    @Autowired
    private IStudentCourseConsumptionService courseConsumptionService;

    /**
     * 查询教师详细信息
     */
    public TeacherDto.DetailResp selectTeacherDetail(String teacherId) {
        if (teacherId == null || teacherId.trim().isEmpty()) {
            throw ServiceException.paramError("教师ID不能为空");
        }

        try {
            // 先查询教师基本信息
            TeacherProfile teacherProfile = lambdaQuery()
                    .eq(TeacherProfile::getTeacherId, teacherId)
                    .eq(TeacherProfile::getDeleted, false)
                    .one();

            if (teacherProfile == null) {
                throw ServiceException.dataNotFound("教师信息不存在: " + teacherId);
            }

            // 查询教学组信息
            TeachingGroupMember groupMember = teachingGroupMemberService.lambdaQuery()
                    .eq(TeachingGroupMember::getTeacherId, teacherId)
                    .eq(TeachingGroupMember::getDeleted, false)
                    .eq(TeachingGroupMember::getStatus, "active")
                    .one();

            TeacherDto.DetailResp resp = buildDetailResp(teacherProfile);

            if (groupMember != null) {
                resp.setGroupId(groupMember.getGroupId());
                // 查询教学组名称
                TeachingGroup group = teachingGroupService.getById(groupMember.getGroupId());
                if (group != null) {
                    resp.setGroupName(group.getName());
                }
            }

            // 计算统计数据
            calculateTeacherStatistics(resp, teacherId);

            return resp;
        } catch (NumberFormatException e) {
            log.error("教师ID格式错误: teacherId={}", teacherId, e);
            throw ServiceException.paramError("教师ID格式错误");
        } catch (Exception e) {
            log.error("查询教师详细信息失败: teacherId={}", teacherId, e);
            throw ServiceException.systemError("查询教师详细信息失败", e);
        }
    }

    /**
     * 查询可分配的教师列表（未分配到任何教学组的教师）
     */
    public List<TeacherDto.AvailableResp> selectAvailableTeachers() {
        // 先查询所有已分配的教师ID
        List<TeachingGroupMember> assignedMembers = teachingGroupMemberService.lambdaQuery()
                .eq(TeachingGroupMember::getDeleted, false)
                .eq(TeachingGroupMember::getStatus, "active")
                .list();

        Set<String> assignedTeacherIds = assignedMembers.stream()
                .map(TeachingGroupMember::getTeacherId)
                .collect(Collectors.toSet());

        // 查询未分配的教师
        List<TeacherProfile> unassignedTeachers;
        if (!assignedTeacherIds.isEmpty()) {
            unassignedTeachers = lambdaQuery()
                    .eq(TeacherProfile::getDeleted, false)
                    .notIn(TeacherProfile::getTeacherId, assignedTeacherIds)
                    .orderByDesc(TeacherProfile::getCreateTime)
                    .list();
        } else {
            unassignedTeachers = lambdaQuery()
                    .eq(TeacherProfile::getDeleted, false)
                    .orderByDesc(TeacherProfile::getCreateTime)
                    .list();
        }

        return unassignedTeachers.stream().map(tp -> {
            TeacherDto.AvailableResp resp = new TeacherDto.AvailableResp();
            resp.setId(String.valueOf(tp.getTeacherId()));
            resp.setName(tp.getNickName());
            resp.setPhone(tp.getPhonenumber());
            resp.setEmail(""); // 暂时为空
            resp.setStatus(tp.getStatus());
            return resp;
        }).collect(Collectors.toList());
    }

    /**
     * 查询教师列表（支持搜索和分页）
     */
    public IPage<TeacherDto.BasicResp> selectTeachersPage(Page<TeacherDto.BasicResp> page, TeacherDto.GetListReq req, Set<String> groupIds) {
        // 如果指定了教学组，先查询符合条件的教师ID
        Set<String> allowedTeacherIds = null;
        if (groupIds != null && !groupIds.isEmpty()) {
            List<TeachingGroupMember> groupMembers = teachingGroupMemberService.lambdaQuery()
                    .in(TeachingGroupMember::getGroupId, groupIds)
                    .eq(TeachingGroupMember::getDeleted, false)
                    .eq(TeachingGroupMember::getStatus, "active")
                    .list();

            allowedTeacherIds = groupMembers.stream()
                    .map(member -> member.getTeacherId())
                    .collect(Collectors.toSet());

            // 如果没有符合条件的教师，直接返回空结果
            if (allowedTeacherIds.isEmpty()) {
                IPage<TeacherDto.BasicResp> result = new Page<>(page.getCurrent(), page.getSize(), 0);
                result.setRecords(new ArrayList<>());
                return result;
            }
        }

        // 构建查询条件
        Page<TeacherProfile> teacherPage = new Page<>(page.getCurrent(), page.getSize());
        IPage<TeacherProfile> teacherResult = lambdaQuery()
                .eq(TeacherProfile::getDeleted, false)
                .and(StrUtil.isNotEmpty(req.getName()), wrapper ->
                        wrapper.like(TeacherProfile::getRealName, req.getName())
                                .or()
                                .like(TeacherProfile::getNickName, req.getName()))
                .like(StrUtil.isNotEmpty(req.getPhone()), TeacherProfile::getPhonenumber, req.getPhone())
                .eq(StrUtil.isNotEmpty(req.getStatus()), TeacherProfile::getStatus, req.getStatus())
                .eq(req.getTeacherId() != null, TeacherProfile::getTeacherId, req.getTeacherId())
                .in(CollUtil.isNotEmpty(allowedTeacherIds), TeacherProfile::getTeacherId, allowedTeacherIds)
                .orderByDesc(TeacherProfile::getCreateTime)
                .page(teacherPage);

        // 批量查询教学组信息
        List<TeacherProfile> teachers = teacherResult.getRecords();
        final Map<String, TeachingGroupMember> teacherGroupMap;
        final Map<String, TeachingGroup> groupMap;

        if (!teachers.isEmpty()) {
            List<String> teacherIds = teachers.stream()
                    .map(TeacherProfile::getTeacherId)
                    .toList();

            List<TeachingGroupMember> allGroupMembers = teachingGroupMemberService.lambdaQuery()
                    .in(TeachingGroupMember::getTeacherId, teacherIds.stream().map(String::valueOf).collect(Collectors.toList()))
                    .eq(TeachingGroupMember::getDeleted, false)
                    .eq(TeachingGroupMember::getStatus, "active")
                    .list();

            teacherGroupMap = allGroupMembers.stream()
                    .collect(Collectors.toMap(
                            TeachingGroupMember::getTeacherId,
                            member -> member,
                            (existing, replacement) -> existing
                    ));

            if (!allGroupMembers.isEmpty()) {
                Set<String> allGroupIds = allGroupMembers.stream()
                        .map(TeachingGroupMember::getGroupId)
                        .collect(Collectors.toSet());

                List<TeachingGroup> groups = teachingGroupService.lambdaQuery()
                        .in(TeachingGroup::getId, allGroupIds)
                        .eq(TeachingGroup::getDeleted, false)
                        .list();

                groupMap = groups.stream()
                        .collect(Collectors.toMap(TeachingGroup::getId, group -> group));
            } else {
                groupMap = new HashMap<>();
            }
        } else {
            teacherGroupMap = new HashMap<>();
            groupMap = new HashMap<>();
        }

        // 转换为响应对象
        List<TeacherDto.BasicResp> respList = teachers.stream().map(teacher -> {
            TeacherDto.BasicResp resp = new TeacherDto.BasicResp();
            resp.setId(String.valueOf(teacher.getTeacherId()));
            resp.setName(teacher.getRealName());
            resp.setNickname(teacher.getNickName());
            resp.setGender(teacher.getGender());
            resp.setPhone(teacher.getPhonenumber());
            resp.setEmail(""); // 暂时为空
            resp.setAvatar(teacher.getAvatar());
            resp.setStatus(teacher.getStatus());
            resp.setCreateTime(teacher.getCreateTime());
            resp.setUpdateTime(teacher.getUpdateTime());

            // 设置教学组信息
            TeachingGroupMember groupMember = teacherGroupMap.get(teacher.getTeacherId());
            if (groupMember != null) {
                resp.setGroupId(groupMember.getGroupId());
                TeachingGroup group = groupMap.get(groupMember.getGroupId());
                if (group != null) {
                    resp.setGroupName(group.getName());
                }
            }

            return resp;
        }).collect(Collectors.toList());

        // 构建分页结果
        IPage<TeacherDto.BasicResp> result = new Page<>(teacherResult.getCurrent(), teacherResult.getSize(), teacherResult.getTotal());
        result.setRecords(respList);

        return result;
    }

    /**
     * 查询所有教师列表（用于选择组长、教务）
     */
    public List<TeacherDto.UserRoleResp> selectAllTeachers() {
        List<TeacherProfile> teachers = lambdaQuery()
                .eq(TeacherProfile::getDeleted, false)
                .orderByDesc(TeacherProfile::getCreateTime)
                .list();

        return teachers.stream().map(tp -> {
            TeacherDto.UserRoleResp resp = new TeacherDto.UserRoleResp();
            resp.setId(String.valueOf(tp.getTeacherId()));
            resp.setName(tp.getRealName());
            resp.setPhone(tp.getPhonenumber());
            resp.setEmail(""); // 暂时为空
            resp.setAvatar(tp.getAvatar());
            return resp;
        }).collect(Collectors.toList());
    }

    /**
     * 查询教师带教信息
     */
    public TeacherDto.TeachingInfoResp selectTeachingInfo(String teacherId) {
        TeacherProfile teacher = lambdaQuery().eq(TeacherProfile::getTeacherId, teacherId)
                .eq(TeacherProfile::getDeleted, false)
                .one();
        if (teacher == null) {
            return null;
        }

        // 查询课程统计信息
        List<Course> courses = courseService.lambdaQuery()
                // 查出除content字段外的所有字段
                .select(Course.class, c -> !c.getColumn().equals("content"))
                .eq(Course::getTeacherId, teacherId)
                .eq(Course::getDeleted, false)
                .list();

        // 统计当前学生数（待开始和进行中的课程）
        long currentStudents = courses.stream()
                .filter(course -> "待开始".equals(course.getCourseStatus()) || "进行中".equals(course.getCourseStatus()))
                .map(Course::getStudentId)
                .distinct()
                .count();

        // 统计课时信息
        long unconsumedHours = courses.stream()
                .filter(course -> "待开始".equals(course.getCourseStatus()))
                .count();

        long consumedHours = courses.stream()
                .filter(course -> "已完成".equals(course.getCourseStatus()))
                .count();

        long totalHours = courses.size();

        TeacherDto.TeachingInfoResp resp = new TeacherDto.TeachingInfoResp();
        resp.setTeacherId(teacherId);
        resp.setTeacherName(teacher.getNickName() != null ? teacher.getNickName() : teacher.getRealName());
        resp.setCurrentStudents((int) currentStudents);
        resp.setUnconsumedHours((int) unconsumedHours);
        resp.setConsumedHours((int) consumedHours);
        resp.setTotalHours((int) totalHours);

        return resp;
    }

    /**
     * 批量查询教师带教信息
     */
    public List<TeacherDto.TeachingInfoResp> selectTeachingInfoBatch(List<String> teacherIds) {
        if (teacherIds == null || teacherIds.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            // 批量查询教师信息
            List<TeacherProfile> teachers = lambdaQuery()
                    .in(TeacherProfile::getTeacherId, teacherIds)
                    .eq(TeacherProfile::getDeleted, false)
                    .list();

            // 批量查询课程信息
            List<Course> allCourses = courseService.lambdaQuery()
                    // 查出除content字段外的所有字段
                    .select(Course.class, c -> !c.getColumn().equals("content"))
                    .in(Course::getTeacherId, teacherIds)
                    .eq(Course::getDeleted, false)
                    .list();

            // 按教师ID分组课程
            Map<String, List<Course>> coursesByTeacher = allCourses.stream()
                    .collect(Collectors.groupingBy(Course::getTeacherId));

            // 构建响应
            return teachers.stream().map(teacher -> {
                String teacherId = String.valueOf(teacher.getTeacherId());
                List<Course> courses = coursesByTeacher.getOrDefault(teacherId, new ArrayList<>());

                return buildTeachingInfoResp(teacher, courses);
            }).collect(Collectors.toList());
        } catch (NumberFormatException e) {
            log.error("教师ID格式错误: teacherIds={}", teacherIds, e);
            throw ServiceException.paramError("教师ID格式错误");
        } catch (Exception e) {
            log.error("批量查询教师带教信息失败: teacherIds={}", teacherIds, e);
            throw ServiceException.systemError("批量查询教师带教信息失败", e);
        }
    }

    /**
     * 构建教师带教信息响应
     */
    private TeacherDto.TeachingInfoResp buildTeachingInfoResp(TeacherProfile teacher, List<Course> courses) {
        // 统计当前学生数（待开始和进行中的课程）
        long currentStudents = courses.stream()
                .filter(course -> "待开始".equals(course.getCourseStatus()) || "进行中".equals(course.getCourseStatus()))
                .map(Course::getStudentId)
                .distinct()
                .count();

        // 统计课时信息
        long unconsumedHours = courses.stream()
                .filter(course -> "待开始".equals(course.getCourseStatus()))
                .count();

        long consumedHours = courses.stream()
                .filter(course -> "已完成".equals(course.getCourseStatus()))
                .count();

        long totalHours = courses.size();

        TeacherDto.TeachingInfoResp resp = new TeacherDto.TeachingInfoResp();
        resp.setTeacherId(String.valueOf(teacher.getTeacherId()));
        resp.setTeacherName(teacher.getNickName() != null ? teacher.getNickName() : teacher.getRealName());
        resp.setCurrentStudents((int) currentStudents);
        resp.setUnconsumedHours((int) unconsumedHours);
        resp.setConsumedHours((int) consumedHours);
        resp.setTotalHours((int) totalHours);

        return resp;
    }

    /**
     * 批量查询教师详细信息
     */
    public List<TeacherDto.DetailResp> selectTeacherDetailBatch(List<String> teacherIds) {
        if (teacherIds == null) {
            teacherIds = new ArrayList<>();
        }
        // 批量查询教师基本信息
        List<TeacherProfile> teachers = lambdaQuery()
                .in(CollUtil.isNotEmpty(teacherIds), TeacherProfile::getTeacherId, teacherIds)
                .eq(TeacherProfile::getDeleted, false)
                .list();

        // 批量查询教学组成员信息
        List<TeachingGroupMember> groupMembers = teachingGroupMemberService.lambdaQuery()
                .in(CollUtil.isNotEmpty(teacherIds), TeachingGroupMember::getTeacherId, teacherIds)
                .eq(TeachingGroupMember::getDeleted, false)
                .eq(TeachingGroupMember::getStatus, "active")
                .list();

        // 构建教师ID到教学组成员的映射
        Map<String, TeachingGroupMember> teacherGroupMap = groupMembers.stream()
                .collect(Collectors.toMap(
                        TeachingGroupMember::getTeacherId,
                        member -> member,
                        (existing, replacement) -> existing
                ));

        // 批量查询教学组信息
        Map<String, TeachingGroup> groupMap = new HashMap<>();
        if (!groupMembers.isEmpty()) {
            Set<String> groupIds = groupMembers.stream()
                    .map(TeachingGroupMember::getGroupId)
                    .collect(Collectors.toSet());

            List<TeachingGroup> groups = teachingGroupService.lambdaQuery()
                    .in(TeachingGroup::getId, groupIds)
                    .eq(TeachingGroup::getDeleted, false)
                    .list();

            groupMap = groups.stream()
                    .collect(Collectors.toMap(TeachingGroup::getId, group -> group));
        }

        // 构建响应
        final Map<String, TeachingGroup> finalGroupMap = groupMap;
        return teachers.stream().map(teacher -> {
            TeacherDto.DetailResp resp = buildDetailResp(teacher);

            // 设置教学组信息
            TeachingGroupMember groupMember = teacherGroupMap.get(teacher.getTeacherId());
            if (groupMember != null) {
                resp.setGroupId(groupMember.getGroupId());
                TeachingGroup group = finalGroupMap.get(groupMember.getGroupId());
                if (group != null) {
                    resp.setGroupName(group.getName());
                }
            }

            return resp;
        }).collect(Collectors.toList());
    }

    /**
     * 构建教师详细信息响应
     */
    private TeacherDto.DetailResp buildDetailResp(TeacherProfile teacherProfile) {
        TeacherDto.DetailResp resp = new TeacherDto.DetailResp();
        resp.setId(String.valueOf(teacherProfile.getTeacherId()));
        resp.setName(teacherProfile.getRealName());
        resp.setNickname(teacherProfile.getNickName());
        resp.setGender(teacherProfile.getGender());
        resp.setAge(teacherProfile.getAge());
        resp.setPhone(teacherProfile.getPhonenumber());
        resp.setEmail(""); // 暂时为空
        resp.setAvatar(teacherProfile.getAvatar());
        resp.setCurrentLocation(teacherProfile.getCurrentLocation());
        resp.setEmploymentType(teacherProfile.getEmploymentType());
        resp.setCurrentStatus(teacherProfile.getCurrentStatus());

        // 教育背景
        resp.setEducation(teacherProfile.getEducation());
        resp.setGraduateSchool(teacherProfile.getGraduateSchool());
        resp.setMajor(teacherProfile.getMajor());
        resp.setUniversityType(teacherProfile.getUniversityType());
        resp.setIsNormalUniversity(teacherProfile.getIsNormalUniversity());
        resp.setStudyAbroad(teacherProfile.getStudyAbroad());
        resp.setStudyAbroadCountry(teacherProfile.getStudyAbroadCountry());

        // 教学资质
        resp.setTeachingCertificateLevel(teacherProfile.getTeachingCertificateLevel());
        resp.setSubjects(teacherProfile.getSubjects());
        resp.setEnglishQualification(teacherProfile.getEnglishQualification());
        resp.setMandarinQualification(teacherProfile.getMandarinQualification());
        resp.setCommunicationAbility(teacherProfile.getCommunicationAbility());
        resp.setEnglishPronunciation(teacherProfile.getEnglishPronunciation());
        resp.setTeachingYears(teacherProfile.getTeachingYears());

        // 教学经历和风格
        resp.setTeachingExperience(teacherProfile.getTeachingExperience());
        resp.setTaughtCourses(teacherProfile.getTaughtCourses());
        resp.setTeachingStyle(teacherProfile.getTeachingStyle());
        resp.setSuitableGrades(teacherProfile.getSuitableGrades());
        resp.setSuitableLevels(teacherProfile.getSuitableLevels());
        resp.setSuitablePersonality(teacherProfile.getSuitablePersonality());
        resp.setAwards(teacherProfile.getAwards());

        // 暑期课上课时间
        resp.setSummerScheduleType(teacherProfile.getSummerScheduleType());
        resp.setTrainingSubjects(teacherProfile.getTrainingSubjects());

        resp.setOther(teacherProfile.getOther());
        resp.setIntroduction(teacherProfile.getIntroduction());
        resp.setStatus(teacherProfile.getStatus());
        resp.setCreateTime(teacherProfile.getCreateTime());
        resp.setUpdateTime(teacherProfile.getUpdateTime());

        resp.setFormalEntryDate(teacherProfile.getFormalEntryDate());
        resp.setQualificationCertificates(teacherProfile.getQualificationCertificates());
        resp.setDemoVideos(teacherProfile.getDemoVideos());
        return resp;
    }

    /**
     * 计算教师统计数据
     */
    private void calculateTeacherStatistics(TeacherDto.DetailResp resp, String teacherId) {
        try {
            // 1. 计算试听课通过率（3个月内）
            Double trialCoursePassRate = calculateTrialCoursePassRate(teacherId);
            resp.setTrialCoursePassRate(trialCoursePassRate);

            // 2. 计算系统已上课时（分钟）
            Integer totalTeachingHours = calculateTotalTeachingHours(teacherId);
            resp.setTotalTeachingHours(totalTeachingHours);

            // 3. 计算老师现有学生数
            Integer currentStudentCount = calculateCurrentStudentCount(teacherId);
            resp.setCurrentStudentCount(currentStudentCount);

        } catch (Exception e) {
            log.error("计算教师统计数据失败: teacherId={}", teacherId, e);
            // 设置默认值
            resp.setTrialCoursePassRate(0.0);
            resp.setTotalTeachingHours(0);
            resp.setCurrentStudentCount(0);
        }
    }

    /**
     * 计算试听课通过率（3个月内）
     * 通过率 = 试听课后有正式课的学生数 / 试听课学生总数
     */
    private Double calculateTrialCoursePassRate(String teacherId) {
        try {
            // 计算3个月前的时间
            LocalDateTime threeMonthsAgo = LocalDateTime.now().minusMonths(3);

            // 查询3个月内该教师的所有试听课
            List<Course> trialCourses = courseService.lambdaQuery()
                    // 查出除content字段外的所有字段
                    .select(Course.class, c -> !c.getColumn().equals("content"))
                    .eq(Course::getTeacherId, teacherId)
                    .eq(Course::getCourseType, "试听课")
                    .eq(Course::getCourseStatus, "已完成")
                    .ge(Course::getActualEndTime, threeMonthsAgo)
                    .eq(Course::getDeleted, false)
                    .list();

            if (trialCourses.isEmpty()) {
                return 0.0;
            }

            // 获取试听课的学生ID列表（去重）
            Set<String> trialStudentIds = trialCourses.stream()
                    .map(Course::getStudentId)
                    .collect(Collectors.toSet());

            // 查询这些学生中有多少人后续上了正式课
            long passedStudentCount = trialStudentIds.stream()
                    .mapToLong(studentId -> {
                        // 查询该学生在试听课之后是否有正式课
                        long formalCourseCount = courseService.lambdaQuery()
                                .eq(Course::getTeacherId, teacherId)
                                .eq(Course::getStudentId, studentId)
                                .eq(Course::getCourseType, "正式课")
                                .eq(Course::getDeleted, false)
                                .count();
                        return formalCourseCount > 0 ? 1 : 0;
                    })
                    .sum();

            // 计算通过率
            return (double) passedStudentCount / trialStudentIds.size() * 100;

        } catch (Exception e) {
            log.error("计算试听课通过率失败: teacherId={}", teacherId, e);
            return 0.0;
        }
    }

    /**
     * 计算系统已上课时（分钟）
     * 统计该教师所有已完成课程的总时长
     */
    private Integer calculateTotalTeachingHours(String teacherId) {
        try {

            if (teacherId == null || teacherId.trim().isEmpty()) {
                return 0;
            }
            // 确保教师ID是有效的数字

            // 查询该教师所有已完成的课程
            List<Course> completedCourses = courseService.lambdaQuery()
                    // 查出除content字段外的所有字段
                    .select(Course.class, c -> !c.getColumn().equals("content"))
                    .eq(Course::getTeacherId, teacherId)
                    .eq(Course::getCourseStatus, "已完成")
                    .eq(Course::getDeleted, false)
                    .list();

            // 计算总时长（分钟）
            return completedCourses.stream()
                    .mapToInt(course -> {
                        if (course.getDurationMinutes() != null) {
                            return course.getDurationMinutes().intValue();
                        }
                        // 如果没有记录实际时长，使用计划时长
                        if (course.getScheduledStartTime() != null && course.getScheduledEndTime() != null) {
                            long diffMs = course.getScheduledEndTime().getTime() - course.getScheduledStartTime().getTime();
                            return (int) (diffMs / (1000 * 60)); // 转换为分钟
                        }
                        return 0;
                    })
                    .sum();

        } catch (Exception e) {
            log.error("计算总上课时长失败: teacherId={}", teacherId, e);
            return 0;
        }
    }

    /**
     * 计算老师现有学生数
     * 统计该教师当前活跃的师生关系数量
     */
    private Integer calculateCurrentStudentCount(String teacherId) {
        try {
            // 查询该教师当前活跃的师生关系
            long studentCount = teacherStudentRelationService.lambdaQuery()
                    .eq(TeacherStudentRelation::getTeacherId, teacherId)
                    .eq(TeacherStudentRelation::getStatus, "active")
                    .eq(TeacherStudentRelation::getDeleted, false)
                    .count();

            return (int) studentCount;

        } catch (Exception e) {
            log.error("计算当前学生数失败: teacherId={}", teacherId, e);
            return 0;
        }
    }

    /**
     * 获取符合筛选条件的活跃教师列表（高性能版本）
     * <p>
     * 优化策略：
     * 1. 使用数据库层面的筛选，避免查询所有教师的详细信息
     * 2. 只查询基本信息，减少数据传输量
     * 3. 在数据库层面应用可以在数据库层面筛选的条件
     * 4. 对于复杂的数组字段筛选，在内存中进行（但只对符合基础条件的教师）
     * <p>
     * 注意：不能使用分页限制，因为教师匹配需要检查所有符合条件的教师的时间段
     */
    @Override
    public List<TeacherDto.BasicResp> selectFilteredActiveTeachers(TeacherMatchDto.MatchTeachersReq request) {
        log.info("开始数据库层面筛选活跃教师，筛选条件: {}", request);
        long startTime = System.currentTimeMillis();

        return selectTeachersWithJoinQuery(request, startTime);
    }

    /**
     * 使用关联查询一次性完成教师属性筛选和时间段预筛选
     * <p>
     * 优化策略：
     * 1. 通过 MPJLambdaWrapper 关联 teacher_profile 和 teacher_time_slot 表
     * 2. 在数据库层面同时应用教师属性筛选和时间段匹配条件
     * 3. 大幅减少需要在内存中处理的数据量
     */
    private List<TeacherDto.BasicResp> selectTeachersWithJoinQuery(TeacherMatchDto.MatchTeachersReq request, long startTime) {
        // 构建关联查询
        MPJLambdaWrapper<TeacherProfile> wrapper = new MPJLambdaWrapper<TeacherProfile>()
                // 选择教师基本信息字段
                .select(TeacherProfile::getTeacherId, TeacherProfile::getRealName, TeacherProfile::getNickName,
                        TeacherProfile::getPhonenumber, TeacherProfile::getStatus)
                // 使用 INNER JOIN 确保只查询有可用时间段的教师
                .innerJoin(TeacherTimeSlot.class, TeacherTimeSlot::getTeacherId, TeacherProfile::getTeacherId,
                        ext -> ext.eq(TeacherTimeSlot::getDeleted, false)
                                .eq(TeacherTimeSlot::getStatus, "available"))
                .innerJoin(TeachingGroupMember.class, TeachingGroupMember::getTeacherId, TeacherProfile::getTeacherId,
                        ext -> ext.eq(TeachingGroupMember::getDeleted, false)
                                .eq(TeachingGroupMember::getStatus, "active"))
                // 基础筛选条件
                .eq(TeacherProfile::getDeleted, false)
                .eq(TeacherProfile::getStatus, "active");

        // 应用教师属性筛选条件
        applyBasicFiltersToWrapper(wrapper, request);

        // 应用时间段筛选条件
        applyTimeSlotFiltersToWrapper(wrapper, request.getTimeSlots());

        // 应用课点更新天数筛选条件
        applyTimeSlotUpdateDaysFilter(wrapper, request.getTimeSlotUpdateDays());

        // 如果有教学组筛选，也在SQL中处理
        if (request.getGroupIds() != null && !request.getGroupIds().isEmpty()) {
            wrapper.leftJoin(TeachingGroupMember.class, TeachingGroupMember::getTeacherId, TeacherProfile::getTeacherId,
                    ext -> ext.eq(TeachingGroupMember::getDeleted, false)
                            .eq(TeachingGroupMember::getStatus, "active")
                            .in(TeachingGroupMember::getGroupId, request.getGroupIds()));
        }

        // 执行查询并去重（因为一个教师可能有多个匹配的时间段）
        List<TeacherProfile> teachers = baseMapper.selectJoinList(TeacherProfile.class, wrapper)
                .stream()
                .distinct()
                .collect(Collectors.toList());

        log.info("关联查询筛选后的教师数量: {}", teachers.size());

        // 如果没有查询到教师，记录详细的调试信息
        if (teachers.isEmpty()) {
            log.warn("关联查询没有找到任何教师！开始详细诊断...");

            // 1. 检查是否有活跃教师
            long activeTeacherCount = lambdaQuery()
                    .eq(TeacherProfile::getDeleted, false)
                    .eq(TeacherProfile::getStatus, "active")
                    .count();
            log.warn("活跃教师总数: {}", activeTeacherCount);

            // 2. 检查是否有教师设置了时间段
            long teachersWithTimeSlots = baseMapper.selectCount(
                    new MPJLambdaWrapper<TeacherProfile>()
                            .innerJoin(TeacherTimeSlot.class, TeacherTimeSlot::getTeacherId, TeacherProfile::getTeacherId,
                                    ext -> ext.eq(TeacherTimeSlot::getDeleted, false))
                            .eq(TeacherProfile::getDeleted, false)
                            .eq(TeacherProfile::getStatus, "active")
            );
            log.warn("有时间段设置的活跃教师数: {}", teachersWithTimeSlots);

            // 3. 检查是否有教师设置了可用时间段
            long teachersWithAvailableSlots = baseMapper.selectCount(
                    new MPJLambdaWrapper<TeacherProfile>()
                            .innerJoin(TeacherTimeSlot.class, TeacherTimeSlot::getTeacherId, TeacherProfile::getTeacherId,
                                    ext -> ext.eq(TeacherTimeSlot::getDeleted, false)
                                            .eq(TeacherTimeSlot::getStatus, "available"))
                            .eq(TeacherProfile::getDeleted, false)
                            .eq(TeacherProfile::getStatus, "active")
            );
            log.warn("有可用时间段的活跃教师数: {}", teachersWithAvailableSlots);

            log.warn("学生需求时间段: {}", request.getTimeSlots());
            log.warn("可能的原因：");
            log.warn("1. 没有教师设置了与学生需求时间段有交集的可用时间段");
            log.warn("2. 教师属性筛选条件过于严格");
            log.warn("3. 时间段格式不匹配");
        }

        // 批量查询教学组信息
        Map<String, TeachingGroupMember> teacherGroupMap = getTeacherGroupMap(teachers);
        Map<String, TeachingGroup> groupMap = getGroupMap(teacherGroupMap.values());

        // 转换为响应对象
        List<TeacherDto.BasicResp> result = teachers.stream()
                .map(teacher -> convertToBasicRespWithGroup(teacher, teacherGroupMap, groupMap))
                .collect(Collectors.toList());

        long endTime = System.currentTimeMillis();
        log.info("关联查询筛选完成，耗时: {}ms, 教师数量: {}", endTime - startTime, result.size());

        return result;
    }

    /**
     * 获取教师教学组映射
     */
    private Map<String, TeachingGroupMember> getTeacherGroupMap(List<TeacherProfile> teachers) {
        if (teachers.isEmpty()) {
            return new HashMap<>();
        }

        List<String> teacherIds = teachers.stream()
                .map(TeacherProfile::getTeacherId)
                .collect(Collectors.toList());

        List<TeachingGroupMember> groupMembers = teachingGroupMemberService.lambdaQuery()
                .in(TeachingGroupMember::getTeacherId, teacherIds)
                .eq(TeachingGroupMember::getDeleted, false)
                .eq(TeachingGroupMember::getStatus, "active")
                .list();

        return groupMembers.stream()
                .collect(Collectors.toMap(
                        TeachingGroupMember::getTeacherId,
                        member -> member,
                        (existing, replacement) -> existing
                ));
    }

    /**
     * 获取教学组映射
     */
    private Map<String, TeachingGroup> getGroupMap(Collection<TeachingGroupMember> groupMembers) {
        if (groupMembers.isEmpty()) {
            return new HashMap<>();
        }

        Set<String> groupIds = groupMembers.stream()
                .map(TeachingGroupMember::getGroupId)
                .collect(Collectors.toSet());

        List<TeachingGroup> groups = teachingGroupService.lambdaQuery()
                .in(TeachingGroup::getId, groupIds)
                .eq(TeachingGroup::getDeleted, false)
                .list();

        return groups.stream()
                .collect(Collectors.toMap(TeachingGroup::getId, group -> group));
    }

    /**
     * 转换为BasicResp格式（包含教学组信息）
     */
    private TeacherDto.BasicResp convertToBasicRespWithGroup(TeacherProfile teacher,
                                                             Map<String, TeachingGroupMember> teacherGroupMap,
                                                             Map<String, TeachingGroup> groupMap) {
        TeacherDto.BasicResp basicResp = new TeacherDto.BasicResp();
        basicResp.setId(String.valueOf(teacher.getTeacherId()));
        basicResp.setName(teacher.getRealName());
        basicResp.setNickname(teacher.getNickName());
        basicResp.setPhone(teacher.getPhonenumber());
        basicResp.setEmail(""); // 暂时为空
        basicResp.setStatus(teacher.getStatus());

        // 设置教学组信息
        TeachingGroupMember groupMember = teacherGroupMap.get(teacher.getTeacherId());
        if (groupMember != null) {
            basicResp.setGroupId(groupMember.getGroupId());
            TeachingGroup group = groupMap.get(groupMember.getGroupId());
            if (group != null) {
                basicResp.setGroupName(group.getName());
            }
        }

        return basicResp;
    }

    /**
     * 应用教师属性筛选条件到 MPJLambdaWrapper
     */
    private void applyBasicFiltersToWrapper(MPJLambdaWrapper<TeacherProfile> wrapper, TeacherMatchDto.MatchTeachersReq request) {
        // 关键词筛选（姓名或手机号）
        if (StrUtil.isNotBlank(request.getKeyword())) {
            String keyword = request.getKeyword().trim();
            wrapper.and(w -> w.like(TeacherProfile::getRealName, keyword)
                    .or().like(TeacherProfile::getNickName, keyword)
                    .or().like(TeacherProfile::getPhonenumber, keyword));
        }

        // 性别筛选
        if (StrUtil.isNotBlank(request.getGender())) {
            wrapper.eq(TeacherProfile::getGender, request.getGender());
        }

        // 年龄范围筛选
        if (request.getMinAge() != null) {
            wrapper.ge(TeacherProfile::getAge, request.getMinAge());
        }
        if (request.getMaxAge() != null) {
            wrapper.le(TeacherProfile::getAge, request.getMaxAge());
        }

        // 兼职/全职筛选
        if (StrUtil.isNotBlank(request.getEmploymentType())) {
            wrapper.eq(TeacherProfile::getEmploymentType, request.getEmploymentType());
        }

        // 目前状态筛选
        if (StrUtil.isNotBlank(request.getCurrentStatus())) {
            wrapper.eq(TeacherProfile::getCurrentStatus, request.getCurrentStatus());
        }

        // 学历筛选
        if (request.getEducation() != null && !request.getEducation().isEmpty()) {
            wrapper.in(TeacherProfile::getEducation, request.getEducation());
        }

        // 大学属性筛选
        if (request.getUniversityType() != null && !request.getUniversityType().isEmpty()) {
            List<String> universityTypesOrder = CollUtil.list(false, "双一流", "985", "211", "一本", "普通");
            Set<String> universityTypes = request.getUniversityType().stream()
                    .map(universityTypesOrder::indexOf)
                    .filter(i -> i > -1)
                    .flatMap(i -> universityTypesOrder.subList(0, Math.min(i + 1, universityTypesOrder.size() -1)).stream())
                    .map(String::toLowerCase)
                    .collect(Collectors.toSet());
            wrapper.in(TeacherProfile::getUniversityType, universityTypes);
        }
        // 是否师范类筛选
        if (request.getIsNormalUniversity() != null) {
            wrapper.eq(TeacherProfile::getIsNormalUniversity, request.getIsNormalUniversity());
        }

        // 是否留学筛选
        if (request.getStudyAbroad() != null) {
            wrapper.eq(TeacherProfile::getStudyAbroad, request.getStudyAbroad());
        }

        // 教资级别筛选
        if (request.getTeachingCertificateLevel() != null && !request.getTeachingCertificateLevel().isEmpty()) {
            wrapper.in(TeacherProfile::getTeachingCertificateLevel, request.getTeachingCertificateLevel());
        }

        // 教龄范围筛选
        if (request.getMinTeachingYears() != null) {
            wrapper.ge(TeacherProfile::getTeachingYears, request.getMinTeachingYears());
        }
        if (request.getMaxTeachingYears() != null) {
            wrapper.le(TeacherProfile::getTeachingYears, request.getMaxTeachingYears());
        }

        // ========== 新增缺失的筛选条件 ==========

        // 数组字段筛选 - 兼容JSON和VARCHAR数组两种类型

        // 教授学科筛选 - 使用兼容的查询方式
        if (request.getSubjects() != null && !request.getSubjects().isEmpty()) {
            applyArrayFieldFilter(wrapper, "subjects", request.getSubjects());
        }

        // 已通过培训科目筛选 - 使用兼容的查询方式
        if (request.getTrainingSubjects() != null && !request.getTrainingSubjects().isEmpty()) {
            applyArrayFieldFilter(wrapper, "training_subjects", request.getTrainingSubjects());
        }

        // 教过课程筛选 - 使用兼容的查询方式
        if (request.getTaughtCourses() != null && !request.getTaughtCourses().isEmpty()) {
            applyArrayFieldFilter(wrapper, "taught_courses", request.getTaughtCourses());
        }

        // 上课风格筛选 - 使用兼容的查询方式
        if (request.getTeachingStyle() != null && !request.getTeachingStyle().isEmpty()) {
            applyArrayFieldFilter(wrapper, "teaching_style", request.getTeachingStyle());
        }

        // 适合学生年级筛选 - 使用兼容的查询方式
        if (request.getSuitableGrades() != null && !request.getSuitableGrades().isEmpty()) {
            applyArrayFieldFilter(wrapper, "suitable_grades", request.getSuitableGrades().stream().map(x->StrUtil.isNumeric(x) ? GradeEnum.fromValue(Integer.valueOf(x)).getText() : x).toList());
        }

        // 适合学生程度筛选 - 使用兼容的查询方式
        if (request.getSuitableLevels() != null && !request.getSuitableLevels().isEmpty()) {
            applyArrayFieldFilter(wrapper, "suitable_levels", request.getSuitableLevels());
        }

        // 普通字符串字段筛选

        // 英语资质筛选 (字符串字段)
        if (request.getEnglishQualification() != null && !request.getEnglishQualification().isEmpty()) {
            wrapper.in(TeacherProfile::getEnglishQualification, request.getEnglishQualification());
        }

        // 普通话资质筛选 (字符串字段)
        {

            if (request.getMandarinQualification() != null && !request.getMandarinQualification().isEmpty()) {
                List<String> mandarinQualificationOrders = CollUtil.list(false, "二级乙等", "二级甲等", "一级乙等", "一级甲等");
                Set<String> mandarinQualifications = request.getMandarinQualification() != null
                        ? request.getMandarinQualification().stream()
                        .map(mandarinQualificationOrders::indexOf)
                        .filter(i -> i > -1)
                        .flatMap(i -> mandarinQualificationOrders.subList(i, mandarinQualificationOrders.size()).stream())
                        .collect(Collectors.toSet())
                        : Collections.emptySet();
                wrapper.in(!mandarinQualifications.isEmpty(), TeacherProfile::getMandarinQualification, request.getMandarinQualification());
            }
        }

        // 沟通能力筛选 (字符串字段)
        if (request.getCommunicationAbility() != null && !request.getCommunicationAbility().isEmpty()) {
            wrapper.in(TeacherProfile::getCommunicationAbility, request.getCommunicationAbility());
        }

        // 英语发音筛选 (字符串字段)
        if (request.getEnglishPronunciation() != null && !request.getEnglishPronunciation().isEmpty()) {
            wrapper.in(TeacherProfile::getEnglishPronunciation, request.getEnglishPronunciation());
        }

        // 适合学生性格筛选 (字符串字段)
        if (StrUtil.isNotBlank(request.getSuitablePersonality())) {
            wrapper.eq(TeacherProfile::getSuitablePersonality, request.getSuitablePersonality());
        }

        // 暑期课上课时间筛选 (字符串字段)
        if (StrUtil.isNotBlank(request.getSummerScheduleType())) {
            wrapper.eq(TeacherProfile::getSummerScheduleType, request.getSummerScheduleType());
        }

        log.debug("应用筛选条件完成，包含所有前端传递的筛选字段");
    }

    /**
     * 应用数组字段筛选，兼容JSON和VARCHAR数组两种类型（用于MPJLambdaWrapper）
     *
     * @param wrapper   查询包装器
     * @param fieldName 字段名
     * @param values    筛选值列表
     */
    private void applyArrayFieldFilter(MPJLambdaWrapper<TeacherProfile> wrapper, String fieldName, List<String> values) {
        if (values == null || values.isEmpty()) {
            return;
        }
        String arrayStr = buildVarcharArrayString(values);
        wrapper.apply(fieldName + " && " + arrayStr);
    }

    /**
     * 应用数组字段筛选，兼容JSON和VARCHAR数组两种类型（用于LambdaQueryChainWrapper）
     *
     * @param queryWrapper 查询包装器
     * @param fieldName    字段名
     * @param values       筛选值列表
     */
    private void applyArrayFieldFilterForLambdaQuery(LambdaQueryWrapper<TeacherProfile> queryWrapper, String fieldName, List<String> values) {
        if (values == null || values.isEmpty()) {
            return;
        }
        // 首先尝试使用 VARCHAR 数组语法
        String arrayStr = buildVarcharArrayString(values);
        queryWrapper.apply(fieldName + " && " + arrayStr);
        log.debug("使用 VARCHAR 数组语法查询字段: {}", fieldName);
    }

    /**
     * 构建PostgreSQL VARCHAR数组字符串
     *
     * @param values 字符串列表
     * @return PostgreSQL数组格式字符串，如 ARRAY['value1','value2']::VARCHAR[]
     */
    private String buildVarcharArrayString(List<String> values) {
        if (values == null || values.isEmpty()) {
            return "ARRAY[]::VARCHAR[]";
        }

        StringBuilder sb = new StringBuilder("ARRAY[");
        for (int i = 0; i < values.size(); i++) {
            if (i > 0) {
                sb.append(",");
            }
            // 转义单引号防止SQL注入
            String escapedValue = values.get(i).replace("'", "''");
            sb.append("'").append(escapedValue).append("'");
        }
        // 显式指定类型为 VARCHAR[] 以匹配数据库字段类型
        sb.append("]::VARCHAR[]");

        return sb.toString();
    }

    /**
     * 构建PostgreSQL数组字符串（向后兼容方法）
     *
     * @param values 字符串列表
     * @return PostgreSQL数组格式字符串
     */
    private String buildArrayString(List<String> values) {
        return buildVarcharArrayString(values);
    }

    /**
     * 应用时间段筛选条件到 MPJLambdaWrapper
     * <p>
     * 使用 EXISTS 子查询确保教师能同时满足所有时间段需求：
     * - 每个学生时间段对应一个 EXISTS 子查询
     * - 多个 EXISTS 之间用 AND 连接
     * - 确保教师必须同时满足所有时间段才能被筛选出来
     * <p>
     * SQL示例：
     * WHERE EXISTS(SELECT 1 FROM teacher_time_slot WHERE weekday=1 AND start_time<=10:00 AND end_time>=09:00)
     * AND EXISTS(SELECT 1 FROM teacher_time_slot WHERE weekday=3 AND start_time<=15:00 AND end_time>=14:00)
     */
    private void applyTimeSlotFiltersToWrapper(MPJLambdaWrapper<TeacherProfile> wrapper,
                                               List<TeacherMatchDto.StudentTimeSlot> studentTimeSlots) {
        if (studentTimeSlots == null || studentTimeSlots.isEmpty()) {
            return;
        }

        log.info("应用时间段筛选条件，学生需求时间段数量: {}", studentTimeSlots.size());
        log.info("使用 EXISTS 子查询确保教师能同时满足所有时间段需求");

        // 为每个学生时间段添加一个 EXISTS 子查询
        for (int i = 0; i < studentTimeSlots.size(); i++) {
            TeacherMatchDto.StudentTimeSlot studentSlot = studentTimeSlots.get(i);

            try {
                // 将学生时间段的字符串时间转换为LocalTime进行比较
                java.time.LocalTime studentStartTime = java.time.LocalTime.parse(studentSlot.getStartTime());
                java.time.LocalTime studentEndTime = java.time.LocalTime.parse(studentSlot.getEndTime());

                // 使用 apply 方法添加 EXISTS 子查询，确保教师有匹配的时间段
                // 为了在lambda中使用
                wrapper.apply(StrUtil.format("EXISTS (SELECT 1 FROM teacher_time_slot tts " +
                                "WHERE tts.teacher_id = t.teacher_id " +
                                "AND tts.deleted = false " +
                                "AND tts.status = 'available' " +
                                "AND tts.weekday = {} " +
                                "AND tts.start_time <= '{}' " +
                                "AND tts.end_time >= '{}')",
                        studentSlot.getWeekday(),
                        studentEndTime,
                        studentStartTime));

                log.debug("添加时间段EXISTS条件[{}]: 星期{} {}~{}",
                        i, studentSlot.getWeekday(), studentSlot.getStartTime(), studentSlot.getEndTime());

            } catch (Exception e) {
                log.error("解析学生时间段失败: {}~{}", studentSlot.getStartTime(), studentSlot.getEndTime(), e);
                throw new RuntimeException("学生时间段格式错误: " + studentSlot.getStartTime() + "~" + studentSlot.getEndTime());
            }
        }

        log.info("时间段筛选策略：使用{}个EXISTS子查询确保教师同时满足所有时间段", studentTimeSlots.size());
    }

    /**
     * 应用课点更新天数筛选条件到 MPJLambdaWrapper
     * <p>
     * 筛选老师可排课时间最后更新时间距离当前时间的天数
     *
     * @param wrapper 查询包装器
     * @param timeSlotUpdateDays 课点更新天数，如果为null则默认为3天
     */
    private void applyTimeSlotUpdateDaysFilter(MPJLambdaWrapper<TeacherProfile> wrapper, Integer timeSlotUpdateDays) {
        if (timeSlotUpdateDays == null) {
            timeSlotUpdateDays = this.timeSlotUpdateDays;
        }

        log.info("应用课点更新天数筛选，天数: {}", timeSlotUpdateDays);

        // 计算截止时间：当前时间减去指定天数
        Date cutoffDate = Date.from(
            LocalDateTime.now()
                .minusDays(timeSlotUpdateDays)
                .atZone(ZoneId.systemDefault())
                .toInstant()
        );

        // 筛选teacher_time_slot表的update_time >= cutoffDate的记录
        wrapper.ge(TeacherTimeSlot::getUpdateTime, cutoffDate);

        log.info("课点更新天数筛选条件应用完成，截止时间: {}", cutoffDate);
    }

}
