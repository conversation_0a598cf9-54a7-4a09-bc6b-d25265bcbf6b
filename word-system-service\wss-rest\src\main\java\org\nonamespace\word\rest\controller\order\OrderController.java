package org.nonamespace.word.rest.controller.order;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.constants.OrderConstants;
import org.nonamespace.word.server.domain.order.Orders;
import org.nonamespace.word.server.domain.order.OrdersTrx;
import org.nonamespace.word.server.dto.order.OrderCreateDto;
import org.nonamespace.word.server.dto.order.OrderDto;
import org.nonamespace.word.server.dto.order.OrderExportDto;
import org.nonamespace.word.server.dto.order.OrderRefundDto;
import org.nonamespace.word.server.dto.order.PaymentDto;
import org.nonamespace.word.server.util.ExcelExportUtil;
import org.nonamespace.word.server.util.ExportPermissionUtil;
import org.nonamespace.word.server.service.order.IOrdersService;
import org.nonamespace.word.server.service.order.IOrdersTrxService;
import org.nonamespace.word.server.service.esign.IESignService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 订单管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@RestController
@RequestMapping("/order")
@RequiredArgsConstructor
@Validated
public class OrderController extends BaseController {

    private final IOrdersService ordersService;
    private final IOrdersTrxService ordersTrxService;
    private final IESignService eSignService;
    private final ExportPermissionUtil exportPermissionUtil;

    /**
     * 创建订单
     */
    @PreAuthorize("@ss.hasPermi('order:create')")
    @Log(title = "创建订单", businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public AjaxResult createOrder(@Valid @RequestBody OrderCreateDto orderCreateDto) {
        try {
            log.info("创建订单请求参数: {}", orderCreateDto);
            Orders orders = ordersService.createOrder(orderCreateDto);
            return AjaxResult.success("订单创建成功", Map.of("orderId", orders.getId(), "orderNo", orders.getNo()));
        } catch (Exception e) {
            log.error("创建订单失败", e);
            return error("创建订单失败: " + e.getMessage());
        }
    }

    /**
     * 根据订单ID查询订单详情
     */
    @PreAuthorize("@ss.hasPermi('order:query')")
    @GetMapping("/{orderId}")
    public AjaxResult getOrderDetail(@PathVariable String orderId) {
        try {
            Orders order = ordersService.getById(orderId);
            if (order == null) {
                return error("订单不存在");
            }
            return AjaxResult.success(order);
        } catch (Exception e) {
            log.error("查询订单详情失败", e);
            return error("查询订单详情失败: " + e.getMessage());
        }
    }

    /**
     * 根据订单号查询订单详情
     */
    @PreAuthorize("@ss.hasPermi('order:query')")
    @GetMapping("/no/{orderNo}")
    public AjaxResult getOrderByNo(@PathVariable String orderNo) {
        try {
            Orders order = ordersService.getByOrderNo(orderNo);
            if (order == null) {
                return error("订单不存在");
            }
            return AjaxResult.success(order);
        } catch (Exception e) {
            log.error("根据订单号查询订单失败", e);
            return error("根据订单号查询订单失败: " + e.getMessage());
        }
    }

    /**
     * 根据学生ID查询订单列表
     */
    @PreAuthorize("@ss.hasPermi('order:query')")
    @GetMapping("/student/{studentId}")
    public AjaxResult getOrdersByStudent(@PathVariable String studentId) {
        try {
            List<Orders> orders = ordersService.getByStudentId(studentId);
            return AjaxResult.success(orders);
        } catch (Exception e) {
            log.error("根据学生ID查询订单列表失败", e);
            return error("根据学生ID查询订单列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据销售员ID查询订单列表
     */
    @PreAuthorize("@ss.hasPermi('order:query')")
    @GetMapping("/saler/{salerId}")
    public AjaxResult getOrdersBySaler(@PathVariable String salerId) {
        try {
            List<Orders> orders = ordersService.getBySalerId(salerId);
            return AjaxResult.success(orders);
        } catch (Exception e) {
            log.error("根据销售员ID查询订单列表失败", e);
            return error("根据销售员ID查询订单列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据订单状态查询订单列表
     */
    @PreAuthorize("@ss.hasPermi('order:query')")
    @GetMapping("/status/{orderStatus}")
    public AjaxResult getOrdersByStatus(@PathVariable String orderStatus) {
        try {
            List<Orders> orders = ordersService.getByOrderStatus(orderStatus);
            return AjaxResult.success(orders);
        } catch (Exception e) {
            log.error("根据订单状态查询订单列表失败", e);
            return error("根据订单状态查询订单列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据订单ID查询交易流水列表
     */
    @PreAuthorize("@ss.hasPermi('order:query')")
    @GetMapping("/{orderId}/transactions")
    public AjaxResult getOrderTransactions(@PathVariable String orderId) {
        try {
            log.info("查询订单交易流水请求参数: {}", orderId);
            List<OrdersTrx> transactions = ordersTrxService.getByOrderId(orderId);
            if(CollUtil.isEmpty(transactions)) {
                return AjaxResult.success("订单暂无交易流水");
            }
            // 如果有多笔交易流水，需要按照支付顺序获取第一笔。
            Optional<OrdersTrx> ordersTrx = transactions.stream().filter(trx -> trx.getTrxStatus().equalsIgnoreCase(OrderConstants.OrderStatus.UNPAID)).findFirst();
            return AjaxResult.success(ordersTrx.orElse(null));
        } catch (Exception e) {
            log.error("查询订单交易流水失败", e);
            return error("查询订单交易流水失败: " + e.getMessage());
        }
    }

//    /**
//     * 发起支付（生成支付二维码）
//     */
//    @PreAuthorize("@ss.hasPermi('order:pay')")
//    @Log(title = "订单支付", businessType = BusinessType.UPDATE)
//    @PostMapping("/pay/{orderTrxId}")
//    public AjaxResult nativePay(@PathVariable String orderTrxId) {
//        try {
//            ordersService.nativePay(orderTrxId);
//            return AjaxResult.success("支付信息生成成功");
//        } catch (Exception e) {
//            log.error("生成支付信息失败", e);
//            return error("生成支付信息失败: " + e.getMessage());
//        }
//    }

    /**
     * 取消订单
     */
    @PreAuthorize("@ss.hasPermi('order:cancel')")
    @Log(title = "订单管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{orderId}/cancel")
    public AjaxResult cancelOrder(@PathVariable String orderId) {
        try {
            ordersService.cancelOrder(orderId);
            return AjaxResult.success("订单取消成功");
        } catch (Exception e) {
            log.error("取消订单失败", e);
            return error("取消订单失败: " + e.getMessage());
        }
    }

    /**
     * 生成支付信息
     */
    @PreAuthorize("@ss.hasPermi('order:pay')")
    @PostMapping("/payment/{orderTrxId}")
    public AjaxResult generatePayment(@PathVariable String orderTrxId) {
        try {
            PaymentDto.PayResp payResp = ordersService.generatePayment(orderTrxId);
            return AjaxResult.success(payResp);
        } catch (Exception e) {
            log.error("生成支付信息失败", e);
            return error("生成支付信息失败: " + e.getMessage());
        }
    }

    /**
     * 生成支付二维码
     */
    @PreAuthorize("@ss.hasPermi('order:pay')")
    @PostMapping("/qrcode/{orderTrxId}")
    public AjaxResult generateQRCode(@PathVariable String orderTrxId) {
        try {
            PaymentDto.QRCodeResp qrCodeResp = ordersService.generateQRCode(orderTrxId);
            return AjaxResult.success(qrCodeResp);
        } catch (Exception e) {
            log.error("生成支付二维码失败", e);
            return error("生成支付二维码失败: " + e.getMessage());
        }
    }

    /**
     * 复制支付链接
     */
    @PreAuthorize("@ss.hasPermi('order:pay')")
    @GetMapping("/payment-link/{orderTrxId}")
    public AjaxResult getPaymentLink(@PathVariable String orderTrxId) {
        try {
            PaymentDto.PayResp payResp = ordersService.generatePayment(orderTrxId);
            return AjaxResult.success("支付链接获取成功", payResp.getPayUrl());
        } catch (Exception e) {
            log.error("获取支付链接失败", e);
            return error("获取支付链接失败: " + e.getMessage());
        }
    }

    /**
     * 订单退款
     */
    @PreAuthorize("@ss.hasPermi('order:refund')")
    @Log(title = "订单退款", businessType = BusinessType.UPDATE)
    @PostMapping("/{orderId}/refund")
    public AjaxResult refundOrder(@PathVariable String orderId, @Valid @RequestBody OrderRefundDto.RefundReq refundReq) {
        try {
            // 验证订单ID一致性
            if (!orderId.equals(refundReq.getOrderId())) {
                return error("订单ID不匹配");
            }

            ordersService.refundOrder(orderId, refundReq.getRefundAmount(), refundReq.getRefundReason());

            OrderRefundDto.RefundResp resp = new OrderRefundDto.RefundResp();
            resp.setOrderId(orderId);
            resp.setRefundAmount(refundReq.getRefundAmount());
            resp.setRefundReason(refundReq.getRefundReason());
            resp.setMessage("订单退款成功");

            return AjaxResult.success("订单退款成功", resp);
        } catch (Exception e) {
            log.error("订单退款失败", e);
            return error("订单退款失败: " + e.getMessage());
        }
    }

    /**
     * 全额退款
     */
    @PreAuthorize("@ss.hasPermi('order:refund')")
    @Log(title = "订单全额退款", businessType = BusinessType.UPDATE)
    @PostMapping("/{orderId}/full-refund")
    public AjaxResult fullRefundOrder(@PathVariable String orderId, @Valid @RequestBody OrderRefundDto.FullRefundReq refundReq) {
        try {
            // 验证订单ID一致性
            if (!orderId.equals(refundReq.getOrderId())) {
                return error("订单ID不匹配");
            }

            ordersService.fullRefundOrder(orderId, refundReq.getRefundReason());

            OrderRefundDto.RefundResp resp = new OrderRefundDto.RefundResp();
            resp.setOrderId(orderId);
            resp.setRefundReason(refundReq.getRefundReason());
            resp.setMessage("订单全额退款成功");

            return AjaxResult.success("订单全额退款成功", resp);
        } catch (Exception e) {
            log.error("订单全额退款失败", e);
            return error("订单全额退款失败: " + e.getMessage());
        }
    }

    /**
     * 导出订单数据
     */
    @PreAuthorize("@ss.hasPermi('order:export')")
    @Log(title = "导出订单数据", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void exportOrders(OrderExportDto.ExportReq req, HttpServletResponse response) {
        try {
            log.info("开始导出订单数据: req={}", req);

            // 权限验证
            if (!exportPermissionUtil.hasOrderExportPermission()) {
                response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                return;
            }

            // 验证导出数量
            ExportPermissionUtil.ExportValidationResult countValidation =
                exportPermissionUtil.validateExportCount(req.getMaxExportCount());
            if (!countValidation.isValid()) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write(countValidation.getMessage());
                return;
            }

            // 验证时间范围
            ExportPermissionUtil.ExportValidationResult timeValidation =
                exportPermissionUtil.validateTimeRange(req.getCreateTimeStart(), req.getCreateTimeEnd());
            if (!timeValidation.isValid()) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write(timeValidation.getMessage());
                return;
            }

            // 获取导出数据
            List<OrderExportDto.ExportResp> exportData = ordersService.exportOrders(req);

            if (exportData.isEmpty()) {
                response.setStatus(HttpServletResponse.SC_NO_CONTENT);
                return;
            }

            // 生成文件名
            String fileName = "订单数据_" + System.currentTimeMillis() + ".xlsx";

            // 创建表头映射
            LinkedHashMap<String, String> headers = ExcelExportUtil.createOrderExportHeaders();

            // 导出Excel
            ExcelExportUtil.exportToResponse(response, fileName, "订单数据", headers, exportData);

            // 记录导出日志
            exportPermissionUtil.logExportOperation("order", exportData.size(), req.toString());

            log.info("订单数据导出成功: fileName={}, count={}", fileName, exportData.size());

        } catch (Exception e) {
            log.error("导出订单数据失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 发起合同签署
     */
    @PreAuthorize("@ss.hasPermi('order:sign')")
    @Log(title = "订单合同签署", businessType = BusinessType.UPDATE)
    @PostMapping("/{orderId}/sign")
    public AjaxResult initiateContractSign(@PathVariable String orderId) {
        try {
            eSignService.buildContractsFile(orderId);
            return AjaxResult.success("合同签署发起成功");
        } catch (Exception e) {
            log.error("发起合同签署失败", e);
            return error("发起合同签署失败: " + e.getMessage());
        }
    }
}
