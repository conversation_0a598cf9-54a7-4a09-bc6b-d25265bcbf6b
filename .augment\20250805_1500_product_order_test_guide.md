# 产品下单功能测试指南

## 功能概述

本次开发实现了完整的产品下单功能，包括：
- 产品管理（CRUD操作）
- 产品选择和学生下单
- 支付方式选择（一次性付款/分期付款）
- 支付功能（二维码支付、复制支付链接、微信模板消息）
- 订单管理和查看

## 测试前准备

### 1. 数据库准备
执行SQL脚本创建产品表和示例数据：
```sql
-- 执行 .augment/20250805_1500_product_management_sql.sql
```

### 2. 后端服务启动
确保以下服务正常运行：
- word-system-service 后端服务
- 数据库连接正常
- 支付服务（通联支付）配置正确

### 3. 前端服务启动
启动管理前端：
```bash
cd words-frontend
npm run dev
```

## 测试流程

### 1. 产品管理测试

#### 1.1 访问产品管理页面
- URL: `/management/product`
- 验证产品列表显示正常
- 验证搜索、筛选功能

#### 1.2 产品CRUD操作测试
- **创建产品**：点击"新增"按钮，填写产品信息
- **编辑产品**：点击"修改"按钮，更新产品信息
- **上架/下架**：测试产品状态切换
- **删除产品**：测试产品删除功能

### 2. 下单流程测试

#### 2.1 访问下单页面
- URL: `/management/order`
- 验证步骤条显示正常

#### 2.2 步骤1：选择产品
- 验证产品列表加载正常
- 验证产品信息显示完整（名称、价格、课时等）
- 选择一个产品，验证选中状态
- 点击"下一步"

#### 2.3 步骤2：选择学生
- 验证学生搜索功能
- 输入学生姓名进行搜索
- 选择一个学生
- 点击"下一步"

#### 2.4 步骤3：确认订单
- 验证产品信息显示正确
- 验证学生信息显示正确
- **测试一次性付款**：
  - 选择"一次性付款"
  - 点击"确认订单"
- **测试分期付款**：
  - 选择"分期付款"
  - 添加多个分期
  - 设置每期金额
  - 验证总金额计算正确
  - 点击"确认订单"

#### 2.5 步骤4：支付
- 验证订单创建成功
- 验证订单号显示
- **测试二维码支付**：
  - 点击"生成支付二维码"
  - 验证二维码显示正常
- **测试复制支付链接**：
  - 点击"复制支付链接"
  - 验证链接复制成功
- **测试微信模板消息**：
  - 点击"发送微信模板消息"
  - 验证消息发送提示

### 3. 订单管理测试

#### 3.1 访问订单管理页面
- URL: `/management/order-management`
- 验证订单列表显示

#### 3.2 订单查询测试
- 测试按订单号搜索
- 测试按学生姓名搜索
- 测试按订单状态筛选
- 测试按支付方式筛选
- 测试按时间范围筛选

#### 3.3 订单操作测试
- **查看订单详情**：
  - 点击"查看"按钮
  - 验证订单基本信息显示
  - 验证交易流水显示
- **订单支付**：
  - 对未支付订单点击"支付"
  - 验证支付弹窗显示
  - 测试生成二维码、复制链接等功能
- **取消订单**：
  - 对未支付订单点击"取消"
  - 验证取消确认提示
  - 验证订单状态更新

## API接口测试

### 产品管理接口
```bash
# 获取产品列表
GET /management/products

# 获取产品详情
GET /management/products/{id}

# 创建产品
POST /management/products

# 更新产品
PUT /management/products

# 删除产品
DELETE /management/products/{ids}

# 上架产品
PUT /management/products/{id}/enable

# 下架产品
PUT /management/products/{id}/disable

# 获取可用产品列表
GET /management/products/available
```

### 订单管理接口
```bash
# 创建订单
POST /order/create

# 获取订单详情
GET /order/{orderId}

# 获取交易流水
GET /order/{orderId}/transactions

# 生成支付信息
POST /order/payment/{orderTrxId}

# 生成支付二维码
POST /order/qrcode/{orderTrxId}

# 获取支付链接
GET /order/payment-link/{orderTrxId}

# 取消订单
PUT /order/{orderId}/cancel
```

## 常见问题排查

### 1. 产品列表为空
- 检查数据库中product表是否有数据
- 检查产品状态是否为"上架"
- 检查API接口是否正常返回

### 2. 学生搜索无结果
- 检查学生管理API是否正常
- 检查学生数据是否存在
- 检查搜索参数是否正确

### 3. 订单创建失败
- 检查产品ID是否有效
- 检查学生ID是否有效
- 检查分期金额总和是否等于产品价格
- 检查后端日志错误信息

### 4. 支付二维码生成失败
- 检查通联支付服务配置
- 检查交易流水状态
- 检查二维码生成服务

### 5. 前端页面报错
- 检查浏览器控制台错误信息
- 检查API接口返回状态
- 检查前端路由配置

## 性能优化建议

1. **产品列表优化**：
   - 添加分页功能
   - 添加图片懒加载
   - 优化产品搜索性能

2. **订单查询优化**：
   - 添加索引优化查询性能
   - 实现订单状态缓存
   - 优化大数据量分页

3. **支付功能优化**：
   - 添加支付状态轮询
   - 实现支付结果回调
   - 优化二维码生成性能

## 安全注意事项

1. **权限控制**：
   - 确保所有接口都有适当的权限验证
   - 验证用户只能操作自己权限范围内的数据

2. **数据验证**：
   - 前后端都要进行数据验证
   - 防止SQL注入和XSS攻击

3. **支付安全**：
   - 验证支付金额的一致性
   - 实现支付结果验证机制
   - 记录所有支付操作日志

## 后续优化方向

1. 添加产品库存管理
2. 实现优惠券和促销功能
3. 添加订单退款功能
4. 实现微信公众号支付
5. 添加订单统计和报表功能
