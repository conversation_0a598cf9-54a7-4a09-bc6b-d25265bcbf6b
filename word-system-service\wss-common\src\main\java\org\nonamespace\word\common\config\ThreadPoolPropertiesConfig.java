package org.nonamespace.word.common.config;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = ThreadPoolPropertiesConfig.PREFIX)
public class ThreadPoolPropertiesConfig {

    public static final String PREFIX = "thread";

    private int corePoolSize = 4;

    private int maximumPoolSize = 8;

    private int keepAliveTime = 3;

}
