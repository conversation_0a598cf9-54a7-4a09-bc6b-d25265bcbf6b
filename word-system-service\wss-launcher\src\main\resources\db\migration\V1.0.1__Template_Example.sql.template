-- 数据库变更脚本模板
-- 复制此模板创建新的数据库变更脚本

-- ============================================================================
-- 脚本信息
-- ============================================================================
-- 脚本描述：[请填写具体的变更描述]
-- 版本：V1.0.X
-- 作者：[开发者姓名]
-- 创建时间：[YYYY-MM-DD]
-- 关联需求：[需求编号或JIRA链接]
-- 影响范围：[影响的表、功能模块]
-- 预计执行时间：[预估的执行时间]
-- 回滚方案：[如何回滚此变更]

-- ============================================================================
-- 变更前检查
-- ============================================================================
-- 检查前置条件
-- 示例：检查表是否存在
-- DO $$
-- BEGIN
--     IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'target_table') THEN
--         RAISE EXCEPTION '前置条件不满足：target_table表不存在';
--     END IF;
-- END $$;

-- ============================================================================
-- 主要变更内容
-- ============================================================================

-- 1. 表结构变更
-- 示例：创建新表
-- CREATE TABLE IF NOT EXISTS new_table (
--     id VARCHAR(64) PRIMARY KEY,
--     name VARCHAR(100) NOT NULL,
--     status VARCHAR(20) DEFAULT 'active',
--     created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
--     updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
-- );

-- 示例：添加列
-- ALTER TABLE existing_table 
-- ADD COLUMN IF NOT EXISTS new_column VARCHAR(50);

-- 示例：修改列
-- ALTER TABLE existing_table 
-- ALTER COLUMN existing_column TYPE VARCHAR(100);

-- 2. 索引变更
-- 示例：创建索引
-- CREATE INDEX IF NOT EXISTS idx_table_column 
-- ON table_name(column_name);

-- 示例：删除索引
-- DROP INDEX IF EXISTS old_index_name;

-- 3. 数据迁移
-- 示例：数据更新
-- UPDATE table_name 
-- SET column_name = 'new_value' 
-- WHERE condition;

-- 示例：数据插入
-- INSERT INTO table_name (column1, column2) 
-- VALUES ('value1', 'value2')
-- ON CONFLICT (unique_column) DO NOTHING;

-- 4. 约束变更
-- 示例：添加外键约束
-- ALTER TABLE child_table 
-- ADD CONSTRAINT fk_child_parent 
-- FOREIGN KEY (parent_id) REFERENCES parent_table(id);

-- 示例：添加检查约束
-- ALTER TABLE table_name 
-- ADD CONSTRAINT chk_status 
-- CHECK (status IN ('active', 'inactive', 'pending'));

-- ============================================================================
-- 变更后验证
-- ============================================================================
-- 验证变更是否成功
-- 示例：检查表结构
-- DO $$
-- BEGIN
--     IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
--                    WHERE table_name = 'target_table' AND column_name = 'new_column') THEN
--         RAISE EXCEPTION '变更验证失败：new_column列未创建成功';
--     END IF;
-- END $$;

-- 示例：检查数据完整性
-- DO $$
-- DECLARE
--     record_count INTEGER;
-- BEGIN
--     SELECT COUNT(*) INTO record_count FROM target_table WHERE condition;
--     IF record_count = 0 THEN
--         RAISE WARNING '警告：目标表中没有符合条件的记录';
--     END IF;
-- END $$;

-- ============================================================================
-- 注释和文档
-- ============================================================================
-- 添加表注释
-- COMMENT ON TABLE new_table IS '新表的用途说明';
-- COMMENT ON COLUMN new_table.id IS '主键ID';
-- COMMENT ON COLUMN new_table.name IS '名称字段';

-- ============================================================================
-- 回滚脚本（仅作参考，不会自动执行）
-- ============================================================================
-- 以下是回滚脚本，需要时手动执行

-- 回滚步骤1：删除新增的约束
-- ALTER TABLE table_name DROP CONSTRAINT IF EXISTS constraint_name;

-- 回滚步骤2：删除新增的索引
-- DROP INDEX IF EXISTS index_name;

-- 回滚步骤3：删除新增的列
-- ALTER TABLE table_name DROP COLUMN IF EXISTS column_name;

-- 回滚步骤4：删除新增的表
-- DROP TABLE IF EXISTS new_table;

-- ============================================================================
-- 执行说明
-- ============================================================================
-- 1. 此脚本在事务中执行，如果任何步骤失败，所有变更将回滚
-- 2. 执行前请确保数据库已备份
-- 3. 建议在非生产环境充分测试
-- 4. 如需回滚，请手动执行上述回滚脚本
