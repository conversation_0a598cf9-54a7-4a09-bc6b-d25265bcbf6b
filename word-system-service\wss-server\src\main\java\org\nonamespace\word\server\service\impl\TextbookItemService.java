package org.nonamespace.word.server.service.impl;

import cn.hutool.core.util.IdUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.nonamespace.word.server.domain.TextbookItem;
import org.nonamespace.word.server.dto.TextbookItemTreeoDto;
import org.nonamespace.word.server.enums.TextBookNodeEnum;
import org.nonamespace.word.server.mapper.TextbookItemMapper;
import org.nonamespace.word.server.service.ITextbookItemService;
import org.nonamespace.word.server.service.base.BaseService;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 词词汇关联: 关联词和具体单词，定义词汇级别等Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Service
public class TextbookItemService extends BaseService<TextbookItemMapper,TextbookItem> implements ITextbookItemService {
    /**
     * 对比教材内容并更新数据库
     *
     * @param textbookId      教材ID
     * @param textbookContent 教材文本内容
     */
    public void compareTextbookItem(String textbookId, String textbookContent) {
        List<Item> newItems = buildItems(textbookContent);
        List<TextbookItem> existingItems = this.lambdaQuery().eq(TextbookItem::getTextbookId, textbookId).list();

        // 构建现有数据映射
        Map<String, TextbookItem> existingUnitsMap = new HashMap<>();
        Map<String, List<TextbookItem>> existingWordsMap = new HashMap<>();
        for (TextbookItem item : existingItems) {
            if (item.getNodeType() == TextBookNodeEnum.UNIT.getValue()) {
                existingUnitsMap.put(item.getUnitName(), item);
            } else if (item.getNodeType() == TextBookNodeEnum.WORD.getValue()) {
                existingWordsMap.computeIfAbsent(item.getPid(), k -> new ArrayList<>()).add(item);
            }
        }

        List<TextbookItem> toSave = new ArrayList<>();
        List<TextbookItem> toUpdate = new ArrayList<>();
        List<String> toDelete = new ArrayList<>();
        int displayOrder = 1;

        // 检查是否有单元结构
        boolean hasUnits = newItems.stream().anyMatch(item -> !item.isLeaf());

        // 收集新内容中的单元和单词，用于后续删除比较
        Set<String> newUnitNames = new HashSet<>();
        Set<String> newWordNames = new HashSet<>();
        collectNewItemNames(newItems, hasUnits, newUnitNames, newWordNames);

        if (hasUnits) {
            // 处理有单元结构的情况
            for (Item unit : newItems) {
                String unitId = processUnit(unit, existingUnitsMap, textbookId, displayOrder++, toSave);
                processWordsInUnit(unit.getItems(), unitId, existingWordsMap, textbookId, toSave, toUpdate);
            }
        } else {
            // 处理无单元结构的情况，所有单词pid=0
            processWordsInUnit(newItems, "0", existingWordsMap, textbookId, toSave, toUpdate);
        }

        // 处理删除逻辑
        processDeletedItems(existingUnitsMap, existingWordsMap, newUnitNames, newWordNames, hasUnits, toDelete);

        // 批量操作
        if (!toSave.isEmpty()) {
            this.saveBatch(toSave);
        }
        if (!toUpdate.isEmpty()) {
            this.updateBatchById(toUpdate);
        }
        if (!toDelete.isEmpty()) {
            this.removeByIds(toDelete);
        }
    }

    @Override
    public void updateDisplayOrder(String textbookId) {
        baseMapper.updateDisplayOrder(textbookId);
    }

    /**
     * 处理单元
     */
    private String processUnit(Item unit, Map<String, TextbookItem> existingUnitsMap,
                              String textbookId, int displayOrder, List<TextbookItem> toSave) {
        TextbookItem existingUnit = existingUnitsMap.get(unit.getName());
        if (existingUnit != null) {
            return existingUnit.getId();
        }

        TextbookItem newUnit = new TextbookItem();
        newUnit.setId(IdUtil.getSnowflakeNextIdStr());
        newUnit.setTextbookId(textbookId);
        newUnit.setNodeType(TextBookNodeEnum.UNIT.getValue());
        newUnit.setUnitName(unit.getName());
        newUnit.setPid("0");
        newUnit.setDisplayOrder(displayOrder);
        toSave.add(newUnit);
        return newUnit.getId();
    }

    /**
     * 处理单词列表
     */
    private void processWordsInUnit(List<Item> words, String parentId,
                                   Map<String, List<TextbookItem>> existingWordsMap,
                                   String textbookId, List<TextbookItem> toSave, List<TextbookItem> toUpdate) {
        if (words == null) return;

        // 构建全局单词映射（从所有父级中查找）
        Map<String, TextbookItem> globalWordMap = new HashMap<>();
        for (List<TextbookItem> wordList : existingWordsMap.values()) {
            for (TextbookItem word : wordList) {
                globalWordMap.put(word.getWordId(), word);
            }
        }

        for (Item word : words) {
            TextbookItem existingWord = globalWordMap.get(word.getName());
            if (existingWord != null) {
                // 单词存在，检查是否需要更新父ID
                if (!parentId.equals(existingWord.getPid())) {
                    existingWord.setPid(parentId);
                    existingWord.setDisplayOrder(word.getDisplayOrder()); // 同时更新显示顺序
                    toUpdate.add(existingWord);
                }
            } else {
                // 单词不存在，创建新单词
                TextbookItem newWord = new TextbookItem();
                newWord.setId(IdUtil.getSnowflakeNextIdStr());
                newWord.setTextbookId(textbookId);
                newWord.setNodeType(TextBookNodeEnum.WORD.getValue());
                newWord.setPid(parentId);
                newWord.setWordId(word.getName());
                newWord.setDisplayOrder(word.getDisplayOrder());
                toSave.add(newWord);
            }
        }
    }


    /**
     * 解析教材内容
     * 支持两种格式：
     * 1. 有单元: > unit1 \n apple \n banana
     * 2. 无单元: apple \n banana (所有单词pid=0)
     */
    private List<Item> buildItems(String textbookContent) {
        String[] lines = textbookContent.split("\\r?\\n");

        // 检查是否有单元结构
        boolean hasUnits = hasUnitStructure(lines);

        if (hasUnits) {
            return buildItemsWithUnits(lines);
        } else {
            return buildItemsWithoutUnits(lines);
        }
    }

    /**
     * 检查是否有单元结构
     */
    private boolean hasUnitStructure(String[] lines) {
        for (String line : lines) {
            String trimmed = line.trim();
            if (!trimmed.isEmpty()) {
                return trimmed.startsWith(">");
            }
        }
        return false;
    }

    /**
     * 构建有单元结构的项目
     */
    private List<Item> buildItemsWithUnits(String[] lines) {
        List<Item> topLevelItems = new ArrayList<>();
        Item currentUnit = null;
        int displayOrder = 0;

        for (String line : lines) {
            String trimmed = line.trim();
            if (trimmed.isEmpty()) continue;

            if (trimmed.startsWith(">")) {
                currentUnit = createUnit(trimmed.substring(1).trim());
                topLevelItems.add(currentUnit);
            } else if (currentUnit != null) {
                currentUnit.getItems().add(createWord(trimmed, ++displayOrder, currentUnit.getId()));
            }
        }
        return topLevelItems;
    }

    /**
     * 构建无单元结构的项目
     */
    private List<Item> buildItemsWithoutUnits(String[] lines) {
        List<Item> items = new ArrayList<>();
        int displayOrder = 0;

        for (String line : lines) {
            String trimmed = line.trim();
            if (!trimmed.isEmpty()) {
                items.add(createWord(trimmed, ++displayOrder, "0"));
            }
        }
        return items;
    }

    /**
     * 创建单元项目
     */
    private Item createUnit(String unitName) {
        return new Item()
                .setId(IdUtil.getSnowflakeNextIdStr())
                .setName(unitName)
                .setLeaf(false)
                .setPid("0")
                .setItems(new ArrayList<>());
    }

    /**
     * 创建单词项目
     */
    private Item createWord(String word, int displayOrder, String pid) {
        return new Item()
                .setId(IdUtil.getSnowflakeNextIdStr())
                .setName(word)
                .setLeaf(true)
                .setDisplayOrder(displayOrder)
                .setPid(pid)
                .setItems(null);
    }

    /**
     * 收集新内容中的单元名称和单词名称
     */
    private void collectNewItemNames(List<Item> newItems, boolean hasUnits,
                                   Set<String> newUnitNames, Set<String> newWordNames) {
        if (hasUnits) {
            // 有单元结构
            for (Item unit : newItems) {
                newUnitNames.add(unit.getName());
                if (unit.getItems() != null) {
                    for (Item word : unit.getItems()) {
                        newWordNames.add(word.getName());
                    }
                }
            }
        } else {
            // 无单元结构，所有都是单词
            for (Item word : newItems) {
                newWordNames.add(word.getName());
            }
        }
    }

    /**
     * 处理需要删除的项目
     * 重要：保持已存在项目的ID不变，只删除真正不存在的项目
     */
    private void processDeletedItems(Map<String, TextbookItem> existingUnitsMap,
                                   Map<String, List<TextbookItem>> existingWordsMap,
                                   Set<String> newUnitNames, Set<String> newWordNames,
                                   boolean hasUnits, List<String> toDelete) {

        // 1. 删除不再存在的单元（但不删除其下的单词，单词单独判断）
        if (hasUnits) {
            for (String existingUnitName : existingUnitsMap.keySet()) {
                if (!newUnitNames.contains(existingUnitName)) {
                    TextbookItem unitToDelete = existingUnitsMap.get(existingUnitName);
                    toDelete.add(unitToDelete.getId());
                    // 注意：不直接删除单元下的单词，单词会在下面单独判断
                }
            }
        } else {
            // 无单元结构时，删除所有现有单元（但不删除单词，单词单独判断）
            for (TextbookItem unit : existingUnitsMap.values()) {
                toDelete.add(unit.getId());
                // 注意：不直接删除单元下的单词，单词会在下面单独判断
            }
        }

        // 2. 删除不再存在的单词（逐个判断，不受单元删除影响）
        for (List<TextbookItem> words : existingWordsMap.values()) {
            for (TextbookItem word : words) {
                // 只有在新内容中完全不存在的单词才删除
                if (!newWordNames.contains(word.getWordId())) {
                    toDelete.add(word.getId());
                }
            }
        }
    }

    public List<TextbookItemTreeoDto> getTreeItemList(String textbookId,String studentId) {
        return this.baseMapper.getTreeItemList(textbookId,studentId);
    }

    public List<TextbookItemTreeoDto> getTreeItemListByUnitId(String unitId, String studentId) {
        return this.baseMapper.getTreeItemListByUnitId(unitId,studentId);
    }


    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    class Item {
        private String id;
        private String pid;
        private String name;
        private boolean isLeaf;
        private int displayOrder;

        private List<Item> items;
    }

}
