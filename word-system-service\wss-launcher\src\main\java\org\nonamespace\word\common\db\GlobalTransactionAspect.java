package org.nonamespace.word.common.db;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

@Aspect
@Component
public class GlobalTransactionAspect {

    private static final Logger logger = LoggerFactory.getLogger(GlobalTransactionAspect.class);

    @Autowired
    private PlatformTransactionManager transactionManager;

    // == 定义切点 ==

    /**
     * 匹配Service包下所有public方法 (一个比较通用的切点)
     * execution([可见性] 返回类型 [方法名模式](参数模式) [异常模式])
     * 第一个 * : 匹配任何可见性 (public, protected, private) 和任何返回类型
     * com.example.yourapp.service..* : service包及其子包下的任何类
     * .*(..) : 任何方法名，任何参数
     */
    @Pointcut("execution(public * org.nonamespace.word.*.service..*.*(..)) || execution(public * org.nonamespace.word..*.facade..*.*(..))")
    public void allPublicMethodsInServicePackage() {}

//    /**
//     * 切点：匹配典型的写操作方法名
//     * 可以在 allPublicMethodsInServicePackage() 的基础上进一步筛选
//     */
//    @Pointcut("allPublicMethodsInServicePackage() && " +
//              "(execution(* *..*.save*(..)) || " +
//              "execution(* *..*.insert*(..)) || " +
//              "execution(* *..*.add*(..)) || " +
//              "execution(* *..*.update*(..)) || " +
//              "execution(* *..*.edit*(..)) || " +
//              "execution(* *..*.delete*(..)) || " +
//              "execution(* *..*.remove*(..)) || " +
//              "execution(* *..*.execute*(..)) || " + // 一些通用的执行方法
//              "execution(* *..*.process*(..)))")    // 一些通用的处理方法
//    public void writeOperations() {}
//
//    /**
//     * 切点：匹配典型的读操作方法名
//     * 可以在 allPublicMethodsInServicePackage() 的基础上进一步筛选
//     * 并且排除掉已经是写操作的方法 (如果writeOperations的范围过大，这里可以用来精确化)
//     */
//    @Pointcut("allPublicMethodsInServicePackage() && " +
//              "(execution(* *..*.get*(..)) || " +
//              "execution(* *..*.find*(..)) || " +
//              "execution(* *..*.select*(..)) || " +
//              "execution(* *..*.query*(..)) || " +
//              "execution(* *..*.list*(..)) || " +
//              "execution(* *..*.is*(..)) || " +
//              "execution(* *..*.exist*(..)) || " +
//              "execution(* *..*.count*(..))) && " +
//              "!writeOperations()") // 确保不与写操作重叠，或者通过@Order控制顺序
//    public void readOperations() {}
//
//
//    // == 定义环绕通知 ==
//
//    /**
//     * 为写操作方法应用读写事务
//     * 使用@Order确保这个通知在只读通知之前或之后有一个明确的顺序，
//     * 如果切点可能重叠，更具体的通知应该有更高的优先级 (更小的Order值)。
//     * 但由于我们这里通过 !writeOperations() 做了排除，重叠问题不大。
//     */
//    @Around("writeOperations()")
//    @Order(10) // 优先级较高
//    public Object manageReadWriteTransaction(ProceedingJoinPoint joinPoint) throws Throwable {
//        String methodName = joinPoint.getSignature().getName();
//        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
//        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
//        def.setIsolationLevel(TransactionDefinition.ISOLATION_DEFAULT); // 根据需要调整
//        def.setReadOnly(false);
//        // def.setTimeout(30); // 30秒超时，可选
//
//        TransactionStatus status = transactionManager.getTransaction(def);
//        logger.debug("Starting READ-WRITE transaction for method: {}", methodName);
//
//        Object result;
//        try {
//            result = joinPoint.proceed();
//            transactionManager.commit(status);
//            logger.debug("Committed READ-WRITE transaction for method: {}", methodName);
//        } catch (Throwable throwable) {
//            transactionManager.rollback(status);
//            logger.error("Rolled back READ-WRITE transaction for method: {} due to exception: {}", methodName, throwable.getMessage(), throwable);
//            throw throwable;
//        }
//        return result;
//    }
//
//    /**
//     * 为读操作方法应用只读事务
//     */
//    @Around("readOperations()")
//    @Order(20) // 优先级较低
//    public Object manageReadOnlyTransaction(ProceedingJoinPoint joinPoint) throws Throwable {
//        String methodName = joinPoint.getSignature().getName();
//        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
//        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED); // 即使是只读，通常也需要加入现有事务或新建
//        def.setReadOnly(true);
//
//        TransactionStatus status = transactionManager.getTransaction(def);
//        logger.debug("Starting READ-ONLY transaction for method: {}", methodName);
//
//        Object result;
//        try {
//            result = joinPoint.proceed();
//            // 对于只读事务，某些优化下可能不需要显式commit，但Spring的标准做法是commit
//            transactionManager.commit(status);
//            logger.debug("Committed READ-ONLY transaction for method: {}", methodName);
//        } catch (Throwable throwable) {
//            // 只读事务理论上不应该修改数据，回滚的意义更多是清理资源和状态
//            transactionManager.rollback(status);
//            logger.error("Rolled back READ-ONLY transaction for method: {} due to exception: {}", methodName, throwable.getMessage(), throwable);
//            throw throwable;
//        }
//        return result;
//    }

    // 如果你不想区分读写，可以只用一个更通用的切点和通知：

    @Around("allPublicMethodsInServicePackage()")
    public Object manageAllServiceMethodsWithDefaultTransaction(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        // 默认读写事务
        // 可以根据方法名动态判断是否read-only，但会使通知逻辑变复杂
        // if (methodName.startsWith("get") || methodName.startsWith("find")) {
        //     def.setReadOnly(true);
        // }

        TransactionStatus status = transactionManager.getTransaction(def);
        logger.debug("Transaction started for method (global): {}", methodName);
        Object result;
        try {
            result = joinPoint.proceed();
            transactionManager.commit(status);
            logger.debug("Transaction committed for method (global): {}", methodName);
        } catch (Throwable throwable) {
            transactionManager.rollback(status);
            logger.error("Transaction rolled back for method (global): {} due to: {}", methodName, throwable.getMessage());
            throw throwable;
        }
        return result;
    }
}