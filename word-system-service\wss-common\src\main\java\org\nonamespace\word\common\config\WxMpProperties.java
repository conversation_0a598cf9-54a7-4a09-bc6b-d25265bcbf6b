package org.nonamespace.word.common.config;

import cn.hutool.json.JSONUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * //TODO
 *
 * <AUTHOR>
 * @date 2025/6/4 13:59
 */
@Data
@Primary
@Component("wxMpProperties")
@ConfigurationProperties(prefix = "wx.mp")
public class WxMpProperties {
    /**
     * 是否使用redis存储access token
     */
    private boolean useRedis;

    /**
     * 多个公众号配置信息
     */
    private List<MpConfig> configs;

    private String redirectUri;
    private String learningReportDetailUrl;
    private String reviewReportDetailUrl;

    @Data
    public static class MpConfig {
        /**
         * 设置微信公众号的appid
         */
        private String appId;

        /**
         * 设置微信公众号的app secret
         */
        private String secret;

        /**
         * 设置微信公众号的token
         */
        private String token;

        /**
         * 设置微信公众号的EncodingAESKey
         */
        private String aesKey;

        /**
         * 课程学习完成通知模板ID
         */
        private String learningReportTemplateId;

        /**
         * 上课通知模板ID
         */
        private String startCourseTemplateId;
    }

    @Override
    public String toString() {
        return JSONUtil.toJsonStr(this);
    }
}
