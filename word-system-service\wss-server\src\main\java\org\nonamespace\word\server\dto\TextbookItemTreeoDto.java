package org.nonamespace.word.server.dto;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

/**
 * <AUTHOR> Created on 2025/06/14 22:37
 */
@Data
public class TextbookItemTreeoDto {
    private String textbookId;

    /** 节点类型(章节、单词) */
    private int nodeType;

    private String id;

    private String unitName;

    /** 父节点ID，同一个词表内pid=0 */
    private String pid;

    /** 单词ID (外键, 级联删除) */
    private String wordId;

    /** 讲解视频 */
    private String videoUrl;

    /** 显示顺序  */
    private int displayOrder;

    private String word;

    //教材类型
    private String bookType;

    //学生学习进度id
    private String studentWordItemId;

    //单词学习状态
    private String progressStatus;
}
