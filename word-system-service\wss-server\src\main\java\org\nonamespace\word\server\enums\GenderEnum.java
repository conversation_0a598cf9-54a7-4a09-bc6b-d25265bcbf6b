package org.nonamespace.word.server.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 性别枚举类
 */
@Getter
public enum GenderEnum {
    MALE("男", "0"),
    FEMALE("女", "1"),
    UNKNOWN("未知", "2");


    private final String text;
    private final String value;

    GenderEnum(String text, String value) {
        this.text = text;
        this.value = value;
    }


    public static GenderEnum fromText(String text) {
        for (GenderEnum grade : GenderEnum.values()) {
            if (grade.getText().equals(text)) {
                return grade;
            }
        }
        return UNKNOWN;
    }

    public static GenderEnum fromValue(String value) {
        for (GenderEnum grade : GenderEnum.values()) {
            if (Objects.equals(grade.getValue(), value)) {
                return grade;
            }
        }
        return UNKNOWN;
    }

}