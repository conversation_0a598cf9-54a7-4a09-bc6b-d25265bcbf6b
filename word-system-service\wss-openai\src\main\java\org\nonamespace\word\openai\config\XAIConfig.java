package org.nonamespace.word.openai.config;


import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * x.ai配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "xai")
public class XAIConfig {

    private String baseUrl;
    private String apiKey;
    private String model;
    private String reasoningEffort;
    private Integer temperature;

    private Proxy proxy;

    @Getter
    @Setter
    public static class Proxy {
        private String host = "127.0.0.1";
        private int port = 7890;
    }

}
