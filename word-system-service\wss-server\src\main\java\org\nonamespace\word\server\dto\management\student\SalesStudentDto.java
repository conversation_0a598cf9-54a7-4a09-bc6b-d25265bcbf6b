package org.nonamespace.word.server.dto.management.student;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 销售版学生管理相关DTO
 * 
 * <AUTHOR>
 * @date 2025-06-29
 */
public class SalesStudentDto {

    /**
     * 学生基础信息响应（销售版）
     */
    @Data
    public static class BasicResp {
        private String id;
        private String name;
        private String phone;
        private String gender;
        private String grade;
        private String school;
        private String className;
        private String parentName;
        private String parentPhone;
        private BigDecimal totalHours;
        private BigDecimal consumedHours;
        private BigDecimal remainingHours;
        private String status;
        private Date createTime;
        private Date updateTime;

        // 销售相关字段
        private String salesId;
        private String salesName;
        private String salesPhone;
        private String salesGroupId;
        private String salesGroupName;
        private Date assignTime;
        
        // 教师相关字段
        private String teacherId;
        private String teacherName;

        // 多个教师信息（新增）
        private List<TeacherInfo> teachers;
    }

    /**
     * 学生详细信息响应（销售版）
     */
    @Data
    public static class DetailResp {
        private String id;
        private String name;
        private String phone;
        private String gender;
        private String grade;
        private String school;
        private String className;
        private String parentName;
        private String parentPhone;
        private BigDecimal totalHours;
        private BigDecimal consumedHours;
        private BigDecimal remainingHours;
        private String learningGoals;
        private String remarks;
        private String status;
        private Date createTime;
        private Date updateTime;

        // 销售相关字段
        private String salesId;
        private String salesName;
        private String salesPhone;
        private String salesGroupId;
        private String salesGroupName;
        private Date assignTime;
        
        // 教师相关字段
        private String teacherId;
        private String teacherName;
    }

    /**
     * 查询学生列表请求（销售版）
     */
    @Data
    public static class GetListReq {
        private Integer pageNum = 1;
        private Integer pageSize = 10;
        private String name;
        private String phone;
        private String grade;
        private String school;
        private String status;
        private String salesId;
        private String salesGroupId;
        private List<String> salesGroupIds;
        private String teacherId;
        private Date createTimeStart;
        private Date createTimeEnd;
    }

    /**
     * 创建学生请求（销售版）
     */
    @Data
    public static class CreateReq {
        @NotBlank(message = "学生姓名不能为空")
        private String name;

        @NotBlank(message = "手机号码不能为空")
        private String phone;

        @NotBlank(message = "性别不能为空")
        private String gender;

        private String grade;
        private String school;
        private String className;
        private String parentName;
        private String parentPhone;
        private String learningGoals;
        private String remarks;
        private String status;
        
        // 销售相关字段
        private String salesId; // 分配给指定销售（可选）
        private String salesGroupId; // 分配给指定销售组（可选）
    }

    /**
     * 更新学生请求（销售版）
     */
    @Data
    public static class UpdateReq {
        @NotBlank(message = "学生ID不能为空")
        private String id;

        @NotBlank(message = "学生姓名不能为空")
        private String name;

        @NotBlank(message = "手机号码不能为空")
        private String phone;

        @NotBlank(message = "性别不能为空")
        private String gender;

        private String grade;
        private String school;
        private String className;
        private String parentName;
        private String parentPhone;
        private String learningGoals;
        private String remarks;
        private String status;
        
        // 销售相关字段
        private String salesId;
        private String salesGroupId;
    }

    /**
     * 分配学生给销售请求
     */
    @Data
    public static class AssignReq {
        @NotBlank(message = "学生ID不能为空")
        private String studentId;

        @NotBlank(message = "销售ID不能为空")
        private String salesId;

        private String salesGroupId; // 可选，如果不指定则从销售信息中获取

        private String assignReason; // 分配原因（可选）
    }

    /**
     * 批量分配学生给销售请求
     */
    @Data
    public static class BatchAssignReq {
        @NotEmpty(message = "学生ID列表不能为空")
        private List<String> studentIds;

        @NotBlank(message = "销售ID不能为空")
        private String salesId;

        private String salesGroupId; // 可选，如果不指定则从销售信息中获取

        private String assignReason; // 分配原因（可选）
    }

    /**
     * 智能分配请求
     */
    @Data
    public static class IntelligentAssignReq {
        @NotEmpty(message = "学生ID列表不能为空")
        private List<String> studentIds;

        private String salesGroupId; // 指定销售组，如果不指定则在所有销售中分配

        @NotBlank(message = "分配策略不能为空")
        private String strategy = "load_balance"; // 分配策略：load_balance(负载均衡), random(随机), round_robin(轮询)

        private Integer maxStudentsPerSales = 50; // 每个销售最大学生数限制

        private String reason = "智能分配"; // 分配原因
    }

    /**
     * 智能分配响应
     */
    @Data
    public static class IntelligentAssignResp {
        private Integer totalStudents; // 总分配学生数
        private Integer successCount; // 成功分配数
        private Integer failCount; // 失败分配数
        private List<AssignmentResult> assignments; // 分配结果详情

        @Data
        public static class AssignmentResult {
            private String studentId;
            private String studentName;
            private String salesId;
            private String salesName;
            private String salesGroupName;
            private String reason;
            private Boolean success;
            private String errorMessage;
        }
    }

    /**
     * 导入结果响应
     */
    @Data
    public static class ImportResp {
        private Integer totalCount; // 总数
        private Integer successCount; // 成功数
        private Integer failCount; // 失败数
        private List<String> errorMessages; // 错误信息列表
        private List<String> successStudentIds; // 成功创建的学生ID列表
    }

    /**
     * 统计信息响应（销售版）
     */
    @Data
    public static class StatsResp {
        private Long totalStudents; // 总学生数
        private Long assignedStudents; // 已分配学生数
        private Long unassignedStudents; // 未分配学生数
        private Long activeStudents; // 活跃学生数
        private Long inactiveStudents; // 非活跃学生数
        private BigDecimal totalHours; // 总课时
        private BigDecimal consumedHours; // 已消费课时
        private BigDecimal remainingHours; // 剩余课时
    }

    /**
     * 可分配学生响应
     */
    @Data
    public static class AvailableResp {
        private String id;
        private String name;
        private String phone;
        private String grade;
        private String school;
        private String status;
        private Date createTime;
    }

    /**
     * 销售人员选项响应
     */
    @Data
    public static class SalesOptionResp {
        private String id;
        private String name;
        private String phone;
        private String groupId;
        private String groupName;
        private String status;
    }

    /**
     * 销售组选项响应
     */
    @Data
    public static class GroupOptionResp {
        private String id;
        private String name;
        private String leaderName;
        private Integer memberCount;
        private String status;
    }

    /**
     * 移除学生分配请求
     */
    @Data
    public static class RemoveReq {
        @NotBlank(message = "学生ID不能为空")
        private String studentId;

        private String reason; // 移除原因
    }

    /**
     * 学生分配历史响应
     */
    @Data
    public static class AssignmentHistoryResp {
        private String id;
        private String studentId;
        private String studentName;
        private String salesId;
        private String salesName;
        private String salesGroupName;
        private String action; // 操作类型：ASSIGN, UNASSIGN, TRANSFER
        private String reason; // 操作原因
        private Date operationTime;
        private String operatorId;
        private String operatorName;
    }

    /**
     * 教师信息
     */
    @Data
    public static class TeacherInfo {
        private String teacherId;
        private String teacherName;
    }
}
