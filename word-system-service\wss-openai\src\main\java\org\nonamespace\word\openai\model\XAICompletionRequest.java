package org.nonamespace.word.openai.model;


import lombok.*;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class XAICompletionRequest {

    private List<Message> messages;
    private String reasoning_effort;
    private String model;
    private Integer temperature;

    @Getter
    @Setter
    @Builder
    public static class Message {
        private String role;
        private String content;


    }

}
