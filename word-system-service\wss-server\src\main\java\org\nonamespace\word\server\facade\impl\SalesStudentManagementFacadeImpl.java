package org.nonamespace.word.server.facade.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.nonamespace.word.server.domain.SaleProfile;
import org.nonamespace.word.server.domain.SalesGroup;
import org.nonamespace.word.server.domain.SalesGroupMember;
import org.nonamespace.word.server.domain.StudentAssignmentHistory;
import org.nonamespace.word.server.domain.StudentCourseHours;
import org.nonamespace.word.server.domain.TeacherProfile;
import org.nonamespace.word.server.domain.TeacherStudentRelation;
import org.nonamespace.word.server.domain.UserStudentExt;
import org.nonamespace.word.server.dto.management.student.SalesStudentDto;
import org.nonamespace.word.server.dto.management.student.StudentDto;
import org.nonamespace.word.server.enums.GenderEnum;
import org.nonamespace.word.server.enums.GradeEnum;
import org.nonamespace.word.server.facade.ISalesStudentManagementFacade;
import org.nonamespace.word.server.service.*;
import org.nonamespace.word.server.service.UserStudentExtService;
import org.nonamespace.word.server.util.SystemDataQueryUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 销售版学生管理Facade实现类
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SalesStudentManagementFacadeImpl implements ISalesStudentManagementFacade {

    private final IStudentManagementService studentManagementService;
    private final UserStudentExtService userStudentExtService;
    private final ISaleProfileService saleProfileService;
    private final ISalesGroupService salesGroupService;
    private final ISalesGroupMemberService salesGroupMemberService;
    private final IStudentAssignmentHistoryService studentAssignmentHistoryService;
    private final IStudentCourseHoursService studentCourseHoursService;
    private final ITeacherProfileService teacherProfileService;
    private final ITeacherStudentRelationService teacherStudentRelationService;
    private final SystemDataQueryUtil systemDataQueryUtil;
    private final IUserService userService;

    @Override
    public IPage<SalesStudentDto.BasicResp> getSalesStudentPage(SalesStudentDto.GetListReq req) {
        log.info("分页查询学生列表（销售版）: req={}", req);

        // 应用销售数据权限
        applySalesDataPermissions(req);

        // 直接查询销售学生数据，不再依赖教学服务
        IPage<SalesStudentDto.BasicResp> result = querySalesStudentPage(req);

        log.info("查询学生列表成功: total={}", result.getTotal());
        return result;

    }

    @Override
    public SalesStudentDto.DetailResp getSalesStudentDetail(String studentId) {
        log.info("查询学生详细信息（销售版）: studentId={}", studentId);

        if (StrUtil.isEmpty(studentId)) {
            throw new IllegalArgumentException("学生ID不能为空");
        }

        // 检查数据权限
        checkStudentDataPermission(studentId);

        // 调用通用学生管理服务
        StudentDto.DetailResp studentDetail = studentManagementService.getStudentDetail(studentId);
        if (studentDetail == null) {
            throw new RuntimeException("学生不存在");
        }

        // 转换为销售版响应
        SalesStudentDto.DetailResp result = convertToSalesStudentDetail(studentDetail);

        log.info("查询学生详细信息成功: name={}", result.getName());
        return result;

    }

    @Override
    public String createSalesStudent(SalesStudentDto.CreateReq req) {
        log.info("创建学生（销售版）: req={}", req);

        // 检查创建权限
        checkHasJoinedGroup();

        // 转换为通用学生创建请求
        StudentDto.CreateReq studentReq = convertToStudentCreateReq(req);

        // 调用通用学生管理服务创建学生
        String studentId = studentManagementService.createStudent(studentReq);

        // 处理销售分配
        handleSalesAssignment(studentId, req);

        log.info("创建学生成功: studentId={}", studentId);
        return studentId;

    }

    @Override
    public boolean updateSalesStudent(SalesStudentDto.UpdateReq req) {
        log.info("更新学生信息（销售版）: req={}", req);

        // 检查编辑权限
        checkEditPermission(req.getId());

        // 转换为通用学生更新请求
        StudentDto.UpdateReq studentReq = convertToStudentUpdateReq(req);

        // 调用通用学生管理服务更新学生
        boolean success = studentManagementService.updateStudent(studentReq);

//            if (success) {
//                // 更新销售分配信息
//                updateSalesAssignment(req.getId(), req);
//            }

        log.info("更新学生信息成功: studentId={}", req.getId());
        return success;

    }

    @Override
    public boolean deleteSalesStudent(String studentId) {
        try {
            log.info("删除学生（销售版）: studentId={}", studentId);

            // 检查删除权限
            checkDeletePermission(studentId);

            // 调用通用学生管理服务删除学生
            boolean success = studentManagementService.deleteStudent(studentId);

            log.info("删除学生成功: studentId={}", studentId);
            return success;
        } catch (Exception e) {
            log.error("删除学生失败", e);
            throw new RuntimeException("删除学生失败: " + e.getMessage());
        }
    }

    @Override
    public boolean deleteSalesStudents(List<String> studentIds) {
        try {
            log.info("批量删除学生（销售版）: studentIds={}", studentIds);

            // 检查批量删除权限
            for (String studentId : studentIds) {
                checkDeletePermission(studentId);
            }

            // 调用通用学生管理服务批量删除学生
            boolean success = studentManagementService.deleteStudents(studentIds);

            log.info("批量删除学生成功: count={}", studentIds.size());
            return success;
        } catch (Exception e) {
            log.error("批量删除学生失败", e);
            throw new RuntimeException("批量删除学生失败: " + e.getMessage());
        }
    }

    @Override
    public SalesStudentDto.ImportResp importSalesStudents(MultipartFile file, String salesId) {
        try {
            log.info("导入学生（销售版）: fileName={}, salesId={}", file.getOriginalFilename(), salesId);

            // 检查导入权限
            checkHasJoinedGroup();

            // 解析Excel文件
            List<List<Object>> rows;
            try {
                Workbook workbook = WorkbookFactory.create(file.getInputStream());
                Sheet sheet = workbook.getSheetAt(0);
                rows = new ArrayList<>();

                // 跳过标题行，从第二行开始读取数据
                for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                    Row row = sheet.getRow(i);
                    if (row == null) continue;

                    List<Object> rowData = new ArrayList<>();
                    for (int j = 0; j < 10; j++) { // 读取10列数据
                        Cell cell = row.getCell(j);
                        if (cell == null) {
                            rowData.add("");
                        } else {
                            switch (cell.getCellType()) {
                                case STRING:
                                    rowData.add(cell.getStringCellValue().trim());
                                    break;
                                case NUMERIC:
                                    if (DateUtil.isCellDateFormatted(cell)) {
                                        rowData.add(cell.getDateCellValue());
                                    } else {
                                        // 处理数字，如果是整数则转为字符串
                                        double numValue = cell.getNumericCellValue();
                                        if (numValue == Math.floor(numValue)) {
                                            rowData.add(String.valueOf((long) numValue));
                                        } else {
                                            rowData.add(String.valueOf(numValue));
                                        }
                                    }
                                    break;
                                case BOOLEAN:
                                    rowData.add(String.valueOf(cell.getBooleanCellValue()));
                                    break;
                                default:
                                    rowData.add("");
                            }
                        }
                    }
                    rows.add(rowData);
                }
                workbook.close();
            } catch (Exception e) {
                log.error("解析Excel文件失败", e);
                throw new RuntimeException("Excel文件格式错误，请检查文件格式");
            }

            // 处理导入数据
            SalesStudentDto.ImportResp result = new SalesStudentDto.ImportResp();
            result.setTotalCount(rows.size());
            result.setSuccessCount(0);
            result.setFailCount(0);
            result.setErrorMessages(new ArrayList<>());
            result.setSuccessStudentIds(new ArrayList<>());

            for (int i = 0; i < rows.size(); i++) {
                List<Object> row = rows.get(i);
                try {
                    // 解析行数据
                    String name = getStringValue(row, 0);
                    String phone = getStringValue(row, 1);
                    String genderStr = getStringValue(row, 2);
                    String grade = getStringValue(row, 3);
                    String school = getStringValue(row, 4);
                    String className = getStringValue(row, 5);
                    String parentName = getStringValue(row, 6);
                    String parentPhone = getStringValue(row, 7);
                    String learningGoal = getStringValue(row, 8);
                    String remark = getStringValue(row, 9);

                    // 验证必填字段
                    if (StrUtil.isBlank(name)) {
                        result.getErrorMessages().add("第" + (i + 2) + "行：姓名不能为空");
                        result.setFailCount(result.getFailCount() + 1);
                        continue;
                    }
                    if (StrUtil.isBlank(phone)) {
                        result.getErrorMessages().add("第" + (i + 2) + "行：手机号不能为空");
                        result.setFailCount(result.getFailCount() + 1);
                        continue;
                    }
                    if (StrUtil.isBlank(genderStr)) {
                        result.getErrorMessages().add("第" + (i + 2) + "行：性别不能为空");
                        result.setFailCount(result.getFailCount() + 1);
                        continue;
                    }

                    // 验证手机号格式
                    if (!phone.matches("^1[3-9]\\d{9}$")) {
                        result.getErrorMessages().add("第" + (i + 2) + "行：手机号格式不正确");
                        result.setFailCount(result.getFailCount() + 1);
                        continue;
                    }

                    // 检查手机号是否已存在（跳过重复记录）
                    boolean phoneExists = userStudentExtService.lambdaQuery()
                            .eq(UserStudentExt::getPhone, phone)
                            .eq(UserStudentExt::getDeleted, false)
                            .exists();

                    if (phoneExists) {
                        result.getErrorMessages().add("第" + (i + 2) + "行：手机号 " + phone + " 已存在，跳过");
                        result.setFailCount(result.getFailCount() + 1);
                        continue;
                    }

                    // 验证性别值
                    if (!genderStr.matches("^(男|女|未知)$")) {
                        result.getErrorMessages().add("第" + (i + 2) + "行：性别值无效，应为男、女或未知");
                        result.setFailCount(result.getFailCount() + 1);
                        continue;
                    }

                    // 验证年级值
                    if (StrUtil.isNotBlank(grade) && GradeEnum.fromText(grade) == GradeEnum.NULL) {
                        result.getErrorMessages().add("第" + (i + 2) + "行：年级值无效，应该为一年级到六年级，初一到初三，高一到高三");
                        result.setFailCount(result.getFailCount() + 1);
                        continue;
                    }

                    SysUser user = new SysUser();
                    user.setDeptId(systemDataQueryUtil.getStudentDept().getDeptId());
                    user.setRoleIds(new Long[]{systemDataQueryUtil.getStudentRole().getRoleId()});
                    user.setNickName(name);
                    user.setUserName(phone);
                    user.setPhonenumber(phone);
                    user.setSex(GenderEnum.fromText(genderStr).getValue());
                    userService.save(user);

                    // 创建学生记录
                    UserStudentExt student = new UserStudentExt();
                    student.setStudentId(String.valueOf(user.getUserId()));
                    student.setName(name);
                    student.setPhone(phone);
                    student.setGender(GenderEnum.fromText(genderStr).getValue()); // gender是String类型
                    student.setGrade(String.valueOf(GradeEnum.fromText(grade).getValue()));
                    student.setSchool(school);
                    student.setClassName(className);
                    student.setParentName(parentName);
                    student.setParentPhone(parentPhone);
                    student.setLearningGoals(learningGoal); // 正确的字段名
                    student.setRemarks(remark); // 正确的字段名
                    student.setStatus("active"); // 设置默认状态
                    student.setDeleted(false);
                    student.setCreateTime(new Date());
                    student.setUpdateTime(new Date());

                    // 保存学生
                    userStudentExtService.save(student);
                    result.getSuccessStudentIds().add(student.getId());
                    result.setSuccessCount(result.getSuccessCount() + 1);

                    // 如果指定了销售人员，进行分配
                    if (StrUtil.isNotBlank(salesId)) {
                        try {
                            assignStudentToSalesInternal(student.getStudentId(), salesId);
                            log.info("成功分配学生给销售: studentId={}, salesId={}", student.getStudentId(), salesId);
                        } catch (Exception e) {
                            log.warn("分配学生给销售失败，但学生创建成功: studentId={}, salesId={}, error={}",
                                    student.getStudentId(), salesId, e.getMessage());
                            // 分配失败不影响导入成功，只记录警告日志
                        }
                    }

                    log.info("成功导入学生: name={}, phone={}", name, phone);

                } catch (Exception e) {
                    log.error("导入第" + (i + 2) + "行数据失败", e);
                    result.getErrorMessages().add("第" + (i + 2) + "行：" + e.getMessage());
                    result.setFailCount(result.getFailCount() + 1);
                }
            }

            log.info("导入学生完成: total={}, success={}, fail={}",
                    result.getTotalCount(), result.getSuccessCount(), result.getFailCount());
            return result;
        } catch (Exception e) {
            log.error("导入学生失败", e);
            throw new RuntimeException("导入学生失败: " + e.getMessage());
        }
    }

    private String getStringValue(List<Object> row, int index) {
        if (index >= row.size() || row.get(index) == null) {
            return "";
        }
        return row.get(index).toString().trim();
    }

    @Override
    public byte[] downloadImportTemplate() {
        try {
            log.info("下载学生导入模板");

            // 创建Excel工作簿 - 使用Apache POI，与学生课时保持一致
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("学生导入模板");

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {"姓名*", "手机号*", "性别*", "年级", "学校", "班级", "家长姓名", "家长手机", "学习目标", "备注"};

            // 创建标题样式
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            headerStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 设置标题
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
                sheet.autoSizeColumn(i);
            }

            // 创建示例数据行
            Row exampleRow1 = sheet.createRow(1);
            String[] exampleData1 = {"张三", "13800138001", "男", "五年级", "北京小学", "五年级一班", "张父", "13800138002", "提高英语成绩", "无"};
            for (int i = 0; i < exampleData1.length; i++) {
                Cell cell = exampleRow1.createCell(i);
                cell.setCellValue(exampleData1[i]);
            }

            Row exampleRow2 = sheet.createRow(2);
            String[] exampleData2 = {"李四", "13800138003", "女", "初一", "上海中学", "初一二班", "李母", "13800138004", "培养英语兴趣", "无"};
            for (int i = 0; i < exampleData2.length; i++) {
                Cell cell = exampleRow2.createCell(i);
                cell.setCellValue(exampleData2[i]);
            }

            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // 写入字节数组
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            workbook.write(out);
            workbook.close();

            byte[] result = out.toByteArray();
            log.info("生成学生导入模板成功: size={}", result.length);
            return result;
        } catch (Exception e) {
            log.error("生成学生导入模板失败", e);
            throw new RuntimeException("生成学生导入模板失败: " + e.getMessage());
        }
    }

    /**
     * 应用数据权限
     */
    private void applyDataPermissions(SalesStudentDto.GetListReq req) {
        // 根据用户角色应用数据权限
        if (systemDataQueryUtil.isAdminOrHr() || systemDataQueryUtil.isSalesDirector()) {
            // 管理员、HR、销售总监可以查看所有学生
            return;
        }

        if (systemDataQueryUtil.isSalesGroupLeader()) {
            // 销售组长可以查看本组及以下所有销售的学生
            String currentUserId = SecurityUtils.getUserId().toString();

            // 查询当前用户管理的销售组
            List<SalesGroup> managedGroups = salesGroupService.lambdaQuery()
                    .eq(SalesGroup::getLeaderId, currentUserId)
                    .eq(SalesGroup::getDeleted, false)
                    .list();

            if (managedGroups.isEmpty()) {
                // 如果不是任何组的组长，则没有权限查看
                req.setSalesId("NONE");
                return;
            }

            // 设置销售组过滤条件
            List<String> groupIds = managedGroups.stream()
                    .map(SalesGroup::getId)
                    .toList();
            req.setSalesGroupIds(groupIds);
            return;
        }

        if (systemDataQueryUtil.isSales()) {
            // 销售只能查看自己名下的学生
            String currentUserId = SecurityUtils.getUserId().toString();
            req.setSalesId(currentUserId);
            return;
        }

        // 其他角色没有权限查看学生
        throw new RuntimeException("没有权限查看学生");
    }

    /**
     * 应用销售数据权限（专用于销售学生查询）
     */
    private void applySalesDataPermissions(SalesStudentDto.GetListReq req) {
        // 复用现有的数据权限逻辑
        applyDataPermissions(req);
    }

    /**
     * 直接查询销售学生分页数据（不依赖教学服务）
     */
    private IPage<SalesStudentDto.BasicResp> querySalesStudentPage(SalesStudentDto.GetListReq req) {
        // 构建分页对象
        Page<UserStudentExt> page = new Page<>(req.getPageNum(), req.getPageSize());

        // 构建查询条件
        var queryWrapper = userStudentExtService.lambdaQuery()
                .eq(UserStudentExt::getDeleted, false)
                .like(StrUtil.isNotEmpty(req.getName()), UserStudentExt::getName, req.getName())
                .like(StrUtil.isNotEmpty(req.getPhone()), UserStudentExt::getPhone, req.getPhone())
                .eq(StrUtil.isNotEmpty(req.getGrade()), UserStudentExt::getGrade, req.getGrade())
                .eq(StrUtil.isNotEmpty(req.getStatus()), UserStudentExt::getStatus, req.getStatus())
                .eq(StrUtil.isNotEmpty(req.getSalesId()), UserStudentExt::getSalesId, req.getSalesId())
                .ge(req.getCreateTimeStart() != null, UserStudentExt::getCreateTime, req.getCreateTimeStart())
                .le(req.getCreateTimeEnd() != null, UserStudentExt::getCreateTime, req.getCreateTimeEnd());

        if (req.getSalesGroupIds() == null) {
            req.setSalesGroupIds(new ArrayList<>());
        }
        if (StrUtil.isNotEmpty(req.getSalesGroupId())) {
            req.getSalesGroupIds().add(req.getSalesGroupId());
        }
        // 处理销售组过滤
        if (StrUtil.isNotEmpty(req.getSalesGroupId()) || CollUtil.isNotEmpty(req.getSalesGroupIds())) {

            // 查询销售组下的所有销售人员
            List<SalesGroupMember> members = salesGroupMemberService.lambdaQuery()
                    .in(SalesGroupMember::getGroupId, req.getSalesGroupIds())
                    .eq(SalesGroupMember::getDeleted, false)
                    .eq(SalesGroupMember::getStatus, "active")
                    .list();

            if (members.isEmpty()) {
                queryWrapper.eq(UserStudentExt::getSalesId, "NONE");
            } else {
                List<String> salesIds = members.stream()
                        .map(SalesGroupMember::getSalesId)
                        .collect(Collectors.toList());
                queryWrapper.in(UserStudentExt::getSalesId, salesIds);
            }
        }

        // 执行查询
        IPage<UserStudentExt> studentPage = queryWrapper
                .orderByDesc(UserStudentExt::getCreateTime)
                .page(page);

        // 转换为销售版响应
        return convertToSalesStudentPageDirect(studentPage);
    }

    /**
     * 直接转换为销售版学生分页响应（优化版，使用批量查询）
     */
    private IPage<SalesStudentDto.BasicResp> convertToSalesStudentPageDirect(IPage<UserStudentExt> studentPage) {
        Page<SalesStudentDto.BasicResp> result = new Page<>(studentPage.getCurrent(), studentPage.getSize(), studentPage.getTotal());

        List<UserStudentExt> students = studentPage.getRecords();
        if (students.isEmpty()) {
            result.setRecords(new ArrayList<>());
            return result;
        }

        // 批量查询相关数据
        SalesStudentBatchData batchData = batchQuerySalesStudentData(students);

        // 转换为响应对象
        List<SalesStudentDto.BasicResp> records = students.stream()
                .map(student -> convertToSalesStudentBasicRespWithBatchData(student, batchData))
                .collect(Collectors.toList());

        result.setRecords(records);
        return result;
    }

    /**
     * 销售学生批量查询数据容器
     */
    private static class SalesStudentBatchData {
        private Map<String, List<StudentCourseHours>> courseHoursMap = new HashMap<>();
        private Map<String, SaleProfile> saleProfileMap = new HashMap<>();
        private Map<String, List<SalesStudentDto.TeacherInfo>> teachersMap = new HashMap<>();
    }

    /**
     * 批量查询销售学生相关数据
     */
    private SalesStudentBatchData batchQuerySalesStudentData(List<UserStudentExt> students) {
        SalesStudentBatchData batchData = new SalesStudentBatchData();

        // 收集所有学生ID和销售ID
        Set<String> studentIds = students.stream()
                .map(UserStudentExt::getStudentId)
                .filter(StrUtil::isNotEmpty)
                .collect(Collectors.toSet());

        Set<String> salesIds = students.stream()
                .map(UserStudentExt::getSalesId)
                .filter(StrUtil::isNotEmpty)
                .collect(Collectors.toSet());

        // 批量查询课时数据
        if (!studentIds.isEmpty()) {
            List<StudentCourseHours> allCourseHours = studentCourseHoursService.lambdaQuery()
                    .in(StudentCourseHours::getStudentId, studentIds)
                    .eq(StudentCourseHours::getStatus, "active")
                    .eq(StudentCourseHours::getDeleted, false)
                    .list();

            // 按学生ID分组
            batchData.courseHoursMap = allCourseHours.stream()
                    .collect(Collectors.groupingBy(StudentCourseHours::getStudentId));
        }

        // 批量查询销售人员信息
        if (!salesIds.isEmpty()) {
            List<SaleProfile> saleProfiles = saleProfileService.lambdaQuery()
                    .in(SaleProfile::getSalesId, salesIds)
                    .eq(SaleProfile::getDeleted, false)
                    .list();

            batchData.saleProfileMap = saleProfiles.stream()
                    .collect(Collectors.toMap(SaleProfile::getSalesId, s -> s));
        }

        // 批量查询学生对应的老师信息
        if (!studentIds.isEmpty()) {
            // 查询师生关系
            List<TeacherStudentRelation> relations = teacherStudentRelationService.lambdaQuery()
                    .in(TeacherStudentRelation::getStudentId, studentIds)
                    .eq(TeacherStudentRelation::getStatus, "active")
                    .eq(TeacherStudentRelation::getDeleted, false)
                    .list();

            if (!relations.isEmpty()) {
                // 收集所有教师ID
                Set<String> teacherIds = relations.stream()
                        .map(TeacherStudentRelation::getTeacherId)
                        .filter(StrUtil::isNotEmpty)
                        .collect(Collectors.toSet());

                // 批量查询教师信息
                Map<String, String> teacherNameMap = new HashMap<>();
                if (!teacherIds.isEmpty()) {
                    List<TeacherProfile> teacherProfiles = teacherProfileService.lambdaQuery()
                            .in(TeacherProfile::getTeacherId, teacherIds)
                            .eq(TeacherProfile::getDeleted, false)
                            .list();

                    teacherNameMap = teacherProfiles.stream()
                            .collect(Collectors.toMap(TeacherProfile::getTeacherId, TeacherProfile::getRealName));
                }

                // 按学生ID分组教师信息
                Map<String, List<SalesStudentDto.TeacherInfo>> teachersMap = new HashMap<>();
                for (TeacherStudentRelation relation : relations) {
                    String studentId = relation.getStudentId();
                    String teacherId = relation.getTeacherId();
                    String teacherName = teacherNameMap.get(teacherId);

                    if (StrUtil.isNotEmpty(teacherName)) {
                        SalesStudentDto.TeacherInfo teacherInfo = new SalesStudentDto.TeacherInfo();
                        teacherInfo.setTeacherId(teacherId);
                        teacherInfo.setTeacherName(teacherName);

                        teachersMap.computeIfAbsent(studentId, k -> new ArrayList<>()).add(teacherInfo);
                    }
                }

                batchData.teachersMap = teachersMap;
            }
        }

        return batchData;
    }

    /**
     * 使用批量数据转换单个学生记录
     */
    private SalesStudentDto.BasicResp convertToSalesStudentBasicRespWithBatchData(
            UserStudentExt student, SalesStudentBatchData batchData) {
        SalesStudentDto.BasicResp resp = new SalesStudentDto.BasicResp();
        resp.setId(student.getStudentId());
        resp.setName(student.getName());
        resp.setPhone(student.getPhone());
        resp.setGender(student.getGender());
        resp.setGrade(student.getGrade());
        resp.setSchool(student.getSchool());
        resp.setClassName(student.getClassName());
        resp.setParentName(student.getParentName());
        resp.setParentPhone(student.getParentPhone());
        resp.setStatus(student.getStatus());
        resp.setCreateTime(student.getCreateTime());
        resp.setUpdateTime(student.getUpdateTime());

        // 设置课时信息（从批量查询数据获取）
        List<StudentCourseHours> courseHoursList = batchData.courseHoursMap.get(student.getStudentId());
        if (courseHoursList != null && !courseHoursList.isEmpty()) {
            BigDecimal totalHours = courseHoursList.stream()
                    .map(StudentCourseHours::getTotalHours)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal remainingHours = courseHoursList.stream()
                    .map(StudentCourseHours::getRemainingHours)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal consumedHours = totalHours.subtract(remainingHours);

            resp.setTotalHours(totalHours);
            resp.setConsumedHours(consumedHours);
            resp.setRemainingHours(remainingHours);
        } else {
            resp.setTotalHours(BigDecimal.ZERO);
            resp.setConsumedHours(BigDecimal.ZERO);
            resp.setRemainingHours(BigDecimal.ZERO);
        }

        // 设置销售信息（从批量查询数据获取）
        if (StrUtil.isNotEmpty(student.getSalesId())) {
            SaleProfile saleProfile = batchData.saleProfileMap.get(student.getSalesId());
            if (saleProfile != null) {
                resp.setSalesId(saleProfile.getSalesId());
                resp.setSalesName(saleProfile.getSalesName());
                resp.setSalesGroupName(saleProfile.getSalesGroupName());
            }
        }

        // 设置老师信息（从批量查询数据获取）
        List<SalesStudentDto.TeacherInfo> teachers = batchData.teachersMap.get(student.getStudentId());
        if (teachers != null && !teachers.isEmpty()) {
            resp.setTeachers(teachers);
            // 为了兼容性，设置第一个老师的信息到原有字段
            SalesStudentDto.TeacherInfo firstTeacher = teachers.get(0);
            resp.setTeacherId(firstTeacher.getTeacherId());
            resp.setTeacherName(firstTeacher.getTeacherName());
        } else {
            resp.setTeachers(new ArrayList<>());
        }

        return resp;
    }


    /**
     * 转换为销售版学生基础响应
     */
    private SalesStudentDto.BasicResp convertToSalesStudentBasic(StudentDto.BasicResp student) {
        SalesStudentDto.BasicResp result = new SalesStudentDto.BasicResp();
        result.setId(student.getId());
        result.setName(student.getName());
        result.setPhone(student.getPhone());
        result.setGender(student.getGender());
        result.setGrade(student.getGrade());
        result.setSchool(student.getSchool());
        result.setClassName(student.getClassName());
        result.setParentName(student.getParentName());
        result.setParentPhone(student.getParentPhone());
        result.setTotalHours(student.getTotalHours());
        result.setConsumedHours(student.getConsumedHours());
        result.setRemainingHours(student.getRemainingHours());
        result.setStatus(student.getStatus());
        result.setCreateTime(student.getCreateTime());
        result.setUpdateTime(student.getUpdateTime());

        // 销售相关字段
        result.setSalesId(student.getSalesId());
        result.setSalesName(student.getSalesName());
        result.setSalesPhone(student.getSalesPhone());
        result.setSalesGroupId(student.getSalesGroupId());
        result.setSalesGroupName(student.getSalesGroupName());
        result.setAssignTime(student.getAssignTime());

        // 教师相关字段
        result.setTeacherId(student.getTeacherId());
        result.setTeacherName(student.getTeacherName());

        return result;
    }

    /**
     * 转换为销售版学生详细响应
     */
    private SalesStudentDto.DetailResp convertToSalesStudentDetail(StudentDto.DetailResp student) {
        SalesStudentDto.DetailResp result = new SalesStudentDto.DetailResp();
        result.setId(student.getId());
        result.setName(student.getName());
        result.setPhone(student.getPhone());
        result.setGender(student.getGender());
        result.setGrade(student.getGrade());
        result.setSchool(student.getSchool());
        result.setClassName(student.getClassName());
        result.setParentName(student.getParentName());
        result.setParentPhone(student.getParentPhone());
        result.setTotalHours(student.getTotalHours());
        result.setConsumedHours(student.getConsumedHours());
        result.setRemainingHours(student.getRemainingHours());
        result.setLearningGoals(student.getLearningGoals());
        result.setRemarks(student.getRemarks());
        result.setStatus(student.getStatus());
        result.setCreateTime(student.getCreateTime());
        result.setUpdateTime(student.getUpdateTime());

        // 销售相关字段
        result.setSalesId(student.getSalesId());
        result.setSalesName(student.getSalesName());
        result.setSalesPhone(student.getSalesPhone());
        result.setSalesGroupId(student.getSalesGroupId());
        result.setSalesGroupName(student.getSalesGroupName());
        result.setAssignTime(student.getAssignTime());

        // 教师相关字段
        result.setTeacherId(student.getTeacherId());
        result.setTeacherName(student.getTeacherName());

        return result;
    }

    /**
     * 转换为通用学生创建请求
     */
    private StudentDto.CreateReq convertToStudentCreateReq(SalesStudentDto.CreateReq req) {
        StudentDto.CreateReq studentReq = new StudentDto.CreateReq();
        studentReq.setName(req.getName());
        studentReq.setPhone(req.getPhone());
        studentReq.setGender(req.getGender());
        studentReq.setGrade(req.getGrade());
        studentReq.setSchool(req.getSchool());
        studentReq.setClassName(req.getClassName());
        studentReq.setParentName(req.getParentName());
        studentReq.setParentPhone(req.getParentPhone());
        studentReq.setLearningGoals(req.getLearningGoals());
        studentReq.setRemarks(req.getRemarks());
        studentReq.setStatus(req.getStatus());
        return studentReq;
    }

    /**
     * 转换为通用学生更新请求
     */
    private StudentDto.UpdateReq convertToStudentUpdateReq(SalesStudentDto.UpdateReq req) {
        StudentDto.UpdateReq studentReq = new StudentDto.UpdateReq();
        studentReq.setId(req.getId());
        studentReq.setName(req.getName());
        studentReq.setPhone(req.getPhone());
        studentReq.setGender(req.getGender());
        studentReq.setGrade(req.getGrade());
        studentReq.setSchool(req.getSchool());
        studentReq.setClassName(req.getClassName());
        studentReq.setParentName(req.getParentName());
        studentReq.setParentPhone(req.getParentPhone());
        studentReq.setLearningGoals(req.getLearningGoals());
        studentReq.setRemarks(req.getRemarks());
        studentReq.setStatus(req.getStatus());
        return studentReq;
    }

    /**
     * 处理销售分配
     */
    private void handleSalesAssignment(String studentId, SalesStudentDto.CreateReq req) {
        try {
            String salesId = determineSalesId(req);
            if (StrUtil.isNotEmpty(salesId)) {
                assignStudentToSalesInternal(studentId, salesId);
            }
        } catch (Exception e) {
            log.error("处理销售分配失败: studentId={}", studentId, e);
            // 不抛出异常，避免影响学生创建
        }
    }

    /**
     * 确定销售ID
     */
    private String determineSalesId(SalesStudentDto.CreateReq req) {
        // 如果指定了销售ID，直接使用
        if (StrUtil.isNotEmpty(req.getSalesId())) {
            return req.getSalesId();
        }

        // 如果当前用户是销售，自动分配给自己
        if (systemDataQueryUtil.isSales()) {
            return SecurityUtils.getUserId().toString();
        }

        // 如果当前用户是销售组长，且指定了销售组，可以不分配具体销售
        // 这种情况下返回null，表示不自动分配
        return null;
    }

    /**
     * 内部分配学生给销售
     */
    private void assignStudentToSalesInternal(String studentId, String salesId) {
        // 获取学生当前的销售分配信息
        UserStudentExt student = userStudentExtService.lambdaQuery()
                .eq(UserStudentExt::getStudentId, studentId)
                .eq(UserStudentExt::getDeleted, false)
                .one();

        if (student == null) {
            log.warn("学生不存在: studentId={}", studentId);
            return;
        }

        String originalSalesId = student.getSalesId();

        // 获取原销售组ID（通过关联查询）
        String originalSalesGroupId = null;
        if (StrUtil.isNotEmpty(originalSalesId)) {
            SaleProfile originalSaleProfile = saleProfileService.lambdaQuery()
                    .eq(SaleProfile::getSalesId, originalSalesId)
                    .eq(SaleProfile::getDeleted, false)
                    .one();
            if (originalSaleProfile != null) {
                originalSalesGroupId = originalSaleProfile.getSalesGroupId();
            }
        }

        // 查询新销售人员的销售组信息
        String salesGroupId = null;
        SaleProfile saleProfile = saleProfileService.lambdaQuery()
                .eq(SaleProfile::getSalesId, salesId)
                .eq(SaleProfile::getDeleted, false)
                .one();
        if (saleProfile != null) {
            salesGroupId = saleProfile.getSalesGroupId();
        }

        // 更新学生的销售分配信息（不再存储销售组ID）
        userStudentExtService.lambdaUpdate()
                .set(UserStudentExt::getSalesId, salesId)
                .set(UserStudentExt::getAssignTime, new Date())
                .eq(UserStudentExt::getStudentId, studentId)
                .update();

        // 记录分配历史
        recordAssignmentHistory(studentId, originalSalesId, salesId, originalSalesGroupId, salesGroupId, "分配学生");

        log.info("分配学生给销售成功: studentId={}, salesId={}, salesGroupId={}", studentId, salesId, salesGroupId);
    }

    /**
     * 记录学生分配历史
     */
    private void recordAssignmentHistory(String studentId, String fromSalesId, String toSalesId,
                                         String fromSalesGroupId, String toSalesGroupId, String reason) {
        StudentAssignmentHistory history = new StudentAssignmentHistory();
        history.setId(cn.hutool.core.util.IdUtil.getSnowflakeNextIdStr());
        history.setStudentId(studentId);
        history.setFromSalesId(fromSalesId);
        history.setToSalesId(toSalesId);
        history.setFromSalesGroupId(fromSalesGroupId);
        history.setToSalesGroupId(toSalesGroupId);
        history.setReason(reason);
        history.setOperationTime(new Date());
        history.setOperatorId(SecurityUtils.getUserId().toString());

        // 确定操作类型
        if (StrUtil.isEmpty(fromSalesId) && StrUtil.isNotEmpty(toSalesId)) {
            history.setOperationType("分配");
        } else if (StrUtil.isNotEmpty(fromSalesId) && StrUtil.isEmpty(toSalesId)) {
            history.setOperationType("取消分配");
        } else if (StrUtil.isNotEmpty(fromSalesId) && StrUtil.isNotEmpty(toSalesId)) {
            history.setOperationType("转移");
        }

        // 设置基础字段
        history.setCreateBy(SecurityUtils.getUserId().toString());
        history.setCreateTime(new Date());
        history.setUpdateBy(SecurityUtils.getUserId().toString());
        history.setUpdateTime(new Date());
        history.setDeleted(false);

        studentAssignmentHistoryService.save(history);
        log.info("记录学生分配历史成功: studentId={}, operationType={}", studentId, history.getOperationType());
    }


    /**
     * 检查学生数据权限
     */
    private void checkStudentDataPermission(String studentId) {
        if (systemDataQueryUtil.isAdminOrHr() || systemDataQueryUtil.isSalesDirector()) {
            return;
        }

        UserStudentExt student = userStudentExtService.lambdaQuery()
                .eq(UserStudentExt::getStudentId, studentId)
                .eq(UserStudentExt::getDeleted, false)
                .one();

        if (student == null) {
            throw new RuntimeException("学生不存在");
        }

        String currentUserId = SecurityUtils.getUserId().toString();

        if (systemDataQueryUtil.isSalesGroupLeader()) {
            // 销售组长检查是否是本组学生
            if (StrUtil.isEmpty(student.getSalesId())) {
                throw new RuntimeException("没有权限查看该学生");
            }

            // 通过销售ID查询销售组信息
            SaleProfile saleProfile = saleProfileService.lambdaQuery()
                    .eq(SaleProfile::getSalesId, student.getSalesId())
                    .eq(SaleProfile::getDeleted, false)
                    .one();

            if (saleProfile == null || StrUtil.isEmpty(saleProfile.getSalesGroupId())) {
                throw new RuntimeException("没有权限查看该学生");
            }

            // 检查是否是当前用户管理的销售组
            boolean isGroupLeader = salesGroupService.lambdaQuery()
                    .eq(SalesGroup::getId, saleProfile.getSalesGroupId())
                    .eq(SalesGroup::getLeaderId, currentUserId)
                    .eq(SalesGroup::getDeleted, false)
                    .exists();

            if (!isGroupLeader) {
                throw new RuntimeException("没有权限查看该学生");
            }
            return;
        }

        if (systemDataQueryUtil.isSales()) {
            // 销售检查是否是自己的学生
            if (!currentUserId.equals(student.getSalesId())) {
                throw new RuntimeException("没有权限查看该学生");
            }
            return;
        }

        throw new RuntimeException("没有权限查看该学生");
    }

    /**
     * 检查创建权限
     */
    private void checkCreatePermission() {
        if (systemDataQueryUtil.isAdminOrHr() ||
                systemDataQueryUtil.isSalesDirector() ||
                systemDataQueryUtil.isSalesGroupLeader() ||
                systemDataQueryUtil.isSales()) {
            return;
        }
        throw new RuntimeException("没有权限创建学生");
    }

    /**
     * 检查编辑权限
     */
    private void checkEditPermission(String studentId) {
        checkStudentDataPermission(studentId);
    }

    /**
     * 检查删除权限
     */
    private void checkDeletePermission(String studentId) {
        if (systemDataQueryUtil.isAdminOrHr() || systemDataQueryUtil.isSalesDirector()) {
            return;
        }

        // 销售组长和销售不能删除学生，只能编辑
        throw new RuntimeException("没有权限删除学生");
    }

    /**
     * 检查导入权限
     */
    private void checkHasJoinedGroup() {
        if (!systemDataQueryUtil.isAdmin() && systemDataQueryUtil.isSales()) {
            if (!salesGroupMemberService.lambdaQuery()
                    .eq(SalesGroupMember::getSalesId, systemDataQueryUtil.getCurrentUserId())
                    .eq(SalesGroupMember::getDeleted, false)
                    .exists()) {
                throw new RuntimeException("请先加入销售组");
            }
        }
    }

    @Override
    public boolean assignStudentToSales(SalesStudentDto.AssignReq req) {
        log.info("分配学生给销售: req={}", req);

        // 分配学生给销售
        assignStudentToSalesInternal(req.getStudentId(), req.getSalesId());

        log.info("分配学生给销售成功: studentId={}, salesId={}", req.getStudentId(), req.getSalesId());
        return true;
    }

    @Override
    public boolean batchAssignStudentsToSales(SalesStudentDto.BatchAssignReq req) {
        log.info("批量分配学生给销售: req={}", req);

        checkHasJoinedGroup();

        // 验证销售存在
        SaleProfile saleProfile = saleProfileService.lambdaQuery()
                .eq(SaleProfile::getId, req.getSalesId())
                .eq(SaleProfile::getDeleted, false)
                .one();

        if (saleProfile == null) {
            throw new RuntimeException("销售不存在");
        }

        // 批量分配
        for (String studentId : req.getStudentIds()) {
            assignStudentToSalesInternal(studentId, req.getSalesId());
        }

        log.info("批量分配学生给销售完成: count={}, salesId={}", req.getStudentIds().size(), req.getSalesId());
        return true;
    }

    @Override
    public SalesStudentDto.IntelligentAssignResp intelligentAssignStudents(SalesStudentDto.IntelligentAssignReq req) {
        try {
            log.info("智能分配学生: req={}", req);

            // 检查权限
            checkHasJoinedGroup();

            // 获取可用的销售人员列表
            List<SaleProfile> availableSales = getAvailableSalesForAssignment(req.getSalesGroupId());
            if (availableSales.isEmpty()) {
                throw new RuntimeException("没有可用的销售人员");
            }

            // 验证学生存在
            List<UserStudentExt> students = userStudentExtService.lambdaQuery()
                    .in(UserStudentExt::getStudentId, req.getStudentIds())
                    .eq(UserStudentExt::getDeleted, false)
                    .list();

            if (students.size() != req.getStudentIds().size()) {
                throw new RuntimeException("部分学生不存在");
            }

            // 执行智能分配
            SalesStudentDto.IntelligentAssignResp result = executeIntelligentAssignment(students, availableSales, req);

            log.info("智能分配学生完成: totalStudents={}, successCount={}, failCount={}",
                    result.getTotalStudents(), result.getSuccessCount(), result.getFailCount());
            return result;
        } catch (Exception e) {
            log.error("智能分配学生失败", e);
            throw new RuntimeException("智能分配学生失败: " + e.getMessage());
        }
    }

    @Override
    public boolean unassignStudent(String studentId) {
        try {
            log.info("取消学生分配: studentId={}", studentId);

            // 检查权限
            checkUnassignPermission(studentId);

            // 获取学生当前的销售分配信息
            UserStudentExt student = userStudentExtService.lambdaQuery()
                    .eq(UserStudentExt::getStudentId, studentId)
                    .eq(UserStudentExt::getDeleted, false)
                    .one();

            if (student == null) {
                log.warn("学生不存在: studentId={}", studentId);
                return false;
            }

            String originalSalesId = student.getSalesId();

            // 获取原销售组ID（通过关联查询）
            String originalSalesGroupId = null;
            if (StrUtil.isNotEmpty(originalSalesId)) {
                SaleProfile originalSaleProfile = saleProfileService.lambdaQuery()
                        .eq(SaleProfile::getSalesId, originalSalesId)
                        .eq(SaleProfile::getDeleted, false)
                        .one();
                if (originalSaleProfile != null) {
                    originalSalesGroupId = originalSaleProfile.getSalesGroupId();
                }
            }

            // 清除学生的销售分配信息
            userStudentExtService.lambdaUpdate()
                    .set(UserStudentExt::getSalesId, null)
                    .set(UserStudentExt::getAssignTime, null)
                    .eq(UserStudentExt::getStudentId, studentId)
                    .update();

            // 记录取消分配历史
            recordAssignmentHistory(studentId, originalSalesId, null, originalSalesGroupId, null, "取消分配");

            log.info("取消学生分配成功: studentId={}", studentId);
            return true;
        } catch (Exception e) {
            log.error("取消学生分配失败", e);
            throw new RuntimeException("取消学生分配失败: " + e.getMessage());
        }
    }

    @Override
    public SalesStudentDto.StatsResp getSalesStudentStats() {
        try {
            log.info("获取销售版学生统计信息");

            // 应用数据权限过滤
            SalesStudentDto.GetListReq filterReq = new SalesStudentDto.GetListReq();
            applyDataPermissions(filterReq);

            // 实现统计逻辑
            SalesStudentDto.StatsResp result = new SalesStudentDto.StatsResp();

            // 构建基础查询条件
            var baseQuery = userStudentExtService.lambdaQuery()
                    .eq(UserStudentExt::getDeleted, false);

            // 应用数据权限过滤
            if (systemDataQueryUtil.isSales()) {
                // 销售只能查看自己的学生
                String currentUserId = SecurityUtils.getUserId().toString();
                baseQuery.eq(UserStudentExt::getSalesId, currentUserId);
            } else if (systemDataQueryUtil.isSalesGroupLeader()) {
                // 销售组长可以查看本组及以下所有销售的学生
                String currentUserId = SecurityUtils.getUserId().toString();
                List<SalesGroup> managedGroups = salesGroupService.lambdaQuery()
                        .eq(SalesGroup::getLeaderId, currentUserId)
                        .eq(SalesGroup::getDeleted, false)
                        .list();

                if (!managedGroups.isEmpty()) {
                    // 获取销售组下的所有销售人员ID
                    List<String> groupIds = managedGroups.stream()
                            .map(SalesGroup::getId)
                            .collect(Collectors.toList());

                    List<SalesGroupMember> members = salesGroupMemberService.lambdaQuery()
                            .in(SalesGroupMember::getGroupId, groupIds)
                            .eq(SalesGroupMember::getDeleted, false)
                            .and(wrapper -> wrapper
                                    .eq(SalesGroupMember::getStatus, "active")
                                    .or()
                                    .eq(SalesGroupMember::getStatus, "0")
                            )
                            .list();

                    if (!members.isEmpty()) {
                        List<String> salesIds = members.stream()
                                .map(SalesGroupMember::getSalesId)
                                .collect(Collectors.toList());
                        baseQuery.in(UserStudentExt::getSalesId, salesIds);
                    } else {
                        // 如果组内没有成员，则没有权限查看
                        baseQuery.eq(UserStudentExt::getSalesId, "NONE");
                    }
                } else {
                    // 如果不是任何组的组长，则没有权限查看
                    baseQuery.eq(UserStudentExt::getSalesId, "NONE");
                }
            }
            // 管理员、人力、销售总监可以查看所有学生，不需要额外过滤

            // 统计总学生数
            long totalStudents = baseQuery.count();
            result.setTotalStudents(totalStudents);

            // 统计已分配学生数
            long assignedStudents = baseQuery.isNotNull(UserStudentExt::getSalesId).count();
            result.setAssignedStudents(assignedStudents);

            // 统计未分配学生数
            long unassignedStudents = baseQuery.isNull(UserStudentExt::getSalesId).count();
            result.setUnassignedStudents(unassignedStudents);

            // 统计活跃学生数
            long activeStudents = baseQuery.eq(UserStudentExt::getStatus, "active").count();
            result.setActiveStudents(activeStudents);

            // 统计非活跃学生数
            long inactiveStudents = totalStudents - activeStudents;
            result.setInactiveStudents(inactiveStudents);

            // 统计课时信息
            List<UserStudentExt> studentsWithHours = baseQuery.list();
            BigDecimal totalHours = studentsWithHours.stream()
                    .filter(s -> s.getTotalHours() != null)
                    .map(UserStudentExt::getTotalHours)
                    .map(BigDecimal::valueOf)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal consumedHours = studentsWithHours.stream()
                    .filter(s -> s.getConsumedHours() != null)
                    .map(UserStudentExt::getConsumedHours)
                    .map(BigDecimal::valueOf)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal remainingHours = totalHours.subtract(consumedHours);

            result.setTotalHours(totalHours);
            result.setConsumedHours(consumedHours);
            result.setRemainingHours(remainingHours);

            log.info("获取销售版学生统计信息成功: totalStudents={}, assignedStudents={}, unassignedStudents={}",
                    totalStudents, assignedStudents, unassignedStudents);
            return result;
        } catch (Exception e) {
            log.error("获取销售版学生统计信息失败", e);
            throw new RuntimeException("获取销售版学生统计信息失败: " + e.getMessage());
        }
    }

    @Override
    public List<SalesStudentDto.AvailableResp> getAvailableStudents() {
        try {
            log.info("获取可分配的学生列表");
            // 查询未分配销售的学生
            List<UserStudentExt> students = userStudentExtService.lambdaQuery()
                    .isNull(UserStudentExt::getSalesId)
                    .eq(UserStudentExt::getDeleted, false)
                    .eq(UserStudentExt::getStatus, "active")
                    .orderByDesc(UserStudentExt::getCreateTime)
                    .list();

            List<SalesStudentDto.AvailableResp> result = students.stream()
                    .map(this::convertToAvailableResp)
                    .collect(Collectors.toList());

            log.info("获取可分配的学生列表成功: count={}", result.size());
            return result;
        } catch (Exception e) {
            log.error("获取可分配的学生列表失败", e);
            throw new RuntimeException("获取可分配的学生列表失败: " + e.getMessage());
        }
    }

    @Override
    public List<SalesStudentDto.SalesOptionResp> getSalesOptions(String groupId) {
        try {
            log.info("获取销售人员选项: groupId={}", groupId);

            // 构建查询条件
            var query = saleProfileService.lambdaQuery()
                    .eq(SaleProfile::getDeleted, false)
                    .eq(SaleProfile::getStatus, "active");

            if (StrUtil.isNotEmpty(groupId)) {
                query.eq(SaleProfile::getSalesGroupId, groupId);
            }

            List<SaleProfile> profiles = query.orderBy(true, true, SaleProfile::getSalesGroupName, SaleProfile::getSalesName)
                    .list();

            List<SalesStudentDto.SalesOptionResp> result = profiles.stream()
                    .map(this::convertToSalesOptionResp)
                    .collect(Collectors.toList());

            log.info("获取销售人员选项成功: count={}", result.size());
            return result;
        } catch (Exception e) {
            log.error("获取销售人员选项失败", e);
            throw new RuntimeException("获取销售人员选项失败: " + e.getMessage());
        }
    }

    @Override
    public List<SalesStudentDto.GroupOptionResp> getSalesGroupOptions() {
        try {
            log.info("获取销售组选项");

            List<SalesGroup> groups = salesGroupService.lambdaQuery()
                    .eq(SalesGroup::getDeleted, false)
                    .eq(SalesGroup::getStatus, "active")
                    .orderByAsc(SalesGroup::getName)
                    .list();

            List<SalesStudentDto.GroupOptionResp> result = groups.stream()
                    .map(this::convertToGroupOptionResp)
                    .collect(Collectors.toList());

            log.info("获取销售组选项成功: count={}", result.size());
            return result;
        } catch (Exception e) {
            log.error("获取销售组选项失败", e);
            throw new RuntimeException("获取销售组选项失败: " + e.getMessage());
        }
    }

    /**
     * 转换为可分配学生响应
     */
    private SalesStudentDto.AvailableResp convertToAvailableResp(UserStudentExt student) {
        SalesStudentDto.AvailableResp result = new SalesStudentDto.AvailableResp();
        result.setId(student.getStudentId());
        result.setName(student.getName());
        result.setPhone(student.getPhone());
        result.setGrade(student.getGrade());
        result.setSchool(student.getSchool());
        result.setStatus(student.getStatus());
        result.setCreateTime(student.getCreateTime());
        return result;
    }

    /**
     * 转换为销售人员选项响应
     */
    private SalesStudentDto.SalesOptionResp convertToSalesOptionResp(SaleProfile profile) {
        SalesStudentDto.SalesOptionResp result = new SalesStudentDto.SalesOptionResp();
        result.setId(profile.getSalesId());
        result.setName(profile.getSalesName());
        result.setPhone(profile.getPhone());
        result.setGroupId(profile.getSalesGroupId());
        result.setGroupName(profile.getSalesGroupName());
        result.setStatus(profile.getStatus());
        return result;
    }

    /**
     * 转换为销售组选项响应
     */
    private SalesStudentDto.GroupOptionResp convertToGroupOptionResp(SalesGroup group) {
        SalesStudentDto.GroupOptionResp result = new SalesStudentDto.GroupOptionResp();
        result.setId(group.getId());
        result.setName(group.getName());
        result.setMemberCount(group.getMemberCount());
        result.setStatus(group.getStatus());

        // 查询组长姓名
        if (StrUtil.isNotEmpty(group.getLeaderId())) {
            SaleProfile leader = saleProfileService.lambdaQuery()
                    .eq(SaleProfile::getId, group.getLeaderId())
                    .eq(SaleProfile::getDeleted, false)
                    .one();
            if (leader != null) {
                result.setLeaderName(leader.getSalesName());
            }
        }

        return result;
    }

    /**
     * 检查取消分配权限
     */
    private void checkUnassignPermission(String studentId) {
        if (systemDataQueryUtil.isAdminOrHr() || systemDataQueryUtil.isSalesDirector()) {
            return;
        }

        // 销售组长可以取消本组学生的分配
        if (systemDataQueryUtil.isSalesGroupLeader()) {
            checkStudentDataPermission(studentId);
            return;
        }

        throw new RuntimeException("没有权限取消学生分配");
    }


    @Override
    public boolean removeStudentFromSales(SalesStudentDto.RemoveReq req) {
        try {
            checkEditPermission(req.getStudentId());

            // 查找学生
            UserStudentExt student = userStudentExtService.getById(req.getStudentId());
            if (student == null) {
                throw new RuntimeException("学生不存在");
            }

            // 清除销售分配信息
            student.setSalesId(null);
            // 注意：UserStudentExt可能没有这些字段，只清除salesId即可

            boolean success = userStudentExtService.updateById(student);

            if (success) {
                log.info("学生[{}]从销售分配中移除成功，原因：{}", req.getStudentId(), req.getReason());
            }

            return success;
        } catch (Exception e) {
            log.error("移除学生销售分配失败", e);
            throw new RuntimeException("移除学生销售分配失败：" + e.getMessage());
        }
    }

    @Override
    public List<SalesStudentDto.AssignmentHistoryResp> getStudentAssignmentHistory(String studentId) {
        try {

            log.info("查询学生[{}]的分配历史", studentId);

            // 查询分配历史记录
            List<StudentAssignmentHistory> histories = studentAssignmentHistoryService.lambdaQuery()
                    .eq(StudentAssignmentHistory::getStudentId, studentId)
                    .eq(StudentAssignmentHistory::getDeleted, false)
                    .orderByDesc(StudentAssignmentHistory::getOperationTime)
                    .list();

            if (histories.isEmpty()) {
                return new ArrayList<>();
            }

            // 获取相关的销售人员信息
            Set<String> salesIds = new HashSet<>();
            histories.forEach(history -> {
                if (StrUtil.isNotEmpty(history.getFromSalesId())) {
                    salesIds.add(history.getFromSalesId());
                }
                if (StrUtil.isNotEmpty(history.getToSalesId())) {
                    salesIds.add(history.getToSalesId());
                }
                if (StrUtil.isNotEmpty(history.getOperatorId())) {
                    salesIds.add(history.getOperatorId());
                }
            });

            Map<String, String> salesNameMap = new HashMap<>();
            if (!salesIds.isEmpty()) {
                List<TeacherProfile> teachers = teacherProfileService.lambdaQuery()
                        .in(TeacherProfile::getTeacherId, salesIds)
                        .eq(TeacherProfile::getDeleted, false)
                        .list();

                teachers.forEach(teacher -> {
                    salesNameMap.put(teacher.getTeacherId(), teacher.getRealName());
                });
            }

            // 获取学生信息
            UserStudentExt student = userStudentExtService.lambdaQuery()
                    .eq(UserStudentExt::getStudentId, studentId)
                    .eq(UserStudentExt::getDeleted, false)
                    .one();

            String studentName = student != null ? student.getName() : "未知学生";

            // 转换为响应对象
            return histories.stream().map(history -> {
                SalesStudentDto.AssignmentHistoryResp resp = new SalesStudentDto.AssignmentHistoryResp();
                resp.setId(history.getId());
                resp.setStudentId(history.getStudentId());
                resp.setStudentName(studentName);
                resp.setSalesId(history.getToSalesId());
                resp.setSalesName(salesNameMap.get(history.getToSalesId()));
                resp.setAction(getActionText(history.getOperationType()));
                resp.setReason(history.getReason());
                resp.setOperationTime(history.getOperationTime());
                resp.setOperatorId(history.getOperatorId());
                resp.setOperatorName(salesNameMap.get(history.getOperatorId()));
                return resp;
            }).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询学生分配历史失败", e);
            throw new RuntimeException("查询学生分配历史失败：" + e.getMessage());
        }
    }

    /**
     * 获取操作类型的中文描述
     */
    private String getActionText(String operationType) {
        if (StrUtil.isEmpty(operationType)) {
            return "未知操作";
        }
        switch (operationType) {
            case "ASSIGN":
                return "分配";
            case "UNASSIGN":
                return "取消分配";
            case "TRANSFER":
                return "转移";
            default:
                return operationType;
        }
    }

    /**
     * 通过销售ID获取销售组ID
     */
    private String getSalesGroupIdBySalesId(String salesId) {
        if (StrUtil.isEmpty(salesId)) {
            return null;
        }

        SaleProfile saleProfile = saleProfileService.lambdaQuery()
                .eq(SaleProfile::getSalesId, salesId)
                .eq(SaleProfile::getDeleted, false)
                .one();

        return saleProfile != null ? saleProfile.getSalesGroupId() : null;
    }

    /**
     * 获取可用于分配的销售人员列表
     */
    private List<SaleProfile> getAvailableSalesForAssignment(String salesGroupId) {
        var query = saleProfileService.lambdaQuery()
                .eq(SaleProfile::getDeleted, false)
                .eq(SaleProfile::getStatus, "active");

        // 如果指定了销售组，则只查询该组的销售
        if (StrUtil.isNotEmpty(salesGroupId)) {
            query.eq(SaleProfile::getSalesGroupId, salesGroupId);
        }

        List<SaleProfile> salesList = query.list();

        // 计算每个销售的当前学生数量，用于负载均衡
        for (SaleProfile sale : salesList) {
            long studentCount = userStudentExtService.lambdaQuery()
                    .eq(UserStudentExt::getSalesId, sale.getSalesId())
                    .eq(UserStudentExt::getDeleted, false)
                    .count();
            sale.setCustomerCount((int) studentCount);
        }

        return salesList;
    }

    /**
     * 执行智能分配算法
     */
    private SalesStudentDto.IntelligentAssignResp executeIntelligentAssignment(
            List<UserStudentExt> students,
            List<SaleProfile> availableSales,
            SalesStudentDto.IntelligentAssignReq req) {

        SalesStudentDto.IntelligentAssignResp result = new SalesStudentDto.IntelligentAssignResp();
        result.setTotalStudents(students.size());
        result.setAssignments(new ArrayList<>());

        int successCount = 0;
        int failCount = 0;

        // 根据策略排序销售人员
        List<SaleProfile> sortedSales = sortSalesByStrategy(availableSales, req.getStrategy());

        for (UserStudentExt student : students) {
            try {
                // 选择最合适的销售
                SaleProfile selectedSales = selectBestSalesForStudent(student, sortedSales, req);

                if (selectedSales != null) {
                    // 执行分配
                    assignStudentToSalesInternal(student.getStudentId(), selectedSales.getSalesId());

                    // 更新销售的学生数量（用于后续分配的负载均衡）
                    selectedSales.setCustomerCount(selectedSales.getCustomerCount() + 1);

                    // 记录成功分配
                    SalesStudentDto.IntelligentAssignResp.AssignmentResult assignment =
                            new SalesStudentDto.IntelligentAssignResp.AssignmentResult();
                    assignment.setStudentId(student.getStudentId());
                    assignment.setStudentName(student.getName());
                    assignment.setSalesId(selectedSales.getSalesId());
                    assignment.setSalesName(selectedSales.getSalesName());
                    assignment.setSalesGroupName(selectedSales.getSalesGroupName());
                    assignment.setReason(req.getReason() + " - " + req.getStrategy());
                    assignment.setSuccess(true);

                    result.getAssignments().add(assignment);
                    successCount++;
                } else {
                    // 没有合适的销售
                    SalesStudentDto.IntelligentAssignResp.AssignmentResult assignment =
                            new SalesStudentDto.IntelligentAssignResp.AssignmentResult();
                    assignment.setStudentId(student.getStudentId());
                    assignment.setStudentName(student.getName());
                    assignment.setSuccess(false);
                    assignment.setErrorMessage("没有合适的销售人员");

                    result.getAssignments().add(assignment);
                    failCount++;
                }
            } catch (Exception e) {
                log.error("分配学生失败: studentId={}", student.getStudentId(), e);

                SalesStudentDto.IntelligentAssignResp.AssignmentResult assignment =
                        new SalesStudentDto.IntelligentAssignResp.AssignmentResult();
                assignment.setStudentId(student.getStudentId());
                assignment.setStudentName(student.getName());
                assignment.setSuccess(false);
                assignment.setErrorMessage(e.getMessage());

                result.getAssignments().add(assignment);
                failCount++;
            }
        }

        result.setSuccessCount(successCount);
        result.setFailCount(failCount);

        return result;
    }

    /**
     * 根据策略排序销售人员
     */
    private List<SaleProfile> sortSalesByStrategy(List<SaleProfile> sales, String strategy) {
        switch (strategy) {
            case "load_balance":
                // 按当前学生数量升序排序（负载均衡）
                return sales.stream()
                        .sorted(Comparator.comparingInt(SaleProfile::getCustomerCount))
                        .collect(Collectors.toList());
            case "random":
                // 随机排序
                List<SaleProfile> randomList = new ArrayList<>(sales);
                Collections.shuffle(randomList);
                return randomList;
            case "round_robin":
                // 轮询排序（这里简化为按ID排序）
                return sales.stream()
                        .sorted(Comparator.comparing(SaleProfile::getSalesId))
                        .collect(Collectors.toList());
            default:
                return sales;
        }
    }

    /**
     * 为学生选择最合适的销售
     */
    private SaleProfile selectBestSalesForStudent(UserStudentExt student, List<SaleProfile> sortedSales,
                                                  SalesStudentDto.IntelligentAssignReq req) {
        for (SaleProfile sales : sortedSales) {
            // 检查销售是否已达到最大学生数限制
            if (sales.getCustomerCount() >= req.getMaxStudentsPerSales()) {
                continue;
            }

            // 这里可以添加更多的匹配逻辑，比如地区匹配、专业匹配等
            // 目前简单返回第一个符合条件的销售
            return sales;
        }

        return null; // 没有合适的销售
    }

}
