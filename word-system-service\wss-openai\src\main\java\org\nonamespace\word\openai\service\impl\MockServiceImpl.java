package org.nonamespace.word.openai.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.openai.model.WordInfo;
import org.nonamespace.word.openai.service.IMockService;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Slf4j
@Service
@Profile({"dev"})
public class MockServiceImpl implements IMockService {

    @Override
    public List<WordInfo> enrichMock(List<WordInfo> words) {
        log.info("--------------enrichMock----------: {}", words);
        WordInfo wordInfo = new WordInfo();
        wordInfo.setWord("mock");
        wordInfo.setMeanings(Map.of("特色", new WordInfo.Meanings(List.of(new WordInfo.PosDefinition("n", "测试")), List.of("选项1", "选项2"))));
        wordInfo.setSentences(Map.of("特色", List.of(new WordInfo.Sentence("mocksyllables", "mocksentenceEn", "mocksentenceCn", "小学", List.of("mock"), List.of("mock"), "mock", "mock"))));
        return List.of(wordInfo);
    }

}
