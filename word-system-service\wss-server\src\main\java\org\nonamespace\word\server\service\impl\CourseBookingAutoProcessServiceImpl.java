package org.nonamespace.word.server.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.misc.WssContext;
import org.nonamespace.word.server.domain.*;
import org.nonamespace.word.server.dto.curriculum.CurriculumCreateScheduleDto;
import org.nonamespace.word.server.facade.CurriculumFacade;
import org.nonamespace.word.server.service.*;
import org.nonamespace.word.server.util.SystemDataQueryUtil;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 预约课自动化处理Service实现
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CourseBookingAutoProcessServiceImpl implements ICourseBookingAutoProcessService {

    private final ITeacherStudentRelationService teacherStudentRelationService;
    private final IStudentCourseHoursService studentCourseHoursService;
    private final ICourseBookingApplicationService courseBookingApplicationService;
    private final IWechatNotificationService wechatNotificationService;
    private final UserStudentExtService userStudentExtService;
    private final SystemDataQueryUtil systemDataQueryUtil;
    private final CurriculumFacade curriculumFacade;
    private final ITeachingGroupMemberService teachingGroupMemberService;

    @Override
    public boolean autoAssignTeacherToStudent(String studentId, String teacherId, String subject, String specification) {
            log.info("自动建立师生关系: studentId={}, teacherId={}, subject={}, specification={}",
                    studentId, teacherId, subject, specification);

            // 检查是否已存在相同的师生关系
            boolean exists = teacherStudentRelationService.lambdaQuery()
                    .eq(TeacherStudentRelation::getTeacherId, teacherId)
                    .eq(TeacherStudentRelation::getStudentId, studentId)
                    .eq(TeacherStudentRelation::getSubject, subject)
                    .eq(TeacherStudentRelation::getSpecification, specification)
                    .eq(TeacherStudentRelation::getRelationType, "teaching")
                    .eq(TeacherStudentRelation::getStatus, "active")
                    .eq(TeacherStudentRelation::getDeleted, false)
                    .exists();

            if (exists) {
                log.info("师生关系已存在，无需重复创建: studentId={}, teacherId={}", studentId, teacherId);
                return true;
            }

            // 创建新的师生关系
            TeacherStudentRelation relation = new TeacherStudentRelation();
            relation.setId(IdUtil.getSnowflakeNextIdStr());
            relation.setTeacherId(teacherId);
            relation.setStudentId(studentId);
            relation.setRelationType("teaching");
            relation.setSubject(subject);
            relation.setSpecification(specification);
            relation.setStatus("active");
            relation.setStartDate(WssContext.now());
            relation.setCreateTime(WssContext.now());
            relation.setUpdateTime(WssContext.now());
            relation.setDeleted(false);

            boolean success = teacherStudentRelationService.save(relation);

            if (success) {
                log.info("师生关系建立成功: relationId={}", relation.getId());
            } else {
                log.error("师生关系建立失败: studentId={}, teacherId={}", studentId, teacherId);
            }

            return success;
    }

    @Override
    public boolean autoAssignCoursePackage(String studentId, String subject, String specification, String nature) {
        log.info("自动分配课时包: studentId={}, subject={}, specification={}, nature={}",
                studentId, subject, specification, nature);

        // 检查是否已存在相同的课时包
        boolean exists = studentCourseHoursService.lambdaQuery()
                .eq(StudentCourseHours::getStudentId, studentId)
                .eq(StudentCourseHours::getSubject, subject)
                .eq(StudentCourseHours::getSpecification, specification)
                .eq(StudentCourseHours::getNature, nature)
                .eq(StudentCourseHours::getDeleted, false)
                .exists();

        if (exists) {
            log.info("课时包已存在，无需重复创建: studentId={}, subject={}, specification={}, nature={}",
                    studentId, subject, specification, nature);
            return true;
        }

        // 创建新的课时包记录
        StudentCourseHours courseHours = new StudentCourseHours();
        courseHours.setId(IdUtil.getSnowflakeNextIdStr());
        courseHours.setStudentId(studentId);
        courseHours.setSubject(subject);
        courseHours.setSpecification(specification);
        courseHours.setNature(nature);

        // 试听课默认分配1课时
        BigDecimal hours = BigDecimal.ONE;
        courseHours.setPurchasedHours(hours);
        courseHours.setGiftHours(BigDecimal.ZERO);
        courseHours.setTotalHours(hours);
        courseHours.setRemainingHours(hours);
        courseHours.setConsumedTotalHours(BigDecimal.ZERO);
        courseHours.setConsumedPurchasedHours(BigDecimal.ZERO);
        courseHours.setConsumedGiftHours(BigDecimal.ZERO);

        // 设置单价（试听课通常免费或优惠价格）
        courseHours.setUnitPrice(BigDecimal.ZERO);

        // 生成批次号
        courseHours.setBatchNo("AUTO_" + System.currentTimeMillis());

        // 设置基础字段
        courseHours.setCreateTime(WssContext.now());
        courseHours.setUpdateTime(WssContext.now());
        courseHours.setDeleted(false);

        boolean success = studentCourseHoursService.save(courseHours);

        if (success) {
            log.info("课时包分配成功: courseHoursId={}, hours={}", courseHours.getId(), hours);
        } else {
            log.error("课时包分配失败: studentId={}, subject={}, specification={}, nature={}",
                    studentId, subject, specification, nature);
        }

        return success;
    }

    @Override
    public ValidationResult validateTeacherAndStudentStatus(String teacherId, String studentId) {
            log.info("验证教师和学生状态: teacherId={}, studentId={}", teacherId, studentId);

            // TODO: 这里可以添加更多的验证逻辑
            // 1. 验证教师是否存在且状态正常
            // 2. 验证学生是否存在且状态正常
            // 3. 验证教师是否有足够的时间安排
            // 4. 验证学生是否有冲突的课程安排

            // 简化实现，直接返回成功
            log.info("教师和学生状态验证通过");
            return ValidationResult.success();
    }

    @Override
    public boolean processTeacherStudentAssignment(String studentId, String teacherId, String subject, String specification, String nature) {
        log.info("处理师生关系和课时包分配: studentId={}, teacherId={}, subject={}, specification={}, nature={}",
                studentId, teacherId, subject, specification, nature);

        // 1. 验证教师和学生状态
        ValidationResult validation = validateTeacherAndStudentStatus(teacherId, studentId);
        if (!validation.isValid()) {
            log.error("教师和学生状态验证失败: {}", validation.getMessage());
            return false;
        }

        // 2. 自动建立师生关系
        boolean relationSuccess = autoAssignTeacherToStudent(studentId, teacherId, subject, specification);
        if (!relationSuccess) {
            log.error("建立师生关系失败: studentId={}, teacherId={}", studentId, teacherId);
            return false;
        }

        // 3. 自动分配课时包
        boolean packageSuccess = autoAssignCoursePackage(studentId, subject, specification, nature);
        if (!packageSuccess) {
            log.error("分配课时包失败: studentId={}, subject={}, specification={}, nature={}",
                    studentId, subject, specification, nature);
            return false;
        }

        log.info("师生关系和课时包分配完成: studentId={}, teacherId={}", studentId, teacherId);
        return true;


    }

    @Override
    public boolean processConfirmation(CourseBookingApplication application, String confirmTeacherId, CourseBookingApplication.PreferredTimeSlot timeSlot) {
        log.info("开始处理确认申请: applicationId={}, confirmTeacherId={}, time={}",
                application.getId(), confirmTeacherId, timeSlot);

        String applicationId = application.getId();
        String operatorId = systemDataQueryUtil.getCurrentUserId();

        // 1. 验证状态
        if (!application.getStatus().equals(CourseBookingApplication.Status.PENDING.getCode()) || application.getDeleted()) {
            throw new RuntimeException("当前申请状态不正确，无法确认");
        }

        // 2. 验证教师和学生状态
        ValidationResult validation = validateTeacherAndStudentStatus(confirmTeacherId, application.getStudentId());
        if (!validation.isValid()) {
            log.error("教师和学生状态验证失败: {}", validation.getMessage());
            throw new RuntimeException("教师或学生状态不符合要求，无法确认申请");
        }

        // 3. 自动建立师生关系
        boolean relationSuccess = autoAssignTeacherToStudent(
                application.getStudentId(),
                confirmTeacherId,
                application.getSubject(),
                application.getSpecification()
        );

        if (!relationSuccess) {
            log.error("建立师生关系失败: applicationId={}", applicationId);
            throw new RuntimeException("建立师生关系失败");
        }

        // 4. 自动分配课时包
        String courseHoursPackageId = autoAssignCoursePackageWithId(
                application.getStudentId(),
                application.getSubject(),
                application.getSpecification(),
                application.getNature()
        );

        // 5. 创建试听课
        if(timeSlot != null){
            String courseId = createTrialCourse(application, confirmTeacherId, timeSlot);
            application.setTrialCourseId(courseId);
            application.setApprovedTimeSlot(JSONUtil.toJsonStr(timeSlot));
        }


        // 6. 更新申请状态
        application.setStatus(CourseBookingApplication.Status.APPROVED.getCode());
        application.setApprovalBy(operatorId);
        application.setApprovalTime(WssContext.now());
        application.setApprovedTeacherId(confirmTeacherId);
        application.setCourseHoursPackageId(courseHoursPackageId);
        application.setApprovedTeachingGroupId(systemDataQueryUtil.getUser(confirmTeacherId).getOrgId());

        application.setUpdateTime(WssContext.now());
        userStudentExtService.lambdaUpdate()
                .set(UserStudentExt::getRemarks, StrUtil.format("\n {} 【首课申请】\n  {} \n----------------\n", DateUtil.formatChineseDate(application.getCreateTime(), false, false), application.getApplicationReason()))
                .eq(UserStudentExt::getStudentId, application.getStudentId())
                .update();

        boolean updateSuccess = courseBookingApplicationService.updateById(application);
        if (!updateSuccess) {
            log.error("更新申请状态失败: applicationId={}", applicationId);
            return false;
        }

        // 6. 发送微信通知
        try {
            // 通知老师
            wechatNotificationService.sendCourseBookingNotification(applicationId, "APPROVED_TO_TEACHER");
            // 通知销售
            wechatNotificationService.sendCourseBookingNotification(applicationId, "APPROVED_TO_SALES");
            // 通知家长
//            wechatNotificationService.sendCourseBookingNotification(applicationId, "APPROVED_TO_PARENT");
        } catch (Exception e) {
            log.warn("发送微信通知失败，但不影响主流程", e);
        }

        log.info("确认申请处理完成: applicationId={}, confirmTeacherId={}", applicationId, confirmTeacherId);
        return true;
    }

    /**
     * 自动分配课时包并返回课时包ID
     */
    private String autoAssignCoursePackageWithId(String studentId, String subject, String specification, String nature) {
        log.info("自动分配课时包: studentId={}, subject={}, specification={}, nature={}",
                studentId, subject, specification, nature);

        // 检查是否已存在相同的课时包
        boolean existingPackage = studentCourseHoursService.lambdaQuery()
                .eq(StudentCourseHours::getStudentId, studentId)
                .eq(StudentCourseHours::getSubject, subject)
                .eq(StudentCourseHours::getSpecification, specification)
                .eq(StudentCourseHours::getNature, nature)
                .eq(StudentCourseHours::getDeleted, false)
                .exists();

        if (existingPackage) {
            log.info("课时包已存在=，无需重复创建: studentId={}, subject={}, specification={}, nature={}",
                    studentId, subject, specification, nature);
            return null;
        }

        // 创建新的课时包记录
        StudentCourseHours courseHours = new StudentCourseHours();
        courseHours.setId(IdUtil.getSnowflakeNextIdStr());
        courseHours.setStudentId(studentId);
        courseHours.setSubject(subject);
        courseHours.setSpecification(specification);
        courseHours.setNature(nature);

        // 试听课默认分配1课时
        BigDecimal hours = BigDecimal.ONE;
        courseHours.setPurchasedHours(hours);
        courseHours.setGiftHours(BigDecimal.ZERO);
        courseHours.setTotalHours(hours);
        courseHours.setRemainingHours(hours);
        courseHours.setConsumedTotalHours(BigDecimal.ZERO);
        courseHours.setConsumedPurchasedHours(BigDecimal.ZERO);
        courseHours.setConsumedGiftHours(BigDecimal.ZERO);

        // 设置单价（试听课通常免费或优惠价格）
        courseHours.setUnitPrice(BigDecimal.ZERO);

        // 生成批次号
        courseHours.setBatchNo("AUTO_" + System.currentTimeMillis());

        // 设置基础字段
        courseHours.setCreateTime(WssContext.now());
        courseHours.setUpdateTime(WssContext.now());
        courseHours.setDeleted(false);

        boolean success = studentCourseHoursService.save(courseHours);

        if (success) {
            log.info("课时包分配成功: courseHoursId={}, hours={}", courseHours.getId(), hours);
            return courseHours.getId();
        } else {
            log.error("课时包分配失败: studentId={}, subject={}, specification={}, nature={}",
                    studentId, subject, specification, nature);
            throw new RuntimeException("课时包分配失败");
        }
    }


    /**
     * 创建试听课
     */
    private String createTrialCourse(CourseBookingApplication application, String teacherId, CourseBookingApplication.PreferredTimeSlot timeSlot) {
        log.info("开始创建试听课: applicationId={}, teacherId={}, timeSlot={}",
                application.getId(), teacherId, timeSlot);
        // 计算试听课日期范围（只创建一次试听课）
        LocalDate nextCourseDate = calculateNextCourseDate(timeSlot.getWeekday());

        String startDateStr = nextCourseDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        // 构建课程创建请求
        CurriculumCreateScheduleDto.Req createReq = new CurriculumCreateScheduleDto.Req();
        createReq.setStudentId(application.getStudentId());
        createReq.setTeacherId(teacherId);
        createReq.setSubject(application.getSubject());
        createReq.setSpecification(application.getSpecification());
        createReq.setCourseType("试听课"); // 试听课类型
        createReq.setType("学习课");
        createReq.setTotalLessons(1); // 只创建一节试听课
        createReq.setUseSystem(true);
//            createReq.setStudentName(student.getName());
//            createReq.setTeacherName(teacher.getRealName());

        // 设置日期范围（只包含一天）
        createReq.setDateRange(new String[]{startDateStr, startDateStr});

        // 设置周课表
        CurriculumCreateScheduleDto.Req.WeeklySchedule weeklySchedule = new CurriculumCreateScheduleDto.Req.WeeklySchedule();
        weeklySchedule.setDayOfWeek(timeSlot.getWeekday());
        weeklySchedule.setStartTime(timeSlot.getStartTime());
        weeklySchedule.setEndTime(timeSlot.getEndTime());

        // 计算课程时长（分钟）
        String[] startParts = timeSlot.getStartTime().split(":");
        String[] endParts = timeSlot.getEndTime().split(":");
        int startMinutes = Integer.parseInt(startParts[0]) * 60 + Integer.parseInt(startParts[1]);
        int endMinutes = Integer.parseInt(endParts[0]) * 60 + Integer.parseInt(endParts[1]);
        weeklySchedule.setDuration(endMinutes - startMinutes);

        createReq.setWeeklySchedules(List.of(weeklySchedule));

        // 调用课程创建接口
        List<String> courseIds = curriculumFacade.createSchedule(createReq);

        log.info("试听课创建成功: applicationId={},courseId={}, courseDate={}, startTime={}, endTime={}",
                application.getId(), courseIds.getFirst(), startDateStr, timeSlot.getStartTime(), timeSlot.getEndTime());

        return courseIds.getFirst();
    }

    /**
     * 计算下一个符合条件的课程日期
     */
    private LocalDate calculateNextCourseDate(int weekday) {
        LocalDate today = LocalDate.now();
        while(today.getDayOfWeek().getValue() != weekday){
            today = today.plusDays(1);
        }
        return today;
    }
}
