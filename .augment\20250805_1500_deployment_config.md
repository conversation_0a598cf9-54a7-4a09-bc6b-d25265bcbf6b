# 产品下单功能部署配置说明

## 概述

本文档说明了产品下单功能的部署配置要求和步骤。

## 后端配置

### 1. 数据库配置

#### 1.1 创建产品表
执行以下SQL脚本：
```sql
-- 参考 .augment/20250805_1500_product_management_sql.sql
```

#### 1.2 权限配置
确保以下权限配置存在：
```sql
-- 产品管理权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('产品管理', 2000, 1, 'product', 'management/product/index', 1, 0, 'C', '0', '0', 'management:products:list', 'shopping', 'admin', NOW(), '', NULL, '产品管理菜单');

-- 产品管理按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('产品查询', (SELECT menu_id FROM sys_menu WHERE perms = 'management:products:list'), 1, '', '', 1, 0, 'F', '0', '0', 'management:products:query', '', 'admin', NOW(), '', NULL, ''),
('产品新增', (SELECT menu_id FROM sys_menu WHERE perms = 'management:products:list'), 2, '', '', 1, 0, 'F', '0', '0', 'management:products:add', '', 'admin', NOW(), '', NULL, ''),
('产品修改', (SELECT menu_id FROM sys_menu WHERE perms = 'management:products:list'), 3, '', '', 1, 0, 'F', '0', '0', 'management:products:edit', '', 'admin', NOW(), '', NULL, ''),
('产品删除', (SELECT menu_id FROM sys_menu WHERE perms = 'management:products:list'), 4, '', '', 1, 0, 'F', '0', '0', 'management:products:remove', '', 'admin', NOW(), '', NULL, '');

-- 订单管理权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('订单下单', 2000, 2, 'order', 'management/order/index', 1, 0, 'C', '0', '0', 'order:create', 'form', 'admin', NOW(), '', NULL, '订单下单菜单'),
('订单管理', 2000, 3, 'order-management', 'management/order-management/index', 1, 0, 'C', '0', '0', 'order:query', 'list', 'admin', NOW(), '', NULL, '订单管理菜单');

-- 订单相关按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('订单支付', (SELECT menu_id FROM sys_menu WHERE perms = 'order:query'), 1, '', '', 1, 0, 'F', '0', '0', 'order:pay', '', 'admin', NOW(), '', NULL, ''),
('订单取消', (SELECT menu_id FROM sys_menu WHERE perms = 'order:query'), 2, '', '', 1, 0, 'F', '0', '0', 'order:cancel', '', 'admin', NOW(), '', NULL, '');
```

### 2. 应用配置

#### 2.1 依赖检查
确保以下依赖已正确配置：
- MyBatis-Plus
- 通联支付SDK
- 二维码生成库
- JSON处理库（Hutool）

#### 2.2 配置文件
检查 `application.yml` 中的配置：
```yaml
# 数据库配置
spring:
  datasource:
    # 确保数据库连接正常
    
# 支付配置
allinpay:
  # 确保通联支付配置正确
  
# 文件上传配置（如果需要上传产品图片）
file:
  upload:
    path: /path/to/upload
```

### 3. 代码部署

#### 3.1 新增文件列表
确保以下文件已正确部署：

**实体类**：
- `word-system-service/wss-server/src/main/java/org/nonamespace/word/server/domain/Product.java`

**DTO类**：
- `word-system-service/wss-server/src/main/java/org/nonamespace/word/server/dto/product/ProductDto.java`
- `word-system-service/wss-server/src/main/java/org/nonamespace/word/server/dto/order/PaymentDto.java`

**Mapper接口**：
- `word-system-service/wss-server/src/main/java/org/nonamespace/word/server/mapper/ProductMapper.java`
- `word-system-service/wss-server/src/main/resources/mapper/ProductMapper.xml`

**Service接口和实现**：
- `word-system-service/wss-server/src/main/java/org/nonamespace/word/server/service/IProductService.java`
- `word-system-service/wss-server/src/main/java/org/nonamespace/word/server/service/impl/ProductServiceImpl.java`

**控制器**：
- `word-system-service/wss-rest/src/main/java/org/nonamespace/word/rest/controller/ProductController.java`

#### 3.2 修改文件列表
确保以下文件的修改已正确部署：

**订单服务**：
- `word-system-service/wss-server/src/main/java/org/nonamespace/word/server/service/order/IOrdersService.java`
- `word-system-service/wss-server/src/main/java/org/nonamespace/word/server/service/order/impl/OrdersServiceImpl.java`

**订单控制器**：
- `word-system-service/wss-rest/src/main/java/org/nonamespace/word/rest/controller/order/OrderController.java`

## 前端配置

### 1. 新增文件列表
确保以下文件已正确部署：

**API接口**：
- `words-frontend/src/api/management/product.js`
- `words-frontend/src/api/management/order.js`

**页面组件**：
- `words-frontend/src/views/management/product/index.vue`
- `words-frontend/src/views/management/order/index.vue`
- `words-frontend/src/views/management/order-management/index.vue`

### 2. 路由配置
在 `words-frontend/src/router/index.js` 中添加路由：
```javascript
{
  path: '/management/product',
  component: () => import('@/views/management/product/index'),
  name: 'ProductManagement',
  meta: { title: '产品管理', icon: 'shopping' }
},
{
  path: '/management/order',
  component: () => import('@/views/management/order/index'),
  name: 'OrderCreate',
  meta: { title: '订单下单', icon: 'form' }
},
{
  path: '/management/order-management',
  component: () => import('@/views/management/order-management/index'),
  name: 'OrderManagement',
  meta: { title: '订单管理', icon: 'list' }
}
```

### 3. 菜单配置
在系统菜单中添加相应的菜单项，或通过数据库直接插入菜单数据。

## 部署步骤

### 1. 后端部署
```bash
# 1. 停止现有服务
# 2. 备份数据库
# 3. 执行数据库脚本
mysql -u username -p database_name < .augment/20250805_1500_product_management_sql.sql

# 4. 部署新代码
# 5. 重启服务
```

### 2. 前端部署
```bash
# 1. 构建前端项目
cd words-frontend
npm run build

# 2. 部署到Web服务器
# 3. 更新Nginx配置（如果需要）
```

## 验证部署

### 1. 后端验证
```bash
# 检查产品管理接口
curl -X GET "http://localhost:8080/management/products/available"

# 检查订单创建接口
curl -X POST "http://localhost:8080/order/create" \
  -H "Content-Type: application/json" \
  -d '{"studentId":"test","productId":"1"}'
```

### 2. 前端验证
- 访问产品管理页面：`http://localhost:3000/management/product`
- 访问订单下单页面：`http://localhost:3000/management/order`
- 访问订单管理页面：`http://localhost:3000/management/order-management`

### 3. 功能验证
按照测试指南进行完整的功能测试。

## 回滚方案

如果部署出现问题，可以按以下步骤回滚：

### 1. 后端回滚
```bash
# 1. 停止服务
# 2. 恢复代码到上一个版本
# 3. 恢复数据库（如果需要）
# 4. 重启服务
```

### 2. 前端回滚
```bash
# 1. 恢复前端代码到上一个版本
# 2. 重新构建和部署
```

## 监控和日志

### 1. 关键日志监控
- 产品创建/更新日志
- 订单创建日志
- 支付相关日志
- 错误异常日志

### 2. 性能监控
- API响应时间
- 数据库查询性能
- 前端页面加载时间

### 3. 业务监控
- 订单创建成功率
- 支付成功率
- 用户操作流程完成率

## 注意事项

1. **数据备份**：部署前务必备份数据库
2. **权限验证**：确保所有新增接口都有适当的权限控制
3. **兼容性**：确保新功能不影响现有功能
4. **性能影响**：监控新功能对系统性能的影响
5. **用户培训**：为使用新功能的用户提供培训
