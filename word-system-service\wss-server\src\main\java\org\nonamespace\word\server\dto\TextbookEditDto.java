package org.nonamespace.word.server.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.nonamespace.word.server.enums.TextBookTypeEnum;

/**
 * 词定义 (统一教材与词): 定义各种词对象 textbook
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@Accessors(chain = true)
public class TextbookEditDto {

    /** 词表ID */
    @NotBlank(message = "词表ID不能为空")
    private String id;

    /** 词表名称 (例如: "高频315词表", "牛津版三年级上册U1") */
    @NotBlank(message = "词表名称不能为空")
    private String name;

    /** 描述 */
    private String description;

    /**
     * 封面
     */
    private String cover;

    /** 类型 (学校教材,特色词表,学生词表) */
    @NotEmpty(message = "词表类型不能为空")
    private TextBookTypeEnum type;

    /** 词表结构 */
    private String wordList;

    /** 标签，如["类型:学校教材", "年级:一年级", "版本:牛津版"] */
    private String[] tags;
}
