{"groups": [{"name": "wechat.course-booking.templates", "type": "org.nonamespace.word.wechat.config.CourseBookingTemplateConfig", "sourceType": "org.nonamespace.word.wechat.config.CourseBookingTemplateConfig"}, {"name": "wechat.course-booking.templates.urls", "type": "org.nonamespace.word.wechat.config.CourseBookingTemplateConfig$UrlConfig", "sourceType": "org.nonamespace.word.wechat.config.CourseBookingTemplateConfig", "sourceMethod": "getUrls()"}], "properties": [{"name": "wechat.course-booking.templates.template-ids", "type": "java.util.Map<java.lang.String,java.lang.String>", "description": "模板ID映射 key: 通知类型 value: 模板ID", "sourceType": "org.nonamespace.word.wechat.config.CourseBookingTemplateConfig"}, {"name": "wechat.course-booking.templates.urls.application-detail-path", "type": "java.lang.String", "description": "申请详情页面路径", "sourceType": "org.nonamespace.word.wechat.config.CourseBookingTemplateConfig$UrlConfig"}, {"name": "wechat.course-booking.templates.urls.base-url", "type": "java.lang.String", "description": "前端基础URL", "sourceType": "org.nonamespace.word.wechat.config.CourseBookingTemplateConfig$UrlConfig"}, {"name": "wechat.course-booking.templates.urls.parent-path", "type": "java.lang.String", "description": "家长端路径", "sourceType": "org.nonamespace.word.wechat.config.CourseBookingTemplateConfig$UrlConfig"}, {"name": "wechat.course-booking.templates.urls.sales-workspace-path", "type": "java.lang.String", "description": "销售工作台路径", "sourceType": "org.nonamespace.word.wechat.config.CourseBookingTemplateConfig$UrlConfig"}, {"name": "wechat.course-booking.templates.urls.teacher-workspace-path", "type": "java.lang.String", "description": "教师工作台路径", "sourceType": "org.nonamespace.word.wechat.config.CourseBookingTemplateConfig$UrlConfig"}], "hints": []}