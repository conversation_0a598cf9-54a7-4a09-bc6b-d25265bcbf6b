package org.nonamespace.word.server.facade.impl;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysRoleService;
import com.ruoyi.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.nonamespace.word.server.dto.management.teacher.group.ManagementGetTeacherGroupDto;
import org.nonamespace.word.server.facade.TeacherManagementFacade;
import org.nonamespace.word.server.service.IDeptService;
import org.nonamespace.word.server.service.IRoleService;
import org.nonamespace.word.server.service.IUserService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class TeacherManagementFacadeImpl implements TeacherManagementFacade {

    private final ISysUserService sysUserService;
    private final ISysDeptService sysDeptService;
    private final ISysRoleService sysRoleService;

    private final IUserService userService;
    private final IDeptService deptService;
    private final IRoleService roleService;

    @Override
    public List<ManagementGetTeacherGroupDto.TeachingGroup> fetchGroups(ManagementGetTeacherGroupDto.Req req) {
        SysDept tcd = deptService.lambdaQuery().eq(SysDept::getDeptName, "教学中心").one();
        List<SysDept> depts = deptService.lambdaQuery().likeRight(SysDept::getAncestors, tcd.getAncestors() + ",")
                .eq(SysDept::getStatus, "0")
                .list();

        Map<String, SysRole> roleMap = sysRoleService.selectRoleAll().stream().collect(Collectors.toMap(SysRole::getRoleName, x -> x));
        SysRole leaderRole = roleMap.get("教学组长");
        SysRole adminRole = roleMap.get("教务");

        if(leaderRole == null) {
            throw new IllegalStateException("请先创建教学组长角色");
        }

        if(adminRole == null) {
            throw new IllegalStateException("请先创建教务角色");
        }

        for (SysDept d : depts) {
            ManagementGetTeacherGroupDto.TeachingGroup tg = new ManagementGetTeacherGroupDto.TeachingGroup();
            tg.setId(String.valueOf(d.getDeptId()));
            tg.setName(d.getDeptName());

        }

        return new ArrayList<>();
    }
}
