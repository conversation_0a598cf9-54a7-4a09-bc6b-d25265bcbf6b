package org.nonamespace.word.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Update;
import org.nonamespace.word.server.domain.TextbookItem;
import org.nonamespace.word.server.dto.TextbookItemTreeoDto;

import java.util.List;

/**
 * 词词汇关联: 关联词和具体单词，定义词汇级别等Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface TextbookItemMapper extends BaseMapper<TextbookItem> {

    List<TextbookItemTreeoDto> getTreeItemList(String textbookId,String studentId);

    List<TextbookItemTreeoDto> getTreeItemListByUnitId(String unitId,String studentId);

    @Update("""
            update textbook_item ti
            set display_order = t.row_number
            from (select row_number() over (order by display_order) as row_number, id
                  from textbook_item
                  where textbook_id = #{textbookId}
                    and deleted = false
                    and node_type = '3'
                  order by display_order) t
            where ti.id = t.id
              and ti.display_order <> t.row_number
    """)
    void updateDisplayOrder(String textbookId);
}
