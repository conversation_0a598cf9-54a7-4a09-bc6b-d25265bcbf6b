package org.nonamespace.word.server.dto.course;

import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * 课堂单词测验
 */
@Data
public class CourseWordTestDto {
   private String textbookId;
   private Integer wordNum;

   /**
    * 测试模式：random(随机) 或 range(自定范围)
    */
   private String testMode = "random";

   /**
    * 范围开始位置（当testMode为range时使用）
    */
   private Integer startIndex;

   /**
    * 范围结束位置（当testMode为range时使用）
    */
   private Integer endIndex;
}