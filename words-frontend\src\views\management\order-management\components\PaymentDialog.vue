<template>
  <el-dialog
    title="支付确认"
    v-model="visible"
    width="600px"
    append-to-body
    :before-close="handleClose"
    class="payment-dialog"
  >
    <div v-if="transaction" class="payment-content">
      <!-- 交易信息 -->
      <el-card class="transaction-info" shadow="never">
        <template #header>
          <span class="card-title">交易信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="流水号">
            <el-tag type="info" size="small">{{ transaction.cusTrxSeq }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="期数">
            <el-tag type="primary" size="small">第{{ transaction.trxIdx }}期</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="支付金额">
            <span class="amount-text">¥{{ (transaction.trxAmt / 100).toFixed(2) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="交易状态">
            <el-tag :type="getStatusType(transaction.trxStatus)" size="small">
              {{ transaction.trxStatus }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">
            {{ formatDateTime(transaction.createTime) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 支付方式选择 -->
      <el-card class="payment-methods" shadow="never">
        <template #header>
          <span class="card-title">选择支付方式</span>
        </template>
        <el-radio-group v-model="selectedPayMethod" class="payment-method-group">
          <el-radio value="微信支付" class="payment-method-item">
            <div class="method-content">
              <el-icon class="method-icon wechat"><ChatDotRound /></el-icon>
              <span class="method-name">微信支付</span>
            </div>
          </el-radio>
          <el-radio value="支付宝" class="payment-method-item">
            <div class="method-content">
              <el-icon class="method-icon alipay"><Wallet /></el-icon>
              <span class="method-name">支付宝</span>
            </div>
          </el-radio>
          <el-radio value="银行卡" class="payment-method-item">
            <div class="method-content">
              <el-icon class="method-icon bank"><CreditCard /></el-icon>
              <span class="method-name">银行卡</span>
            </div>
          </el-radio>
        </el-radio-group>
      </el-card>

      <!-- 支付二维码 -->
      <el-card v-if="qrCodeUrl" class="qr-code-section" shadow="never">
        <template #header>
          <div class="card-header">
            <span class="card-title">扫码支付</span>
            <el-button type="text" size="small" @click="refreshQRCode">
              <el-icon><Refresh /></el-icon>
              刷新二维码
            </el-button>
          </div>
        </template>
        <div class="qr-code-container">
          <div class="qr-code">
            <img :src="qrCodeUrl" alt="支付二维码" />
          </div>
          <div class="qr-code-tips">
            <p>请使用{{ selectedPayMethod }}扫描二维码完成支付</p>
            <p class="amount-tip">支付金额：<span class="amount">¥{{ (transaction.trxAmt / 100).toFixed(2) }}</span></p>
          </div>
        </div>
      </el-card>

      <!-- 支付链接 -->
      <el-card v-if="paymentLink" class="payment-link-section" shadow="never">
        <template #header>
          <span class="card-title">支付链接</span>
        </template>
        <div class="link-container">
          <el-input
            v-model="paymentLink"
            readonly
            class="payment-link-input"
          >
            <template #append>
              <el-button @click="copyPaymentLink">
                <el-icon><DocumentCopy /></el-icon>
                复制
              </el-button>
            </template>
          </el-input>
          <div class="link-actions">
            <el-button type="primary" @click="openPaymentLink">
              <el-icon><Link /></el-icon>
              打开支付页面
            </el-button>
            <el-button @click="sendWechatMessage">
              <el-icon><ChatDotRound /></el-icon>
              发送微信
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="generatePayment" :loading="loading">
          {{ qrCodeUrl || paymentLink ? '重新生成' : '生成支付' }}
        </el-button>
        <el-button v-if="qrCodeUrl || paymentLink" type="success" @click="confirmPayment">
          确认支付完成
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ChatDotRound,
  Wallet,
  CreditCard,
  Refresh,
  DocumentCopy,
  Link
} from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/date.js'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  transaction: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success', 'generate-qr', 'copy-link', 'send-wechat'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const selectedPayMethod = ref('微信支付')
const qrCodeUrl = ref('')
const paymentLink = ref('')
const loading = ref(false)

// 监听交易变化，重置状态
watch(() => props.transaction, () => {
  if (props.transaction) {
    selectedPayMethod.value = '微信支付'
    qrCodeUrl.value = ''
    paymentLink.value = ''
  }
})

// 方法
const handleClose = () => {
  visible.value = false
}

const getStatusType = (status) => {
  const statusMap = {
    '未支付': 'warning',
    '已付款': 'success',
    '已取消': 'danger',
    '已退款': 'info',
    '部分支付': 'primary'
  }
  return statusMap[status] || 'info'
}

const generatePayment = async () => {
  if (!props.transaction) return
  
  loading.value = true
  try {
    // 模拟生成支付二维码和链接
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 生成模拟的二维码URL
    qrCodeUrl.value = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=pay_${props.transaction.cusTrxSeq}_${Date.now()}`
    
    // 生成模拟的支付链接
    paymentLink.value = `https://pay.example.com/transaction/${props.transaction.cusTrxSeq}?method=${selectedPayMethod.value}&amount=${props.transaction.trxAmt}`
    
    emit('generate-qr', {
      transaction: props.transaction,
      payMethod: selectedPayMethod.value,
      qrCodeUrl: qrCodeUrl.value,
      paymentLink: paymentLink.value
    })
    
    ElMessage.success('支付信息生成成功')
  } catch (error) {
    ElMessage.error('生成支付信息失败')
    console.error('Generate payment error:', error)
  } finally {
    loading.value = false
  }
}

const refreshQRCode = () => {
  generatePayment()
}

const copyPaymentLink = async () => {
  try {
    await navigator.clipboard.writeText(paymentLink.value)
    ElMessage.success('支付链接已复制到剪贴板')
    emit('copy-link', paymentLink.value)
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const openPaymentLink = () => {
  window.open(paymentLink.value, '_blank')
}

const sendWechatMessage = () => {
  emit('send-wechat', {
    transaction: props.transaction,
    paymentLink: paymentLink.value
  })
  ElMessage.success('微信消息发送成功')
}

const confirmPayment = async () => {
  try {
    await ElMessageBox.confirm(
      '请确认用户已完成支付，确认后将更新订单状态。',
      '确认支付',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 模拟确认支付
    await new Promise(resolve => setTimeout(resolve, 500))
    
    emit('success', props.transaction)
    visible.value = false
  } catch (error) {
    // 用户取消确认
  }
}
</script>

<style scoped>
.payment-dialog {
  .payment-content {
    max-height: 60vh;
    overflow-y: auto;
  }
}

.transaction-info,
.payment-methods,
.qr-code-section,
.payment-link-section {
  margin-bottom: 20px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.amount-text {
  font-size: 18px;
  font-weight: bold;
  color: #67C23A;
}

.payment-method-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.payment-method-item {
  padding: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  transition: all 0.3s;
  
  &:hover {
    border-color: #409EFF;
    background-color: #f0f9ff;
  }
  
  &.is-checked {
    border-color: #409EFF;
    background-color: #f0f9ff;
  }
}

.method-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.method-icon {
  font-size: 24px;
  
  &.wechat {
    color: #07c160;
  }
  
  &.alipay {
    color: #1677ff;
  }
  
  &.bank {
    color: #722ed1;
  }
}

.method-name {
  font-size: 16px;
  font-weight: 500;
}

.qr-code-container {
  display: flex;
  gap: 24px;
  align-items: center;
}

.qr-code {
  flex-shrink: 0;
  
  img {
    width: 200px;
    height: 200px;
    border: 1px solid #dcdfe6;
    border-radius: 8px;
  }
}

.qr-code-tips {
  flex: 1;
  
  p {
    margin: 0 0 12px 0;
    color: #666;
    line-height: 1.6;
  }
  
  .amount-tip {
    font-size: 16px;
    
    .amount {
      font-size: 20px;
      font-weight: bold;
      color: #67C23A;
    }
  }
}

.link-container {
  .payment-link-input {
    margin-bottom: 16px;
  }
  
  .link-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>