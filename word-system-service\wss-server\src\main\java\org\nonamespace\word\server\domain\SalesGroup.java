package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nonamespace.word.server.entity.DataEntity;

/**
 * 销售组实体类
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "sales_group", autoResultMap = true)
public class SalesGroup extends DataEntity {

    /**
     * 销售组名称
     */
    @TableField("name")
    private String name;

    /**
     * 销售组描述
     */
    @TableField("description")
    private String description;

    /**
     * 组长ID
     */
    @TableField("leader_id")
    private String leaderId;

    /**
     * 关联的部门ID (sys_dept表)
     */
    @TableField("dept_id")
    private Long deptId;

    /**
     * 状态 (active: 活跃, inactive: 停用)
     */
    @TableField("status")
    private String status;

    /**
     * 成员数量（冗余字段，用于快速查询）
     */
    @TableField("member_count")
    private Integer memberCount;
}
