package org.nonamespace.word.openai.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.openai.config.VolcengineConfig;
import org.nonamespace.word.openai.model.VolcengineTtsRequest;
import org.nonamespace.word.openai.model.VolcengineTtsResponse;
import org.nonamespace.word.openai.service.IVolcengineService;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

/**
 * 火山模型
 *
 * <AUTHOR>
 * @date 2025/5/20 9:41
 */
@Slf4j
@Service
public class VolcengineServiceImpl implements IVolcengineService {

    @Resource
    private VolcengineConfig volcengineConfig;
    private static final String SPEECH_VOICE_TTS_URL = "/api/v1/tts";

    @Override
    public VolcengineTtsResponse enrich(String text, int type) {
        if(StrUtil.isEmptyIfStr(text)) {
            return null;
        }

        VolcengineTtsRequest request = new VolcengineTtsRequest(volcengineConfig.getSpeech().getAppid(),
                volcengineConfig.getSpeech().getAccessToken(),
                volcengineConfig.getSpeech().getCluster(),
                type == 0 ? volcengineConfig.getSpeech().getVoiceType().getUs() : volcengineConfig.getSpeech().getVoiceType().getUk(),
                IdUtil.fastSimpleUUID())
                .text(text)
                ;

        HttpResponse httpResponse = HttpUtil.createPost(volcengineConfig.getBaseUrl() + SPEECH_VOICE_TTS_URL)
                .bearerAuth(volcengineConfig.getSpeech().getAccessToken())
                .header("Authorization", "Bearer; " + volcengineConfig.getSpeech().getAccessToken())
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .body(JSONUtil.toJsonStr(request))
                .execute();

        if(httpResponse == null || httpResponse.getStatus() != 200) {
            throw new RuntimeException("调用火山引擎合成语音接口 http请求 失败:" + (httpResponse != null ?  httpResponse.body() : null));
        }

        VolcengineTtsResponse enrichResponse = JSONUtil.toBean(httpResponse.body(), VolcengineTtsResponse.class, true);
        if(enrichResponse == null || enrichResponse.getCode() != 3000 || StrUtil.isBlankIfStr(enrichResponse.getData())) {
            throw new RuntimeException("调用火山引擎合成语音接口 响应体 失败");
        }

        return enrichResponse;
    }
}
