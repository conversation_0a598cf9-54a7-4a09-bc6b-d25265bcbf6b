package org.nonamespace.word.server.util;

import com.ruoyi.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 导出权限工具类
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Slf4j
@Component
public class ExportPermissionUtil {

    /**
     * 检查是否有订单导出权限
     * @return true-有权限，false-无权限
     */
    public boolean hasOrderExportPermission() {
        return SecurityUtils.hasPermi("order:export") || 
               SecurityUtils.hasPermi("order:manager:export");
    }

    /**
     * 检查是否有管理员订单导出权限
     * @return true-有权限，false-无权限
     */
    public boolean hasManagerOrderExportPermission() {
        return SecurityUtils.hasPermi("order:manager:export");
    }

    /**
     * 检查是否有交易流水导出权限
     * @return true-有权限，false-无权限
     */
    public boolean hasOrderTrxExportPermission() {
        return SecurityUtils.hasPermi("order:trx:export") || 
               SecurityUtils.hasPermi("order:manager:trx:export");
    }

    /**
     * 检查是否有管理员交易流水导出权限
     * @return true-有权限，false-无权限
     */
    public boolean hasManagerOrderTrxExportPermission() {
        return SecurityUtils.hasPermi("order:manager:trx:export");
    }

    /**
     * 检查是否为管理员角色（超级管理员、人力、销售总监）
     * @return true-是管理员，false-不是管理员
     */
    public boolean isManagerRole() {
        return SecurityUtils.hasRole("admin") || 
               SecurityUtils.hasRole("hr") || 
               SecurityUtils.hasRole("sales_director");
    }

    /**
     * 检查是否为销售相关角色
     * @return true-是销售角色，false-不是销售角色
     */
    public boolean isSalesRole() {
        return SecurityUtils.hasRole("sales_director") || 
               SecurityUtils.hasRole("sales_group_leader") || 
               SecurityUtils.hasRole("sale");
    }

    /**
     * 获取当前用户的数据权限范围
     * @return 数据权限范围描述
     */
    public String getDataScope() {
        if (SecurityUtils.hasRole("admin") || SecurityUtils.hasRole("hr")) {
            return "全部数据";
        } else if (SecurityUtils.hasRole("sales_director")) {
            return "全部销售数据";
        } else if (SecurityUtils.hasRole("sales_group_leader")) {
            return "本组数据";
        } else if (SecurityUtils.hasRole("sale")) {
            return "个人数据";
        } else if (SecurityUtils.hasRole("teaching_group_leader")) {
            return "教学数据";
        } else {
            return "受限数据";
        }
    }

    /**
     * 验证导出数量限制
     * @param requestCount 请求导出数量
     * @return 验证结果
     */
    public ExportValidationResult validateExportCount(Integer requestCount) {
        if (requestCount == null || requestCount <= 0) {
            return new ExportValidationResult(false, "导出数量必须大于0");
        }

        // 根据角色设置不同的导出限制
        int maxCount;
        if (isManagerRole()) {
            maxCount = 50000; // 管理员可以导出更多数据
        } else if (isSalesRole()) {
            maxCount = 10000; // 销售角色限制
        } else {
            maxCount = 5000;  // 其他角色限制
        }

        if (requestCount > maxCount) {
            return new ExportValidationResult(false, 
                String.format("导出数量不能超过%d条，当前角色限制：%s", maxCount, getDataScope()));
        }

        return new ExportValidationResult(true, "验证通过");
    }

    /**
     * 验证导出时间范围
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 验证结果
     */
    public ExportValidationResult validateTimeRange(Date startTime, Date endTime) {
        if (startTime == null || endTime == null) {
            return new ExportValidationResult(true, "时间范围验证通过");
        }

        try {
            long start = startTime.getTime();
            long end = endTime.getTime();
            
            long diffDays = (end - start) / (24 * 60 * 60);
            
            // 根据角色设置不同的时间范围限制
            int maxDays;
            if (isManagerRole()) {
                maxDays = 365; // 管理员可以导出一年的数据
            } else if (isSalesRole()) {
                maxDays = 90;  // 销售角色限制3个月
            } else {
                maxDays = 30;  // 其他角色限制1个月
            }

            if (diffDays > maxDays) {
                return new ExportValidationResult(false, 
                    String.format("导出时间范围不能超过%d天，当前角色限制：%s", maxDays, getDataScope()));
            }

            return new ExportValidationResult(true, "时间范围验证通过");
            
        } catch (Exception e) {
            log.warn("时间范围验证失败: startTime={}, endTime={}, error={}", startTime, endTime, e.getMessage());
            return new ExportValidationResult(false, "时间格式错误");
        }
    }

    /**
     * 记录导出操作日志
     * @param exportType 导出类型（order, order_trx）
     * @param exportCount 导出数量
     * @param conditions 导出条件
     */
    public void logExportOperation(String exportType, int exportCount, String conditions) {
        String userId = SecurityUtils.getUserId().toString();
        String username = SecurityUtils.getUsername();
        String roles = String.join(",", SecurityUtils.getLoginUser().getUser().getRoles().stream()
                .map(role -> role.getRoleKey()).toArray(String[]::new));
        
        log.info("用户导出操作: userId={}, username={}, roles={}, exportType={}, exportCount={}, conditions={}", 
                userId, username, roles, exportType, exportCount, conditions);
    }

    /**
     * 导出验证结果
     */
    public static class ExportValidationResult {
        private final boolean valid;
        private final String message;

        public ExportValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }

        public boolean isValid() {
            return valid;
        }

        public String getMessage() {
            return message;
        }
    }
}
