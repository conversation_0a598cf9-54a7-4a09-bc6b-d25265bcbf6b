package org.nonamespace.word.server.facade.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.json.JSONObject;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.misc.WssContext;
import org.nonamespace.word.server.domain.Course;
import org.nonamespace.word.server.domain.CourseScheduleInfo;
import org.nonamespace.word.server.dto.curriculum.*;
import org.nonamespace.word.server.entity.BaseEntity;
import org.nonamespace.word.server.facade.CurriculumFacade;
import org.nonamespace.word.server.service.ICourseScheduleService;
import org.nonamespace.word.server.service.ICourseService;
import org.nonamespace.word.server.service.IUserService;
import org.nonamespace.word.server.service.IWxSendMessageService;
import org.nonamespace.word.server.util.SystemDataQueryUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CurriculumFacadeImpl implements CurriculumFacade {

    @Autowired
    private IUserService userService;
    @Autowired
    private ICourseService courseService;

    @Autowired
    private ICourseScheduleService courseScheduleService;
    @Autowired
    private IWxSendMessageService wxSendMessageService;

    @Autowired
    private SystemDataQueryUtil systemDataQueryUtil;

    // 默认配置值，可考虑移到配置文件
    private static final String DEFAULT_COURSE_TYPE = "学习课";
    private static final String DEFAULT_SUBJECT = "英语";
    private static final String DEFAULT_SPECIFICATION = "单词课";
    private static final int DEFAULT_DURATION_MONTHS = 3;
    private static final int DEFAULT_LESSON_DURATION_MINUTES = 60;


    @Override
    public List<String> createSchedule(CurriculumCreateScheduleDto.Req req) {
        log.info("创建课程排课, req: {}", JSONUtil.toJsonStr(req));

        if (!StrUtil.isNotEmpty(req.getStudentId())) {
            throw new IllegalArgumentException("学生ID不能为空");
        }

        if (req.getWeeklySchedules() == null || req.getWeeklySchedules().isEmpty()) {
            throw new IllegalArgumentException("每周排课时间不能为空");
        }

        if (!StrUtil.isNotEmpty(req.getDateRange()[0]) || !StrUtil.isNotEmpty(req.getDateRange()[1])) {
            throw new IllegalArgumentException("日期范围不能为空");
        }

        if (!StrUtil.isNotEmpty(req.getTeacherId())) {
            req.setTeacherId(WssContext.userId());
        }

        if(StrUtil.equals("复习课", req.getType())){
            if (req.getWeeklySchedules().stream().anyMatch(x->x.getDuration()>25)) {
                throw new IllegalArgumentException("复习课的时长不能超过25分钟");
            }
        }
//
//        if (!WssContext.isAdmin()) {
//            if (StrUtil.isEmpty(req.getTeacherId())) {
//                req.setTeacherId(WssContext.userId());
//            } else if (!WssContext.userId().equals(req.getTeacherId())) {
//                throw new IllegalArgumentException("只能排自己的课");
//            }
//        }

        if (userService.lambdaQuery().in(SysUser::getUserId, Long.valueOf(req.getTeacherId()), Long.valueOf(req.getStudentId())).count() != 2) {
            throw new IllegalArgumentException("教师或学生不存在");
        }

        LocalDate start = LocalDate.parse(req.getDateRange()[0]);
        LocalDate end = LocalDate.parse(req.getDateRange()[1]);

        // 判断老师和学生的排课时间是否冲突
        List<Course> existingCourses = courseService.lambdaQuery()
                .select(Course.class, c -> !c.getColumn().equals("content"))
                .and(q -> q.eq(Course::getTeacherId, req.getTeacherId())
                        .or().eq(Course::getStudentId, req.getStudentId()))
                .between(Course::getScheduledStartTime, start.atStartOfDay(), end.atTime(23, 59, 59))
                .eq(Course::getType, "学习课")
                .in(Course::getCourseStatus, "待开始", "进行中", "已完成")
                .list();

        Set<Long> userIds = new HashSet<>();
        userIds.add(Long.valueOf(req.getStudentId()));
        userIds.add(Long.valueOf(req.getTeacherId()));
        for (Course c : existingCourses) {
            userIds.add(Long.valueOf(c.getTeacherId()));
            userIds.add(Long.valueOf(c.getStudentId()));
        }

        Map<String, SysUser> userMap = userService.lambdaQuery()
                .select(SysUser::getUserId, SysUser::getUserName, SysUser::getNickName, SysUser::getPhonenumber)
                .in(CollUtil.isNotEmpty(userIds), SysUser::getUserId, userIds)
                .list()
                .stream().collect(Collectors.toMap(x -> String.valueOf(x.getUserId()), x -> x));


        List<Course> toSave = new ArrayList<>();
        int count = 0;

        CourseScheduleInfo scheduleInfo = new CourseScheduleInfo();
        scheduleInfo.setId(IdUtil.getSnowflakeNextIdStr());
        scheduleInfo.setTeacherId(req.getTeacherId());
        scheduleInfo.setStudentId(req.getStudentId());
        scheduleInfo.setSubject(req.getSubject());
        scheduleInfo.setSpecification(req.getSpecification());
        scheduleInfo.setType(req.getType());
        scheduleInfo.setCourseType(req.getCourseType() != null ? req.getCourseType() : "正式课"); // 设置课程类型，默认为正式课
        scheduleInfo.setBeginDate(DateUtil.parseDate(req.getDateRange()[0]));
        scheduleInfo.setEndDate(DateUtil.parseDate(req.getDateRange()[1]));
        scheduleInfo.setScheduleData(JSONUtil.parseObj(req));
        scheduleInfo.setTotalLessons(req.getTotalLessons() != null ? req.getTotalLessons() : 0);

        courseScheduleService.save(scheduleInfo);

        for (CurriculumCreateScheduleDto.Req.WeeklySchedule weeklySchedule : req.getWeeklySchedules()) {
            // 前端周日为0，Java周日为7
            if(weeklySchedule.getDayOfWeek() == 0){
                weeklySchedule.setDayOfWeek(7);
            }
        }

        for (LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
            for (CurriculumCreateScheduleDto.Req.WeeklySchedule ws : req.getWeeklySchedules()) {
                if (date.getDayOfWeek().getValue() == ws.getDayOfWeek()) {
                    Date sTime = Date.from(LocalDateTime.of(date, LocalTime.parse(ws.getStartTime()))
                            .atZone(ZoneId.systemDefault()).toInstant());
                    Date eTime = Date.from(LocalDateTime.of(date, LocalTime.parse(ws.getEndTime()))
                            .atZone(ZoneId.systemDefault()).toInstant());

                    // 判断要排的课程是否与已添加待保存的课程冲突
                    Optional<Course> selfConflictCourse = toSave.stream().filter(ec ->
                            (sTime.before(ec.getScheduledEndTime()) && eTime.after(ec.getScheduledStartTime()))
                    ).findFirst();
                    if (selfConflictCourse.isPresent()) {
                        Course conflict = selfConflictCourse.get();
                        String msg = StrUtil.format("排课时间冲突：{} {}-{}，与待保存课程[老师:{} 学生:{} 科目:{} 时间:{}-{}]冲突",
                                date, ws.getStartTime(), ws.getEndTime(),
                                userService.getUserDisplayName(userMap.get(conflict.getTeacherId())),
                                userService.getUserDisplayName(userMap.get(conflict.getStudentId())),
                                conflict.getSubject(),
                                DateUtil.format(conflict.getScheduledStartTime(), "yyyy-MM-dd HH:mm"),
                                DateUtil.format(conflict.getScheduledEndTime(), "yyyy-MM-dd HH:mm"));
                        log.warn(msg);
                        throw new RuntimeException(msg);
                    }

                    // 判断时间是否与已存在课程冲突
                    Optional<Course> conflictCourse = existingCourses.stream().filter(ec ->
                            (sTime.before(ec.getScheduledEndTime()) && eTime.after(ec.getScheduledStartTime()))
                    ).findFirst();

                    if (conflictCourse.isPresent()) {
                        Course conflict = conflictCourse.get();
                        String msg = StrUtil.format("排课时间跟已有课程冲突：{} {}-{}，与已有课程[老师:{} 学生:{} 科目:{} 时间:{}-{}]冲突",
                                date, ws.getStartTime(), ws.getEndTime(),
                                userService.getUserDisplayName(userMap.get(conflict.getTeacherId())),
                                userService.getUserDisplayName(userMap.get(conflict.getStudentId())),
                                conflict.getSubject(),
                                DateUtil.format(conflict.getScheduledStartTime(), "yyyy-MM-dd HH:mm"),
                                DateUtil.format(conflict.getScheduledEndTime(), "yyyy-MM-dd HH:mm"));
                        log.warn(msg);
                        throw new RuntimeException(msg);
                    }

                    Course c = new Course();
                    c.setId(IdUtil.getSnowflakeNextIdStr());
                    c.setTeacherId(req.getTeacherId());
                    c.setStudentId(req.getStudentId());
                    c.setScheduledStartTime(sTime);
                    c.setScheduledEndTime(eTime);
                    c.setDurationMinutes((long) ws.getDuration());
                    c.setType(req.getType());
                    c.setCourseType(req.getCourseType() != null ? req.getCourseType() : "正式课"); // 设置课程类型，默认为正式课
                    c.setUseSystem(req.getUseSystem() != null ? req.getUseSystem() : true); // 设置使用系统，默认为true
                    c.setSubject(req.getSubject());
                    c.setSpecification(req.getSpecification());
                    c.setScheduleId(scheduleInfo.getId());
                    c.setCourseStatus("待开始");


                    if(c.getType().equals("复习课") && c.getDurationMinutes() > 25){
                        throw new IllegalArgumentException("复习课的时长不能超过25分钟");
                    }

                    toSave.add(c);
                    count++;
                    if (count >= req.getTotalLessons()) {
                        break;
                    }

                }
            }
        }
        courseService.saveBatch(toSave);

        return toSave.stream().map(BaseEntity::getId).toList();
    }

    @Override
    public List<CurriculemGetScheduleDto.Course> getSchedule(CurriculemGetScheduleDto.Req req) {
        if(StrUtil.isEmpty(req.getTeacherId() ) && StrUtil.isEmpty(req.getStudentId())){
            if(systemDataQueryUtil.isTeacher()){
                req.setTeacherId(WssContext.userId());
            }else if(systemDataQueryUtil.isStudent()){
                req.setStudentId(WssContext.userId());
            }
        }
        Set<String> userIds = new HashSet<>();
//        Set<String> types = Objects.equals("all", req.getType()) ? CollUtil.set(false, "学习课", "复习课") : CollUtil.set(false, req.getType());
        List<CurriculemGetScheduleDto.Course> courses = courseService.lambdaQuery()
                .select(Course.class, c -> !c.getColumn().equals("content"))
                .eq(StrUtil.isNotEmpty(req.getTeacherId()), Course::getTeacherId, ObjectUtil.defaultIfNull(req.getTeacherId(), WssContext.userId()))
                .eq(StrUtil.isNotEmpty(req.getStudentId()), Course::getStudentId, req.getStudentId())
//                .in(Course::getType, types)
                .eq(StrUtil.isNotEmpty(req.getStatus()), Course::getCourseStatus, req.getStatus())
                .in(StrUtil.isEmpty(req.getStatus()), Course::getCourseStatus, "待开始", "进行中", "已完成")
                .orderByAsc(Course::getScheduledStartTime).list().stream().map(x -> {
                    CurriculemGetScheduleDto.Course schedule = new CurriculemGetScheduleDto.Course();
                    schedule.setId(x.getId());
                    schedule.setType(x.getType());
                    schedule.setCourseType(x.getCourseType()); // 设置课程性质
                    schedule.setStatus(x.getCourseStatus());
                    schedule.setDuration(x.getDurationMinutes());
                    schedule.setStatus(x.getCourseStatus());
                    schedule.setStudentId(x.getStudentId());
                    schedule.setTeacherId(x.getTeacherId());
                    schedule.setSubject(x.getSubject());
                    schedule.setSpecification(x.getSpecification());


                    schedule.setStartTime(x.getScheduledStartTime());
                    schedule.setEndTime(x.getScheduledEndTime());

//                    if (x.getType().equals("学习课")) {
//                        schedule.setStartTime(x.getScheduledStartTime());
//                        schedule.setEndTime(x.getScheduledEndTime());
//                    } else {
//                        schedule.setStartTime(ObjectUtil.defaultIfNull(x.getActualStartTime(), x.getScheduledStartTime()));
//                        schedule.setEndTime(ObjectUtil.defaultIfNull(x.getActualEndTime(), x.getScheduledEndTime()));
//                        // 如果课程状态已完成，则设置duration为实际分钟数
//                        if ("已完成".equals(x.getCourseStatus()) && x.getActualStartTime() != null && x.getActualEndTime() != null) {
//                            long duration = Duration.between(x.getActualStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime(),
//                                    x.getActualEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()).toMinutes();
//                            schedule.setDuration(duration);
//                        }
//                    }

                    userIds.add(x.getTeacherId());
                    userIds.add(x.getStudentId());
                    return schedule;
                }).collect(Collectors.toList());
        if (!userIds.isEmpty()) {
            Map<String, SysUser> userMap = userService.lambdaQuery().in(SysUser::getUserId, userIds)
                    .select(SysUser::getUserId, SysUser::getUserName, SysUser::getNickName, SysUser::getPhonenumber).list().stream().collect(Collectors.toMap(x -> String.valueOf(x.getUserId()), x -> x));
            Map<String, String> usernameMap = userMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> {
                SysUser user = e.getValue();
                return StrUtil.isNotEmpty(user.getNickName()) ? user.getNickName() : user.getUserName();
            }));
            courses.forEach(x -> {
                x.setTeacherName(usernameMap.getOrDefault(x.getTeacherId(), "未知"));
                x.setStudentName(usernameMap.getOrDefault(x.getStudentId(), "未知"));
            });
        }

        return courses;
    }

    @Override
    public void cancelCourse(CurriculeCancelScheduleDto.Req req) {
        Course course = courseService.getById(req.getCourseId());
        if (course == null) {
            throw new IllegalArgumentException("课程不存在");
        }

        String status = course.getCourseStatus();
        if (status.equals("已完成")) {
            throw new IllegalArgumentException("已完成课程不能停课");
        }

        if (status.equals("停课")) {
            throw new IllegalArgumentException("课程已停课，不用重复停课");
        }

        course.addExceptionType("停课");

        courseService.lambdaUpdate()
                .set(Course::getCourseStatus, "停课")
                .set(Course::getCancelReason, req.getReason())
                .set(Course::getCancelType, req.getType())
                .set(Course::getExceptionTypes, course.getExceptionTypes(), "typeHandler=org.nonamespace.word.server.misc.mybatis.ListStringTypeHandler")
                .eq(Course::getId, req.getCourseId())
                .in(Course::getCourseStatus, "待开始", "进行中") // 只能停课未开始的课程
                .update();
    }

    @Override
    public void reschedule(CurriculumCourseRescheduleDto.Req req) {

        if (StrUtil.isEmpty(req.getNewStartTime()) || StrUtil.isEmpty(req.getNewEndTime())) {
            throw new IllegalArgumentException("新的开始和结束时间不能为空");
        }

        Course course = courseService.getById(req.getCourseId());
        if(course == null) {
            throw new IllegalArgumentException("课程不存在");
        }

        if(!course.getCourseStatus().equals("待开始")) {
            throw new IllegalArgumentException("只能调整未开始的课程");
        }

        course.addExceptionType("调课");

        courseService.lambdaUpdate().set(Course::getCourseStatus, "调课")
                .set(Course::getCancelType, req.getRescheduleType())
                .set(Course::getCancelReason, req.getReason())
                .set(Course::getExceptionTypes, course.getExceptionTypes(), "typeHandler=org.nonamespace.word.server.misc.mybatis.ListStringTypeHandler")
                .eq(Course::getId, req.getCourseId())
                .update();

        course.setId(IdUtil.getSnowflakeNextIdStr());
        course.setScheduledStartTime(DateUtil.parseDateTime(StrUtil.format("{} {}:00", req.getNewDate(), req.getNewStartTime())));
        course.setScheduledEndTime(DateUtil.parseDateTime(StrUtil.format("{} {}:00", req.getNewDate(), req.getNewEndTime())));
        course.setCreateTime(null);
        course.setCreateBy(null);
        course.setUpdateTime(null);
        course.setUpdateBy(null);

        List<Course> courses = courseService.lambdaQuery()
                .select(Course::getId, Course::getScheduledStartTime, Course::getScheduledEndTime, Course::getTeacherId, Course::getStudentId)
                .between(Course::getScheduledStartTime, DateUtil.parseDateTime(StrUtil.format("{} 00:00:00", req.getNewDate())), DateUtil.parseDateTime(StrUtil.format("{} 23:59:59", req.getNewDate())))
                .eq(Course::getTeacherId, course.getTeacherId())
                .eq(Course::getStudentId, course.getStudentId())
                .in(Course::getCourseStatus, "待开始", "进行中", "已完成")
                .list();

        // 检查是否有时间冲突
        for (Course existingCourse : courses) {
            if (course.getScheduledStartTime().before(existingCourse.getScheduledEndTime()) &&
                    course.getScheduledEndTime().after(existingCourse.getScheduledStartTime())) {
                throw new IllegalArgumentException("调课时间与已有课程冲突");
            }
        }

        // 保存新的课程
        courseService.save(course);

        ThreadUtil.execute(() -> {
            // 如果当前时间是晚上20点后，实时推送
            // 如果被调整的课程，计划时间是今天或明天的，才需要实时推送
            Date now = DateUtil.date();
            Date tomorrowEnd = DateUtil.endOfDay(DateUtil.offsetDay(now, 1));

            String today20hStr = DateUtil.today() + " 20:00:00";
            if(DateUtil.date().after(DateUtil.parse(today20hStr))
                        && DateUtil.isIn(course.getScheduledStartTime(), now, tomorrowEnd)) {
                log.info("当前时间 20点 之后，实时推送");
                wxSendMessageService.buildRightNowCourseList(List.of(course));
            }
        });

    }

    @Override
    public void importCurriculum(InputStream inputStream) {
        try {

            ExcelReader reader = ExcelUtil.getReader(inputStream);
            List<Map<String, Object>> rows = reader.readAll();
            reader.close();

            if (rows == null || rows.isEmpty()) {
                throw new IllegalArgumentException("Excel文件为空或格式不正确");
            }

            log.info("读取到 {} 行数据", rows.size());


            List<CurriculumCreateScheduleDto.Req> scheduleDtos = convertToDto(rows);


            // 预加载用户数据，提高性能
            Map<String, String> userNameToIdMap = loadUserMappings();
            log.info("加载了 {} 个用户映射", userNameToIdMap.size());

            for (CurriculumCreateScheduleDto.Req dto : scheduleDtos) {
                dto.setStudentId(userNameToIdMap.get(dto.getStudentName()));
                dto.setTeacherId(userNameToIdMap.get(dto.getTeacherName()));
                if (dto.getStudentId() == null ) {
                    throw new IllegalArgumentException("找不到学生: " + dto.getStudentName());
                }
                if (dto.getTeacherId() == null) {
                    throw new IllegalArgumentException("找不到老师: " + dto.getTeacherName());
                }
            }

            // 根据起止日期日期初始化课表实例
            for (CurriculumCreateScheduleDto.Req dto : scheduleDtos) {
                try {
                    createSchedule(dto);
                }catch (Exception e) {
                    log.error("创建课表失败: {}, 错误: {}", dto, e.getMessage());
                    throw new RuntimeException("创建课表失败: " + e.getMessage(), e);
                }
            }


        } catch (Exception e) {
            log.error("导入课表异常", e);
            throw new RuntimeException("导入课表失败: " + e.getMessage(), e);
        }
    }


    /**
     * 验证Excel格式
     */
    private List<CurriculumCreateScheduleDto.Req> convertToDto(List<Map<String, Object>> rows) {
        if (rows.isEmpty()) {
            throw new IllegalArgumentException("Excel文件为空或格式不正确");
        }

        // 检查必需的列
        String[] requiredColumns = {"学生姓名", "老师姓名", "开始日期", "结束日期"};
        Map<String, Object> firstRow = rows.getFirst();

        for (String column : requiredColumns) {
            if (!firstRow.containsKey(column)) {
                throw new IllegalArgumentException(String.format("缺少必需的列: %s。请确保Excel包含以下列: %s",
                        column, String.join(", ", requiredColumns)));
            }
        }

        List<CurriculumCreateScheduleDto.Req> schedules = new ArrayList<>();

        // 验证数据格式
        for (int i = 0; i < rows.size(); i++) {
            Map<String, Object> row = rows.get(i);
            String rowInfo = String.format("第%d行", i + 1);

            // 验证必填字段
            for (String column : requiredColumns) {
                Object value = row.get(column);
                if (value == null || StrUtil.isBlank(String.valueOf(value))) {
                    throw new IllegalArgumentException(String.format("%s的%s不能为空", rowInfo, column));
                }
            }


            CurriculumCreateScheduleDto.Req dto = new CurriculumCreateScheduleDto.Req();

            schedules.add(dto);

            dto.setStudentName(getStringValueOrBlank(row, "学生姓名"));
            dto.setTeacherName(getStringValueOrBlank(row, "老师姓名"));
            dto.setDateRange(new String[]{
                    parseDateString(getStringValueOrBlank(row, "开始日期")).format(DatePattern.NORM_DATE_FORMATTER),
                    parseDateString(getStringValueOrBlank(row, "结束日期")).format(DatePattern.NORM_DATE_FORMATTER),
            });

            dto.setSubject(DEFAULT_SUBJECT);
            dto.setSpecification(DEFAULT_SPECIFICATION);
            dto.setType(DEFAULT_COURSE_TYPE);
            dto.setCourseType("正式课"); // 设置默认课程性质为正式课
            dto.setTotalLessons(1000);
            dto.setWeeklySchedules(new ArrayList<>());

            // 验证星期几
            String[] weekDays = {"周一", "周二", "周三", "周四", "周五", "周六", "周日"};
            for (String weekDay : weekDays) {
                String cell = getStringValueOrBlank(row, weekDay);
                if (StrUtil.isEmpty(cell)) {
                    continue;
                }

                CurriculumCreateScheduleDto.Req.WeeklySchedule ws = new CurriculumCreateScheduleDto.Req.WeeklySchedule();
                ws.setDayOfWeek(parseDayOfWeek(weekDay));


                // 校验时间段是否正确
                String[] timeParts = cell.split("-");
                if (timeParts.length != 2) {
                    throw new IllegalArgumentException(String.format("%s的%s格式错误，应为HH:mm-HH:mm", rowInfo, weekDay));
                }
                try {
                    LocalTime startTime = parseTimeString(timeParts[0].trim());
                    ws.setStartTime(startTime.format(DateTimeFormatter.ofPattern("HH:mm")));
                    LocalTime endTime = parseTimeString(timeParts[1].trim());
                    ws.setEndTime(endTime.format(DateTimeFormatter.ofPattern("HH:mm")));

                    ws.setDuration((int)Math.abs(ChronoUnit.MINUTES.between(startTime, endTime)));

                    dto.getWeeklySchedules().add(ws);
                } catch (Exception e) {
                    throw new IllegalArgumentException(String.format("%s的%s时间格式错误: %s", rowInfo, weekDay, e.getMessage()), e);
                }

            }
        }

        return schedules;
    }

    private String getStringValueOrBlank(Map<String, Object> row, String column) {
        return getStringValueOrDefault(row, column, "");
    }

    /**
     * 加载用户映射
     */
    private Map<String, String> loadUserMappings() {
        List<SysUser> allUsers = userService.list();
        return allUsers.stream()
                .filter(user -> StrUtil.isNotBlank(user.getUserName()) || StrUtil.isNotBlank(user.getNickName()))
                .collect(Collectors.toMap(
                        user -> ObjectUtil.defaultIfNull(user.getNickName(), user.getUserName()),
                        user -> String.valueOf(user.getUserId()),
                        (existing, replacement) -> existing
                ));
    }

    /**
     * 从Excel获取字符串值或返回默认值
     */
    private String getStringValueOrDefault(Map<String, Object> row, String column, String defaultValue) {
        Object value = row.get(column);
        if (value == null || StrUtil.isBlank(String.valueOf(value))) {
            return defaultValue;
        }
        return String.valueOf(value).trim();
    }
    /**
     * 解析星期几
     */
    private int parseDayOfWeek(Object dayOfWeekObj) {
        if (dayOfWeekObj instanceof Number) {
            int value = ((Number) dayOfWeekObj).intValue();
            // 与CourseServiceImpl保持一致：1-7分别代表周一到周日
            if (value < 1 || value > 7) {
                throw new IllegalArgumentException("星期几数值必须在1-7之间: " + value);
            }
            return value;
        } else if (dayOfWeekObj instanceof String) {
            String str = dayOfWeekObj.toString().trim();
            try {
                int value = Integer.parseInt(str);
                if (value < 1 || value > 7) {
                    throw new IllegalArgumentException("星期几数值必须在1-7之间: " + value);
                }
                return value;
            } catch (NumberFormatException e) {
                // 尝试解析中文星期，与CourseServiceImpl保持一致：1-7分别代表周一到周日
                switch (str) {
                    case "星期一":
                    case "周一":
                    case "一":
                        return 1; // Monday
                    case "星期二":
                    case "周二":
                    case "二":
                        return 2; // Tuesday
                    case "星期三":
                    case "周三":
                    case "三":
                        return 3; // Wednesday
                    case "星期四":
                    case "周四":
                    case "四":
                        return 4; // Thursday
                    case "星期五":
                    case "周五":
                    case "五":
                        return 5; // Friday
                    case "星期六":
                    case "周六":
                    case "六":
                        return 6; // Saturday
                    case "星期日":
                    case "星期天":
                    case "周日":
                    case "周天":
                    case "日":
                    case "天":
                        return 7; // Sunday
                    default:
                        throw new IllegalArgumentException("无法解析的星期几: " + str);
                }
            }
        }
        throw new IllegalArgumentException("无效的星期几格式: " + dayOfWeekObj);
    }

    /**
     * 解析时间字符串
     */
    private LocalTime parseTimeString(String timeStr) {
        if (StrUtil.isBlank(timeStr)) {
            throw new IllegalArgumentException("时间字符串不能为空");
        }

        timeStr = timeStr.trim();

        // 支持多种时间格式
        String[] formats = {"HH:mm", "H:mm", "HH:mm:ss", "H:mm:ss"};

        for (String format : formats) {
            try {
                return LocalTime.parse(timeStr, DateTimeFormatter.ofPattern(format));
            } catch (DateTimeParseException ignored) {
                // 尝试下一个格式
            }
        }

        throw new IllegalArgumentException("无法解析的时间格式: " + timeStr);
    }

    /**
     * 解析日期字符串
     */
    private LocalDate parseDateString(String dateStr) {
        if (StrUtil.isBlank(dateStr)) {
            throw new IllegalArgumentException("时间字符串不能为空");
        }

        dateStr = dateStr.trim();

        // 支持多种时间格式
        String[] formats = {DatePattern.NORM_DATE_PATTERN, DatePattern.NORM_DATETIME_PATTERN};

        try {
            for (String format : formats) {
                try {
                    return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(format));
                } catch (DateTimeParseException ignored) {
                    // 尝试下一个格式
                }
            }
        } catch (DateTimeParseException ignored) {
            throw new IllegalArgumentException("无法解析的日期格式: " + dateStr);
        }
        return null;
    }

    @Override
    public void consumeCourse(CurriculumConsumeCourseDto.Req req) {
        if (StrUtil.isEmpty(req.getCourseId())) {
            throw new IllegalArgumentException("课程ID不能为空");
        }

        if (StrUtil.isEmpty(req.getCourseDate())) {
            throw new IllegalArgumentException("上课日期不能为空");
        }

        if (StrUtil.isEmpty(req.getStartTime())) {
            throw new IllegalArgumentException("开始时间不能为空");
        }

        if (StrUtil.isEmpty(req.getEndTime())) {
            throw new IllegalArgumentException("结束时间不能为空");
        }

        if (StrUtil.isEmpty(req.getImages())) {
            throw new IllegalArgumentException("请至少上传2张图片");
        }

        // 验证图片数量
        String[] imageArray = req.getImages().split(",");
        if (imageArray.length < 2) {
            throw new IllegalArgumentException("请至少上传2张图片");
        }

        // 获取课程信息
        Course course = courseService.getById(req.getCourseId());
        if (course == null) {
            throw new IllegalArgumentException("课程不存在");
        }

        // 检查课程状态
        if (!"待开始".equals(course.getCourseStatus()) && !"进行中".equals(course.getCourseStatus())) {
            throw new IllegalArgumentException("只能对待开始或进行中的课程进行消课操作");
        }

        // 构建消课信息JSON
        JSONObject consumptionInfo = new JSONObject();
        consumptionInfo.put("courseDate", req.getCourseDate());
        consumptionInfo.put("startTime", req.getStartTime());
        consumptionInfo.put("endTime", req.getEndTime());
        consumptionInfo.put("recordingUrl", req.getRecordingUrl());
        consumptionInfo.put("images", req.getImages());
        consumptionInfo.put("description", req.getDescription());
        consumptionInfo.put("consumptionTime", WssContext.now());
        consumptionInfo.put("operatorId", SecurityUtils.getUserId());
        consumptionInfo.put("operatorName", SecurityUtils.getUsername());

        // 更新课程状态和消课信息
        course.setCourseStatus("已完成");
        course.setConsumptionMethod("人工消课");
        course.setConsumptionInfo(consumptionInfo);
        course.setActualEndTime(WssContext.now());
        course.setUpdateTime(WssContext.now());

        // 保存课程更新
        courseService.updateById(course);

        // 课消
        courseService.performCourseHoursConsumption(course);

        log.info("人工消课完成: courseId={}, operatorId={}, operatorName={}",
                req.getCourseId(), SecurityUtils.getUserId(), SecurityUtils.getUsername());
//        }
//
//        // 检查课程是否存在
//        Course course = courseService.getById(req.getCourseId());
//        if (course == null) {
//            throw new IllegalArgumentException("课程不存在");
//        }
//
//        // 检查课程状态
//        if (!course.getUseSystem()) {
//            throw new IllegalArgumentException("只有使用系统的课程才能消课");
//        }
//
//        if (!"待开始".equals(course.getCourseStatus()) && !"进行中".equals(course.getCourseStatus())) {
//            throw new IllegalArgumentException("只能消课待开始或进行中的课程");
//        }
//
//        // 解析上课时间
//        try {
//            String startDateTime = req.getCourseDate() + " " + req.getStartTime() + ":00";
//            String endDateTime = req.getCourseDate() + " " + req.getEndTime() + ":00";
//
//            Date actualStartTime = DateUtil.parseDateTime(startDateTime);
//            Date actualEndTime = DateUtil.parseDateTime(endDateTime);
//
//            // 更新课程状态为已完成，并记录实际上课时间和录屏链接
//            courseService.lambdaUpdate()
//                    .set(Course::getCourseStatus, "已完成")
//                    .set(Course::getActualStartTime, actualStartTime)
//                    .set(Course::getActualEndTime, actualEndTime)
//                    .set(Course::getRecordingUrl, req.getRecordingUrl())
//                    .set(Course::getRemark, req.getDescription())
//                    .eq(Course::getId, req.getCourseId())
//                    .update();
//
//            log.info("消课成功，课程ID: {}, 实际上课时间: {} - {}",
//                    req.getCourseId(), actualStartTime, actualEndTime);
//
//        } catch (Exception e) {
//            log.error("消课失败，课程ID: {}, 错误: {}", req.getCourseId(), e.getMessage());
//            throw new IllegalArgumentException("消课失败: " + e.getMessage());
//        }
    }
}
