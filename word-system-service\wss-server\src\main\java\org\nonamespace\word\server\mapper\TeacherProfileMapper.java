package org.nonamespace.word.server.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.nonamespace.word.server.domain.TeacherProfile;
import org.nonamespace.word.server.dto.management.teachinggroup.TeacherDto;

import java.util.List;

/**
 * 教师档案Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Mapper
public interface TeacherProfileMapper extends MPJBaseMapper<TeacherProfile> {

    /**
     * 查询教师详细信息
     * 
     * @param teacherId 教师ID
     * @return 教师详细信息
     */
    TeacherDto.DetailResp selectTeacherDetail(@Param("teacherId") String teacherId);

    /**
     * 查询可分配的教师列表（未分配到任何教学组的教师）
     * 
     * @return 可分配教师列表
     */
    List<TeacherDto.AvailableResp> selectAvailableTeachers();



    /**
     * 查询所有教师列表（用于选择组长、教务）
     *
     * @return 教师列表
     */
    List<TeacherDto.UserRoleResp> selectAllTeachers();

    /**
     * 查询教师带教信息
     * 
     * @param teacherId 教师ID
     * @return 带教信息
     */
    TeacherDto.TeachingInfoResp selectTeachingInfo(@Param("teacherId") String teacherId);

    /**
     * 批量查询教师带教信息
     *
     * @param teacherIds 教师ID列表
     * @return 带教信息列表
     */
    List<TeacherDto.TeachingInfoResp> selectTeachingInfoBatch(@Param("teacherIds") List<String> teacherIds);

    /**
     * 批量查询教师详细信息
     *
     * @param teacherIds 教师ID列表
     * @return 教师详细信息列表
     */
    List<TeacherDto.DetailResp> selectTeacherDetailBatch(@Param("teacherIds") List<String> teacherIds);
}
