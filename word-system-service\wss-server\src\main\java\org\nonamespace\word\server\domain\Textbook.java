package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.ArrayTypeHandler;
import org.nonamespace.word.server.entity.DataEntity;

/**
 * 词定义 (统一教材与词): 定义各种词对象 textbook
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel(value = "词词汇关联表")
@TableName("textbook")
public class Textbook extends DataEntity
{

    /** 词表名称 (例如: "高频315词表", "牛津版三年级上册U1") */
    @Excel(name = "词表名称 (例如: \"高频315词表\", \"牛津版三年级上册U1\")")
    @TableField("name")
    private String name;

    /** 描述 */
    @Excel(name = "描述")
    @TableField("description")
    private String description;

    /** 封面 */
    @Excel(name = "封面")
    @TableField("cover")
    private String cover;

    /** 类型 (学校教材,特色词表,学生词表) */
    @Excel(name = "类型 (学校教材,特色词表,学生词表)")
    @TableField("type")
    private String type;

    /** 来源详情 (JSON格式) */
    @Excel(name = "来源详情 (JSON格式)")
    @TableField("source_details")
    private String sourceDetails;

    /** 创建者/所有者ID (学生/公共) */
    @Excel(name = "创建者/所有者ID (学生/公共)")
    @TableField("owner_id")
    private String ownerId;

    /** 词表结构 */
    @Excel(name = "词表结构")
    @TableField("word_list")
    private String wordList;

    /** 标签，如["类型:学校教材", "年级:一年级", "版本:牛津版"] */
    @Excel(name = "标签，如[\"类型:学校教材\", \"年级:一年级\", \"版本:牛津版\"]")
    @TableField(value = "tags",typeHandler = ArrayTypeHandler.class)
    private String[] tags;

    /** 版本号, 每次修改递增 */
    @Excel(name = "版本号, 每次修改递增")
    @TableField(
            fill = FieldFill.INSERT_UPDATE,
            value = "version"
    )
    private Long version = 1L;

    /** 单词数 */
    @Excel(name = "单词数")
    @TableField("stat_word_cnt")
    private Long statWordCnt;

    /** 单元数 */
    @Excel(name = "单元数")
    @TableField("stat_unit_cnt")
    private Long statUnitCnt;

    /** 出版社 */
    @Excel(name = "出版社")
    private String publisher;
    /** 年级 */
    @Excel(name = "年级")
    private Integer grade;
    /** 学期 */
    @Excel(name = "学期")
    private Integer semester;

    /** 必修版本 */
    @Excel(name = "必修版本")
    private String required;

    /**
     * 阶段
     */
    @Excel(name = "阶段")
    private String stage;
}
