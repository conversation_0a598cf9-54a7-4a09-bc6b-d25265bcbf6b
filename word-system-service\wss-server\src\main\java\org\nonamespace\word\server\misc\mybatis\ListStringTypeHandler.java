package org.nonamespace.word.server.misc.mybatis;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@MappedTypes(List.class) // 映射 List<String>
@MappedJdbcTypes(JdbcType.ARRAY)
public class ListStringTypeHandler extends BaseTypeHandler<List<String>> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<String> parameter, JdbcType jdbcType) throws SQLException {
        if (parameter == null || parameter.isEmpty()) {
            ps.setArray(i, ps.getConnection().createArrayOf("varchar", new String[0]));
        } else {
            String[] array = parameter.toArray(new String[0]);
            ps.setArray(i, ps.getConnection().createArrayOf("varchar", array));
        }
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Array array = rs.getArray(columnName);
        return arrayToList(array);
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Array array = rs.getArray(columnIndex);
        return arrayToList(array);
    }

    @Override
    public List<String> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Array array = cs.getArray(columnIndex);
        return arrayToList(array);
    }

    private List<String> arrayToList(Array array) throws SQLException {
        if (array == null) {
            return new ArrayList<>();
        }

        Object[] objects = (Object[]) array.getArray();
        if (objects == null || objects.length == 0) {
            return new ArrayList<>();
        }

        return Arrays.stream(objects)
                .map(Object::toString)
                .collect(Collectors.toList());
    }
}