package org.nonamespace.word.server.service.esign.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReflectUtil;
import com.ruoyi.common.core.domain.entity.SysUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.constants.OrderConstants;
import org.nonamespace.word.server.domain.order.Orders;
import org.nonamespace.word.server.model.ESignDocTemplateStructureV1;
import org.nonamespace.word.server.service.IUserService;
import org.nonamespace.word.server.service.esign.IESignService;
import org.nonamespace.word.server.service.order.IOrdersService;
import org.nonamespace.word.thirdpart.esign.config.ESignConfig;
import org.nonamespace.word.thirdpart.esign.domain.EsignTemplateComponents;
import org.nonamespace.word.thirdpart.esign.exception.ESignException;
import org.nonamespace.word.thirdpart.esign.model.*;
import org.nonamespace.word.thirdpart.esign.service.IESignContractsFileService;
import org.nonamespace.word.thirdpart.esign.service.IESignFlowService;
import org.nonamespace.word.thirdpart.esign.service.IESignTemplateService;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.*;


@Slf4j
@Service
@RequiredArgsConstructor
public class ESignServiceImpl implements IESignService {

    private final IESignTemplateService eSignTemplateService;
    private final IESignContractsFileService eSignContractsFileService;
    private final IESignFlowService eSignFlowService;
    private final IOrdersService ordersService;
    private final ESignConfig eSignConfig;
    private final IUserService userService;

    @Override
    public void buildContractsFile(String orderId) throws ESignException {
        // todo... 获取订单信息
        Orders orders = ordersService.getById(orderId);
        Objects.requireNonNull(orders, "该订单不存在，请重新确认订单编号是否有误");

        checkOrderSignStatus(orders);

        // 获取学生信息
        SysUser sysUser = userService.getById(orders.getStudentId());

        //
        // 填充模板文件
        ESignDocTemplateStructureV1 structureV1 = new ESignDocTemplateStructureV1();
        structureV1.setSignDate(DateUtil.format(DateUtil.date(), "yyyy-MM-dd"));
        structureV1.setStudentName(sysUser.getUserName());
        structureV1.setParentName(sysUser.getUserName());
        structureV1.setParentPhone(sysUser.getPhonenumber());

        // todo...
        CreateFileByTemplateOutput output =  eSignContractsFileService.createFileByTemplate(this.buildContractFileByTemplate(structureV1));
        String fileId = output.getFileId();

        // 发起签署
        CreateSignFlowByFileInput.DocInfo docInfo = new CreateSignFlowByFileInput.DocInfo(fileId, "合同文件");
        CreateSignFlowByFileInput.SignFlowConfig signFlowConfig = new CreateSignFlowByFileInput.SignFlowConfig();
        signFlowConfig.setSignFlowTitle("合同签署");

        CreateSignFlowByFileInput.SignFlowConfig.RedirectConfig redirectConfig = new CreateSignFlowByFileInput.SignFlowConfig.RedirectConfig();
        redirectConfig.setRedirectUrl("http://xxxxxxxxxxxx");
        signFlowConfig.setRedirectConfig(redirectConfig);

        CreateSignFlowByFileInput.SignerInfo signerInfo = new CreateSignFlowByFileInput.SignerInfo();
        signerInfo.setSignerType(0);

        // 设置个人签署方信息
        CreateSignFlowByFileInput.PsnSignerInfo psnSignerInfo = new CreateSignFlowByFileInput.PsnSignerInfo();
        psnSignerInfo.setPsnAccount("<EMAIL>"); // 个人账号标识

        // 设置个人身份信息
        CreateSignFlowByFileInput.PsnSignerInfo.PsnInfo psnInfo = new CreateSignFlowByFileInput.PsnSignerInfo.PsnInfo();
        psnInfo.setPsnName(sysUser.getUserName()); // 个人姓名
        psnInfo.setPsnIDCardNum("110101199001011234"); // 身份证号
        psnInfo.setPsnIDCardType("CRED_PSN_CH_IDCARD"); // 身份证类型
        psnInfo.setPsnMobile(sysUser.getPhonenumber()); // 手机号
        psnSignerInfo.setPsnInfo(psnInfo);

        signerInfo.setPsnSignerInfo(psnSignerInfo);

        // 设置签署区信息
        List<CreateSignFlowByFileInput.SignFieldInfo> signFields = new ArrayList<>();
        CreateSignFlowByFileInput.SignFieldInfo signField = new CreateSignFlowByFileInput.SignFieldInfo();
        signField.setFileId(fileId); // 对应文档的文件ID
        signField.setSignFieldType(0); // 签章区

        signFields.add(signField);
        signerInfo.setSignFields(signFields);

        List<CreateSignFlowByFileInput.SignerInfo> signers = new ArrayList<>();
        signers.add(signerInfo);


        CreateSignFlowByFileInput input = new CreateSignFlowByFileInput();
        input.setSigners(signers);
        input.setDocs(List.of(docInfo));
        input.setSignFlowConfig(signFlowConfig);

        CreateSignFlowByFileOutput signFlowByFile = eSignFlowService.createSignFlowByFile(input);
        String signFlowId = signFlowByFile.getData().getSignFlowId();


        // 获取签署连接
        GetSignUrlInput signUrlInput = new GetSignUrlInput();
        signUrlInput.setSignFlowId(signFlowId);
        GetSignUrlOutput getSignUrl = eSignFlowService.getSignUrl(signUrlInput);
        String signUrl = getSignUrl.getData().getUrl();
        log.info("签署连接: {}", signUrl);
    }

    private void checkOrderSignStatus(Orders order) {
        if(!(order.getOrderStatus().equalsIgnoreCase(OrderConstants.OrderStatus.PART_PAID)
                || order.getOrderStatus().equalsIgnoreCase(OrderConstants.OrderStatus.FULL_PAID))) {
            throw new IllegalStateException("该订单未支付，暂时无法发起合同签署");
        }

        if(!order.getSignStatus().equalsIgnoreCase(OrderConstants.SignStatus.UN_SIGN)) {
            throw new IllegalStateException("该订单无需签署，或已经完成合同签署");
        }
    }


    /**
     * 填充模板控件
     * @param structureV1
     * @return
     */
    private CreateFileByTemplateInput buildContractFileByTemplate(ESignDocTemplateStructureV1 structureV1) {

        /**
         * 获取控件列表
         */
        List<EsignTemplateComponents> componentsList = eSignTemplateService.lambdaQuery()
                .eq(EsignTemplateComponents::getDocTemplateId, eSignConfig.getDocTemplateId()).list();
        if(structureV1 == null || CollUtil.isEmpty(componentsList)) {
            throw new IllegalArgumentException("填充合同模板文件有误，请确认");
        }

        List< CreateFileByTemplateInput.ComponentInfo> componentInfos = new ArrayList<>();
        Field[] fields = ReflectUtil.getFields(ESignDocTemplateStructureV1.class);
        componentsList.forEach(component -> {
            Optional<Field> fieldOpt = Arrays.stream(fields).filter(f -> f.getName().equals(component.getReflectPropertyId())).findFirst();
            Field field = fieldOpt.orElse( null);
            try {
                CreateFileByTemplateInput.ComponentInfo componentInfo = getComponentInfo(structureV1, component, field);
                componentInfos.add(componentInfo);

            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        });
        return CreateFileByTemplateInput.builder().docTemplateId(eSignConfig.getDocTemplateId())
                .components(componentInfos)
                .build();
    }

    private static CreateFileByTemplateInput.ComponentInfo getComponentInfo(ESignDocTemplateStructureV1 structureV1, EsignTemplateComponents component, Field field) throws IllegalAccessException {
        Object value = field != null ? field.get(structureV1) : null;
        if(component.getRequired().equals(Boolean.TRUE)) {  // 必填
            if(value == null) {
                throw new IllegalArgumentException("合同模板文件有必填项未填写，请确认");
            }
        }

        return new CreateFileByTemplateInput.ComponentInfo(
                component.getComponentId(),
                value != null ? value.toString() : null
        );
    }
}
