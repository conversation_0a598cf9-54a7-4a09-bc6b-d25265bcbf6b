package org.nonamespace.word.server.jobs;


import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.openai.model.enums.ModelEnum;
import org.nonamespace.word.server.service.IWordEnrichService;
import org.nonamespace.word.server.service.IWordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 单词补全定时任务
 *  - 单词释义补全，例句补全，文本补全
 *  - 音标mp3文件补全
 *  - 例句mp3音频文件补全
 */
@Slf4j
@Component("wordEnrichJobTask")
@ConditionalOnProperty(prefix = "word.enrich.job", name = "enabled", havingValue = "true", matchIfMissing = true)
public class WordEnrichJobTask extends BaseJobTask {

    @Autowired
    private IWordService wordService;
    @Autowired
    private IWordEnrichService wordEnrichService;
    @Autowired
    public WordEnrichJobTask(StringRedisTemplate stringRedisTemplate) {
        super(stringRedisTemplate);
    }

    /**
     * 单词补全， 每隔10分钟获取一次。如果前面的任务还没执行完，则等待执行结束
     */
    @Scheduled(cron ="0 */10 * * * ?")
    public void enrich() {
        log.info("单词自动补全定时任务开始");
        executeJob("wordEnrichJobTask", (Void) -> {
            try {
                wordEnrichService.enrichBasic(Set.of(), true);
                wordEnrichService.enrichMeanings(Set.of(), true);
                wordEnrichService.enrichSentences(Set.of(), true);
                wordService.enrichSentencesAudio(Set.of(), true);
                wordService.fetchVoices(Set.of(), 6, ModelEnum.CLAWCLOUD);
                log.info("单词自动补全定时任务结束");
            } catch (Exception e) {
                log.error("单词补全定时任务异常", e);
            }
        }, 1, TimeUnit.HOURS);
    }

}
