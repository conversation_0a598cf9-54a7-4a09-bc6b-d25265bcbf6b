package org.nonamespace.word.rest.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.dto.curriculum.CurriculumUsersDto;
import org.nonamespace.word.server.dto.management.teacher.TeacherListSelectDto;
import org.nonamespace.word.server.dto.management.teachinggroup.TeacherDto;
import org.nonamespace.word.server.dto.management.teachinggroup.TeachingGroupDto;
import org.nonamespace.word.server.service.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/management")
@RequiredArgsConstructor
@Slf4j
@Validated
public class TeacherManagementController extends BaseController {

    private final ITeachingGroupService teachingGroupService;
    private final ITeachingGroupMemberService teachingGroupMemberService;
    private final ITeacherManagementService teacherManagementService;
    private final IEnhancedTeachingGroupService enhancedTeachingGroupService;

    /**
     * 获取教学组列表
     */
    @GetMapping("/teaching-groups")
    public AjaxResult getTeachingGroups(TeachingGroupDto.GetListReq req) {
        try {
            IPage<TeachingGroupDto.Resp> page = teachingGroupService.selectTeachingGroupPage(req);
            return success(page);
        } catch (Exception e) {
            log.error("获取教学组列表失败", e);
            return error("获取教学组列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取教学组详情
     */
    @GetMapping("/teaching-groups/{id}")
    public AjaxResult getTeachingGroup(@PathVariable String id) {
        try {
            TeachingGroupDto.Resp resp = teachingGroupService.selectTeachingGroupById(id);
            if (resp == null) {
                return error("教学组不存在");
            }
            return success(resp);
        } catch (Exception e) {
            log.error("获取教学组详情失败", e);
            return error("获取教学组详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建教学组（同时创建对应的部门）
     */
    @PostMapping("/teaching-groups")
    public AjaxResult createTeachingGroup(@Valid @RequestBody TeachingGroupDto.CreateReq req) {
        try {
            boolean success = enhancedTeachingGroupService.createTeachingGroupWithDept(req);
            return success ? success() : error("创建教学组失败");
        } catch (Exception e) {
            log.error("创建教学组失败", e);
            return error("创建教学组失败: " + e.getMessage());
        }
    }

    /**
     * 更新教学组（同步更新对应的部门）
     */
    @PutMapping("/teaching-groups/{id}")
    public AjaxResult updateTeachingGroup(@PathVariable String id, @Valid @RequestBody TeachingGroupDto.UpdateReq req) {
        try {
            req.setId(id);
            boolean success = enhancedTeachingGroupService.updateTeachingGroupWithDept(req);
            return success ? success() : error("更新教学组失败");
        } catch (Exception e) {
            log.error("更新教学组失败", e);
            return error("更新教学组失败: " + e.getMessage());
        }
    }

    /**
     * 删除教学组（同时删除对应的部门）
     */
    @DeleteMapping("/teaching-groups/{id}")
    public AjaxResult deleteTeachingGroup(@PathVariable String id) {
        try {
            boolean success = enhancedTeachingGroupService.deleteTeachingGroupWithDept(id);
            return success ? success() : error("删除教学组失败");
        } catch (Exception e) {
            log.error("删除教学组失败", e);
            return error("删除教学组失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除教学组
     */
    @DeleteMapping("/teaching-groups")
    public AjaxResult deleteTeachingGroups(@RequestBody List<String> ids) {
        try {
            boolean success = teachingGroupService.deleteTeachingGroups(ids);
            return success ? success() : error("批量删除教学组失败");
        } catch (Exception e) {
            log.error("批量删除教学组失败", e);
            return error("批量删除教学组失败: " + e.getMessage());
        }
    }

    /**
     * 获取教学组统计信息
     */
    @GetMapping("/teaching-groups/stats")
    public AjaxResult getTeachingGroupStats() {
        try {
            TeachingGroupDto.StatsResp stats = teachingGroupService.getTeachingGroupStats();
            return success(stats);
        } catch (Exception e) {
            log.error("获取教学组统计信息失败", e);
            return error("获取教学组统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取教学组教师列表
     */
    @GetMapping("/teaching-groups/{groupId}/teachers")
    public AjaxResult getGroupTeachers(@PathVariable String groupId, TeachingGroupDto.GetGroupTeachersReq req) {
        try {
            req.setGroupId(groupId);
            IPage<TeacherDto.BasicResp> page = teachingGroupMemberService.selectGroupTeachersPage(req);
            return success(page);
        } catch (Exception e) {
            log.error("获取教学组教师列表失败", e);
            return error("获取教学组教师列表失败: " + e.getMessage());
        }
    }

    /**
     * 分配教师到教学组（包含角色检查和部门分配）
     */
    @PostMapping("/teaching-groups/{groupId}/teachers")
    public AjaxResult assignTeachers(@PathVariable String groupId, @RequestBody TeachingGroupDto.AssignTeachersReq req) {
        try {
            req.setGroupId(groupId);
            boolean success = enhancedTeachingGroupService.assignTeachersWithRoleCheck(req);
            return success ? success() : error("分配教师失败");
        } catch (Exception e) {
            log.error("分配教师失败", e);
            return error("分配教师失败: " + e.getMessage());
        }
    }

    /**
     * 从教学组移除教师（同时从部门移除）
     */
    @DeleteMapping("/teaching-groups/{groupId}/teachers")
    public AjaxResult removeTeachers(@PathVariable String groupId, @RequestBody TeachingGroupDto.RemoveTeachersReq req) {
        try {
            req.setGroupId(groupId);
            boolean success = enhancedTeachingGroupService.removeTeachersFromDept(req);
            return success ? success() : error("移除教师失败");
        } catch (Exception e) {
            log.error("移除教师失败", e);
            return error("移除教师失败: " + e.getMessage());
        }
    }

    /**
     * 获取可分配的教师列表（未分配到任何教学组的教师）
     */
    @GetMapping("/teachers/available")
    public AjaxResult getAvailableTeachers() {
        try {
            List<TeacherDto.AvailableResp> teachers = teacherManagementService.getAvailableTeachers();
            return success(teachers);
        } catch (Exception e) {
            log.error("获取可分配教师列表失败", e);
            return error("获取可分配教师列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取教师列表（支持搜索和分页）
     */
    @GetMapping("/teachers")
    public AjaxResult getTeachers(TeacherDto.GetListReq req) {
        try {
            IPage<TeacherDto.BasicResp> page = teacherManagementService.getTeachersPage(req);
            return success(page);
        } catch (Exception e) {
            log.error("获取教师列表失败", e);
            return error("获取教师列表失败: " + e.getMessage());
        }
    }


    /**
     * 获取教师列表（支持搜索和分页）
     */
    @GetMapping("/teacher/list")
    public AjaxResult teacherList(TeacherListSelectDto.Req req) {
        try {
            CurriculumUsersDto.Req curriculumUsersReq = new CurriculumUsersDto.Req();
            curriculumUsersReq.setKeyword(req.getKeyword());
            return success(teacherManagementService.teachers(curriculumUsersReq));
        } catch (Exception e) {
            log.error("获取教师列表失败", e);
            return error("获取教师列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取可选老师列表（根据权限控制）
     * 规则：1. admin和人力查询所有；2. 教学组长和教务查看所在组内老师；3. 老师只能查自己；4. 其他返回空
     */
    @GetMapping("/teacher/select-lists")
    public AjaxResult teacherSelectLists() {
        try {
            List<TeacherDto.SelectOption> teachers = teacherManagementService.teacherSelectLists();
            return success(teachers);
        } catch (Exception e) {
            log.error("获取可选老师列表失败", e);
            return error("获取可选老师列表失败: " + e.getMessage());
        }
    }


    /**
     * 获取所有教师列表（用于选择组长、教务）
     */
    @GetMapping("/teachers/all")
    public AjaxResult getAllTeachers() {
        try {
            List<TeacherDto.UserRoleResp> teachers = teacherManagementService.getAllTeachers();
            return success(teachers);
        } catch (Exception e) {
            log.error("获取所有教师列表失败", e);
            return error("获取所有教师列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取教师详细信息
     */
    @GetMapping("/teachers/{teacherId}/detail")
    public AjaxResult getTeacherDetail(@PathVariable String teacherId) {
        try {
            TeacherDto.DetailResp detail = teacherManagementService.getTeacherDetail(teacherId);
            if (detail == null) {
                return error("教师不存在");
            }
            return success(detail);
        } catch (Exception e) {
            log.error("获取教师详细信息失败", e);
            return error("获取教师详细信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取教师可上课时间
     */
    @GetMapping("/teachers/{teacherId}/time-slots")
    public AjaxResult getTeacherTimeSlots(@PathVariable String teacherId) {
        try {
            List<TeacherDto.TimeSlotResp> timeSlots = teacherManagementService.getTeacherTimeSlots(teacherId);
            return success(timeSlots);
        } catch (Exception e) {
            log.error("获取教师时间表失败", e);
            return error("获取教师时间表失败: " + e.getMessage());
        }
    }

    /**
     * 更新教师可上课时间
     */
    @PutMapping("/teachers/{teacherId}/time-slots")
    public AjaxResult updateTeacherTimeSlots(@PathVariable String teacherId, @Valid @RequestBody TeacherDto.UpdateTimeSlotsReq req) {
        try {
            req.setTeacherId(teacherId);
            boolean success = teacherManagementService.updateTeacherTimeSlotsWithPermissionCheck(req);
            return success ? success() : error("更新教师时间表失败");
        } catch (Exception e) {
            log.error("更新教师时间表失败", e);
            return error("更新教师时间表失败: " + e.getMessage());
        }
    }

    /**
     * 检查教师时间表最后更新时间
     */
    @GetMapping("/teachers/{teacherId}/time-slots/update-check")
    public AjaxResult checkTeacherTimeSlotUpdateTime(@PathVariable String teacherId) {
        try {
            TeacherDto.TimeSlotUpdateCheckResp result = teacherManagementService.checkTeacherTimeSlotUpdateTime(teacherId);
            return success(result);
        } catch (Exception e) {
            log.error("检查教师时间表更新时间失败", e);
            return error("检查教师时间表更新时间失败: " + e.getMessage());
        }
    }

    /**
     * 获取教师带教信息
     */
    @GetMapping("/teachers/{teacherId}/teaching-info")
    public AjaxResult getTeachingInfo(@PathVariable String teacherId) {
        try {
            TeacherDto.TeachingInfoResp info = teacherManagementService.getTeachingInfo(teacherId);
            return success(info);
        } catch (Exception e) {
            log.error("获取教师带教信息失败", e);
            return error("获取教师带教信息失败: " + e.getMessage());
        }
    }

    /**
     * 批量获取教师带教信息
     */
    @PostMapping("/teachers/teaching-info/batch")
    public AjaxResult getTeachingInfoBatch(@RequestBody Map<String, List<String>> request) {
        try {
            List<String> teacherIds = request.get("teacherIds");
            if (teacherIds == null || teacherIds.isEmpty()) {
                return error("教师ID列表不能为空");
            }

            List<TeacherDto.TeachingInfoResp> teachingInfoList = teacherManagementService.getTeachingInfoBatch(teacherIds);
            return success(teachingInfoList);
        } catch (Exception e) {
            log.error("批量获取教师带教信息失败", e);
            return error("批量获取教师带教信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新教师信息
     */
    @PutMapping("/teachers/{teacherId}")
    public AjaxResult updateTeacherInfo(@PathVariable String teacherId, @Valid @RequestBody TeacherDto.UpdateTeacherReq req) {
        try {
            req.setTeacherId(teacherId);
            boolean success = teacherManagementService.updateTeacherInfo(req);
            return success ? success() : error("更新教师信息失败");
        } catch (Exception e) {
            log.error("更新教师信息失败", e);
            return error("更新教师信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新教师暑期课上课时间
     */
    @PutMapping("/teachers/{teacherId}/summer-schedule")
    public AjaxResult updateTeacherSummerSchedule(@PathVariable String teacherId, @Valid @RequestBody TeacherDto.UpdateSummerScheduleReq req) {
        try {
            req.setTeacherId(teacherId);
            boolean success = teacherManagementService.updateTeacherSummerSchedule(req);
            return success ? success() : error("更新暑期课上课时间失败");
        } catch (Exception e) {
            log.error("更新暑期课上课时间失败", e);
            return error("更新暑期课上课时间失败: " + e.getMessage());
        }
    }

    /**
     * 设置教学组长（包含角色检查和分配）
     */
    @PutMapping("/teaching-groups/{groupId}/leader/{teacherId}")
    public AjaxResult setGroupLeader(@PathVariable String groupId, @PathVariable String teacherId) {
        try {
            boolean success = enhancedTeachingGroupService.setGroupLeaderWithRole(groupId, teacherId);
            return success ? success() : error("设置组长失败");
        } catch (Exception e) {
            log.error("设置组长失败", e);
            return error("设置组长失败: " + e.getMessage());
        }
    }

    /**
     * 设置教务（包含角色检查和分配）
     */
    @PutMapping("/teaching-groups/{groupId}/admin/{teacherId}")
    public AjaxResult setGroupAdmin(@PathVariable String groupId, @PathVariable String teacherId) {
        try {
            boolean success = enhancedTeachingGroupService.setGroupAdminWithRole(groupId, teacherId);
            return success ? success() : error("设置教务失败");
        } catch (Exception e) {
            log.error("设置教务失败", e);
            return error("设置教务失败: " + e.getMessage());
        }
    }

    /**
     * 删除教师
     */
    @DeleteMapping("/teachers/{teacherId}")
    public AjaxResult deleteTeacher(@PathVariable String teacherId) {
        try {
            boolean success = teacherManagementService.deleteTeacher(teacherId);
            return success ? success() : error("删除教师失败");
        } catch (Exception e) {
            log.error("删除教师失败", e);
            return error("删除教师失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除教师
     */
    @DeleteMapping("/teachers/batch-delete")
    public AjaxResult batchDeleteTeachers(@RequestBody List<String> teacherIds) {
        try {
            boolean success = teacherManagementService.batchDeleteTeachers(teacherIds);
            return success ? success() : error("批量删除教师失败");
        } catch (Exception e) {
            log.error("批量删除教师失败", e);
            return error("批量删除教师失败: " + e.getMessage());
        }
    }

    /**
     * 重置教师密码
     */
    @PutMapping("/teachers/{teacherId}/reset-password")
    @Log(title = "重置老师密码", businessType = BusinessType.UPDATE)
    public AjaxResult resetTeacherPassword(@PathVariable String teacherId) {
        try {
            boolean success = teacherManagementService.resetTeacherPassword(teacherId);
            return success ? success("重置密码成功") : error("重置密码失败");
        } catch (Exception e) {
            log.error("重置教师密码失败: teacherId={}", teacherId, e);
            return error("重置密码失败: " + e.getMessage());
        }
    }



    /**
     * 导出教学组列表
     */
    @GetMapping("/teaching-groups/export")
    public AjaxResult exportTeachingGroups(TeachingGroupDto.GetListReq req) {
        try {
            // TODO: 实现导出功能
            return success("导出功能待实现");
        } catch (Exception e) {
            log.error("导出教学组列表失败", e);
            return error("导出教学组列表失败: " + e.getMessage());
        }
    }

    /**
     * 创建教师
     */
    @PostMapping("/teachers")
    // @PreAuthorize("hasAuthority('management:teacher:create')")
    public AjaxResult createTeacher(@Valid @RequestBody TeacherDto.CreateTeacherReq req) {
        try {
            String teacherId = teacherManagementService.createTeacher(req);
            return success(teacherId);
        } catch (Exception e) {
            log.error("创建教师失败", e);
            return error(e.getMessage());
        }
    }

    /**
     * 批量导入教师
     */
    @PostMapping("/teachers/import")
    @PreAuthorize("@ss.hasPermi('management:teacher:import')")
    public AjaxResult importTeachers(@RequestBody List<TeacherDto.ImportTeacherData> teacherDataList) {
        try {
            TeacherDto.ImportResult result = teacherManagementService.importTeachers(teacherDataList);
            return success(result);
        } catch (Exception e) {
            log.error("批量导入教师失败", e);
            return error("批量导入教师失败: " + e.getMessage());
        }
    }

    /**
     * 导出教师列表
     */
    @GetMapping("/teachers/export")
    @PreAuthorize("@ss.hasPermi('management:teacher:export')")
    public AjaxResult exportTeachers(TeacherDto.GetListReq req) {
        try {
            List<TeacherDto.ExportData> exportData = teacherManagementService.exportTeachers(req);
            return success(exportData);
        } catch (Exception e) {
            log.error("导出教师列表失败", e);
            return error("导出教师列表失败: " + e.getMessage());
        }
    }

    /**
     * 更新教师状态（停课/复课）
     */
    @PutMapping("/teachers/{teacherId}/status")
    // @PreAuthorize("hasAuthority('management:teacher:status')")
    public AjaxResult updateTeacherStatus(@PathVariable String teacherId, @RequestParam String status) {
        try {
            boolean success = teacherManagementService.updateTeacherStatus(teacherId, status);
            if (success) {
                return success("更新教师状态成功");
            } else {
                return error("更新教师状态失败");
            }
        } catch (Exception e) {
            log.error("更新教师状态失败：teacherId={}, status={}", teacherId, status, e);
            return error("更新教师状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取教师课表
     */
    @GetMapping("/teachers/{teacherId}/schedule")
    public AjaxResult getTeacherSchedule(@PathVariable String teacherId,
                                       @RequestParam(required = false) String startDate,
                                       @RequestParam(required = false) String endDate) {
        try {
            List<TeacherDto.ScheduleResp> schedule = teacherManagementService.getTeacherSchedule(teacherId, startDate, endDate);
            return success(schedule);
        } catch (Exception e) {
            log.error("获取教师课表失败", e);
            return error("获取教师课表失败: " + e.getMessage());
        }
    }

    /**
     * 分配学生给教师
     */
    @PostMapping("/teachers/assign-students")
    public AjaxResult assignStudentsToTeacher(@Valid @RequestBody TeacherDto.AssignStudentsReq req) {
        try {
            boolean success = teacherManagementService.assignStudentsToTeacher(req);
            return success ? success() : error("分配学生失败");
        } catch (Exception e) {
            log.error("分配学生给教师失败", e);
            return error("分配学生给教师失败: " + e.getMessage());
        }
    }

    /**
     * 获取教师的学生列表
     */
    @GetMapping("/teachers/{teacherId}/students")
    public AjaxResult getTeacherStudents(@PathVariable String teacherId) {
        try {
            List<TeacherDto.StudentResp> students = teacherManagementService.getTeacherStudents(teacherId);
            return success(students);
        } catch (Exception e) {
            log.error("获取教师学生列表失败", e);
            return error("获取教师学生列表失败: " + e.getMessage());
        }
    }
}
