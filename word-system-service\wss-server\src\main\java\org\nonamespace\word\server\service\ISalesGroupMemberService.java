package org.nonamespace.word.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.nonamespace.word.server.domain.SalesGroup;
import org.nonamespace.word.server.domain.SalesGroupMember;

/**
 * 销售组成员数据层服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
public interface ISalesGroupMemberService extends IService<SalesGroupMember> {
    SalesGroup getSalesGroupBySalesId(String salesId);
    // 纯数据层服务，只提供基础的CRUD操作
    // 业务逻辑已移至SalesGroupFacade
}
