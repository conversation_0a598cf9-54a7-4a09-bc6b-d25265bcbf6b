package org.nonamespace.word.server.facade.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.NotImplementedException;
import org.nonamespace.word.common.misc.WssContext;
import org.nonamespace.word.server.domain.*;
import org.nonamespace.word.server.dto.management.booking.CourseBookingDto;
import org.nonamespace.word.server.facade.CurriculumFacade;
import org.nonamespace.word.server.facade.ICourseBookingFacade;
import org.nonamespace.word.server.service.*;
import org.nonamespace.word.server.util.SystemDataQueryUtil;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 预约课申请Facade实现类
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CourseBookingFacadeImpl implements ICourseBookingFacade {

    private final ICourseBookingApplicationService courseBookingApplicationService;
    private final UserStudentExtService userStudentExtService;
    private final ISaleProfileService saleProfileService;
    private final ISalesGroupService salesGroupService;
    private final ISalesGroupMemberService salesGroupMemberService;
    private final ITeacherProfileService teacherProfileService;
    private final ITeachingGroupService teachingGroupService;
    private final ITeachingGroupMemberService teachingGroupMemberService;
    private final IStudentCourseHoursService studentCourseHoursService;
    private final ITeacherStudentRelationService teacherStudentRelationService;
    private final SystemDataQueryUtil systemDataQueryUtil;
    private final IWechatNotificationService wechatNotificationService;
    private final CurriculumFacade curriculumFacade;
    private final ICourseBookingAutoProcessService courseBookingAutoProcessService;
    private final ITeacherMatchService teacherMatchService;

    @Override
    public IPage<CourseBookingDto.BasicResp> getCourseBookingPage(CourseBookingDto.GetListReq req) {
        log.info("分页查询预约课申请列表: req={}", req);

        // 应用数据权限
        applyDataPermissions(req);

        // 构建分页查询
        Page<CourseBookingApplication> page = new Page<>(req.getPageNum(), req.getPageSize());

        // 构建查询条件
        var queryWrapper = courseBookingApplicationService.lambdaQuery()
                .eq(CourseBookingApplication::getDeleted, false)
                .eq(StrUtil.isNotEmpty(req.getStudentId()), CourseBookingApplication::getStudentId, req.getStudentId())
                .eq(StrUtil.isNotEmpty(req.getSubject()), CourseBookingApplication::getSubject, req.getSubject())
                .eq(StrUtil.isNotEmpty(req.getSpecification()), CourseBookingApplication::getSpecification, req.getSpecification())
//                    .eq(StrUtil.isNotEmpty(req.getTeachingGroupId()), CourseBookingApplication::getTeachingGroupId, req.getTeachingGroupId())
                .ge(req.getCreateTimeStart() != null, CourseBookingApplication::getCreateTime, req.getCreateTimeStart())
                .le(req.getCreateTimeEnd() != null, CourseBookingApplication::getCreateTime, req.getCreateTimeEnd());

        // 处理状态查询（支持多选）
        if (req.getStatusList() != null && !req.getStatusList().isEmpty()) {
            queryWrapper.in(CourseBookingApplication::getStatus, req.getStatusList());
        } else if (StrUtil.isNotEmpty(req.getStatus())) {
            // 兼容单个状态查询
            queryWrapper.eq(CourseBookingApplication::getStatus, req.getStatus());
        }

        // 处理审核结果查询
        if (StrUtil.isNotEmpty(req.getApprovalResult())) {
            queryWrapper.eq(CourseBookingApplication::getStatus, req.getApprovalResult());
        }

        // 处理学生姓名和手机号查询（需要关联查询）
        if (StrUtil.isNotEmpty(req.getStudentName()) || StrUtil.isNotEmpty(req.getStudentPhone())) {
            applyStudentFilter(queryWrapper, req);
        }

        // 处理老师姓名和手机号查询（需要关联查询）
        if (StrUtil.isNotEmpty(req.getTeacherName()) || StrUtil.isNotEmpty(req.getTeacherPhone())) {
            applyTeacherFilter(queryWrapper, req);
        }

        // 处理教学组管理员权限过滤（新逻辑）
        String teachingGroupFilter = buildTeachingGroupManagerFilter(req);
        if (StrUtil.isNotEmpty(teachingGroupFilter)) {
            queryWrapper.apply(teachingGroupFilter);
        }

        // 处理销售相关的权限过滤
        applySalesPermissionFilter(queryWrapper, req);

        IPage<CourseBookingApplication> applicationPage = queryWrapper
                .orderByDesc(CourseBookingApplication::getCreateTime)
                .page(page);

        // 转换为响应DTO
        IPage<CourseBookingDto.BasicResp> result = convertToCourseBookingPage(applicationPage);

        log.info("查询预约课申请列表成功: total={}", result.getTotal());
        return result;

    }

    @Override
    public CourseBookingDto.DetailResp getCourseBookingDetail(String applicationId) {
        log.info("查询预约课申请详细信息: applicationId={}", applicationId);

        if (StrUtil.isEmpty(applicationId)) {
            throw new IllegalArgumentException("申请ID不能为空");
        }

        // 查询申请信息
        CourseBookingApplication application = courseBookingApplicationService.lambdaQuery()
                .eq(CourseBookingApplication::getId, applicationId)
                .eq(CourseBookingApplication::getDeleted, false)
                .one();

        if (application == null) {
            throw new RuntimeException("预约课申请不存在");
        }

        // 检查数据权限
        checkDataPermission(application);

        // 转换为详细响应DTO
        CourseBookingDto.DetailResp result = convertToCourseBookingDetail(application);

        log.info("查询预约课申请详细信息成功: subject={}", result.getSubject());
        return result;

    }

    @Override
    public String createCourseBooking(CourseBookingDto.CreateReq req) {
        log.info("创建预约课申请: req={}", req);

        // 检查创建权限
        checkCreatePermission();

        // 验证学生信息
        UserStudentExt student = validateStudent(req.getStudentId());

        validateBooking(req);

        // 验证教师信息
        validateTeachers(req.getPreferredTeachers());

        // 创建申请记录
        CourseBookingApplication application = new CourseBookingApplication();

        // 获取教学组ID
        application.setPreferredTeachingGroupIds(getTeachingGroupFromTeachers(req.getPreferredTeachers()));

        application.setId(IdUtil.getSnowflakeNextIdStr());
        application.setStudentId(req.getStudentId());
        application.setSalesId(systemDataQueryUtil.getCurrentUserId());
        application.setSalesGroupId(salesGroupMemberService.getSalesGroupBySalesId(application.getSalesId()).getId());
        application.setSubject(ObjectUtil.defaultIfNull(req.getSubject(), "英语"));
        application.setSpecification(ObjectUtil.defaultIfNull(req.getSpecification(), "单词课"));

        if (CollUtil.isNotEmpty(req.getPreferredTeachers())) {
            application.setPreferredTeachers(req.getPreferredTeachers());
        }

        if (CollUtil.isNotEmpty(req.getPreferredTimeSlots())) {
            application.setPreferredTimeSlots(BeanUtil.copyToList(req.getPreferredTimeSlots(), CourseBookingApplication.PreferredTimeSlot.class));
        }

        application.setApplicationReason(req.getApplicationReason());
        application.setStatus(CourseBookingApplication.Status.PENDING.getCode());
        application.setCreateBy(SecurityUtils.getUserId().toString());
        application.setCreateTime(WssContext.now());

        boolean success = courseBookingApplicationService.save(application);
        if (!success) {
            throw new RuntimeException("创建预约课申请失败");
        }

        // 如果自动通过审核，则取消注释以下代码
        // 自动审核
//        courseBookingAutoProcessService.processConfirmation(application, application.getPreferredTeachers().getFirst(), application.getPreferredTimeSlots().getFirst());

        // 发送微信通知给教学组长
        sendWechatNotification(application.getId(), "NEW_APPLICATION");

        log.info("创建预约课申请成功: applicationId={}", application.getId());
        return application.getId();
    }

    @Override
    public String createCourseBookingWithTrialTime(CourseBookingDto.CreateReq req) {
        log.info("创建预约课申请（支持试听课时间）: req={}", req);

        // 检查创建权限
        checkCreatePermission();

        // 验证学生信息
        UserStudentExt student = validateStudent(req.getStudentId());

        // 验证试听课时间（业务逻辑）
        validateTrialClassTime(req.getTrialClassTime());

        // 验证教师信息
        validateTeachers(req.getPreferredTeachers());

        // 验证教师试听课时间可用性（业务逻辑）
        validateTeachersTrialTimeAvailability(req.getPreferredTeachers(), req.getTrialClassTime());

        // 创建申请记录
        CourseBookingApplication application = new CourseBookingApplication();

        // 获取教学组ID
        application.setPreferredTeachingGroupIds(getTeachingGroupFromTeachers(req.getPreferredTeachers()));

        application.setId(IdUtil.getSnowflakeNextIdStr());
        application.setStudentId(req.getStudentId());
        application.setSalesId(systemDataQueryUtil.getCurrentUserId());
        application.setSalesGroupId(salesGroupMemberService.getSalesGroupBySalesId(application.getSalesId()).getId());
        application.setSubject(ObjectUtil.defaultIfNull(req.getSubject(), "英语"));
        application.setSpecification(ObjectUtil.defaultIfNull(req.getSpecification(), "单词课"));

        if (CollUtil.isNotEmpty(req.getPreferredTeachers())) {
            application.setPreferredTeachers(req.getPreferredTeachers());
        }

        // 设置试听课时间
        if (req.getTrialClassTime() != null) {
            application.setTrialClassDate(req.getTrialClassTime().getDate());
            try {
                application.setTrialClassStartTime(java.sql.Time.valueOf(req.getTrialClassTime().getStartTime() + ":00"));
                application.setTrialClassEndTime(java.sql.Time.valueOf(req.getTrialClassTime().getEndTime() + ":00"));
            } catch (Exception e) {
                throw new RuntimeException("试听课时间格式错误: " + e.getMessage());
            }
        }

        // 设置偏好时间段（可选）
        if (CollUtil.isNotEmpty(req.getPreferredTimeSlots())) {
            application.setPreferredTimeSlots(BeanUtil.copyToList(req.getPreferredTimeSlots(), CourseBookingApplication.PreferredTimeSlot.class));
        }

        application.setApplicationReason(req.getApplicationReason());
        application.setStatus(CourseBookingApplication.Status.PENDING.getCode());
        application.setCreateBy(SecurityUtils.getUserId().toString());
        application.setCreateTime(new java.util.Date());

        boolean success = courseBookingApplicationService.save(application);
        if (!success) {
            throw new RuntimeException("创建预约课申请失败");
        }

        // 发送微信通知给教学组长
        sendWechatNotification(application.getId(), "NEW_APPLICATION");

        log.info("创建预约课申请成功: applicationId={}", application.getId());
        return application.getId();
    }

    @Override
    public boolean updateCourseBooking(CourseBookingDto.UpdateReq req) {
        log.info("更新预约课申请: req={}", req);

        // 查询现有申请
        CourseBookingApplication application = courseBookingApplicationService.lambdaQuery()
                .eq(CourseBookingApplication::getId, req.getId())
                .eq(CourseBookingApplication::getDeleted, false)
                .one();

        if (application == null) {
            throw new RuntimeException("预约课申请不存在");
        }

        // 检查编辑权限
        checkEditPermission(application);

        // 只有待确认状态的申请才能编辑
        if (!CourseBookingApplication.Status.PENDING.getCode().equals(application.getStatus())) {
            throw new RuntimeException("只有待确认状态的申请才能编辑");
        }

        // 验证教师信息
        validateTeachers(req.getPreferredTeachers());

        // 获取教学组ID
        application.setPreferredTeachingGroupIds(getTeachingGroupFromTeachers(req.getPreferredTeachers()));

        // 更新申请信息
        application.setSubject(req.getSubject());
        application.setSpecification(req.getSpecification());
//            application.setPreferredTeachers(req.getPreferredTeachers().toArray(new String[0]));
//            application.setPreferredTimeSlots(convertTimeSlots(req.getPreferredTimeSlots()));
        application.setApplicationReason(req.getApplicationReason());
        application.setUpdateBy(SecurityUtils.getUserId().toString());
        application.setUpdateTime(WssContext.now());

        boolean success = courseBookingApplicationService.updateById(application);

        log.info("更新预约课申请成功: applicationId={}", req.getId());
        return success;

    }

    @Override
    public boolean deleteCourseBooking(String applicationId) {
        log.info("删除预约课申请: applicationId={}", applicationId);

        // 查询申请信息
        CourseBookingApplication application = courseBookingApplicationService.lambdaQuery()
                .eq(CourseBookingApplication::getId, applicationId)
                .eq(CourseBookingApplication::getDeleted, false)
                .one();

        if (application == null) {
            throw new RuntimeException("预约课申请不存在");
        }

        // 检查删除权限
        checkDeletePermission(application);

        // 软删除
        boolean success = courseBookingApplicationService.lambdaUpdate()
                .set(CourseBookingApplication::getDeleted, true)
                .set(CourseBookingApplication::getUpdateBy, SecurityUtils.getUserId().toString())
                .set(CourseBookingApplication::getUpdateTime, WssContext.now())
                .eq(CourseBookingApplication::getId, applicationId)
                .update();

        log.info("删除预约课申请成功: applicationId={}", applicationId);
        return success;

    }

    @Override
    public boolean deleteCourseBookings(List<String> applicationIds) {
            log.info("批量删除预约课申请: applicationIds={}", applicationIds);

            // 检查每个申请的删除权限
            for (String applicationId : applicationIds) {
                CourseBookingApplication application = courseBookingApplicationService.lambdaQuery()
                        .eq(CourseBookingApplication::getId, applicationId)
                        .eq(CourseBookingApplication::getDeleted, false)
                        .one();

                if (application != null) {
                    checkDeletePermission(application);
                }
            }

            // 批量软删除
            boolean success = courseBookingApplicationService.lambdaUpdate()
                    .set(CourseBookingApplication::getDeleted, true)
                    .set(CourseBookingApplication::getUpdateBy, SecurityUtils.getUserId().toString())
                    .set(CourseBookingApplication::getUpdateTime, WssContext.now())
                    .in(CourseBookingApplication::getId, applicationIds)
                    .update();

            log.info("批量删除预约课申请成功: count={}", applicationIds.size());
            return success;

    }

    /**
     * 构建教学组管理员权限过滤条件（新逻辑）
     */
    private String buildTeachingGroupManagerFilter(CourseBookingDto.GetListReq req) {
        if (StrUtil.isEmpty(req.getTeachingGroupManagerUserId())) {
            return null;
        }

        try {
            // 获取该教学组管理员管理的所有教学组
            List<TeachingGroup> managedGroups = teachingGroupService.lambdaQuery()
                    .and(wrapper -> wrapper.eq(TeachingGroup::getLeaderId, req.getTeachingGroupManagerUserId())
                            .or()
                            .eq(TeachingGroup::getAdminId, req.getTeachingGroupManagerUserId()))
                    .eq(TeachingGroup::getDeleted, false)
                    .list();

            if (managedGroups.isEmpty()) {
                // 如果不管理任何教学组，则查询不到任何结果
                return "id = 'NONE'";
            }

            // 获取所有管理的教学组的老师ID
            List<String> allTeacherIds = new ArrayList<>();
            for (TeachingGroup group : managedGroups) {
                List<String> groupTeacherIds = teachingGroupMemberService.lambdaQuery()
                        .eq(TeachingGroupMember::getGroupId, group.getId())
                        .eq(TeachingGroupMember::getDeleted, false)
                        .eq(TeachingGroupMember::getStatus, "active")
                        .list()
                        .stream()
                        .map(TeachingGroupMember::getTeacherId)
                        .collect(Collectors.toList());
                allTeacherIds.addAll(groupTeacherIds);
            }

            if (allTeacherIds.isEmpty()) {
                // 如果管理的教学组都没有老师，则查询不到任何结果
                return "id = 'NONE'";
            }

            // 使用PostgreSQL数组操作符过滤申请
            String teacherIdsStr = allTeacherIds.stream()
                    .map(id -> "'" + id + "'::varchar")
                    .collect(Collectors.joining(","));

            String filterCondition = "preferred_teachers && ARRAY[" + teacherIdsStr + "]::varchar[]";

            log.info("构建教学组管理员权限过滤条件: userId={}, managedGroups={}, teacherIds={}",
                    req.getTeachingGroupManagerUserId(), managedGroups.size(), allTeacherIds.size());

            return filterCondition;

        } catch (Exception e) {
            log.error("构建教学组管理员权限过滤条件失败: userId={}", req.getTeachingGroupManagerUserId(), e);
            // 出错时不显示任何结果
            return "id = 'NONE'";
        }
    }

    /**
     * 应用数据权限
     */
    private void applyDataPermissions(CourseBookingDto.GetListReq req) {
        // 根据用户角色应用数据权限
        if (systemDataQueryUtil.isAdminOrHr()) {
            // 管理员和HR可以查看所有申请
            return;
        }

        if (systemDataQueryUtil.isTeacherGroupManager()) {
            // 教学组长可以查看本组的申请
            String currentUserId = SecurityUtils.getUserId().toString();

            // 查询当前用户管理的教学组
            List<TeachingGroup> managedGroups = teachingGroupService.lambdaQuery()
                    .and(wrapper -> wrapper.eq(TeachingGroup::getLeaderId, currentUserId)
                            .or()
                            .eq(TeachingGroup::getAdminId, currentUserId))
                    .eq(TeachingGroup::getDeleted, false)
                    .list();

            if (managedGroups.isEmpty()) {
                // 如果不是任何组的管理员，则没有权限查看
                req.setTeachingGroupId("NONE");
                return;
            }

            // 设置教学组管理员标记，在查询时使用新的逻辑
            req.setTeachingGroupManagerUserId(currentUserId);
            return;
        }

        if (systemDataQueryUtil.isSalesDirector()) {
            // 销售总监可以查看所有销售的申请
            return;
        }

        if (systemDataQueryUtil.isSalesGroupLeader()) {
            // 销售组长可以查看本组销售的申请
            String currentUserId = SecurityUtils.getUserId().toString();

            // 查询当前用户管理的销售组
            List<SalesGroup> managedGroups = salesGroupService.lambdaQuery()
                    .eq(SalesGroup::getLeaderId, currentUserId)
                    .eq(SalesGroup::getDeleted, false)
                    .list();

            if (managedGroups.isEmpty()) {
                // 如果不是任何组的组长，则没有权限查看
                req.setSalesId("NONE");
                return;
            }

            // 获取本组所有成员的ID列表
            List<String> groupMemberIds = new ArrayList<>();
            for (SalesGroup group : managedGroups) {
                // 兼容状态值：active 或 0（历史数据）
                List<SalesGroupMember> members = salesGroupMemberService.lambdaQuery()
                        .eq(SalesGroupMember::getGroupId, group.getId())
                        .eq(SalesGroupMember::getDeleted, false)
                        .and(wrapper -> wrapper
                                .eq(SalesGroupMember::getStatus, "active")
                                .or()
                                .eq(SalesGroupMember::getStatus, "0")
                        )
                        .list();

                for (SalesGroupMember member : members) {
                    groupMemberIds.add(member.getSalesId());
                }
            }

            if (groupMemberIds.isEmpty()) {
                // 如果组内没有成员，则没有申请可查看
                req.setSalesId("NONE");
                return;
            }

            // 设置销售组过滤条件 - 这里需要在查询中处理多个销售ID的情况
            // 暂时设置第一个组的ID，实际查询时会在lambdaQuery中处理成员ID列表
            if (managedGroups.size() == 1) {
                req.setSalesGroupId(managedGroups.get(0).getId());
            }
            return;
        }

        if (systemDataQueryUtil.isSales()) {
            // 销售只能查看自己的申请
            String currentUserId = SecurityUtils.getUserId().toString();
            req.setSalesId(currentUserId);
            return;
        }

        // 其他角色没有权限查看申请
        throw new RuntimeException("没有权限查看预约课申请");
    }

    /**
     * 应用销售权限过滤
     */
    private void applySalesPermissionFilter(com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper<CourseBookingApplication> queryWrapper,
                                            CourseBookingDto.GetListReq req) {
        // 如果已经设置了salesId，直接使用
        if (StrUtil.isNotEmpty(req.getSalesId())) {
            queryWrapper.eq(CourseBookingApplication::getSalesId, req.getSalesId());
            return;
        }

        // 如果设置了salesGroupId，需要查询该组的所有成员
        if (StrUtil.isNotEmpty(req.getSalesGroupId())) {
            // 兼容状态值：active 或 0（历史数据）
            List<SalesGroupMember> members = salesGroupMemberService.lambdaQuery()
                    .eq(SalesGroupMember::getGroupId, req.getSalesGroupId())
                    .eq(SalesGroupMember::getDeleted, false)
                    .and(wrapper -> wrapper
                            .eq(SalesGroupMember::getStatus, "active")
                            .or()
                            .eq(SalesGroupMember::getStatus, "0")
                    )
                    .list();

            if (members.isEmpty()) {
                // 如果组内没有成员，返回空结果
                queryWrapper.eq(CourseBookingApplication::getSalesId, "NONE");
            } else {
                // 查询组内所有成员的申请
                List<String> memberIds = members.stream()
                        .map(SalesGroupMember::getSalesId)
                        .collect(Collectors.toList());
                queryWrapper.in(CourseBookingApplication::getSalesId, memberIds);
            }
        }
    }

    @Override
    public boolean approveCourseBooking(CourseBookingDto.ApprovalReq req) {
        throw new NotImplementedException("确认预约课申请功能尚未实现");
    }

    @Override
    public boolean rejectCourseBooking(CourseBookingDto.RejectionReq req) {
        throw new NotImplementedException("拒绝预约课申请功能尚未实现");
    }

    @Override
    public boolean cancelCourseBooking(String applicationId) {
        try {
            log.info("取消预约课申请: applicationId={}", applicationId);

            // 查询申请信息
            CourseBookingApplication application = courseBookingApplicationService.lambdaQuery()
                    .eq(CourseBookingApplication::getId, applicationId)
                    .eq(CourseBookingApplication::getDeleted, false)
                    .one();

            if (application == null) {
                throw new RuntimeException("预约课申请不存在");
            }

            // 检查取消权限
            checkCancelPermission(application);

            // 只有待确认状态的申请才能取消
            if (!CourseBookingApplication.Status.PENDING.getCode().equals(application.getStatus())) {
                throw new RuntimeException("只有待确认状态的申请才能取消");
            }

            // 更新申请状态
            application.setStatus(CourseBookingApplication.Status.CANCELLED.getCode());
            application.setUpdateBy(SecurityUtils.getUserId().toString());
            application.setUpdateTime(WssContext.now());

            boolean success = courseBookingApplicationService.updateById(application);

            log.info("取消预约课申请成功: applicationId={}", applicationId);
            return success;
        } catch (Exception e) {
            log.error("取消预约课申请失败", e);
            throw new RuntimeException("取消预约课申请失败: " + e.getMessage());
        }
    }

    @Override
    public CourseBookingDto.StatsResp getCourseBookingStats() {
        try {
            log.info("获取预约课申请统计信息");

            // 应用数据权限过滤
            CourseBookingDto.GetListReq filterReq = new CourseBookingDto.GetListReq();
            applyDataPermissions(filterReq);

            // TODO: 实现统计逻辑
            // 这里需要根据权限过滤条件统计申请数据

            CourseBookingDto.StatsResp result = new CourseBookingDto.StatsResp();
            result.setTotalApplications(0L);
            result.setPendingApplications(0L);
            result.setApprovedApplications(0L);
            result.setRejectedApplications(0L);
            result.setApprovalRate(0.0);
            result.setMonthlyApplications(0L);
            result.setMonthlyApprovals(0L);
            result.setAverageProcessingTime(0.0);

            log.info("获取预约课申请统计信息成功: {}", result);
            return result;
        } catch (Exception e) {
            log.error("获取预约课申请统计信息失败", e);
            throw new RuntimeException("获取预约课申请统计信息失败: " + e.getMessage());
        }
    }

    @Override
    public List<CourseBookingDto.AvailableTeacherResp> getAvailableTeachers(CourseBookingDto.AvailableTeachersReq req) {
        throw new NotImplementedException("查询可选教师列表功能尚未实现");
    }

    @Override
    public List<CourseBookingDto.TeachingGroupOptionResp> getTeachingGroupOptions() {
        try {
            log.info("获取教学组选项");

            // 检查权限
            checkViewPermission();

            List<TeachingGroup> groups = teachingGroupService.lambdaQuery()
                    .eq(TeachingGroup::getDeleted, false)
                    .eq(TeachingGroup::getStatus, "active")
                    .orderByAsc(TeachingGroup::getName)
                    .list();

            List<CourseBookingDto.TeachingGroupOptionResp> result = groups.stream()
                    .map(this::convertToTeachingGroupOption)
                    .collect(Collectors.toList());

            log.info("获取教学组选项成功: count={}", result.size());
            return result;
        } catch (Exception e) {
            log.error("获取教学组选项失败", e);
            throw new RuntimeException("获取教学组选项失败: " + e.getMessage());
        }
    }

    @Override
    public Long getPendingApplicationCount(String teachingGroupId) {
        try {
            log.info("获取待确认申请数量: teachingGroupId={}", teachingGroupId);

            // 检查权限
            checkViewPermission();

            var query = courseBookingApplicationService.lambdaQuery()
                    .eq(CourseBookingApplication::getDeleted, false)
                    .eq(CourseBookingApplication::getStatus, CourseBookingApplication.Status.PENDING.getCode());

//            if (StrUtil.isNotEmpty(teachingGroupId)) {
//                query.eq(CourseBookingApplication::getTeachingGroupId, teachingGroupId);
//            }

            long count = query.count();

            log.info("获取待确认申请数量成功: count={}", count);
            return count;
        } catch (Exception e) {
            log.error("获取待确认申请数量失败", e);
            throw new RuntimeException("获取待确认申请数量失败: " + e.getMessage());
        }
    }

    @Override
    public boolean sendWechatNotification(String applicationId, String notificationType) {
        try {
            log.info("发送微信通知: applicationId={}, notificationType={}", applicationId, notificationType);

            // 使用微信通知服务发送通知
            boolean success = wechatNotificationService.sendCourseBookingNotification(applicationId, notificationType);

            if (success) {
                log.info("发送微信通知成功: applicationId={}, notificationType={}", applicationId, notificationType);
            } else {
                log.warn("发送微信通知失败: applicationId={}, notificationType={}", applicationId, notificationType);
            }

            return success;
        } catch (Exception e) {
            log.error("发送微信通知失败", e);
            // 微信通知失败不影响主要业务流程
            return false;
        }
    }

    /**
     * 转换为教学组选项响应
     */
    private CourseBookingDto.TeachingGroupOptionResp convertToTeachingGroupOption(TeachingGroup group) {
        CourseBookingDto.TeachingGroupOptionResp result = new CourseBookingDto.TeachingGroupOptionResp();
        result.setId(group.getId());
        result.setName(group.getName());
        result.setStatus(group.getStatus());

        // 填充组长信息
        if (StrUtil.isNotEmpty(group.getLeaderId())) {
            TeacherProfile leader = teacherProfileService.lambdaQuery()
                    .eq(TeacherProfile::getTeacherId, group.getLeaderId())
                    .eq(TeacherProfile::getDeleted, false)
                    .one();
            if (leader != null) {
                result.setLeaderName(leader.getRealName());
            }
        }

        // 填充管理员信息
        if (StrUtil.isNotEmpty(group.getAdminId())) {
            TeacherProfile admin = teacherProfileService.lambdaQuery()
                    .eq(TeacherProfile::getTeacherId, group.getAdminId())
                    .eq(TeacherProfile::getDeleted, false)
                    .one();
            if (admin != null) {
                result.setAdminName(admin.getRealName());
            }
        }

        // 统计成员数量
        long memberCount = teachingGroupMemberService.lambdaQuery()
                .eq(TeachingGroupMember::getGroupId, group.getId())
                .eq(TeachingGroupMember::getDeleted, false)
                .count();
        result.setMemberCount((int) memberCount);

        return result;
    }

    /**
     * 验证是否已经申请过
     */
    private void validateBooking(CourseBookingDto.CreateReq req) {

        CourseBookingApplication cba = courseBookingApplicationService.lambdaQuery()
                .eq(CourseBookingApplication::getStudentId, req.getStudentId())
//                .eq(CourseBookingApplication::getSubject, req.getSubject())
                .in(CourseBookingApplication::getStatus, "待审核", "已通过")
                .orderByDesc(CourseBookingApplication::getCreateTime)
                .last(" limit 1 ").one();
        if (cba != null) {
            if(cba.getStatus().equals("已通过")){
                throw new RuntimeException(StrUtil.format("已存在预约课申请，当前状态为【{}】,请勿重复申请", cba.getStatus()));
            }else if(cba.getStatus().equals("待审核")){
                courseBookingApplicationService.lambdaUpdate()
                        .set(CourseBookingApplication::getStatus, "已取消")
                        .set(CourseBookingApplication::getDeleted, true)
                        .set(CourseBookingApplication::getUpdateBy, systemDataQueryUtil.getCurrentUserId())
                        .set(CourseBookingApplication::getUpdateTime, WssContext.now())
                        .eq(CourseBookingApplication::getId, cba.getId())
                        .update();
            }
        }

        // 验证预约时间限制
        validateBookingTimeConstraints(req);
    }

    /**
     * 验证预约时间限制
     * 规则：
     * 1. 当天14点前，可约次日6点以后的课
     * 2. 当天14点以后，可约次日14点以后的课
     */
    private void validateBookingTimeConstraints(CourseBookingDto.CreateReq req) {
        if (req.getPreferredTimeSlots() == null || req.getPreferredTimeSlots().isEmpty()) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        LocalTime currentTime = now.toLocalTime();

        req.getPreferredTimeSlots().sort(Comparator.comparing(CourseBookingDto.PreferredTimeSlot::getWeekday).thenComparing(CourseBookingDto.PreferredTimeSlot::getStartTime));

        CourseBookingDto.PreferredTimeSlot timeSlot = req.getPreferredTimeSlots().getFirst();
        Integer weekday = timeSlot.getWeekday();
        if (weekday == null || weekday < 1 || weekday > 7) {
            throw new RuntimeException("请选择有效的星期");
        }

        // 如果是当天
        if (now.getDayOfWeek().getValue() == weekday) {
            // 如果是今天，获取当前时间
            throw new RuntimeException("不能预约当天的课程");
        }

        // 获取从今天开始的下一个指定星期的日期
        LocalDate nextPreferredDate = now.toLocalDate().with(TemporalAdjusters.next(DayOfWeek.of(weekday)));

        // 定义次日的日期
        LocalDate tomorrow = now.toLocalDate().plusDays(1);

        // 只有“次日”的预约才应用此特殊规则，更往后的日期则不受此限制
        if (nextPreferredDate.isEqual(tomorrow)) {
            // 定义规则的时间边界：14:00
            final LocalTime ruleBoundaryTime = LocalTime.of(14, 0);
            // 获取用户期望的预约时间
            final LocalTime preferredStartTime = LocalTime.parse(timeSlot.getStartTime());

            // 规则1：当前时间在14点之前
            if (currentTime.isBefore(ruleBoundaryTime)) {
                // 定义允许的最早预约时间：06:00
                final LocalTime earliestAllowedTime = LocalTime.of(6, 0);
                if (preferredStartTime.isBefore(earliestAllowedTime)) {
                    throw new RuntimeException("当前时间14点前，只能预约次日6点以后的课程");
                }
            }
            // 规则2：当前时间在14点（含）之后
            else {
                // 定义允许的最早预约时间：14:00
                final LocalTime earliestAllowedTime = LocalTime.of(14, 0);
                if (preferredStartTime.isBefore(earliestAllowedTime)) {
                    throw new RuntimeException("当前时间14点后，只能预约次日14点以后的课程");
                }
            }
        }



    }

    /**
     * 验证学生信息
     */
    private UserStudentExt validateStudent(String studentId) {
        UserStudentExt student = userStudentExtService.lambdaQuery()
                .eq(UserStudentExt::getStudentId, studentId)
                .eq(UserStudentExt::getDeleted, false)
                .one();

        if (student == null) {
            throw new RuntimeException("学生不存在");
        }

        return student;
    }

    /**
     * 验证教师信息
     */
    private void validateTeachers(List<String> teacherIds) {
        if (teacherIds == null || teacherIds.isEmpty()) {
            throw new RuntimeException("首选教师不能为空");
        }

        if (teacherIds.size() > 3) {
            throw new RuntimeException("首选教师最多只能选择3个");
        }

        for (String teacherId : teacherIds) {
            TeacherProfile teacher = teacherProfileService.lambdaQuery()
                    .eq(TeacherProfile::getTeacherId, teacherId)
                    .eq(TeacherProfile::getDeleted, false)
                    .one();

            if (teacher == null) {
                throw new RuntimeException("教师不存在: " + teacherId);
            }

            if (!"active".equals(teacher.getStatus())) {
                throw new RuntimeException("教师状态不可用: " + teacher.getRealName());
            }
        }
    }

    /**
     * 从教师列表获取教学组ID（取第一个教师的教学组）
     */
    private List<String> getTeachingGroupFromTeachers(List<String> teacherIds) {
        if (teacherIds == null || teacherIds.isEmpty()) {
            return null;
        }

        // 获取第一个教师的教学组（移除同一教学组限制）
        String firstTeacherId = teacherIds.get(0);
        List<TeachingGroupMember> members = teachingGroupMemberService.lambdaQuery()
                .eq(TeachingGroupMember::getTeacherId, firstTeacherId)
                .eq(TeachingGroupMember::getDeleted, false)
                .list();

        if (CollUtil.isNotEmpty(members)) {
            return members.stream().map(TeachingGroupMember::getGroupId).distinct().collect(Collectors.toList());
        }

        return new ArrayList<>();
    }

    /**
     * 获取当前销售ID
     */
    private String getCurrentSalesId(UserStudentExt student) {
        // 如果学生已分配销售，使用学生的销售
        if (StrUtil.isNotEmpty(student.getSalesId())) {
            return student.getSalesId();
        }

        // 如果当前用户是销售，使用当前用户
        if (systemDataQueryUtil.isSales()) {
            return SecurityUtils.getUserId().toString();
        }

        // 其他情况返回null
        return null;
    }

    /**
     * 检查数据权限
     */
    private void checkDataPermission(CourseBookingApplication application) {
        if (systemDataQueryUtil.isAdminOrHr()) {
            return;
        }

        String currentUserId = SecurityUtils.getUserId().toString();

        if (systemDataQueryUtil.isTeacherGroupManager()) {
            // 教学组长检查是否是本组的申请

            boolean isGroupManager = teachingGroupService.lambdaQuery()
                    .in(CollUtil.isNotEmpty(application.getPreferredTeachingGroupIds()), TeachingGroup::getId, application.getPreferredTeachingGroupIds())
                    .and(wrapper -> wrapper.eq(TeachingGroup::getLeaderId, currentUserId)
                            .or()
                            .eq(TeachingGroup::getAdminId, currentUserId))
                    .eq(TeachingGroup::getDeleted, false)
                    .exists();

            if (!isGroupManager) {
                throw new RuntimeException("没有权限查看该申请");
            }
            return;
        }

        if (systemDataQueryUtil.isSalesDirector()) {
            return;
        }

        if (systemDataQueryUtil.isSalesGroupLeader()) {
            // 销售组长检查是否是本组的申请
            if (StrUtil.isEmpty(application.getSalesGroupId())) {
                throw new RuntimeException("没有权限查看该申请");
            }

            boolean isGroupLeader = salesGroupService.lambdaQuery()
                    .eq(SalesGroup::getId, application.getSalesGroupId())
                    .eq(SalesGroup::getLeaderId, currentUserId)
                    .eq(SalesGroup::getDeleted, false)
                    .exists();

            if (!isGroupLeader) {
                throw new RuntimeException("没有权限查看该申请");
            }
            return;
        }

        if (systemDataQueryUtil.isSales()) {
            // 销售检查是否是自己的申请
            if (!currentUserId.equals(application.getSalesId())) {
                throw new RuntimeException("没有权限查看该申请");
            }
            return;
        }

        throw new RuntimeException("没有权限查看该申请");
    }

    /**
     * 检查创建权限
     */
    private void checkCreatePermission() {
        if (systemDataQueryUtil.isAdminOrHr() ||
                systemDataQueryUtil.isSalesDirector() ||
                systemDataQueryUtil.isSalesGroupLeader() ||
                systemDataQueryUtil.isSales()) {
            return;
        }
        throw new RuntimeException("没有权限创建预约课申请");
    }

    /**
     * 检查编辑权限
     */
    private void checkEditPermission(CourseBookingApplication application) {
        checkDataPermission(application);

        // 只有销售相关角色可以编辑申请
        if (!systemDataQueryUtil.isSales() &&
                !systemDataQueryUtil.isSalesGroupLeader() &&
                !systemDataQueryUtil.isSalesDirector() &&
                !systemDataQueryUtil.isAdminOrHr()) {
            throw new RuntimeException("没有权限编辑预约课申请");
        }
    }

    /**
     * 检查删除权限
     */
    private void checkDeletePermission(CourseBookingApplication application) {
        if (systemDataQueryUtil.isAdminOrHr() || systemDataQueryUtil.isSalesDirector()) {
            return;
        }

        // 销售组长和销售不能删除申请，只能取消
        throw new RuntimeException("没有权限删除预约课申请");
    }

    /**
     * 检查确认权限
     */
    private void checkApprovalPermission(CourseBookingApplication application) {
        if (systemDataQueryUtil.isAdminOrHr()) {
            return;
        }

        // 只有教学组长和管理员可以确认申请
        if (systemDataQueryUtil.isTeacherGroupManager()) {
            checkDataPermission(application);
            return;
        }

        throw new RuntimeException("没有权限确认预约课申请");
    }

    /**
     * 检查取消权限
     */
    private void checkCancelPermission(CourseBookingApplication application) {
        checkDataPermission(application);

        // 销售相关角色可以取消自己的申请
        if (systemDataQueryUtil.isSales() ||
                systemDataQueryUtil.isSalesGroupLeader() ||
                systemDataQueryUtil.isSalesDirector() ||
                systemDataQueryUtil.isAdminOrHr()) {
            return;
        }

        throw new RuntimeException("没有权限取消预约课申请");
    }

    /**
     * 检查查看权限
     */
    private void checkViewPermission() {
        if (systemDataQueryUtil.isAdminOrHr() ||
                systemDataQueryUtil.isSalesDirector() ||
                systemDataQueryUtil.isSalesGroupLeader() ||
                systemDataQueryUtil.isSales() ||
                systemDataQueryUtil.isTeacherGroupManager()) {
            return;
        }
        throw new RuntimeException("没有权限查看");
    }

    /**
     * 验证确认教师
     */
    private void validateApprovedTeacher(String teacherId, CourseBookingApplication application) {
        // 检查教师是否在首选教师列表中
        if (application.getPreferredTeachers() != null) {
            boolean isPreferred = application.getPreferredTeachers().contains(teacherId);
            if (!isPreferred) {
                throw new RuntimeException("确认的教师必须在首选教师列表中");
            }
        }

        // 检查教师状态
        TeacherProfile teacher = teacherProfileService.lambdaQuery()
                .eq(TeacherProfile::getTeacherId, teacherId)
                .eq(TeacherProfile::getDeleted, false)
                .one();

        if (teacher == null) {
            throw new RuntimeException("教师不存在");
        }

        if (!"active".equals(teacher.getStatus())) {
            throw new RuntimeException("教师状态不可用");
        }
    }

    /**
     * 分配学生给教师
     */
    private void assignStudentToTeacher(String studentId, String teacherId) {
        try {
            // 通过teacher_student_relation表建立师生关系
            // 首先检查是否已存在关系
            boolean exists = teacherStudentRelationService.lambdaQuery()
                    .eq(TeacherStudentRelation::getStudentId, studentId)
                    .eq(TeacherStudentRelation::getTeacherId, teacherId)
                    .eq(TeacherStudentRelation::getDeleted, false)
                    .exists();

            if (!exists) {
                // 创建新的师生关系
                TeacherStudentRelation relation = new TeacherStudentRelation();
                relation.setId(IdUtil.getSnowflakeNextIdStr());
                relation.setStudentId(studentId);
                relation.setTeacherId(teacherId);
                relation.setRelationType("teaching");
                relation.setSubject("英语"); // 默认英语，实际应该从申请中获取
                relation.setSpecification("一对一"); // 默认一对一，实际应该从申请中获取
                relation.setStartDate(WssContext.now());
                relation.setStatus("active");
                relation.setCreateBy(SecurityUtils.getUserId().toString());
                relation.setCreateTime(WssContext.now());

                teacherStudentRelationService.save(relation);
            } else {
                // 如果关系已存在，更新为活跃状态
                teacherStudentRelationService.lambdaUpdate()
                        .set(TeacherStudentRelation::getStatus, "active")
                        .set(TeacherStudentRelation::getStartDate, WssContext.now())
                        .set(TeacherStudentRelation::getUpdateBy, SecurityUtils.getUserId().toString())
                        .set(TeacherStudentRelation::getUpdateTime, WssContext.now())
                        .eq(TeacherStudentRelation::getStudentId, studentId)
                        .eq(TeacherStudentRelation::getTeacherId, teacherId)
                        .update();
            }

            log.info("分配学生给教师成功: studentId={}, teacherId={}", studentId, teacherId);
        } catch (Exception e) {
            log.error("分配学生给教师失败: studentId={}, teacherId={}", studentId, teacherId, e);
            throw new RuntimeException("分配学生给教师失败: " + e.getMessage());
        }
    }

    /**
     * 转换为预约课申请分页响应
     */
    private IPage<CourseBookingDto.BasicResp> convertToCourseBookingPage(IPage<CourseBookingApplication> applicationPage) {
        Page<CourseBookingDto.BasicResp> result = new Page<>(applicationPage.getCurrent(), applicationPage.getSize(), applicationPage.getTotal());

        List<CourseBookingDto.BasicResp> records = applicationPage.getRecords().stream()
                .map(this::convertToCourseBookingBasic)
                .collect(Collectors.toList());

        result.setRecords(records);
        return result;
    }

    /**
     * 转换为预约课申请基础响应
     */
    private CourseBookingDto.BasicResp convertToCourseBookingBasic(CourseBookingApplication application) {
        CourseBookingDto.BasicResp result = new CourseBookingDto.BasicResp();
        result.setId(application.getId());
        result.setStudentId(application.getStudentId());
        result.setSalesId(application.getSalesId());
        result.setSalesGroupId(application.getSalesGroupId());
        result.setSubject(application.getSubject());
        result.setSpecification(application.getSpecification());
//        result.setTeachingGroupId(application.getTeachingGroupId());
        result.setApplicationReason(application.getApplicationReason());
        result.setStatus(application.getStatus());
        result.setStatusText(getStatusText(application.getStatus()));
        result.setApprovedTeacherId(application.getApprovedTeacherId());
        result.setApprovalTime(application.getApprovalTime());
        result.setApprovalBy(application.getApprovalBy());
        result.setRejectionReason(application.getRejectionReason());
        result.setCourseHoursPackageId(application.getCourseHoursPackageId());
        result.setCreateTime(application.getCreateTime());
        result.setUpdateTime(application.getUpdateTime());

        // 转换首选时间段
        if (application.getPreferredTimeSlots() != null) {
//            result.setPreferredTimeSlots(convertFromTimeSlots(application.getPreferredTimeSlots()));
        }

        // 转换首选教师信息
        if (application.getPreferredTeachers() != null) {
//            result.setPreferredTeacherInfos(convertToTeacherInfos(application.getPreferredTeachers()));
        }

        // 填充关联信息
        enrichCourseBookingInfo(result);

        return result;
    }

    /**
     * 转换为预约课申请详细响应
     */
    private CourseBookingDto.DetailResp convertToCourseBookingDetail(CourseBookingApplication application) {
        CourseBookingDto.DetailResp result = new CourseBookingDto.DetailResp();

        // 复制基础信息
        CourseBookingDto.BasicResp basic = convertToCourseBookingBasic(application);
        result.setId(basic.getId());
        result.setStudentId(basic.getStudentId());
        result.setStudentName(basic.getStudentName());
        result.setStudentPhone(basic.getStudentPhone());
        result.setSalesId(basic.getSalesId());
        result.setSalesName(basic.getSalesName());
        result.setSalesGroupId(basic.getSalesGroupId());
        result.setSalesGroupName(basic.getSalesGroupName());
        result.setSubject(basic.getSubject());
        result.setSpecification(basic.getSpecification());
        result.setPreferredTeacherInfos(basic.getPreferredTeacherInfos());
        result.setTeachingGroupId(basic.getTeachingGroupId());
        result.setTeachingGroupName(basic.getTeachingGroupName());
        result.setTeachingGroupLeaderName(basic.getTeachingGroupLeaderName());
        result.setPreferredTimeSlots(basic.getPreferredTimeSlots());
        result.setApplicationReason(basic.getApplicationReason());
        result.setStatus(basic.getStatus());
        result.setStatusText(basic.getStatusText());
        result.setApprovedTeacherId(basic.getApprovedTeacherId());
        result.setApprovedTeacherName(basic.getApprovedTeacherName());
        result.setApprovalTime(basic.getApprovalTime());
        result.setApprovalBy(basic.getApprovalBy());
        result.setApprovalByName(basic.getApprovalByName());
        result.setRejectionReason(basic.getRejectionReason());
        result.setCourseHoursPackageId(basic.getCourseHoursPackageId());
        result.setCreateTime(basic.getCreateTime());
        result.setUpdateTime(basic.getUpdateTime());

        // 设置详细信息特有字段
        result.setCreateBy(application.getCreateBy());
        result.setUpdateBy(application.getUpdateBy());

        return result;
    }

    /**
     * 填充预约课申请关联信息
     */
    private void enrichCourseBookingInfo(CourseBookingDto.BasicResp result) {
        try {
            // 填充学生信息
            if (StrUtil.isNotEmpty(result.getStudentId())) {
                UserStudentExt student = userStudentExtService.lambdaQuery()
                        .eq(UserStudentExt::getStudentId, result.getStudentId())
                        .eq(UserStudentExt::getDeleted, false)
                        .one();
                if (student != null) {
                    result.setStudentName(student.getName());
                    result.setStudentPhone(student.getPhone());
                }
            }

            // 填充销售信息
            if (StrUtil.isNotEmpty(result.getSalesId())) {
                SaleProfile saleProfile = saleProfileService.lambdaQuery()
                        .eq(SaleProfile::getSalesId, result.getSalesId())
                        .eq(SaleProfile::getDeleted, false)
                        .one();
                if (saleProfile != null) {
                    result.setSalesName(saleProfile.getSalesName());
                    result.setSalesGroupName(saleProfile.getSalesGroupName());
                }
            }

            // 填充教学组信息
            if (StrUtil.isNotEmpty(result.getTeachingGroupId())) {
                TeachingGroup teachingGroup = teachingGroupService.lambdaQuery()
                        .eq(TeachingGroup::getId, result.getTeachingGroupId())
                        .eq(TeachingGroup::getDeleted, false)
                        .one();
                if (teachingGroup != null) {
                    result.setTeachingGroupName(teachingGroup.getName());

                    // 填充教学组长信息
                    if (StrUtil.isNotEmpty(teachingGroup.getLeaderId())) {
                        TeacherProfile leader = teacherProfileService.lambdaQuery()
                                .eq(TeacherProfile::getTeacherId, teachingGroup.getLeaderId())
                                .eq(TeacherProfile::getDeleted, false)
                                .one();
                        if (leader != null) {
                            result.setTeachingGroupLeaderName(leader.getRealName());
                        }
                    }
                }
            }

            // 填充确认教师信息
            if (StrUtil.isNotEmpty(result.getApprovedTeacherId())) {
                TeacherProfile teacher = teacherProfileService.lambdaQuery()
                        .eq(TeacherProfile::getTeacherId, result.getApprovedTeacherId())
                        .eq(TeacherProfile::getDeleted, false)
                        .one();
                if (teacher != null) {
                    result.setApprovedTeacherName(teacher.getRealName());
                }
            }

            // 填充确认人信息
            if (StrUtil.isNotEmpty(result.getApprovalBy())) {
                TeacherProfile approver = teacherProfileService.lambdaQuery()
                        .eq(TeacherProfile::getTeacherId, result.getApprovalBy())
                        .eq(TeacherProfile::getDeleted, false)
                        .one();
                if (approver != null) {
                    result.setApprovalByName(approver.getRealName());
                }
            }
        } catch (Exception e) {
            log.error("填充预约课申请关联信息失败", e);
            // 不抛出异常，避免影响主要功能
        }
    }

    /**
     * 获取状态文本
     */
    private String getStatusText(String status) {
        try {
            return CourseBookingApplication.Status.fromCode(status).getDescription();
        } catch (Exception e) {
            return status;
        }
    }

    /**
     * 应用学生过滤条件
     */
    private void applyStudentFilter(com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper<CourseBookingApplication> queryWrapper,
                                    CourseBookingDto.GetListReq req) {
        try {
            List<String> studentIds = new ArrayList<>();

            // 根据学生姓名查询学生ID
            if (StrUtil.isNotEmpty(req.getStudentName())) {
                List<UserStudentExt> students = userStudentExtService.lambdaQuery()
                        .like(UserStudentExt::getName, req.getStudentName())
                        .eq(UserStudentExt::getDeleted, false)
                        .list();
                studentIds.addAll(students.stream().map(UserStudentExt::getStudentId).collect(Collectors.toList()));
            }

            // 根据学生手机号查询学生ID
            if (StrUtil.isNotEmpty(req.getStudentPhone())) {
                List<UserStudentExt> students = userStudentExtService.lambdaQuery()
                        .like(UserStudentExt::getPhone, req.getStudentPhone())
                        .eq(UserStudentExt::getDeleted, false)
                        .list();
                studentIds.addAll(students.stream().map(UserStudentExt::getStudentId).collect(Collectors.toList()));
            }

            if (!studentIds.isEmpty()) {
                // 去重
                studentIds = studentIds.stream().distinct().collect(Collectors.toList());
                queryWrapper.in(CourseBookingApplication::getStudentId, studentIds);
            } else {
                // 如果没有找到匹配的学生，返回空结果
                queryWrapper.eq(CourseBookingApplication::getStudentId, "NONE");
            }

        } catch (Exception e) {
            log.error("应用学生过滤条件失败", e);
            // 出错时不显示任何结果
            queryWrapper.eq(CourseBookingApplication::getStudentId, "NONE");
        }
    }

    /**
     * 应用老师过滤条件
     */
    private void applyTeacherFilter(com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper<CourseBookingApplication> queryWrapper,
                                    CourseBookingDto.GetListReq req) {
        try {
            List<String> teacherIds = new ArrayList<>();

            // 根据老师姓名查询老师ID
            if (StrUtil.isNotEmpty(req.getTeacherName())) {
                List<TeacherProfile> teachers = teacherProfileService.lambdaQuery()
                        .like(TeacherProfile::getRealName, req.getTeacherName())
                        .eq(TeacherProfile::getDeleted, false)
                        .list();
                teacherIds.addAll(teachers.stream().map(TeacherProfile::getTeacherId).collect(Collectors.toList()));
            }

            // 根据老师手机号查询老师ID
            if (StrUtil.isNotEmpty(req.getTeacherPhone())) {
                List<TeacherProfile> teachers = teacherProfileService.lambdaQuery()
                        .like(TeacherProfile::getPhonenumber, req.getTeacherPhone())
                        .eq(TeacherProfile::getDeleted, false)
                        .list();
                teacherIds.addAll(teachers.stream().map(TeacherProfile::getTeacherId).collect(Collectors.toList()));
            }

            if (!teacherIds.isEmpty()) {
                // 去重
                teacherIds = teacherIds.stream().distinct().collect(Collectors.toList());

                // 使用PostgreSQL数组操作符过滤申请（候选老师中包含查询的老师）
                String teacherIdsStr = teacherIds.stream()
                        .map(id -> "'" + id + "'::varchar")
                        .collect(Collectors.joining(","));

                queryWrapper.apply("preferred_teachers && ARRAY[" + teacherIdsStr + "]::varchar[]");
            } else {
                // 如果没有找到匹配的老师，返回空结果
                queryWrapper.eq(CourseBookingApplication::getId, "NONE");
            }

        } catch (Exception e) {
            log.error("应用老师过滤条件失败", e);
            // 出错时不显示任何结果
            queryWrapper.eq(CourseBookingApplication::getId, "NONE");
        }
    }

    @Override
    public boolean voidCourseBooking(CourseBookingDto.VoidReq req) {
        try {
            log.info("作废预约课申请: req={}", req);


            // 业务逻辑：检查申请状态
            CourseBookingApplication application = courseBookingApplicationService.getById(req.getApplicationId());

            if (application == null || application.getDeleted()) {
                throw new RuntimeException("申请不存在");
            }


            String currentUserId = systemDataQueryUtil.getCurrentUserId();

            // 业务逻辑：检查权限
            hasVoidPermission(application, currentUserId);

            String currentStatus = application.getStatus();
            if (!CourseBookingApplication.Status.PENDING.getCode().equals(currentStatus)
                && !CourseBookingApplication.Status.APPROVED.getCode().equals(currentStatus)) {
                throw new RuntimeException("申请状态不允许作废");
            }

            // 调用Service层的纯数据操作
            boolean success = courseBookingApplicationService.voidApplicationData(
                    req.getApplicationId(),
                    currentUserId,
                    req.getVoidReason()
            );

            if (success) {
                log.info("作废预约课申请成功: applicationId={}", req.getApplicationId());

                // 发送作废通知（业务逻辑）
                try {
                    wechatNotificationService.sendCourseBookingNotification(req.getApplicationId(), "VOIDED");
                } catch (Exception e) {
                    log.warn("发送作废通知失败: applicationId={}", req.getApplicationId(), e);
                    // 通知失败不影响作废操作
                }
            } else {
                log.warn("作废预约课申请失败: applicationId={}", req.getApplicationId());
            }

            return success;

        } catch (Exception e) {
            log.error("作废预约课申请失败: req={}", req, e);
            throw new RuntimeException("作废预约课申请失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户是否有作废申请的权限（业务逻辑）
     */
    private void hasVoidPermission(CourseBookingApplication application, String userId) {
        String applicationId = application.getId();
        log.info("检查作废权限: applicationId={}, userId={}", applicationId, userId);


        String status = application.getStatus();
        log.info("申请状态: applicationId={}, status={}", applicationId, status);

        // 根据申请状态判断权限
        if (CourseBookingApplication.Status.PENDING.getCode().equals(status)) {
            // 待审核状态：发起申请的销售可以作废
            if(!(systemDataQueryUtil.getCurrentUserId().equals(application.getCreateBy()) || systemDataQueryUtil.isAdminOrHr())){
                throw new RuntimeException("当前状态为待审核，只有申请的销售可以作废和管理员可以作废");
            }
        } else if (CourseBookingApplication.Status.APPROVED.getCode().equals(status)) {
            // 已通过状态：admin和hr可以作废
            if(!(systemDataQueryUtil.isAdmin() || systemDataQueryUtil.isHr())){
                throw new RuntimeException("当前状态已通过，请联系管理员作废");
            }
        } else {
            // 其他状态不允许作废
            log.info("申请状态不允许作废: applicationId={}, status={}", applicationId, status);
            throw new RuntimeException("当前状态不允许作废: " + status);
        }
    }

    /**
     * 检查用户是否为申请的创建者（销售）（业务逻辑）
     */
    private boolean isApplicationCreator(CourseBookingApplication application, String userId) {
        try {
            // 通过学生ID查找对应的销售
            UserStudentExt studentExt = userStudentExtService.lambdaQuery()
                    .eq(UserStudentExt::getStudentId, application.getStudentId())
                    .eq(UserStudentExt::getDeleted, false)
                    .one();

            if (studentExt != null && StrUtil.isNotEmpty(studentExt.getSalesId())) {
                boolean isCreator = userId.equals(studentExt.getSalesId());
                log.info("检查申请创建者: applicationId={}, userId={}, salesId={}, isCreator={}",
                        application.getId(), userId, studentExt.getSalesId(), isCreator);
                return isCreator;
            }

            log.warn("未找到学生对应的销售: studentId={}", application.getStudentId());
            return false;

        } catch (Exception e) {
            log.error("检查申请创建者失败: applicationId={}, userId={}", application.getId(), userId, e);
            return false;
        }
    }

    /**
     * 批量检查用户是否为申请的创建者（销售）（业务逻辑，优化N+1查询）
     */
    private Map<String, Boolean> batchCheckApplicationCreators(List<CourseBookingApplication> applications, String userId) {
        Map<String, Boolean> result = new HashMap<>();

        if (applications == null || applications.isEmpty()) {
            return result;
        }

        try {
            // 提取所有学生ID
            List<String> studentIds = applications.stream()
                    .map(CourseBookingApplication::getStudentId)
                    .distinct()
                    .collect(Collectors.toList());

            // 批量查询学生对应的销售ID
            Map<String, String> studentSalesMap = userStudentExtService.getSalesIdsByStudentIds(studentIds);

            // 为每个申请检查权限
            for (CourseBookingApplication application : applications) {
                String salesId = studentSalesMap.get(application.getStudentId());
                boolean isCreator = StrUtil.isNotEmpty(salesId) && userId.equals(salesId);
                result.put(application.getId(), isCreator);

                log.debug("批量检查申请创建者: applicationId={}, studentId={}, salesId={}, isCreator={}",
                        application.getId(), application.getStudentId(), salesId, isCreator);
            }

            log.info("批量检查申请创建者完成: 检查{}个申请，用户{}有{}个申请的创建权限",
                    applications.size(), userId, result.values().stream().mapToLong(v -> v ? 1 : 0).sum());

            return result;

        } catch (Exception e) {
            log.error("批量检查申请创建者失败: userId={}", userId, e);
            // 发生错误时，返回所有申请都无权限
            for (CourseBookingApplication application : applications) {
                result.put(application.getId(), false);
            }
            return result;
        }
    }

    /**
     * 验证试听课时间基本信息（业务逻辑）
     */
    private void validateTrialClassTime(CourseBookingDto.TrialClassTime trialTime) {
        if (trialTime == null) {
            throw new RuntimeException("试听课时间不能为空");
        }

        if (trialTime.getDate() == null) {
            throw new RuntimeException("试听课日期不能为空");
        }

        if (StrUtil.isBlank(trialTime.getStartTime()) || StrUtil.isBlank(trialTime.getEndTime())) {
            throw new RuntimeException("试听课开始时间和结束时间不能为空");
        }
        LocalTime startTime = LocalTime.parse(trialTime.getStartTime());
        LocalTime endTime = LocalTime.parse(trialTime.getEndTime());

        if (!startTime.isBefore(endTime)) {
            throw new RuntimeException("试听课开始时间必须早于结束时间");
        }

        // 验证时间段长度（建议1-2小时）
        long minutes = java.time.Duration.between(startTime, endTime).toMinutes();
        if (minutes < 60) {
            throw new RuntimeException("试听课时间段不能少于1小时");
        }

        // 验证预约时间限制规则
        // validateBookingTimeRestrictions(trialTime, startTime);
    }

    /**
     * 验证预约时间限制规则
     * 业务规则：
     * - 不能申请当天的试听课时间
     * - 如果当前时间在14:00之前，可以预约次日6:00之后的试听课时间
     * - 如果当前时间在14:00之后，可以预约次日14:00之后的试听课时间
     * - 只对次日的预约进行此规则校验，其他日期的预约不受此限制
     */
    private void validateBookingTimeRestrictions(CourseBookingDto.TrialClassTime trialTime, LocalTime trialStartTime) {
        LocalDateTime now = LocalDateTime.now();
        LocalDate trialDate = trialTime.getDate().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
        LocalDate today = now.toLocalDate();
        LocalDate tomorrow = today.plusDays(1);

        // 不能预约当天的试听课
        if (trialDate.isBefore(tomorrow)) {
            throw new RuntimeException("不能预约当天的试听课时间");
        }

        // 只对次日的预约进行此规则校验，其他日期的预约不受此限制
        if (!trialDate.equals(tomorrow)) {
            return;
        }

        LocalTime currentTime = now.toLocalTime();

        // 判断当前时间段并验证预约限制
        if (currentTime.isBefore(LocalTime.of(14, 0))) {
            // 当前时间在14:00之前，可以预约次日6:00之后的试听课时间
            if (trialStartTime.isBefore(LocalTime.of(6, 0))) {
                throw new RuntimeException("当前时间在14:00之前，只能预约次日6:00之后的试听课时间");
            }
        } else {
            // 当前时间在14:00之后，可以预约次日14:00之后的试听课时间
            if (trialStartTime.isBefore(LocalTime.of(14, 0))) {
                throw new RuntimeException("当前时间在14:00之后，只能预约次日14:00之后的试听课时间");
            }
        }
    }

    /**
     * 验证教师试听课时间可用性（业务逻辑）
     */
    private void validateTeachersTrialTimeAvailability(List<String> teacherIds, CourseBookingDto.TrialClassTime trialTime) {
        if (CollUtil.isEmpty(teacherIds) || trialTime == null) {
            return;
        }

        // 批量检查教师试听课时间可用性
        Map<String, Boolean> availabilityMap = teacherMatchService.batchCheckTrialTimeAvailability(
                teacherIds,
                trialTime.getDate(),
                trialTime.getStartTime(),
                trialTime.getEndTime()
        );

        // 检查是否有可用的教师
        long availableCount = availabilityMap.values().stream().mapToLong(available -> available ? 1 : 0).sum();

        if (availableCount == 0) {
            throw new RuntimeException("所选教师在试听课时间段都不可用，请选择其他时间或教师");
        }

        log.info("教师试听课时间可用性检查完成: {}个教师中有{}个可用", teacherIds.size(), availableCount);
    }

    /**
     * 批量检查作废权限（优化N+1查询）
     *
     * @param applications 申请列表
     * @param userId 用户ID
     * @return 申请ID -> 是否有权限的映射
     */
    public Map<String, Boolean> batchCheckVoidPermissions(List<CourseBookingApplication> applications, String userId) {
        Map<String, Boolean> result = new HashMap<>();

        if (applications == null || applications.isEmpty()) {
            return result;
        }

        try {
            // 检查用户角色（一次性检查）
            boolean isAdminOrHr = systemDataQueryUtil.isAdmin() || systemDataQueryUtil.isHr();

            // 如果是admin或hr，对所有已通过的申请都有权限
            if (isAdminOrHr) {
                for (CourseBookingApplication application : applications) {
                    String status = application.getStatus();
                    boolean hasPermission = CourseBookingApplication.Status.PENDING.getCode().equals(status) ||
                            CourseBookingApplication.Status.APPROVED.getCode().equals(status);
                    result.put(application.getId(), hasPermission);
                }
                return result;
            }

            // 对于销售用户，需要检查申请创建者权限
            // 批量查询申请创建者关系
            Map<String, Boolean> creatorCheckResults = batchCheckApplicationCreators(applications, userId);

            for (CourseBookingApplication application : applications) {
                String status = application.getStatus();
                boolean hasPermission = false;

                if (CourseBookingApplication.Status.PENDING.getCode().equals(status)) {
                    // 待审核状态：检查是否为申请创建者
                    hasPermission = creatorCheckResults.getOrDefault(application.getId(), false);
                } else if (CourseBookingApplication.Status.APPROVED.getCode().equals(status)) {
                    // 已通过状态：销售无权限（已在上面处理admin/hr）
                    hasPermission = false;
                }

                result.put(application.getId(), hasPermission);
            }

            log.info("批量检查作废权限完成: 用户{}对{}个申请中的{}个有权限",
                    userId, applications.size(),
                    result.values().stream().mapToLong(v -> v ? 1 : 0).sum());

            return result;

        } catch (Exception e) {
            log.error("批量检查作废权限失败: userId={}", userId, e);
            // 发生错误时，返回所有申请都无权限
            for (CourseBookingApplication application : applications) {
                result.put(application.getId(), false);
            }
            return result;
        }
    }
}
