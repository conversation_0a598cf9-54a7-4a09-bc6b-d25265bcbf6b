# 课时包产品管理系统完整实现

## 概述

已完成从MySQL到PostgreSQL 17.0的迁移，并重新设计了产品结构为课时包概念，使用Flyway进行数据库版本管理。

## 系统特性

### 1. 课时包结构设计

#### 核心属性
- **学科**: 英语、数学、语文、物理、化学等
- **课型**: 基础课、提升课、专项课、冲刺课、思维课、实验课等
- **适用年级**: 多选支持，从小学1年级到高中3年级
- **单价**: 每课时的价格（分为单位）
- **数量**: 课时包包含的课时数量

#### 增值服务
- **赠送课时**: 可选择是否赠送额外课时
- **教材费**: 可选择是否包含教材费用

#### 价格计算
- **原价**: 自动计算 = 单价 × 数量 + 教材费
- **售价**: 手动输入的最终销售价格

### 2. 数据库设计 (PostgreSQL 17.0)

#### Flyway版本管理
```
V1__Create_product_table.sql    # 创建产品表
V2__Insert_sample_products.sql  # 插入示例数据
```

#### 表结构特点
- 使用JSONB存储适用年级和标签
- 支持GIN索引提升JSON查询性能
- 自动更新时间戳触发器
- 完整的字段注释

### 3. 后端实现

#### 实体类更新
```java
public class Product extends DataEntity {
    private String subject;              // 学科
    private String courseType;           // 课型
    private String applicableGrades;     // 适用年级（JSON）
    private Long unitPrice;              // 单价（分）
    private Integer quantity;            // 数量
    private Boolean hasBonusHours;       // 是否有赠送课时
    private Integer bonusHoursQuantity;  // 赠送课时数量
    private Boolean hasMaterialFee;      // 是否包含教材费
    private Long materialFee;            // 教材费（分）
    private Long originalPrice;          // 原价（分）
    private Long sellingPrice;           // 售价（分）
    // ... 其他字段
}
```

#### DTO类更新
- 支持新的课时包结构
- 添加价格计算相关字段
- 完善的验证注解

### 4. 前端实现

#### 产品管理界面
- **列表展示**: 显示学科、课型、适用年级、单价、数量、原价、售价等
- **智能表单**: 支持原价自动计算
- **多选年级**: 支持选择多个适用年级
- **条件筛选**: 按学科、课型、状态等筛选

#### 表单功能
```javascript
// 原价自动计算
calculateOriginalPrice() {
  const basePrice = this.form.unitPrice * this.form.quantity
  const materialFee = this.form.hasMaterialFee ? this.form.materialFee : 0
  this.form.originalPrice = basePrice + materialFee
}
```

## 部署指南

### 1. 数据库部署

#### 安装PostgreSQL 17.0
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql-17

# 启动服务
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### 配置Flyway
```properties
# application.yml
spring:
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
  datasource:
    url: *****************************************
    username: words_user
    password: your_password
    driver-class-name: org.postgresql.Driver
```

#### 执行迁移
```bash
# 自动执行Flyway迁移
mvn flyway:migrate

# 或者启动应用时自动执行
java -jar words-system-service.jar
```

### 2. 示例数据

系统包含7个示例产品：

1. **小学英语基础课时包** - 20课时，赠送2课时，含教材费
2. **初中数学提升课时包** - 25课时，赠送3课时
3. **高中语文阅读专项课时包** - 30课时，赠送5课时，含教材费
4. **英语语法单次课** - 1课时，无赠送
5. **小学数学思维训练课时包** - 24课时，赠送4课时，含教材费
6. **高考英语冲刺课时包** - 40课时，赠送8课时，含教材费
7. **初中物理实验课时包** - 15课时，含教材费（下架状态）

### 3. 前端部署

#### 更新依赖
```bash
cd words-frontend
npm install
```

#### 启动开发服务器
```bash
npm run dev
```

#### 生产构建
```bash
npm run build
```

## 功能验证

### 1. 产品管理功能

#### 创建课时包
1. 选择学科和课型
2. 选择适用年级（可多选）
3. 输入单价和数量
4. 选择是否赠送课时
5. 选择是否包含教材费
6. 系统自动计算原价
7. 输入售价

#### 价格计算验证
```
示例：
单价：150元/课时
数量：20课时
教材费：50元
原价 = 150 × 20 + 50 = 3050元
售价：2999元（手动输入）
```

### 2. 查询功能

#### 支持的查询条件
- 产品名称（模糊搜索）
- 学科（下拉选择）
- 课型（下拉选择）
- 产品状态（上架/下架）

#### 列表展示
- 产品名称
- 学科
- 课型
- 适用年级（多个年级用逗号分隔）
- 单价
- 数量
- 原价
- 售价
- 状态

### 3. 订单集成

#### 产品选择
- 只显示上架状态的产品
- 显示完整的课时包信息
- 支持按售价排序

## 技术亮点

### 1. PostgreSQL 17.0特性
- **JSONB支持**: 高效存储和查询JSON数据
- **GIN索引**: 优化JSON字段查询性能
- **触发器**: 自动更新时间戳
- **完整约束**: 保证数据完整性

### 2. Flyway版本管理
- **版本控制**: 数据库结构版本化管理
- **自动迁移**: 应用启动时自动执行迁移
- **回滚支持**: 支持数据库版本回滚
- **团队协作**: 统一的数据库版本

### 3. 前端智能表单
- **自动计算**: 原价自动计算
- **条件显示**: 根据选择动态显示字段
- **数据验证**: 完整的表单验证
- **用户体验**: 友好的交互设计

### 4. 业务逻辑优化
- **价格精度**: 使用分为单位避免浮点误差
- **数据一致性**: 事务保证数据一致性
- **软删除**: 保护重要数据
- **审计日志**: 完整的操作记录

## 扩展建议

### 1. 功能扩展
- **批量导入**: Excel批量导入产品
- **价格策略**: 支持阶梯定价
- **优惠券**: 集成优惠券系统
- **库存预警**: 库存不足自动提醒

### 2. 性能优化
- **缓存策略**: Redis缓存热门产品
- **分页优化**: 大数据量分页优化
- **索引优化**: 根据查询模式优化索引
- **连接池**: 数据库连接池优化

### 3. 监控告警
- **性能监控**: 数据库性能监控
- **业务监控**: 产品销售监控
- **异常告警**: 系统异常自动告警
- **日志分析**: 完整的日志分析

这个课时包产品管理系统为教育行业提供了完整的产品管理解决方案，支持灵活的课时包配置和智能的价格计算。
