package org.nonamespace.word.server.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PointTransactionSourceCategoryEnum {
    // 答题奖励，老师奖励，自学奖励
    ANSWER_REWARD("答题奖励"),
    TEACHER_REWARD("老师奖励"),
    SELF_STUDY_REWARD("自学奖励");
    private final String value;

    public static PointTransactionSourceCategoryEnum fromValue(String value) {
        for (PointTransactionSourceCategoryEnum type : PointTransactionSourceCategoryEnum.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown value: " + value);
    }
}