package org.nonamespace.word.server.domain;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nonamespace.word.server.entity.DataEntity;

import java.util.Date;

/**
 * 词定义 (统一教材与词): 定义各种词对象 student_textbook_progress
 * 
 * <AUTHOR>
 * @date 2025-05-20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class StudentTextbookProgress extends DataEntity {


    /** 词表ID */
    @Excel(name = "词表ID")
    private String textbookId;

    /** 学生ID */
    @Excel(name = "学生ID")
    private String studentId;

    /** 单词数 */
    @Excel(name = "单词数")
    private Long statWordCnt;

    /** 已学单词数 */
    @Excel(name = "已学单词数")
    private Long statWordLearnCnt;

    /** 错过单词书 */
    @Excel(name = "错过单词书")
    private Long statWordMistakesCnt;

    /** 首次学习时间 */
    @Excel(name = "首次学习时间")
    private Date firstLearnAt;

    /** 最后一次学习时间 */
    @Excel(name = "最后一次学习时间")
    private Date lastLearnAt;

    /** 状态(未学习、学习中、已学习) */
    @Excel(name = "状态(未学习、学习中、已学习)")
    private String status;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long version;

    public Long getStatWordCnt() {
        return statWordCnt == null ? 0L : statWordCnt;
    }

    public Long getStatWordLearnCnt() {
        return statWordLearnCnt == null ? 0L : statWordLearnCnt;
    }

    public Long getStatWordMistakesCnt() {
        return statWordMistakesCnt == null ? 0L : statWordMistakesCnt;
    }
}
