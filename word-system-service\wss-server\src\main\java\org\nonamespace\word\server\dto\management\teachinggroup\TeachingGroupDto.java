package org.nonamespace.word.server.dto.management.teachinggroup;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;


import java.util.Date;
import java.util.List;

/**
 * 教学组相关DTO
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public class TeachingGroupDto {

    /**
     * 获取教学组列表请求参数
     */
    @Data
    public static class GetListReq {
        private Integer pageNum = 1;
        private Integer pageSize = 20;
        private String name;
        private String status;
        private String leaderId;
        private String adminId;
    }

    /**
     * 创建教学组请求参数
     */
    @Data
    public static class CreateReq {
        @NotBlank(message = "教学组名称不能为空")
        @Size(min = 2, max = 50, message = "教学组名称长度在 2 到 50 个字符")
        private String name;

        @Size(max = 200, message = "描述长度不能超过 200 个字符")
        private String description;

        private Long deptId; // 关联的部门ID
        private String leaderId;
        private String adminId;

        @Size(max = 100, message = "备注长度不能超过 100 个字符")
        private String remark;
    }

    /**
     * 更新教学组请求参数
     */
    @Data
    public static class UpdateReq {
        @NotBlank(message = "教学组ID不能为空")
        private String id;
        
        @NotBlank(message = "教学组名称不能为空")
        @Size(min = 2, max = 50, message = "教学组名称长度在 2 到 50 个字符")
        private String name;
        
        @Size(max = 200, message = "描述长度不能超过 200 个字符")
        private String description;
        
        private String leaderId;
        private String adminId;
        
        @Size(max = 100, message = "备注长度不能超过 100 个字符")
        private String remark;
    }

    /**
     * 教学组响应数据
     */
    @Data
    public static class Resp {
        private String id;
        private String name;
        private String description;
        private Long deptId; // 关联的部门ID
        private String deptName; // 部门名称
        private String leaderId;
        private String leaderName;
        private String adminId;
        private String adminName;
        private Integer memberCount;
        private String status;
        private String remark;
        private Date createTime;
        private Date updateTime;
    }

    /**
     * 教学组统计信息
     */
    @Data
    public static class StatsResp {
        private Integer totalGroups;
        private Integer activeGroups;
        private Integer totalTeachers;
        private Integer unassignedTeachers;
    }

    /**
     * 分配教师请求参数
     */
    @Data
    public static class AssignTeachersReq {
        @NotBlank(message = "教学组ID不能为空")
        private String groupId;
        
        private List<String> teacherIds;
    }

    /**
     * 移除教师请求参数
     */
    @Data
    public static class RemoveTeachersReq {
        @NotBlank(message = "教学组ID不能为空")
        private String groupId;
        
        private List<String> teacherIds;
    }

    /**
     * 获取教学组教师列表请求参数
     */
    @Data
    public static class GetGroupTeachersReq {
        @NotBlank(message = "教学组ID不能为空")
        private String groupId;
        
        private Integer pageNum = 1;
        private Integer pageSize = 20;
        private String name;
        private String status;
    }
}
