package org.nonamespace.word.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.domain.*;
import org.nonamespace.word.server.dto.management.booking.CourseBookingDto;
import org.nonamespace.word.server.dto.management.student.SalesStudentDto;
import org.nonamespace.word.server.service.*;
import org.nonamespace.word.server.util.SystemDataQueryUtil;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 缓存数据服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CachedDataServiceImpl implements ICachedDataService {

    private final ISaleProfileService saleProfileService;
    private final ISalesGroupService salesGroupService;
    private final ITeacherProfileService teacherProfileService;
    private final ITeachingGroupService teachingGroupService;
    private final ITeachingGroupMemberService teachingGroupMemberService;
    private final UserStudentExtService userStudentExtService;
    private final ICourseBookingApplicationService courseBookingApplicationService;
    private final SystemDataQueryUtil systemDataQueryUtil;

    @Override
    @Cacheable(value = "sales:options",
               key = "#salesGroupId ?: 'all'",
               unless = "#result == null or #result.isEmpty()")
    public List<SalesStudentDto.SalesOptionResp> getCachedSalesOptions(String salesGroupId) {
        log.info("从数据库查询销售选项: salesGroupId={}", salesGroupId);

        var wrapper = saleProfileService.lambdaQuery()
                .eq(SaleProfile::getDeleted, false)
                .eq(SaleProfile::getStatus, "active");

        if (StrUtil.isNotEmpty(salesGroupId)) {
            wrapper.eq(SaleProfile::getSalesGroupId, salesGroupId);
        }

        return wrapper.orderByAsc(SaleProfile::getSalesGroupName, SaleProfile::getSalesName)
                .list()
                .stream()
                .map(this::convertToSalesOption)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "sales:options",
               key = "'groups'",
               unless = "#result == null or #result.isEmpty()")
    public List<SalesStudentDto.GroupOptionResp> getCachedSalesGroupOptions() {
        log.info("从数据库查询销售组选项");

        var wrapper = salesGroupService.lambdaQuery()
                .eq(SalesGroup::getDeleted, false)
                .eq(SalesGroup::getStatus, "active");

        return wrapper.orderByAsc(SalesGroup::getName)
                .list()
                .stream()
                .map(this::convertToGroupOption)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "teachers:available",
               key = "#subject + ':' + #specification + ':' + (#keyword ?: 'all')",
               unless = "#result == null or #result.isEmpty()")
    public List<CourseBookingDto.AvailableTeacherResp> getCachedAvailableTeachers(
            String subject, String specification, String keyword) {
        log.info("从数据库查询可选教师: subject={}, specification={}, keyword={}",
                subject, specification, keyword);

        var wrapper = teacherProfileService.lambdaQuery()
                .eq(TeacherProfile::getDeleted, false)
                .eq(TeacherProfile::getStatus, "active");

        // 按关键词搜索
        if (StrUtil.isNotEmpty(keyword)) {
            wrapper.and(w -> w.like(TeacherProfile::getRealName, keyword)
                           .or()
                           .like(TeacherProfile::getPhonenumber, keyword));
        }

        // 暂时不应用数据权限，避免类型不匹配问题

        List<TeacherProfile> teachers = wrapper.orderByAsc(TeacherProfile::getRealName)
                .list();

        // 过滤符合学科和课型要求的教师
        return teachers.stream()
                .filter(teacher -> isTeacherSuitable(teacher, subject, specification))
                .map(this::convertToAvailableTeacher)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "teaching:groups",
               key = "'all'",
               unless = "#result == null or #result.isEmpty()")
    public List<CourseBookingDto.TeachingGroupOptionResp> getCachedTeachingGroupOptions() {
        log.info("从数据库查询教学组选项");

        var wrapper = teachingGroupService.lambdaQuery()
                .eq(TeachingGroup::getDeleted, false)
                .eq(TeachingGroup::getStatus, "active");

        // 暂时不应用数据权限，避免类型不匹配问题

        return wrapper.orderByAsc(TeachingGroup::getName)
                .list()
                .stream()
                .map(this::convertToTeachingGroupOption)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "stats:students",
               key = "#userId + ':' + #userRole",
               unless = "#result == null")
    public SalesStudentDto.StatsResp getCachedStudentStats(String userId, String userRole) {
        log.info("从数据库计算学生统计: userId={}, userRole={}", userId, userRole);

        SalesStudentDto.StatsResp stats = new SalesStudentDto.StatsResp();

        var wrapper = userStudentExtService.lambdaQuery()
                .eq(UserStudentExt::getDeleted, false);

        // 暂时不应用数据权限，避免类型不匹配问题

        // 总学生数
        Long totalStudents = userStudentExtService.count(wrapper);
        stats.setTotalStudents(totalStudents);

        // 已分配学生数
        var assignedWrapper = userStudentExtService.lambdaQuery()
                .eq(UserStudentExt::getDeleted, false)
                .isNotNull(UserStudentExt::getSalesId);
        Long assignedStudents = userStudentExtService.count(assignedWrapper);
        stats.setAssignedStudents(assignedStudents);

        // 未分配学生数
        stats.setUnassignedStudents(totalStudents - assignedStudents);

        // 本月新增学生数
        var monthlyWrapper = userStudentExtService.lambdaQuery()
                .eq(UserStudentExt::getDeleted, false)
                .ge(UserStudentExt::getCreateTime, getMonthStart());
        Long monthlyStudents = userStudentExtService.count(monthlyWrapper);
        // 暂时注释掉，因为字段不存在
        // stats.setMonthlyNewStudents(monthlyStudents);

        return stats;
    }

    @Override
    @Cacheable(value = "stats:bookings",
               key = "#userId + ':' + #userRole",
               unless = "#result == null")
    public CourseBookingDto.StatsResp getCachedBookingStats(String userId, String userRole) {
        log.info("从数据库计算预约课申请统计: userId={}, userRole={}", userId, userRole);

        CourseBookingDto.StatsResp stats = new CourseBookingDto.StatsResp();

        var wrapper = courseBookingApplicationService.lambdaQuery()
                .eq(CourseBookingApplication::getDeleted, false);

        // 暂时不应用数据权限，避免类型不匹配问题

        // 总申请数
        Long totalApplications = courseBookingApplicationService.count(wrapper);
        stats.setTotalApplications(totalApplications);

        // 各状态申请数
        for (CourseBookingApplication.Status status : CourseBookingApplication.Status.values()) {
            var statusWrapper = courseBookingApplicationService.lambdaQuery()
                    .eq(CourseBookingApplication::getDeleted, false)
                    .eq(CourseBookingApplication::getStatus, status.getCode());
            // 暂时不应用数据权限，避免类型不匹配问题
            Long count = courseBookingApplicationService.count(statusWrapper);

            switch (status) {
                case PENDING:
                    stats.setPendingApplications(count);
                    break;
                case APPROVED:
                    stats.setApprovedApplications(count);
                    break;
                case REJECTED:
                    stats.setRejectedApplications(count);
                    break;
                case CANCELLED:
                    // 暂时注释掉，因为字段不存在
                    // stats.setCancelledApplications(count);
                    break;
                case WITHDRAWN:
                    // 已撤回状态，暂时不统计
                    break;
                case VOIDED:
                    // 已作废状态，暂时不统计
                    break;
            }
        }
        
        // 计算确认率
        if (stats.getApprovedApplications() + stats.getRejectedApplications() > 0) {
            double approvalRate = (double) stats.getApprovedApplications() / 
                    (stats.getApprovedApplications() + stats.getRejectedApplications()) * 100;
            stats.setApprovalRate(Math.round(approvalRate * 100.0) / 100.0);
        } else {
            stats.setApprovalRate(0.0);
        }
        
        return stats;
    }

    @Override
    @CacheEvict(value = "sales:options", allEntries = true)
    public void evictSalesCache() {
        log.info("清除销售相关缓存");
    }

    @Override
    @CacheEvict(value = "teachers:available", allEntries = true)
    public void evictTeacherCache() {
        log.info("清除教师相关缓存");
    }

    @Override
    @CacheEvict(value = {"stats:students", "stats:bookings"},
                allEntries = true)
    public void evictStatsCache() {
        log.info("清除统计相关缓存");
    }

    @Override
    @CacheEvict(value = "courseStatistics", allEntries = true)
    public void evictCourseStatisticsCache() {
        log.info("清除课程统计缓存");
    }

    @Override
    @CacheEvict(value = {
            "sales:options",
            "teachers:available",
            "teaching:groups",
            "stats:students",
            "stats:bookings",
            "courseStatistics"
    }, allEntries = true)
    public void evictAllCache() {
        log.info("清除所有缓存");
    }

    @Override
    public void warmUpCache() {
        log.info("开始预热缓存");
        
        try {
            // 预热销售选项
            getCachedSalesOptions(null);
            getCachedSalesGroupOptions();
            
            // 预热教学组选项
            getCachedTeachingGroupOptions();
            
            // 预热常用教师查询
            getCachedAvailableTeachers("英语", "一对一", null);
            getCachedAvailableTeachers("数学", "一对一", null);
            
            log.info("缓存预热完成");
        } catch (Exception e) {
            log.error("缓存预热失败", e);
        }
    }

    // 私有辅助方法
    
    private SalesStudentDto.SalesOptionResp convertToSalesOption(SaleProfile saleProfile) {
        SalesStudentDto.SalesOptionResp resp = new SalesStudentDto.SalesOptionResp();
        resp.setId(saleProfile.getSalesId());
        resp.setName(saleProfile.getSalesName());
        resp.setPhone(saleProfile.getPhone());
        resp.setGroupId(saleProfile.getSalesGroupId());
        resp.setGroupName(saleProfile.getSalesGroupName());
        return resp;
    }

    private SalesStudentDto.GroupOptionResp convertToGroupOption(SalesGroup salesGroup) {
        SalesStudentDto.GroupOptionResp resp = new SalesStudentDto.GroupOptionResp();
        resp.setId(salesGroup.getId());
        resp.setName(salesGroup.getName());
        // TODO: 需要根据leaderId查询组长姓名
        // resp.setLeaderId(salesGroup.getLeaderId());
        // resp.setLeaderName(""); // 暂时设为空，实际需要查询
        return resp;
    }

    private CourseBookingDto.AvailableTeacherResp convertToAvailableTeacher(TeacherProfile teacher) {
        CourseBookingDto.AvailableTeacherResp resp = new CourseBookingDto.AvailableTeacherResp();
        resp.setTeacherId(teacher.getTeacherId());
        resp.setTeacherName(teacher.getRealName());
        resp.setTeacherPhone(teacher.getPhonenumber());
        resp.setGender(teacher.getGender());
        // 这里可以设置教学组ID等其他字段
        return resp;
    }

    private CourseBookingDto.TeachingGroupOptionResp convertToTeachingGroupOption(TeachingGroup group) {
        CourseBookingDto.TeachingGroupOptionResp resp = new CourseBookingDto.TeachingGroupOptionResp();
        resp.setId(group.getId());
        resp.setName(group.getName());
        // TODO: 需要根据leaderId查询组长姓名
        resp.setLeaderName(""); // 暂时设为空，实际需要查询
        resp.setMemberCount(group.getMemberCount());
        return resp;
    }

    private boolean isTeacherSuitable(TeacherProfile teacher, String subject, String specification) {
        // 检查教师是否适合指定的学科和课型
        // 这里简化实现，实际应该根据教师的教学科目和课型能力进行判断
        if (StrUtil.isNotEmpty(subject) && CollUtil.isNotEmpty(teacher.getSubjects())) {
            return teacher.getSubjects().contains(subject);
        }
        return true;
    }

    private java.util.Date getMonthStart() {
        java.util.Calendar cal = java.util.Calendar.getInstance();
        cal.set(java.util.Calendar.DAY_OF_MONTH, 1);
        cal.set(java.util.Calendar.HOUR_OF_DAY, 0);
        cal.set(java.util.Calendar.MINUTE, 0);
        cal.set(java.util.Calendar.SECOND, 0);
        cal.set(java.util.Calendar.MILLISECOND, 0);
        return cal.getTime();
    }
}
