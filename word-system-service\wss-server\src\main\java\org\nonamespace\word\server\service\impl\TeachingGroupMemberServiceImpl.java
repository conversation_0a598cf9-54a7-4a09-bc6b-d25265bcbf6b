package org.nonamespace.word.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.misc.WssContext;
import org.nonamespace.word.server.domain.TeachingGroupMember;
import org.nonamespace.word.server.dto.management.teachinggroup.TeacherDto;
import org.nonamespace.word.server.dto.management.teachinggroup.TeachingGroupDto;
import org.nonamespace.word.server.mapper.TeachingGroupMemberMapper;
import org.nonamespace.word.server.service.ITeachingGroupMemberService;
import org.nonamespace.word.server.service.IUserService;
import org.nonamespace.word.server.util.SystemDataQueryUtil;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 教学组成员Service实现类
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TeachingGroupMemberServiceImpl extends ServiceImpl<TeachingGroupMemberMapper, TeachingGroupMember> implements ITeachingGroupMemberService {

    private final TeachingGroupMemberMapper teachingGroupMemberMapper;
    private final SystemDataQueryUtil systemDataQueryUtil;

    private final IUserService userService;

    @Override
    public IPage<TeacherDto.BasicResp> selectGroupTeachersPage(TeachingGroupDto.GetGroupTeachersReq req) {
        Page<TeacherDto.BasicResp> page = new Page<>(req.getPageNum(), req.getPageSize());
        return teachingGroupMemberMapper.selectGroupTeachersPage(page, req);
    }

    @Override
    public List<TeacherDto.BasicResp> selectGroupTeachers(String groupId) {
        return teachingGroupMemberMapper.selectGroupTeachers(groupId);
    }

    @Override
        public boolean addTeachersToGroup(String groupId, List<String> teacherIds) {
        try {
            if (teacherIds == null || teacherIds.isEmpty()) {
                return true;
            }
            
            List<TeachingGroupMember> members = new ArrayList<>();
            Date now = new Date();
            
            for (String teacherId : teacherIds) {
                TeachingGroupMember member = new TeachingGroupMember();
                member.setId(IdUtil.getSnowflakeNextIdStr());
                member.setGroupId(groupId);
                member.setTeacherId(teacherId);
                member.setRoleType("member");
                member.setJoinTime(now);
                member.setStatus("active");
                member.setCreateTime(now);
                member.setUpdateTime(now);
                member.setCreateBy(WssContext.userId());
                member.setUpdateBy(WssContext.userId());
                
                members.add(member);
            }

            return this.saveBatch(members);

        } catch (Exception e) {
            log.error("添加教师到教学组失败", e);
            throw new RuntimeException("添加教师到教学组失败: " + e.getMessage());
        }
    }

    @Override
    public boolean removeTeachersFromGroup(String groupId, List<String> teacherIds) {
        try {
            if (teacherIds == null || teacherIds.isEmpty()) {
                return true;
            }

            userService.lambdaUpdate()
                    .set(SysUser::getDeptId, systemDataQueryUtil.getTeachingCenterDept().getDeptId())
                    .in(SysUser::getUserId, teacherIds.stream().map(Long::valueOf).toList())
                    .update();


            return lambdaUpdate()
                    .set(TeachingGroupMember::getDeleted, true)
                    .set(TeachingGroupMember::getStatus, "inactive")
                    .set(TeachingGroupMember::getUpdateTime, WssContext.now())
                    .set(TeachingGroupMember::getUpdateBy, WssContext.userId())
                    .in(TeachingGroupMember::getTeacherId, teacherIds)
                    .eq(StrUtil.isNotBlank(groupId), TeachingGroupMember::getGroupId, groupId)
                    .eq(TeachingGroupMember::getStatus, "active")
                    .update();
        } catch (Exception e) {
            log.error("从教学组移除教师失败", e);
            throw new RuntimeException("从教学组移除教师失败: " + e.getMessage());
        }
    }

    @Override
        public boolean removeAllMembersByGroupId(String groupId) {
        try {
            if (StrUtil.isBlank(groupId)) {
                return true;
            }

            List<Long> teacherIds = lambdaQuery().eq(TeachingGroupMember::getGroupId, groupId)
                    .eq(TeachingGroupMember::getStatus, "active")
                    .list()
                    .stream().map(x -> Long.valueOf(x.getTeacherId())).toList();

            if(CollUtil.isEmpty(teacherIds)){
                return true;
            }

            userService.lambdaUpdate()
                    .set(SysUser::getDeptId, systemDataQueryUtil.getTeachingCenterDept().getDeptId())
                    .in(SysUser::getUserId, teacherIds)
                    .update();

            return lambdaUpdate()
                    .set(TeachingGroupMember::getDeleted, true)
                    .set(TeachingGroupMember::getStatus, "inactive")
                    .set(TeachingGroupMember::getUpdateTime, WssContext.now())
                    .set(TeachingGroupMember::getUpdateBy, WssContext.userId())
                    .eq(TeachingGroupMember::getGroupId, groupId)
                    .eq(TeachingGroupMember::getStatus, "active")
                    .update();
        } catch (Exception e) {
            log.error("删除教学组所有成员失败", e);
            throw new RuntimeException("删除教学组所有成员失败: " + e.getMessage());
        }
    }

    @Override
    public List<String> checkAssignedTeachers(List<String> teacherIds) {
        if (teacherIds == null || teacherIds.isEmpty()) {
            return new ArrayList<>();
        }
        return teachingGroupMemberMapper.selectAssignedTeachers(teacherIds);
    }

    @Override
    public TeachingGroupDto.Resp getTeacherGroup(String teacherId) {
        return teachingGroupMemberMapper.selectTeacherGroup(teacherId);
    }
}
