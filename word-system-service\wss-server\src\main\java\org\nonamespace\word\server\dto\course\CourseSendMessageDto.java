package org.nonamespace.word.server.dto.course;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 上课消息推送dto
 *
 * <AUTHOR>
 * @date 2025/6/7 10:17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CourseSendMessageDto {

    private String courseId;
    private String type;
    private String userName;
    private String openid;
    private Date scheduledStartTime;
    private String subject;
    private String specification;
    private String userId;

    private String trigger;

}
