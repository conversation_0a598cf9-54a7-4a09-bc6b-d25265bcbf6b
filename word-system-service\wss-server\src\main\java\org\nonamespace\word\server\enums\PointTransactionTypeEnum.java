package org.nonamespace.word.server.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PointTransactionTypeEnum {
    REWARD("奖励"),
    CONSUMPTION("消费");
    private final String value;

    public static PointTransactionTypeEnum fromValue(String value) {
        for (PointTransactionTypeEnum type : PointTransactionTypeEnum.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown value: " + value);
    }
}