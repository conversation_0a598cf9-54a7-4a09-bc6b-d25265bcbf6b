package org.nonamespace.word.server.config;

import lombok.RequiredArgsConstructor;
import org.nonamespace.word.server.cache.SessionLevelCacheManager;
import org.nonamespace.word.server.interceptor.SystemDataCacheInterceptor;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Arrays;

/**
 * 系统数据缓存配置
 * 使用Spring Cache注解实现会话级别缓存
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Configuration
@EnableCaching
@RequiredArgsConstructor
public class SystemDataCacheConfig implements WebMvcConfigurer {

    private final SystemDataCacheInterceptor systemDataCacheInterceptor;

    /**
     * 会话级别缓存管理器
     * 使用自定义的SessionLevelCacheManager实现请求级别缓存隔离
     */
    @Bean("sessionCacheManager")
    @Primary
    public CacheManager sessionCacheManager() {
        return new SessionLevelCacheManager(Arrays.asList(
            "systemData:roles",           // 角色缓存
            "systemData:depts",           // 部门缓存
            "systemData:deptsByParent",   // 父部门+名称部门缓存
            "systemData:permissions",     // 权限检查缓存
            "systemData:currentUser",     // 当前用户缓存
            "courseStatistics"            // 课程统计缓存
        ));
    }

    /**
     * 注册缓存清理拦截器
     */
    @Override
    public void addInterceptors(@NonNull InterceptorRegistry registry) {
        registry.addInterceptor(systemDataCacheInterceptor)
                .addPathPatterns("/**") // 拦截所有请求
                .excludePathPatterns(
                    "/static/**",
                    "/css/**",
                    "/js/**",
                    "/images/**",
                    "/favicon.ico"
                ); // 排除静态资源
    }
}
