package org.nonamespace.word.server.dto.management.teacher.group;

import lombok.Data;

@Data
public class ManagementGetTeacherGroupDto {

    @Data
    public static class Req {
        private String keyword;
        private String leaderId;
        private String adminId;
    }

    @Data
    public static class TeachingGroup{
        String id;
        String name;
        String description;
        String leaderId;
        String leaderName;
        String adminId;
        String adminName;
        Integer memberCount;
        String status; // 'active' | 'inactive'
        String remark;
    }
}
