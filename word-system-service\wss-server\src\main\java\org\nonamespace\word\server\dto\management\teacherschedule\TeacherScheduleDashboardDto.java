package org.nonamespace.word.server.dto.management.teacherschedule;

import lombok.Data;

import java.util.List;

/**
 * 老师可排课时间看板DTO
 * 
 * <AUTHOR>
 * @since 2025-06-19
 */
public class TeacherScheduleDashboardDto {

    /**
     * 查询请求
     */
    @Data
    public static class QueryReq {
        /**
         * 开始日期 (YYYY-MM-DD)
         */
        private String startDate;

        /**
         * 结束日期 (YYYY-MM-DD)
         */
        private String endDate;

        /**
         * 教学组ID列表（可选，为空则查询所有教学组）
         */
        private List<String> groupIds;
    }

    /**
     * 看板响应
     */
    @Data
    public static class DashboardResp {
        /**
         * 查询时间范围
         */
        private String startDate;
        private String endDate;

        /**
         * 总体统计
         */
        private OverallStats overallStats;

        /**
         * 各教学组统计
         */
        private List<GroupStats> groupStats;

        /**
         * 每日统计
         */
        private List<DailyStats> dailyStats;

        /**
         * 每周统计
         */
        private List<WeeklyStats> weeklyStats;
    }

    /**
     * 总体统计
     */
    @Data
    public static class OverallStats {
        /**
         * 总教师数
         */
        private Integer totalTeachers;

        /**
         * 总可排课课次
         */
        private Integer totalAvailableSlots;

        /**
         * 平均每位教师可排课课次
         */
        private Double averageSlotsPerTeacher;

        /**
         * 查询天数
         */
        private Integer totalDays;
    }

    /**
     * 教学组统计
     */
    @Data
    public static class GroupStats {
        /**
         * 教学组ID
         */
        private String groupId;

        /**
         * 教学组名称
         */
        private String groupName;

        /**
         * 教师数量
         */
        private Integer teacherCount;

        /**
         * 总可排课课次
         */
        private Integer totalAvailableSlots;

        /**
         * 平均每位教师可排课课次
         */
        private Double averageSlotsPerTeacher;

        /**
         * 教师列表
         */
        private List<TeacherStats> teachers;
    }

    /**
     * 教师统计
     */
    @Data
    public static class TeacherStats {
        /**
         * 教师ID
         */
        private String teacherId;

        /**
         * 教师姓名
         */
        private String teacherName;

        /**
         * 总可排课课次
         */
        private Integer totalAvailableSlots;

        /**
         * 每日可排课课次
         */
        private List<DailyTeacherSlots> dailySlots;
    }

    /**
     * 每日教师可排课课次
     */
    @Data
    public static class DailyTeacherSlots {
        /**
         * 日期 (YYYY-MM-DD)
         */
        private String date;

        /**
         * 星期几 (1-7, 1=周一)
         */
        private Integer weekday;

        /**
         * 可排课课次
         */
        private Integer availableSlots;
    }

    /**
     * 每日统计
     */
    @Data
    public static class DailyStats {
        /**
         * 日期 (YYYY-MM-DD)
         */
        private String date;

        /**
         * 星期几 (1-7, 1=周一)
         */
        private Integer weekday;

        /**
         * 星期几名称
         */
        private String weekdayName;

        /**
         * 总可排课课次
         */
        private Integer totalAvailableSlots;

        /**
         * 有课时的教师数
         */
        private Integer availableTeachers;

        /**
         * 平均每位有课时教师的课次
         */
        private Double averageSlotsPerAvailableTeacher;

        /**
         * 各教学组当日统计
         */
        private List<DailyGroupStats> groupStats;
    }

    /**
     * 每日教学组统计
     */
    @Data
    public static class DailyGroupStats {
        /**
         * 教学组ID
         */
        private String groupId;

        /**
         * 教学组名称
         */
        private String groupName;

        /**
         * 可排课课次
         */
        private Integer availableSlots;

        /**
         * 有课时的教师数
         */
        private Integer availableTeachers;
    }

    /**
     * 每周统计
     */
    @Data
    public static class WeeklyStats {
        /**
         * 周开始日期 (YYYY-MM-DD)
         */
        private String weekStartDate;

        /**
         * 周结束日期 (YYYY-MM-DD)
         */
        private String weekEndDate;

        /**
         * 周标识 (如: 2025年第25周)
         */
        private String weekLabel;

        /**
         * 总可排课课次
         */
        private Integer totalAvailableSlots;

        /**
         * 平均每日可排课课次
         */
        private Double averageDailySlots;

        /**
         * 各教学组周统计
         */
        private List<WeeklyGroupStats> groupStats;
    }

    /**
     * 每周教学组统计
     */
    @Data
    public static class WeeklyGroupStats {
        /**
         * 教学组ID
         */
        private String groupId;

        /**
         * 教学组名称
         */
        private String groupName;

        /**
         * 周总可排课课次
         */
        private Integer totalAvailableSlots;

        /**
         * 周平均每日课次
         */
        private Double averageDailySlots;
    }

    /**
     * 教学组趋势查询请求
     */
    @Data
    public static class GroupTrendReq {
        /**
         * 教学组ID
         */
        private String groupId;

        /**
         * 开始日期 (YYYY-MM-DD)
         */
        private String startDate;

        /**
         * 结束日期 (YYYY-MM-DD)
         */
        private String endDate;
    }

    /**
     * 教学组趋势响应
     */
    @Data
    public static class GroupTrendResp {
        /**
         * 教学组ID
         */
        private String groupId;

        /**
         * 教学组名称
         */
        private String groupName;

        /**
         * 查询时间范围
         */
        private String startDate;
        private String endDate;

        /**
         * 总课次
         */
        private Integer totalSlots;

        /**
         * 日均课次
         */
        private Double averageSlots;

        /**
         * 峰值课次
         */
        private Integer peakSlots;

        /**
         * 教师数量
         */
        private Integer teacherCount;

        /**
         * 每日数据
         */
        private List<GroupTrendDailyData> dailyData;
    }

    /**
     * 教学组趋势每日数据
     */
    @Data
    public static class GroupTrendDailyData {
        /**
         * 日期 (YYYY-MM-DD)
         */
        private String date;

        /**
         * 星期几名称
         */
        private String weekdayName;

        /**
         * 可排课课次
         */
        private Integer availableSlots;

        /**
         * 有课时教师数
         */
        private Integer availableTeachers;

        /**
         * 平均课次
         */
        private Double averageSlots;

        /**
         * 利用率 (%)
         */
        private Double utilization;
    }

    /**
     * 指定日期教师详情查询请求
     */
    @Data
    public static class DayTeacherDetailReq {
        /**
         * 日期 (YYYY-MM-DD)
         */
        private String date;

        /**
         * 教学组ID列表（可选，为空则查询所有教学组）
         */
        private List<String> groupIds;
    }

    /**
     * 指定日期教师详情响应
     */
    @Data
    public static class DayTeacherDetailResp {
        /**
         * 日期
         */
        private String date;

        /**
         * 星期几名称
         */
        private String weekdayName;

        /**
         * 汇总信息
         */
        private DayTeacherSummary summary;

        /**
         * 教师列表
         */
        private List<DayTeacherInfo> teachers;
    }

    /**
     * 指定日期教师汇总信息
     */
    @Data
    public static class DayTeacherSummary {
        /**
         * 总教师数
         */
        private Integer totalTeachers;

        /**
         * 有可排课课次的教师数
         */
        private Integer availableTeachers;

        /**
         * 总可排课课次
         */
        private Integer totalSlots;

        /**
         * 平均每位教师可排课课次
         */
        private Double averageSlots;

        /**
         * 各教学组统计
         */
        private List<DayGroupSummary> groupSummaries;
    }

    /**
     * 指定日期教学组汇总信息
     */
    @Data
    public static class DayGroupSummary {
        /**
         * 教学组ID
         */
        private String groupId;

        /**
         * 教学组名称
         */
        private String groupName;

        /**
         * 教师数量
         */
        private Integer teacherCount;

        /**
         * 可排课课次
         */
        private Integer availableSlots;
    }

    /**
     * 指定日期教师信息
     */
    @Data
    public static class DayTeacherInfo {
        /**
         * 教师ID
         */
        private String teacherId;

        /**
         * 教师姓名
         */
        private String teacherName;

        /**
         * 教师手机号
         */
        private String teacherPhone;

        /**
         * 教师邮箱
         */
        private String teacherEmail;

        /**
         * 教师头像
         */
        private String teacherAvatar;

        /**
         * 教学组ID
         */
        private String groupId;

        /**
         * 教学组名称
         */
        private String groupName;

        /**
         * 可排课课次
         */
        private Integer availableSlots;

        /**
         * 时间段详情
         */
        private List<DayTimeSlotInfo> timeSlots;

        /**
         * 已排课程数量
         */
        private Integer scheduledCoursesCount;

        /**
         * 已排课程列表（用于前端显示）
         */
        private List<Object> scheduledCourses;
    }

    /**
     * 指定日期时间段信息
     */
    @Data
    public static class DayTimeSlotInfo {
        /**
         * 时间段ID
         */
        private String timeSlotId;

        /**
         * 开始时间 (HH:mm)
         */
        private String startTime;

        /**
         * 结束时间 (HH:mm)
         */
        private String endTime;

        /**
         * 状态 (available: 可排课, scheduled: 已排课)
         */
        private String status;

        /**
         * 备注
         */
        private String remark;
    }

    /**
     * 快捷日期选择枚举
     */
    public enum QuickDateRange {
        TODAY("今天"),
        TOMORROW("明天"),
        THIS_WEEK("本周"),
        NEXT_WEEK("下周"),
        THIS_MONTH("本月"),
        NEXT_MONTH("下月");

        private final String label;

        QuickDateRange(String label) {
            this.label = label;
        }

        public String getLabel() {
            return label;
        }
    }
}
