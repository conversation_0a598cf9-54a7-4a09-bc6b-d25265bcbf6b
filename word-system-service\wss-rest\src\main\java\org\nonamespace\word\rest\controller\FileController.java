package org.nonamespace.word.rest.controller;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.config.AliyunOssConfig;
import org.nonamespace.word.common.utils.OssService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文件上传下载控制器
 */
@Slf4j
@RestController
@RequestMapping("/word/file")
public class FileController {

    @Autowired
    private OssService ossService;
    @Resource
    private AliyunOssConfig ossConfig;

    /**
     * 上传文件
     *
     * @param file 文件对象
     * @param dir  存储目录（可选）
     * @return 上传结果
     */
    @PostMapping("/upload")
    public ResponseEntity<Map<String, Object>> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "dir", required = false, defaultValue = "uploads") String dir) {

        try {
            if (file.isEmpty()) {
                return ResponseEntity.badRequest().body(createResponse(false, "上传失败，请选择文件", null));
            }

            // 上传文件到OSS
            String fileUrl = ossService.uploadFile(file, dir);

            // 提取对象名称（用于后续下载或删除）
            String objectName = fileUrl.replace(ossConfig.getUrlPrefix(), "");

            Map<String, String> data = new HashMap<>();
            data.put("fileUrl", fileUrl);
            data.put("objectName", objectName);
            data.put("fileName", file.getOriginalFilename());
            data.put("fileSize", String.valueOf(file.getSize()));

            return ResponseEntity.ok(createResponse(true, "文件上传成功", data));
        } catch (Exception e) {
            log.error("文件上传异常: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(createResponse(false, "文件上传失败: " + e.getMessage(), null));
        }
    }

    /**
     * 获取文件上传签名URL
     *
     * @param fileName   文件名
     * @param dir        存储目录（可选）
     * @param expiration 过期时间（毫秒，可选，默认30分钟）
     * @return 签名URL
     */
    @GetMapping("/upload/sign")
    public ResponseEntity<Map<String, Object>> getUploadSignedUrl(
            @RequestParam("fileName") String fileName,
            @RequestParam(value = "dir", required = false, defaultValue = "uploads") String dir,
            @RequestParam(value = "expiration", required = false, defaultValue = "1800000") Long expiration) {

        try {
            // 获取上传签名URL
            String result = ossService.generateUploadSignedUrl(dir, fileName, expiration);
            String[] parts = result.split(",");

            Map<String, String> data = new HashMap<>();
            data.put("signedUrl", parts[0]);
            data.put("objectName", parts[1]);
            data.put("fileName", fileName);

            return ResponseEntity.ok(createResponse(true, "获取上传签名URL成功", data));
        } catch (Exception e) {
            log.error("获取上传签名URL异常: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(createResponse(false, "获取上传签名URL失败: " + e.getMessage(), null));
        }
    }

    /**
     * 获取文件下载签名URL
     *
     * @param objectName OSS对象名称
     * @param expiration 过期时间（毫秒，可选，默认30分钟）
     * @return 签名URL
     */
    @GetMapping("/download/sign")
    public ResponseEntity<Map<String, Object>> getDownloadSignedUrl(
            @RequestParam("objectName") String objectName,
            @RequestParam(value = "expiration", required = false, defaultValue = "1800000") Long expiration) {

        try {
            // 获取下载签名URL
            String signedUrl = ossService.generateDownloadSignedUrl(objectName, expiration);

            Map<String, String> data = new HashMap<>();
            data.put("signedUrl", signedUrl);
            data.put("objectName", objectName);

            return ResponseEntity.ok(createResponse(true, "获取下载签名URL成功", data));
        } catch (Exception e) {
            log.error("获取下载签名URL异常: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(createResponse(false, "获取下载签名URL失败: " + e.getMessage(), null));
        }
    }

    /**
     * 删除文件
     *
     * @param objectName OSS对象名称
     * @return 删除结果
     */
//    @DeleteMapping("/delete")
    public ResponseEntity<Map<String, Object>> deleteFile(@RequestParam("objectName") String objectName) {
        try {
            boolean result = ossService.deleteFile(objectName);

            if (result) {
                return ResponseEntity.ok(createResponse(true, "文件删除成功", null));
            } else {
                return ResponseEntity.badRequest().body(createResponse(false, "文件删除失败", null));
            }
        } catch (Exception e) {
            log.error("文件删除异常: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(createResponse(false, "文件删除失败: " + e.getMessage(), null));
        }
    }

    /**
     * 批量上传教师资质证书
     *
     * @param files 证书文件数组
     * @return 上传结果
     */
    @PostMapping("/teacher/certificates")
    public ResponseEntity<Map<String, Object>> uploadTeacherCertificates(
            @RequestParam(value = "file", required = false) MultipartFile[] files,
            @RequestParam(value = "files", required = false) MultipartFile[] filesArray) {
        try {
            // 处理两种可能的参数名
            MultipartFile[] actualFiles = files != null ? files : filesArray;

            if (actualFiles == null || actualFiles.length == 0) {
                return ResponseEntity.badRequest().body(createResponse(false, "请选择要上传的证书文件", null));
            }

            if (actualFiles.length > 5) {
                return ResponseEntity.badRequest().body(createResponse(false, "最多只能上传5个证书文件", null));
            }

            List<Map<String, String>> uploadResults = new ArrayList<>();

            for (MultipartFile file : actualFiles) {
                if (file.isEmpty()) {
                    continue;
                }

                // 验证文件类型
                String fileName = file.getOriginalFilename();
                if (!isValidCertificateFile(fileName)) {
                    return ResponseEntity.badRequest().body(
                        createResponse(false, "文件 " + fileName + " 格式不支持，请上传PDF、Word文档或图片格式", null));
                }

                // 上传文件
                String fileUrl = ossService.uploadFile(file, "teacher/certificates");
                String objectName = fileUrl.replace(ossConfig.getUrlPrefix(), "");

                Map<String, String> fileInfo = new HashMap<>();
                fileInfo.put("fileUrl", fileUrl);
                fileInfo.put("objectName", objectName);
                fileInfo.put("fileName", fileName);
                fileInfo.put("fileSize", String.valueOf(file.getSize()));

                uploadResults.add(fileInfo);
            }

            Map<String, Object> data = new HashMap<>();
            data.put("files", uploadResults);
            data.put("count", uploadResults.size());

            return ResponseEntity.ok(createResponse(true, "证书文件上传成功", data));
        } catch (Exception e) {
            log.error("证书文件上传异常: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(createResponse(false, "证书文件上传失败: " + e.getMessage(), null));
        }
    }

    /**
     * 批量上传教师示范视频
     *
     * @param files 视频文件数组
     * @return 上传结果
     */
    @PostMapping("/teacher/videos")
    public ResponseEntity<Map<String, Object>> uploadTeacherVideos(
            @RequestParam(value = "file", required = false) MultipartFile[] files,
            @RequestParam(value = "files", required = false) MultipartFile[] filesArray) {
        try {
            // 处理两种可能的参数名
            MultipartFile[] actualFiles = files != null ? files : filesArray;

            if (actualFiles == null || actualFiles.length == 0) {
                return ResponseEntity.badRequest().body(createResponse(false, "请选择要上传的视频文件", null));
            }

            if (actualFiles.length > 3) {
                return ResponseEntity.badRequest().body(createResponse(false, "最多只能上传3个视频文件", null));
            }

            List<Map<String, String>> uploadResults = new ArrayList<>();

            for (MultipartFile file : actualFiles) {
                if (file.isEmpty()) {
                    continue;
                }

                // 验证文件类型
                String fileName = file.getOriginalFilename();
                if (!isValidVideoFile(fileName)) {
                    return ResponseEntity.badRequest().body(
                        createResponse(false, "文件 " + fileName + " 格式不支持，请上传MP4、AVI、MOV等视频格式", null));
                }

                // 上传文件
                String fileUrl = ossService.uploadFile(file, "teacher/videos");
                String objectName = fileUrl.replace(ossConfig.getUrlPrefix(), "");

                Map<String, String> fileInfo = new HashMap<>();
                fileInfo.put("fileUrl", fileUrl);
                fileInfo.put("objectName", objectName);
                fileInfo.put("fileName", fileName);
                fileInfo.put("fileSize", String.valueOf(file.getSize()));

                uploadResults.add(fileInfo);
            }

            Map<String, Object> data = new HashMap<>();
            data.put("files", uploadResults);
            data.put("count", uploadResults.size());

            return ResponseEntity.ok(createResponse(true, "示范视频上传成功", data));
        } catch (Exception e) {
            log.error("示范视频上传异常: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(createResponse(false, "示范视频上传失败: " + e.getMessage(), null));
        }
    }

    /**
     * 单个文件上传（教师资质证书）
     *
     * @param file 证书文件
     * @return 上传结果
     */
    @PostMapping("/teacher/certificate")
    public ResponseEntity<Map<String, Object>> uploadTeacherCertificate(
            @RequestParam("file") MultipartFile file) {
        try {
            if (file == null || file.isEmpty()) {
                return ResponseEntity.badRequest().body(createResponse(false, "请选择要上传的证书文件", null));
            }

            // 验证文件类型
            String fileName = file.getOriginalFilename();
            if (!isValidCertificateFile(fileName)) {
                return ResponseEntity.badRequest().body(
                    createResponse(false, "文件格式不支持，请上传PDF、Word文档或图片格式", null));
            }

            // 上传文件
            String fileUrl = ossService.uploadFile(file, StrUtil.format("teacher/certificates/{}_{}", IdUtil.getSnowflakeNextIdStr(), fileName));
            String objectName = fileUrl.replace(ossConfig.getUrlPrefix(), "");

            Map<String, String> data = new HashMap<>();
            data.put("fileUrl", fileUrl);
            data.put("objectName", objectName);
            data.put("fileName", fileName);
            data.put("fileSize", String.valueOf(file.getSize()));

            return ResponseEntity.ok(createResponse(true, "证书文件上传成功", data));
        } catch (Exception e) {
            log.error("证书文件上传异常: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(createResponse(false, "证书文件上传失败: " + e.getMessage(), null));
        }
    }

    /**
     * 单个文件上传（教师示范视频）
     *
     * @param file 视频文件
     * @return 上传结果
     */
    @PostMapping("/teacher/video")
    public ResponseEntity<Map<String, Object>> uploadTeacherVideo(
            @RequestParam("file") MultipartFile file) {
        try {
            if (file == null || file.isEmpty()) {
                return ResponseEntity.badRequest().body(createResponse(false, "请选择要上传的视频文件", null));
            }

            // 验证文件类型
            String fileName = file.getOriginalFilename();
            if (!isValidVideoFile(fileName)) {
                return ResponseEntity.badRequest().body(
                    createResponse(false, "文件格式不支持，请上传MP4、AVI、MOV等视频格式", null));
            }

            // 上传文件
            String fileUrl = ossService.uploadFile(file, StrUtil.format("teacher/videos/{}_{}", IdUtil.getSnowflakeNextIdStr(), fileName));
            String objectName = fileUrl.replace(ossConfig.getUrlPrefix(), "");

            Map<String, String> data = new HashMap<>();
            data.put("fileUrl", fileUrl);
            data.put("objectName", objectName);
            data.put("fileName", fileName);
            data.put("fileSize", String.valueOf(file.getSize()));

            return ResponseEntity.ok(createResponse(true, "示范视频上传成功", data));
        } catch (Exception e) {
            log.error("示范视频上传异常: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(createResponse(false, "示范视频上传失败: " + e.getMessage(), null));
        }
    }

    /**
     * 验证证书文件格式
     */
    private boolean isValidCertificateFile(String fileName) {
        if (fileName == null) return false;
        String extension = fileName.toLowerCase().substring(fileName.lastIndexOf(".") + 1);
        return extension.matches("pdf|doc|docx|jpg|jpeg|png");
    }

    /**
     * 验证视频文件格式
     */
    private boolean isValidVideoFile(String fileName) {
        if (fileName == null) return false;
        String extension = fileName.toLowerCase().substring(fileName.lastIndexOf(".") + 1);
        return extension.matches("mp4|avi|mov|wmv|flv");
    }

    /**
     * 创建响应数据
     *
     * @param success 是否成功
     * @param message 消息
     * @param data    数据
     * @return 响应数据
     */
    private Map<String, Object> createResponse(boolean success, String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", success);
        response.put("message", message);
        response.put("data", data);
        return response;
    }
}