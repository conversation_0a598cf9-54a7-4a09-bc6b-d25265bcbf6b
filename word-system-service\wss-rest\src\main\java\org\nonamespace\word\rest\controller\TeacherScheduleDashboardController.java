package org.nonamespace.word.rest.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.dto.management.teacherschedule.TeacherScheduleDashboardDto;
import org.nonamespace.word.server.service.ITeacherScheduleDashboardService;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

/**
 * 老师可排课时间看板控制器
 * 
 * <AUTHOR>
 * @since 2025-06-19
 */
@Slf4j
@RestController
@RequestMapping("/api/teacher-schedule-dashboard")
@RequiredArgsConstructor
public class TeacherScheduleDashboardController {

    private final ITeacherScheduleDashboardService teacherScheduleDashboardService;

    /**
     * 获取老师可排课时间看板
     */
    @PostMapping("/dashboard")
    public AjaxResult getDashboard(@RequestBody TeacherScheduleDashboardDto.QueryReq request) {
        try {
            log.info("获取老师可排课时间看板: {}", request);
            
            // 参数验证
            if (request.getStartDate() == null || request.getEndDate() == null) {
                return AjaxResult.error("开始日期和结束日期不能为空");
            }

            TeacherScheduleDashboardDto.DashboardResp response = 
                teacherScheduleDashboardService.getDashboard(request);

            return AjaxResult.success("获取看板数据成功", response);
        } catch (Exception e) {
            log.error("获取老师可排课时间看板失败", e);
            return AjaxResult.error("获取看板数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取快捷日期范围
     */
    @GetMapping("/quick-date-range/{quickRange}")
    public AjaxResult getQuickDateRange(@PathVariable String quickRange) {
        try {
            TeacherScheduleDashboardDto.QuickDateRange range = 
                TeacherScheduleDashboardDto.QuickDateRange.valueOf(quickRange.toUpperCase());
            
            Map<String, String> dateRange = teacherScheduleDashboardService.getQuickDateRange(range);
            
            return AjaxResult.success("获取快捷日期范围成功", dateRange);
        } catch (IllegalArgumentException e) {
            return AjaxResult.error("不支持的快捷日期范围: " + quickRange);
        } catch (Exception e) {
            log.error("获取快捷日期范围失败: quickRange={}", quickRange, e);
            return AjaxResult.error("获取快捷日期范围失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有快捷日期选项
     */
    @GetMapping("/quick-date-options")
    public AjaxResult getQuickDateOptions() {
        try {
            Map<String, Object> options = new HashMap<>();
            
            for (TeacherScheduleDashboardDto.QuickDateRange range : 
                 TeacherScheduleDashboardDto.QuickDateRange.values()) {
                
                Map<String, String> dateRange = teacherScheduleDashboardService.getQuickDateRange(range);
                
                Map<String, Object> option = new HashMap<>();
                option.put("label", range.getLabel());
                option.put("value", range.name().toLowerCase());
                option.put("startDate", dateRange.get("startDate"));
                option.put("endDate", dateRange.get("endDate"));
                
                options.put(range.name().toLowerCase(), option);
            }
            
            return AjaxResult.success("获取快捷日期选项成功", options);
        } catch (Exception e) {
            log.error("获取快捷日期选项失败", e);
            return AjaxResult.error("获取快捷日期选项失败: " + e.getMessage());
        }
    }

    /**
     * 计算单个教师指定日期的可排课课次
     */
    @GetMapping("/teacher/{teacherId}/available-slots")
    public AjaxResult getTeacherAvailableSlots(
            @PathVariable String teacherId,
            @RequestParam String date) {
        try {
            LocalDate targetDate = LocalDate.parse(date);
            Integer slots = teacherScheduleDashboardService.calculateTeacherAvailableSlots(teacherId, targetDate);
            
            Map<String, Object> result = new HashMap<>();
            result.put("teacherId", teacherId);
            result.put("date", date);
            result.put("availableSlots", slots);
            
            return AjaxResult.success("获取教师可排课课次成功", result);
        } catch (Exception e) {
            log.error("获取教师可排课课次失败: teacherId={}, date={}", teacherId, date, e);
            return AjaxResult.error("获取教师可排课课次失败: " + e.getMessage());
        }
    }

    /**
     * 验证日期范围
     */
    @GetMapping("/validate-date-range")
    public AjaxResult validateDateRange(
            @RequestParam String startDate,
            @RequestParam String endDate) {
        try {
            boolean isValid = teacherScheduleDashboardService.validateDateRange(startDate, endDate);
            
            Map<String, Object> result = new HashMap<>();
            result.put("valid", isValid);
            result.put("startDate", startDate);
            result.put("endDate", endDate);
            
            if (!isValid) {
                result.put("message", "日期范围无效：不能超过31天，且开始日期不能早于今天");
            }
            
            return AjaxResult.success("日期范围验证完成", result);
        } catch (Exception e) {
            log.error("验证日期范围失败: startDate={}, endDate={}", startDate, endDate, e);
            return AjaxResult.error("验证日期范围失败: " + e.getMessage());
        }
    }

    /**
     * 获取今天的日期（用于前端默认值）
     */
    @GetMapping("/today")
    public AjaxResult getToday() {
        try {
            String today = LocalDate.now().toString();
            
            Map<String, String> result = new HashMap<>();
            result.put("today", today);
            
            return AjaxResult.success("获取今天日期成功", result);
        } catch (Exception e) {
            log.error("获取今天日期失败", e);
            return AjaxResult.error("获取今天日期失败: " + e.getMessage());
        }
    }

    /**
     * 获取教学组趋势数据
     */
    @PostMapping("/group-trend")
    public AjaxResult getGroupTrend(@RequestBody TeacherScheduleDashboardDto.GroupTrendReq request) {
        try {
            log.info("获取教学组趋势数据: {}", request);

            // 参数验证
            if (request.getGroupId() == null || request.getGroupId().trim().isEmpty()) {
                return AjaxResult.error("教学组ID不能为空");
            }
            if (request.getStartDate() == null || request.getEndDate() == null) {
                return AjaxResult.error("开始日期和结束日期不能为空");
            }

            TeacherScheduleDashboardDto.GroupTrendResp response =
                teacherScheduleDashboardService.getGroupTrend(request);

            return AjaxResult.success("获取教学组趋势数据成功", response);
        } catch (Exception e) {
            log.error("获取教学组趋势数据失败", e);
            return AjaxResult.error("获取教学组趋势数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取看板配置信息
     */
    @GetMapping("/config")
    public AjaxResult getConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("maxDateRange", 31); // 最大日期范围
            config.put("minDate", LocalDate.now().toString()); // 最小日期（今天）
            config.put("maxDate", LocalDate.now().plusDays(365).toString()); // 最大日期（一年后）

            // 快捷日期选项
            Map<String, String> quickOptions = new HashMap<>();
            for (TeacherScheduleDashboardDto.QuickDateRange range :
                 TeacherScheduleDashboardDto.QuickDateRange.values()) {
                quickOptions.put(range.name().toLowerCase(), range.getLabel());
            }
            config.put("quickDateOptions", quickOptions);

            return AjaxResult.success("获取看板配置成功", config);
        } catch (Exception e) {
            log.error("获取看板配置失败", e);
            return AjaxResult.error("获取看板配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定日期的教师详细数据
     */
    @PostMapping("/day-teacher-detail")
    public AjaxResult getDayTeacherDetail(@RequestBody TeacherScheduleDashboardDto.DayTeacherDetailReq request) {
        try {
            log.info("获取指定日期教师详细数据: {}", request);

            // 参数验证
            if (request.getDate() == null || request.getDate().trim().isEmpty()) {
                return AjaxResult.error("日期不能为空");
            }

            TeacherScheduleDashboardDto.DayTeacherDetailResp response =
                teacherScheduleDashboardService.getDayTeacherDetail(request);

            return AjaxResult.success("获取指定日期教师详细数据成功", response);
        } catch (Exception e) {
            log.error("获取指定日期教师详细数据失败", e);
            return AjaxResult.error("获取指定日期教师详细数据失败: " + e.getMessage());
        }
    }
}
