package org.nonamespace.word.server.dto.management.teachingleader;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 教学组长相关DTO
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public class TeachingGroupLeaderDto {

    /**
     * 获取待审核申请列表请求参数
     */
    @Data
    public static class GetPendingApplicationsReq {
        private Integer pageNum = 1;
        private Integer pageSize = 20;
        private String studentName;
        private String studentPhone;
        private String teacherName;
        private String teacherPhone;
        private List<String> statusList; // 状态列表（支持多选）
        private String status; // 单个状态（兼容旧代码）
        private String subject;
        private String specification;
        private String salesGroupId; // 销售组ID
        private String salesId; // 销售人员ID
        private String approvalResult; // 审核结果查询条件
        private String createTimeStart;
        private String createTimeEnd;
    }

    /**
     * 审核申请请求参数
     */
    @Data
    public static class ReviewApplicationReq {
        @NotBlank(message = "申请ID不能为空")
        private String applicationId;

        @NotBlank(message = "审核结果不能为空")
        private String reviewResult; // approved: 通过, rejected: 拒绝, need_more_info: 需要更多信息

        private String assignedTeacherId; // 分配的教师ID（通过时必填）
        private TimeSlotInfo assignedTimeSlot; // 分配的时间段（学生偏好时间段，可选）
//        @NotNull(message = "试听课时间不能为空")
        private TrialTimeSlotInfo assignedTrialTimeSlot; // 分配的试听课时间段（具体试听课时间，必填）
        private Boolean autoSchedule; // 是否自动排课（根据是否选择时间段决定）

        @NotBlank(message = "审核意见不能为空")
        private String reviewComment;

        private String rejectionReason; // 拒绝原因（拒绝时必填）
        private String suggestion; // 建议（可选）
    }

    /**
     * 时间段信息（用于学生偏好时间段）
     */
    @Data
    public static class TimeSlotInfo {
        @NotNull(message = "星期不能为空")
        private Integer weekday; // 1-7 (周一到周日)

        @NotBlank(message = "开始时间不能为空")
        private String startTime; // 格式: "09:15"

        @NotBlank(message = "结束时间不能为空")
        private String endTime; // 格式: "10:55"
    }

    /**
     * 试听课时间段信息（用于试听课具体时间）
     */
    @Data
    public static class TrialTimeSlotInfo {
        @NotBlank(message = "试听课日期不能为空")
        private String date; // 格式: "2025-01-13"

        @NotBlank(message = "开始时间不能为空")
        private String startTime; // 格式: "09:15"

        @NotBlank(message = "结束时间不能为空")
        private String endTime; // 格式: "10:15" (固定1小时)
    }

    /**
     * 批量审核请求参数
     */
    @Data
    public static class BatchReviewReq {
        @NotEmpty(message = "申请ID列表不能为空")
        private List<String> applicationIds;
        
        @NotBlank(message = "审核结果不能为空")
        private String reviewResult; // approved: 批量通过, rejected: 批量拒绝
        
        private String reviewComment;
        private String rejectionReason; // 批量拒绝时的原因
        
        // 批量通过时的分配信息
        private List<BatchAssignmentInfo> assignments;
    }

    /**
     * 批量分配信息
     */
    @Data
    public static class BatchAssignmentInfo {
        @NotBlank(message = "申请ID不能为空")
        private String applicationId;
        
        @NotBlank(message = "教师ID不能为空")
        private String teacherId;
        
        @NotNull(message = "时间段不能为空")
        private TimeSlotInfo timeSlot;
    }

    /**
     * 批量审核响应
     */
    @Data
    public static class BatchReviewResp {
        private Integer totalCount; // 总数
        private Integer successCount; // 成功数
        private Integer failedCount; // 失败数
        private List<String> failedApplicationIds; // 失败的申请ID列表
        private List<String> failedReasons; // 失败原因列表
    }

    /**
     * 审核统计响应
     */
    @Data
    public static class ReviewStatsResp {
        private Integer pendingCount; // 待审核数量
        private Integer todayReviewedCount; // 今日已审核数量
        private Integer weekReviewedCount; // 本周已审核数量
        private Integer monthReviewedCount; // 本月已审核数量
        
        private Integer approvedCount; // 通过数量
        private Integer rejectedCount; // 拒绝数量
        private Double approvalRate; // 通过率
        
        private Integer highPriorityCount; // 高优先级待审核数量
        private Integer overdueCount; // 超时未审核数量
        
        // 按学科统计
        private List<SubjectStats> subjectStats;
    }

    /**
     * 学科统计
     */
    @Data
    public static class SubjectStats {
        private String subject;
        private Integer pendingCount;
        private Integer approvedCount;
        private Integer rejectedCount;
    }

    /**
     * 教学组教师响应
     */
    @Data
    public static class GroupTeacherResp {
        private String teacherId;
        private String teacherName;
        private String teacherPhone;
        private String status; // active: 活跃, inactive: 停用
        private List<String> teachingSubjects;
        private List<String> teachingSpecifications;
        private Integer currentStudentCount; // 当前学生数
        private Integer maxStudentCount; // 最大学生数
        private Boolean isAvailable; // 是否可分配
        private String unavailableReason; // 不可分配原因
    }

    /**
     * 教师时间段响应
     */
    @Data
    public static class TeacherTimeSlotResp {
        private String id;
        private Integer weekday;
        private String startTime;
        private String endTime;
        private String status; // available: 可用, occupied: 已占用, blocked: 被阻止
        private String occupiedReason; // 占用原因
    }

    /**
     * 试听课时间段响应（用于审核时选择具体时间）
     */
    @Data
    public static class TrialTimeSlotResp {
        private String date;      // 试听课日期 (YYYY-MM-DD格式)
        private String startTime; // 开始时间 (HH:mm格式)
        private String endTime;   // 结束时间 (HH:mm格式)
        private Boolean available; // 是否可用
        private String unavailableReason; // 不可用原因
    }

    /**
     * 分配教师请求参数
     */
    @Data
    public static class AssignTeacherReq {
        @NotBlank(message = "申请ID不能为空")
        private String applicationId;
        
        @NotBlank(message = "教师ID不能为空")
        private String teacherId;
        
        @NotNull(message = "时间段不能为空")
        private TimeSlotInfo timeSlot;
        
        private String assignmentComment; // 分配说明
    }

    /**
     * 获取审核历史请求参数
     */
    @Data
    public static class GetReviewHistoryReq {
        private Integer pageNum = 1;
        private Integer pageSize = 20;
        private String reviewResult; // approved, rejected, need_more_info
        private String reviewTimeStart;
        private String reviewTimeEnd;
        private String studentName;
        private String subject;
    }

    /**
     * 审核历史响应
     */
    @Data
    public static class ReviewHistoryResp {
        private String applicationId;
        private String studentName;
        private String studentPhone;
        private String subject;
        private String specification;
        private String reviewResult;
        private String reviewComment;
        private String assignedTeacherName;
        private String assignedTimeSlot;
        private Date reviewTime;
        private String reviewerName;
    }

    /**
     * 导出审核报告请求参数
     */
    @Data
    public static class ExportReviewReportReq {
        private String reportType; // daily: 日报, weekly: 周报, monthly: 月报
        private String startDate;
        private String endDate;
        private List<String> subjects; // 筛选学科
        private Boolean includeDetails; // 是否包含详细信息
    }

    /**
     * 教学组信息响应
     */
    @Data
    public static class TeachingGroupInfoResp {
        private String groupId;
        private String groupName;
        private String description;
        private Integer memberCount;
        private Integer pendingApplicationCount;
        private String leaderName;
        private String adminName;
        private Date createTime;
    }

    /**
     * 设置审核规则请求参数
     */
    @Data
    public static class SetReviewRulesReq {
        private Integer autoApprovalHours; // 自动审核时间（小时）
        private Boolean enableAutoAssignment; // 是否启用自动分配
        private Integer maxDailyAssignments; // 每日最大分配数量
        private List<String> prioritySubjects; // 优先学科
        private Boolean enableOverdueAlert; // 是否启用超时提醒
        private Integer overdueHours; // 超时小时数
    }

    /**
     * 审核规则响应
     */
    @Data
    public static class ReviewRulesResp {
        private Integer autoApprovalHours;
        private Boolean enableAutoAssignment;
        private Integer maxDailyAssignments;
        private List<String> prioritySubjects;
        private Boolean enableOverdueAlert;
        private Integer overdueHours;
        private Date lastUpdateTime;
        private String lastUpdateBy;
    }
}
