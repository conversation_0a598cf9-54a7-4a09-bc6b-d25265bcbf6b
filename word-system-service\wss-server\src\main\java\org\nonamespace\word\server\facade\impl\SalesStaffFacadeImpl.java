package org.nonamespace.word.server.facade.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.misc.WssContext;
import org.nonamespace.word.server.domain.SaleProfile;
import org.nonamespace.word.server.domain.SalesGroup;
import org.nonamespace.word.server.domain.SalesGroupMember;
import org.nonamespace.word.server.domain.UserStudentExt;
import org.nonamespace.word.server.dto.sales.SalesStaffDto;
import org.nonamespace.word.server.facade.SalesStaffFacade;
import org.nonamespace.word.server.service.*;
import org.nonamespace.word.server.util.SystemDataQueryUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 销售人员管理业务门面实现类
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SalesStaffFacadeImpl implements SalesStaffFacade {

    private final IUserService userService;
    private final IDeptService deptService;
    private final ISaleProfileService saleProfileService;
    private final ISalesGroupService salesGroupService;
    private final ISalesGroupMemberService salesGroupMemberService;
    private final SystemDataQueryUtil systemDataQueryUtil;
    private final UserStudentExtService userStudentExtService;

    @Override
    public IPage<SalesStaffDto.Resp> getSalesStaffPage(SalesStaffDto.GetListReq req) {
        log.info("分页查询销售人员列表: req={}", req);

        // 检查权限
        checkViewPermission();

        Set<String> groupIds = new HashSet<>();

        if(systemDataQueryUtil.isAdminOrHr()){
            // 管理员或人事可以查看所有销售人员
            if(StrUtil.isNotEmpty(req.getGroupId())){
                groupIds.add(req.getGroupId());
            }
        }else if(systemDataQueryUtil.isSalesGroupLeader()){
            List<SalesGroup> groups = salesGroupService.lambdaQuery()
                    .eq(SalesGroup::getLeaderId, systemDataQueryUtil.getCurrentUserId())
                    .eq(SalesGroup::getDeleted, false)
                    .select(SalesGroup::getId)
                    .list();
            if(CollUtil.isNotEmpty(groups)){
                groupIds.addAll(groups.stream()
                        .map(SalesGroup::getId)
                        .collect(Collectors.toSet()));
            }

            if(StrUtil.isNotEmpty(req.getGroupId())){
                groupIds.removeIf(x-> !x.equals(req.getGroupId()));
            }

            if(CollUtil.isEmpty(groupIds)){
                // 如果没有组，返回空结果
                return new Page<>(req.getPageNum(), req.getPageSize(), 0);
            }
        }


        Page<SaleProfile> page = new Page<>(req.getPageNum(), req.getPageSize());

        // 从sale_profile表查询销售人员
        IPage<SaleProfile> profilePage = saleProfileService.lambdaQuery()
                .eq(SaleProfile::getDeleted, false)
                .like(StrUtil.isNotEmpty(req.getSalesName()), SaleProfile::getSalesName, req.getSalesName())
                .like(StrUtil.isNotEmpty(req.getPhone()), SaleProfile::getPhone, req.getPhone())
                .in(CollUtil.isNotEmpty(groupIds), SaleProfile::getSalesGroupId, groupIds)
                .eq(StrUtil.isNotEmpty(req.getStatus()), SaleProfile::getStatus, req.getStatus())
                .orderByDesc(SaleProfile::getCreateTime)
                .page(page);

        // 转换为响应DTO
        List<SalesStaffDto.Resp> respList = profilePage.getRecords().stream().map(profile -> {
            SalesStaffDto.Resp resp = new SalesStaffDto.Resp();
            resp.setId(profile.getId());
            resp.setUserId(profile.getSalesId());
            resp.setSalesName(profile.getSalesName());
            resp.setNickName(profile.getSalesName()); // 前端期望nickName字段
            resp.setPhone(profile.getPhone());
            resp.setPhonenumber(profile.getPhone()); // 前端期望phonenumber字段
            resp.setEmail(profile.getEmail());
            resp.setStatus(profile.getStatus());
            resp.setGroupId(profile.getSalesGroupId());
            resp.setGroupName(profile.getSalesGroupName());
            resp.setJoinTime(profile.getJoinTime());
            resp.setStudentCount(getStudentCountBySalesId(profile.getSalesId())); // 正确计算学生数量
            resp.setCreateTime(profile.getCreateTime());
            resp.setUpdateTime(profile.getUpdateTime());
            return resp;
        }).collect(Collectors.toList());

        // 构建结果
        IPage<SalesStaffDto.Resp> result = new Page<>(profilePage.getCurrent(), profilePage.getSize(), profilePage.getTotal());
        result.setRecords(respList);

        log.info("查询销售人员列表成功: total={}, size={}", result.getTotal(), result.getRecords().size());
        return result;
    }

    @Override
    public SalesStaffDto.Resp getSalesStaffById(String id) {
        log.info("查询销售人员详情: id={}", id);

        if (StrUtil.isEmpty(id)) {
            throw new IllegalArgumentException("销售人员ID不能为空");
        }

        // 查询销售人员档案
        SaleProfile profile = saleProfileService.lambdaQuery()
                .eq(SaleProfile::getSalesId, id)
                .eq(SaleProfile::getDeleted, false)
                .one();

        if (profile == null) {
            throw new RuntimeException("销售人员档案不存在");
        }

        // 检查权限
        checkDataPermission(id);

        // 查询sys_user表获取性别等信息
        SysUser user = userService.lambdaQuery()
                .eq(SysUser::getUserId, profile.getSalesId())
                .eq(SysUser::getDelFlag, "0")
                .one();

        // 转换为响应DTO
        SalesStaffDto.Resp result = new SalesStaffDto.Resp();
        result.setId(profile.getId());
        result.setUserId(profile.getSalesId());
        result.setSalesName(profile.getSalesName());
        result.setNickName(profile.getSalesName()); // 前端兼容字段
        result.setPhone(profile.getPhone());
        result.setPhonenumber(profile.getPhone()); // 前端兼容字段
        result.setEmail(profile.getEmail());
        result.setSex(user != null ? user.getSex() : "2"); // 设置性别，默认为未知
        result.setStatus(profile.getStatus());
        result.setGroupId(profile.getSalesGroupId());
        result.setGroupName(profile.getSalesGroupName());
        result.setJoinTime(profile.getJoinTime());
        result.setStudentCount(getStudentCountBySalesId(profile.getSalesId()));
        result.setCreateTime(profile.getCreateTime());
        result.setUpdateTime(profile.getUpdateTime());

        // 设置关联信息（部门、销售组等）
        enrichSalesStaffInfo(Arrays.asList(result));

        log.info("查询销售人员详情成功: name={}", result.getSalesName());
        return result;
    }

    @Override
    public String createSalesStaff(SalesStaffDto.CreateReq req) {
        log.info("创建销售人员: req={}", req);

        // 检查权限
        checkCreatePermission();

        // 检查手机号是否已存在
        boolean phoneExists = userService.lambdaQuery()
                .eq(SysUser::getPhonenumber, req.getPhone())
                .eq(SysUser::getDelFlag, "0")
                .exists();
        if (phoneExists) {
            throw new RuntimeException("手机号已存在");
        }

        Long deptId = null;

        if(StrUtil.isNotEmpty(req.getGroupId())){
            deptId = salesGroupService.getById(req.getGroupId()).getDeptId();
        }else{
            deptId = systemDataQueryUtil.getSalesDept().getDeptId();
        }

        // 创建用户
        SysUser user = new SysUser();
        user.setUserName(req.getPhone()); // 使用手机号作为用户名
        user.setNickName(req.getSalesName());
        user.setPhonenumber(req.getPhone());
        user.setEmail(req.getEmail());
        user.setSex(req.getSex()); // 设置性别
        user.setStatus("0"); // 正常状态
        user.setDeptId(deptId);
        user.setCreateBy(WssContext.userId());
        user.setCreateTime(WssContext.now());
        user.setRoleIds(new Long[]{systemDataQueryUtil.getSalesRole().getRoleId()});
        boolean success = userService.save(user);
        if (!success) {
            throw new RuntimeException("创建用户失败");
        }

        // 创建销售人员档案
        createSaleProfile(user, req);

        // 如果指定了销售组，则加入销售组
        if (StrUtil.isNotEmpty(req.getGroupId())) {
            addToSalesGroup(user.getUserId().toString(), req.getGroupId(), "member");
        }

        log.info("创建销售人员成功: id={}, name={}", user.getUserId(), req.getSalesName());
        return user.getUserId().toString();
    }

    @Override
    public boolean updateSalesStaff(SalesStaffDto.UpdateReq req) {
        log.info("更新销售人员: req={}", req);

        // 检查权限
        checkUpdatePermission(req.getId());

        // 查询现有销售人员档案
        SaleProfile existingProfile = saleProfileService.lambdaQuery()
                .eq(SaleProfile::getSalesId, req.getId())
                .eq(SaleProfile::getDeleted, false)
                .one();
        if (existingProfile == null) {
            throw new RuntimeException("销售人员档案不存在");
        }

        // 检查手机号是否被其他销售人员使用
        boolean phoneExists = saleProfileService.lambdaQuery()
                .eq(SaleProfile::getPhone, req.getPhone())
                .ne(SaleProfile::getSalesId, req.getId())
                .eq(SaleProfile::getDeleted, false)
                .exists();
        if (phoneExists) {
            throw new RuntimeException("手机号已被其他销售人员使用");
        }

        // 更新销售人员档案
        existingProfile.setSalesName(req.getSalesName());
        existingProfile.setPhone(req.getPhone());
        existingProfile.setEmail(req.getEmail());
        existingProfile.setStatus(req.getStatus());
        existingProfile.setUpdateBy(WssContext.userId());
        existingProfile.setUpdateTime(WssContext.now());

        // 确保角色始终为销售，不允许修改
//            existingProfile.setRoleType("sales");
        // 注意：不更新系统角色，保持为销售角色

        // 更新销售组关联
        if (StrUtil.isNotEmpty(req.getGroupId())) {
            // 更新档案中的销售组信息
            SalesGroup group = salesGroupService.getById(req.getGroupId());
            if (group != null) {
                existingProfile.setSalesGroupId(req.getGroupId());
                existingProfile.setSalesGroupName(group.getName());
            }
            // 更新销售组成员关系
            updateUserGroup(existingProfile.getSalesId(), req.getGroupId());
        } else {
            // 清空销售组信息
            existingProfile.setSalesGroupId(null);
            existingProfile.setSalesGroupName(null);
            updateUserGroup(existingProfile.getSalesId(), null);
        }

        boolean profileSuccess = saleProfileService.updateById(existingProfile);
        if (!profileSuccess) {
            throw new RuntimeException("更新销售人员档案失败");
        }

        // 同步更新sys_user表的基本信息
        SysUser user = new SysUser();
        user.setUserId(Long.valueOf(existingProfile.getSalesId()));
        user.setNickName(req.getSalesName());
        user.setPhonenumber(req.getPhone());
        user.setEmail(req.getEmail());
        user.setSex(req.getSex()); // 设置性别
        // 转换状态：active -> 0, inactive -> 1
        user.setStatus("active".equals(req.getStatus()) ? "0" : "1");
        user.setDeptId(req.getDeptId());
        user.setUpdateBy(SecurityUtils.getUsername());
        user.setUpdateTime(WssContext.now());

        if(StrUtil.isNotBlank(existingProfile.getSalesGroupId())){
            SalesGroup group = salesGroupService.getById(existingProfile.getSalesGroupId());
            user.setDeptId(group.getDeptId());
        }

        boolean userSuccess = userService.updateById(user);
        if (!userSuccess) {
            log.warn("同步更新sys_user表失败，但销售人员档案更新成功");
        }

        log.info("更新销售人员成功: id={}, name={}", req.getId(), req.getSalesName());
        return true;
    }

    @Override
    public boolean deleteSalesStaff(String[] ids) {
        log.info("删除销售人员: ids={}", Arrays.toString(ids));

        // 检查权限
        checkDeletePermission();

        for (String id : ids) {
            // 检查是否有关联的学生
            // TODO: 检查学生关联

            // 查询销售人员档案
            SaleProfile profile = saleProfileService.lambdaQuery()
                    .eq(SaleProfile::getSalesId, id)
                    .eq(SaleProfile::getDeleted, false)
                    .one();

            if (profile != null) {
                // 从销售组中移除
                removeFromAllGroups(id);

                // 软删除销售人员档案

                saleProfileService.lambdaUpdate()
                        .set(SaleProfile::getStatus, "inactive")
                        .set(SaleProfile::getDeleted, true)
                        .set(SaleProfile::getUpdateTime, WssContext.now())
                        .eq(SaleProfile::getSalesId, id)
                        .update();

                // 同时软删除sys_user记录
                SysUser user = new SysUser();
                user.setUserId(Long.valueOf(id));
                user.setDelFlag("1");
                user.setUpdateBy(WssContext.userId());
                user.setUpdateTime(WssContext.now());
                userService.updateById(user);
            }
        }

        log.info("删除销售人员成功: count={}", ids.length);
        return true;
    }

    @Override
    public List<SalesStaffDto.AvailableResp> getAvailableSalesStaff(SalesStaffDto.GetAvailableReq req) {
        log.info("获取可用销售人员列表: req={}", req);

        // 从sale_profile表查询销售人员
        List<SaleProfile> profiles = saleProfileService.lambdaQuery()
                .eq(SaleProfile::getDeleted, false)
                .eq(SaleProfile::getStatus, "active")
                .isNull(SaleProfile::getSalesGroupId)
                .orderBy(true, true, SaleProfile::getSalesName)
                .list();

        // 如果指定了排除的销售组，则排除该组的成员
        if (StrUtil.isNotEmpty(req.getExcludeGroupId())) {
            profiles = profiles.stream()
                    .filter(profile -> !req.getExcludeGroupId().equals(profile.getSalesGroupId()))
                    .collect(Collectors.toList());
        }

        // 转换为响应DTO
        List<SalesStaffDto.AvailableResp> result = profiles.stream().map(profile -> {
            SalesStaffDto.AvailableResp resp = new SalesStaffDto.AvailableResp();
            resp.setId(profile.getId());
            resp.setUserId(profile.getSalesId());
            resp.setSalesName(profile.getSalesName());
            resp.setPhone(profile.getPhone());
            resp.setStudentCount(profile.getCustomerCount());
            return resp;
        }).collect(Collectors.toList());

        log.info("获取可用销售人员列表成功: count={}", result.size());
        return result;
    }

    @Override
    public SalesStaffDto.StatsResp getSalesStaffStats() {
        log.info("获取销售人员统计信息");

        SalesStaffDto.StatsResp result = new SalesStaffDto.StatsResp();

        // 统计总销售人员数（从sale_profile表）
        long totalSales = saleProfileService.lambdaQuery()
                .eq(SaleProfile::getDeleted, false)
                .count();
        result.setTotalSales((int) totalSales);

        // 统计活跃销售人员数
        long activeSales = saleProfileService.lambdaQuery()
                .eq(SaleProfile::getDeleted, false)
                .eq(SaleProfile::getStatus, "active")
                .count();
        result.setActiveSales((int) activeSales);

        // 统计已分配销售人员数（有销售组的）
        long assignedSales = saleProfileService.lambdaQuery()
                .eq(SaleProfile::getDeleted, false)
                .isNotNull(SaleProfile::getSalesGroupId)
                .count();
        result.setAssignedSales((int) assignedSales);

        // 统计未分配销售人员数
        result.setUnassignedSales(result.getTotalSales() - result.getAssignedSales());

        // 统计组长数量
        long leaderCount = salesGroupMemberService.lambdaQuery()
                .eq(SalesGroupMember::getRoleType, "leader")
                .count();;
        result.setLeaderCount((int) leaderCount);

        // 统计普通成员数量
        long memberCount = salesGroupMemberService.lambdaQuery()
                .eq(SalesGroupMember::getRoleType, "member")
                .count();
        result.setMemberCount((int) memberCount);

        log.info("获取销售人员统计信息成功: {}", result);
        return result;
    }

    @Override
    public boolean resetSalesStaffPassword(String id, String newPassword) {
        log.info("重置销售人员密码: id={}", id);

        // 检查权限
        checkUpdatePermission(id);

        boolean success = userService.resetPassword(Long.valueOf(id));

        log.info("重置销售人员密码成功: id={}", id);
        return success;
    }

    @Override
    public boolean changeSalesStaffStatus(String id, String status) {
        log.info("修改销售人员状态: id={}, status={}", id, status);

        // 检查权限
        checkUpdatePermission(id);

        SysUser user = new SysUser();
        user.setUserId(Long.valueOf(id));
        user.setStatus(status.equals("active") ? "0" : "1"); // 转换状态
        user.setUpdateBy(WssContext.userId());
        user.setUpdateTime(WssContext.now());

        boolean success = userService.updateById(user);

        saleProfileService.lambdaUpdate()
                .set(SaleProfile::getStatus, status)
                .eq(SaleProfile::getSalesId, id)
                .update();

        log.info("修改销售人员状态成功: id={}, status={}", id, status);
        return success;
    }

    @Override
    public boolean assignSalesToGroup(SalesStaffDto.AssignReq req) {
        log.info("分配销售到销售组: req={}", req);

        // 检查权限
        checkAssignPermission();

        // 检查销售人员是否已在其他组
        boolean alreadyAssigned = salesGroupMemberService.lambdaQuery()
                .eq(SalesGroupMember::getSalesId, req.getSalesId())
                .eq(SalesGroupMember::getDeleted, false)
                .exists();
        if (alreadyAssigned) {
            throw new RuntimeException("销售人员已在其他销售组中");
        }

        // 添加到销售组
        addToSalesGroup(req.getSalesId(), req.getGroupId(), req.getRoleType());

        log.info("分配销售到销售组成功: salesId={}, groupId={}", req.getSalesId(), req.getGroupId());
        return true;
    }

    @Override
    public boolean batchAssignSalesToGroup(SalesStaffDto.BatchAssignReq req) {
        log.info("批量分配销售到销售组: req={}", req);

        // 检查权限
        checkAssignPermission();

        // 检查销售人员是否已在其他组
        List<String> assignedSales = salesGroupMemberService.lambdaQuery()
                .in(SalesGroupMember::getSalesId, req.getSalesIds())
                .eq(SalesGroupMember::getDeleted, false)
                .list()
                .stream()
                .map(SalesGroupMember::getSalesId)
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(assignedSales)) {
            throw new RuntimeException("部分销售人员已在其他销售组中");
        }

        // 批量添加到销售组
        for (String salesId : req.getSalesIds()) {
            addToSalesGroup(salesId, req.getGroupId(), req.getRoleType());
        }

        log.info("批量分配销售到销售组成功: count={}, groupId={}", req.getSalesIds().size(), req.getGroupId());
        return true;
    }

    @Override
    public boolean removeSalesFromGroup(String salesId, String groupId) {
        log.info("从销售组移除销售: salesId={}, groupId={}", salesId, groupId);

        // 检查权限
        checkAssignPermission();

        // 移除销售组成员
        boolean success = salesGroupMemberService.lambdaUpdate()
                .eq(SalesGroupMember::getSalesId, salesId)
                .eq(SalesGroupMember::getGroupId, groupId)
                .set(SalesGroupMember::getDeleted, true)
                .set(SalesGroupMember::getUpdateTime, WssContext.now())
                .update();

        if (success) {
            // 更新销售组成员数量
            updateGroupMemberCount(groupId);
        }

        log.info("从销售组移除销售成功: salesId={}, groupId={}", salesId, groupId);
        return success;
    }

    /**
     * 批量查询销售人员关联信息
     */
    private void enrichSalesStaffInfo(List<SalesStaffDto.Resp> respList) {
        if (CollUtil.isEmpty(respList)) {
            return;
        }

        List<String> userIds = respList.stream()
                .map(SalesStaffDto.Resp::getUserId)
                .collect(Collectors.toList());

        // 批量查询用户信息（包括性别）
        Map<String, SysUser> userMap = new HashMap<>();
        if (CollUtil.isNotEmpty(userIds)) {
            userMap = userService.lambdaQuery()
                    .select(SysUser::getUserId, SysUser::getSex, SysUser::getDeptId)
                    .in(SysUser::getUserId, userIds)
                    .eq(SysUser::getDelFlag, "0")
                    .list()
                    .stream()
                    .collect(Collectors.toMap(user -> user.getUserId().toString(), user -> user));
        }

        // 批量查询部门信息
        List<Long> deptIds = respList.stream()
                .map(SalesStaffDto.Resp::getDeptId)
                .filter(deptId -> deptId != null)
                .distinct()
                .collect(Collectors.toList());

        Map<Long, String> deptNameMap = new HashMap<>();
        if (CollUtil.isNotEmpty(deptIds)) {
            deptNameMap = deptService.lambdaQuery()
                    .in(SysDept::getDeptId, deptIds)
                    .eq(SysDept::getDelFlag, "0")
                    .list()
                    .stream()
                    .collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        }

        // 批量查询销售组成员信息
        Map<String, SalesGroupMember> memberMap = salesGroupMemberService.lambdaQuery()
                .in(SalesGroupMember::getSalesId, userIds)
                .list()
                .stream()
                .collect(Collectors.toMap(SalesGroupMember::getSalesId, member -> member));

        // 批量查询销售组信息
        List<String> groupIds = memberMap.values().stream()
                .map(SalesGroupMember::getGroupId)
                .distinct()
                .collect(Collectors.toList());

        Map<String, String> groupNameMap = new HashMap<>();
        if (CollUtil.isNotEmpty(groupIds)) {
            groupNameMap = salesGroupService.lambdaQuery()
                    .in(SalesGroup::getId, groupIds)
                    .list()
                    .stream()
                    .collect(Collectors.toMap(SalesGroup::getId, SalesGroup::getName));
        }

        // 设置关联信息
        for (SalesStaffDto.Resp resp : respList) {
            // 设置用户信息（性别、部门等）
            SysUser user = userMap.get(resp.getUserId());
            if (user != null) {
                resp.setSex(user.getSex() != null ? user.getSex() : "2"); // 设置性别，默认为未知
                resp.setDeptId(user.getDeptId());
            }

            // 设置部门名称
            if (resp.getDeptId() != null) {
                resp.setDeptName(deptNameMap.get(resp.getDeptId()));
            }

            // 设置销售组信息
            SalesGroupMember member = memberMap.get(resp.getId());
            if (member != null) {
                resp.setGroupId(member.getGroupId());
                resp.setGroupName(groupNameMap.get(member.getGroupId()));
                resp.setRoleType(member.getRoleType());
                resp.setRoleName("leader".equals(member.getRoleType()) ? "组长" : "成员");
            }

            // 设置负责学生数
            resp.setStudentCount(getStudentCountBySalesId(resp.getId()));
        }
    }

    /**
     * 检查查看权限
     */
    private void checkViewPermission() {
        if (!systemDataQueryUtil.isAdminOrHr()
                && !systemDataQueryUtil.isSalesDirector()
                && !systemDataQueryUtil.isSalesGroupLeader()
                && !systemDataQueryUtil.isSales()
                && !systemDataQueryUtil.isTeacherGroupManager()
        ) {
            throw new RuntimeException("没有权限查看销售人员信息");
        }
    }

    /**
     * 检查数据权限
     */
    private void checkDataPermission(String salesId) {
        if (systemDataQueryUtil.isAdminOrHr() || systemDataQueryUtil.isSalesDirector()) {
            return;
        }

        if (systemDataQueryUtil.isSalesGroupLeader()) {
            // 组长只能查看自己组的成员
            String currentUserId = SecurityUtils.getUserId().toString();
            SalesGroupMember currentMember = salesGroupMemberService.lambdaQuery()
                    .eq(SalesGroupMember::getSalesId, currentUserId)
                    .eq(SalesGroupMember::getDeleted, false)
                    .eq(SalesGroupMember::getRoleType, "leader")
                    .one();

            if (currentMember == null) {
                throw new RuntimeException("没有权限查看该销售人员信息");
            }

            boolean inSameGroup = salesGroupMemberService.lambdaQuery()
                    .eq(SalesGroupMember::getSalesId, salesId)
                    .eq(SalesGroupMember::getGroupId, currentMember.getGroupId())
                    .eq(SalesGroupMember::getDeleted, false)
                    .exists();

            if (!inSameGroup) {
                throw new RuntimeException("没有权限查看该销售人员信息");
            }
            return;
        }

        throw new RuntimeException("没有权限查看销售人员信息");
    }

    /**
     * 检查创建权限
     */
    private void checkCreatePermission() {
        if (!systemDataQueryUtil.isAdminOrHr() && !systemDataQueryUtil.isSalesDirector()) {
            throw new RuntimeException("没有权限创建销售人员");
        }
    }

    /**
     * 检查更新权限
     */
    private void checkUpdatePermission(String salesId) {
        if (systemDataQueryUtil.isAdminOrHr() || systemDataQueryUtil.isSalesDirector()) {
            return;
        }

        throw new RuntimeException("没有权限修改销售人员信息");
    }

    /**
     * 检查删除权限
     */
    private void checkDeletePermission() {
        if (!systemDataQueryUtil.isAdminOrHr() && !systemDataQueryUtil.isSalesDirector()) {
            throw new RuntimeException("没有权限删除销售人员");
        }
    }

    /**
     * 检查分配权限
     */
    private void checkAssignPermission() {
        if (!systemDataQueryUtil.isAdminOrHr() && !systemDataQueryUtil.isSalesDirector()) {
            throw new RuntimeException("没有权限分配销售人员");
        }
    }

    /**
     * 添加到销售组
     */
    private void addToSalesGroup(String salesId, String groupId, String roleType) {

        if (salesGroupMemberService.lambdaQuery()
                .eq(SalesGroupMember::getGroupId, groupId)
                .eq(SalesGroupMember::getSalesId, salesId)
                .exists()) {
            return;
        }

        SalesGroupMember member = new SalesGroupMember();
        member.setId(IdUtil.getSnowflakeNextIdStr());
        member.setGroupId(groupId);
        member.setSalesId(salesId);
        member.setRoleType(roleType);
        member.setStatus("active");
        member.setJoinTime(WssContext.now());
        member.setCreateTime(WssContext.now());
        member.setDeleted(false);

        salesGroupMemberService.save(member);

        saleProfileService.lambdaUpdate()
                .set(SaleProfile::getSalesGroupId, groupId)
                .set(SaleProfile::getUpdateTime, WssContext.now())
                .set(SaleProfile::getUpdateBy, WssContext.userId())
                .eq(SaleProfile::getSalesId, salesId)
                .update();

        // 更新销售组成员数量
        updateGroupMemberCount(groupId);
    }

    /**
     * 创建销售人员档案
     */
    private void createSaleProfile(SysUser user, SalesStaffDto.CreateReq req) {
        log.info("创建销售人员档案: userId={}, salesName={}", user.getUserId(), req.getSalesName());

        SaleProfile profile = new SaleProfile();
        profile.setId(user.getUserId().toString());
        profile.setSalesId(user.getUserId().toString());
        profile.setSalesName(req.getSalesName());
        profile.setPhone(req.getPhone());
        profile.setStatus("active"); // 默认活跃状态
        profile.setJoinTime(req.getJoinTime() != null ? req.getJoinTime() : WssContext.now());
        profile.setMonthlyTarget(BigDecimal.ZERO);
        profile.setMonthlyPerformance(BigDecimal.ZERO);
        profile.setCustomerCount(0);
        profile.setCreateBy(WssContext.userId());
        profile.setCreateTime(WssContext.now());
        profile.setDeleted(false);

        // 如果指定了销售组，设置销售组信息
        if (StrUtil.isNotEmpty(req.getGroupId())) {
            profile.setSalesGroupId(req.getGroupId());
            // 查询销售组名称
            SalesGroup group = salesGroupService.getById(req.getGroupId());
            if (group != null) {
                profile.setSalesGroupName(group.getName());
            }
        }

        boolean success = saleProfileService.save(profile);
        if (!success) {
            throw new RuntimeException("创建销售人员档案失败");
        }

        log.info("销售人员档案创建成功: profileId={}", profile.getId());
    }

    /**
     * 更新用户销售组
     */
    private void updateUserGroup(String userId, String groupId) {
        // 先移除现有的组关系
        salesGroupMemberService.lambdaUpdate()
                .eq(SalesGroupMember::getSalesId, userId)
                .set(SalesGroupMember::getDeleted, true)
                .set(SalesGroupMember::getStatus, "inactive")
                .set(SalesGroupMember::getUpdateBy, WssContext.userId())
                .set(SalesGroupMember::getUpdateTime, WssContext.now())
                .update();

        // 如果指定了新的组，则添加
        if (StrUtil.isNotEmpty(groupId)) {
            addToSalesGroup(userId, groupId, "member");
        }
    }

    /**
     * 从所有销售组中移除
     */
    private void removeFromAllGroups(String salesId) {
        List<SalesGroupMember> members = salesGroupMemberService.lambdaQuery()
                .eq(SalesGroupMember::getSalesId, salesId)
                .eq(SalesGroupMember::getDeleted, false)
                .list();

        salesGroupMemberService.lambdaUpdate()
                .eq(SalesGroupMember::getSalesId, salesId)
                .set(SalesGroupMember::getDeleted, true)
                .set(SalesGroupMember::getUpdateTime, WssContext.now())
                .update();

        for (SalesGroupMember member : members) {
            // 更新销售组成员数量
            updateGroupMemberCount(member.getGroupId());
        }
    }

    /**
     * 更新销售组成员数量
     */
    private void updateGroupMemberCount(String groupId) {
        long memberCount = salesGroupMemberService.lambdaQuery()
                .eq(SalesGroupMember::getGroupId, groupId)
                .eq(SalesGroupMember::getDeleted, false)
                .count();

        SalesGroup salesGroup = new SalesGroup();
        salesGroup.setId(groupId);
        salesGroup.setMemberCount((int) memberCount);
        salesGroup.setUpdateTime(WssContext.now());
        salesGroupService.updateById(salesGroup);
    }

    /**
     * 根据销售ID获取负责的学生数量
     */
    private Integer getStudentCountBySalesId(String salesId) {
            long studentCount = userStudentExtService.lambdaQuery()
                    .eq(UserStudentExt::getSalesId, salesId)
                    .eq(UserStudentExt::getDeleted, false)
                    .count();
            return (int) studentCount;
    }
}
