package org.nonamespace.word.server.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.service.base.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.nonamespace.word.server.mapper.StudentTextbookProgressMapper;
import org.nonamespace.word.server.domain.StudentTextbookProgress;
import org.nonamespace.word.server.service.IStudentTextbookProgressService;

/**
 * 词定义 (统一教材与词): 定义各种词Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Service
@Slf4j
public class StudentTextbookProgressServiceImpl extends ServiceImpl<StudentTextbookProgressMapper,StudentTextbookProgress> implements IStudentTextbookProgressService
{

}
