package org.nonamespace.word.server.facade;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.nonamespace.word.server.dto.management.booking.CourseBookingReviewDto;

import java.util.List;

/**
 * 预约课审核管理Facade接口
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface ICourseBookingReviewFacade {

    /**
     * 分页查询预约课申请列表（根据角色权限）
     *
     * @param req 查询请求
     * @return 分页结果
     */
    IPage<CourseBookingReviewDto.ListResp> getCourseBookingReviewPage(CourseBookingReviewDto.GetListReq req);

    /**
     * 根据ID查询预约课申请详情
     *
     * @param id 申请ID
     * @return 申请详情
     */
    CourseBookingReviewDto.DetailResp getCourseBookingReviewDetail(String id);

    /**
     * 审核预约课申请（仅教学组长可操作）
     *
     * @param req 审核请求
     * @return 是否成功
     */
    boolean reviewCourseBookingApplication(CourseBookingReviewDto.ReviewReq req);

    /**
     * 批量审核预约课申请（仅教学组长可操作）
     *
     * @param req 批量审核请求
     * @return 批量审核结果
     */
    CourseBookingReviewDto.BatchReviewResp batchReviewApplications(CourseBookingReviewDto.BatchReviewReq req);

    /**
     * 获取审核统计信息
     *
     * @return 统计信息
     */
    CourseBookingReviewDto.StatsResp getReviewStats();

    /**
     * 获取当前用户的审核权限信息
     *
     * @return 权限信息
     */
    CourseBookingReviewDto.PermissionResp getCurrentUserPermissions();

    /**
     * 获取可分配的教师列表（仅教学组长可查看）
     *
     * @param teachingGroupId 教学组ID（可选）
     * @return 教师列表
     */
    List<CourseBookingReviewDto.AvailableTeacherResp> getAvailableTeachers(String teachingGroupId);
}
