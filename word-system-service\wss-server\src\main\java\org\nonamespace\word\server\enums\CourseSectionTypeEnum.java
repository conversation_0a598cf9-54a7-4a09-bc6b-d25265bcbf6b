package org.nonamespace.word.server.enums;

import lombok.Getter;

/**
 * 学习环节类型枚举类
 */
@Getter
public enum CourseSectionTypeEnum {
    // 新课程学习、抗遗忘复习、下课复习
    LEARNING("新课程学习"),
    REVIEW("抗遗忘复习"),
    WORD_TEST("词汇测验"),
    CLASS_END_REVIEW("下课复习");
    private final String value;
    CourseSectionTypeEnum(String value) {
        this.value = value;
    }

    public static CourseSectionTypeEnum getByDefault(String value, CourseSectionTypeEnum defaultValue) {
        for (CourseSectionTypeEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return defaultValue;
    }

}
