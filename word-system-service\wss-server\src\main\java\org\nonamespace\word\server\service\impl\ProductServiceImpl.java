package org.nonamespace.word.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.domain.Product;
import org.nonamespace.word.server.dto.product.ProductDto;
import org.nonamespace.word.server.mapper.ProductMapper;
import org.nonamespace.word.server.service.IProductService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 产品信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements IProductService {

    private final ProductMapper productMapper;

    @Override
    public IPage<ProductDto.BasicResp> getProductPage(ProductDto.GetListReq req) {
        try {
            Page<Product> page = new Page<>(req.getPageNum(), req.getPageSize());

            LambdaQueryWrapper<Product> wrapper = Wrappers.lambdaQuery(Product.class)
                    .eq(Product::getDeleted, false);

            // 添加查询条件
            if (StrUtil.isNotBlank(req.getName())) {
                wrapper.like(Product::getName, req.getName());
            }
            if (StrUtil.isNotBlank(req.getSubject())) {
                wrapper.eq(Product::getSubject, req.getSubject());
            }
            if (StrUtil.isNotBlank(req.getCourseType())) {
                wrapper.eq(Product::getCourseType, req.getCourseType());
            }
            if (StrUtil.isNotBlank(req.getStatus())) {
                wrapper.eq(Product::getStatus, req.getStatus());
            }
            if (req.getMinPrice() != null) {
                wrapper.ge(Product::getSellingPrice, req.getMinPrice());
            }
            if (req.getMaxPrice() != null) {
                wrapper.le(Product::getSellingPrice, req.getMaxPrice());
            }

            // 排序
            if (StrUtil.isNotBlank(req.getOrderBy())) {
                if ("sort_order".equals(req.getOrderBy())) {
                    if ("DESC".equalsIgnoreCase(req.getOrderDirection())) {
                        wrapper.orderByDesc(Product::getSortOrder);
                    } else {
                        wrapper.orderByAsc(Product::getSortOrder);
                    }
                }
            } else {
                wrapper.orderByAsc(Product::getSortOrder).orderByDesc(Product::getCreateTime);
            }

            IPage<Product> productPage = this.page(page, wrapper);

            // 转换为DTO
            Page<ProductDto.BasicResp> resultPage = new Page<>(req.getPageNum(), req.getPageSize(), productPage.getTotal());
            List<ProductDto.BasicResp> records = productPage.getRecords().stream()
                    .map(this::convertToBasicResp)
                    .collect(Collectors.toList());
            resultPage.setRecords(records);

            return resultPage;
        } catch (Exception e) {
            log.error("分页查询产品列表失败", e);
            throw new RuntimeException("分页查询产品列表失败: " + e.getMessage());
        }
    }

    @Override
    public ProductDto.DetailResp getProductDetail(String productId) {
        try {
            Product product = this.baseMapper.selectById(productId);
            ProductDto.DetailResp resp = BeanUtil.copyProperties(product, ProductDto.DetailResp.class);
            if (product != null) {
                // 处理JSON字段
                if (StrUtil.isNotEmpty(product.getDetailImages())) {
                    resp.setDetailImages(JSONUtil.toList(product.getDetailImages(), String.class));
                }
                if (StrUtil.isNotEmpty(product.getTags())) {
                    resp.setTags(JSONUtil.toList(product.getTags(), String.class));
                }
            }
            return resp;
        } catch (Exception e) {
            log.error("查询产品详细信息失败", e);
            throw new RuntimeException("查询产品详细信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createProduct(ProductDto.CreateReq req) {
        try {
            Product product = new Product();
            BeanUtil.copyProperties(req, product);
            
            // 生成产品ID
            String productId = IdUtil.getSnowflakeNextIdStr();
            product.setId(productId);
            
            // 处理JSON字段
            if (req.getDetailImages() != null && !req.getDetailImages().isEmpty()) {
                product.setDetailImages(JSONUtil.toJsonStr(req.getDetailImages()));
            }
            if (req.getTags() != null && !req.getTags().isEmpty()) {
                product.setTags(JSONUtil.toJsonStr(req.getTags()));
            }
            
            // 设置默认值
            if (product.getSortOrder() == null) {
                product.setSortOrder(0);
            }
            if (product.getStock() == null) {
                product.setStock(0);
            }
            if (product.getStockLimited() == null) {
                product.setStockLimited(false);
            }
            if (product.getSalesCount() == null) {
                product.setSalesCount(0);
            }
            
            this.save(product);
            return productId;
        } catch (Exception e) {
            log.error("创建产品失败", e);
            throw new RuntimeException("创建产品失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProduct(ProductDto.UpdateReq req) {
        try {
            Product product = this.getById(req.getId());
            if (product == null) {
                throw new RuntimeException("产品不存在");
            }
            
            BeanUtil.copyProperties(req, product);
            
            // 处理JSON字段
            if (req.getDetailImages() != null && !req.getDetailImages().isEmpty()) {
                product.setDetailImages(JSONUtil.toJsonStr(req.getDetailImages()));
            } else {
                product.setDetailImages(null);
            }
            if (req.getTags() != null && !req.getTags().isEmpty()) {
                product.setTags(JSONUtil.toJsonStr(req.getTags()));
            } else {
                product.setTags(null);
            }
            
            return this.updateById(product);
        } catch (Exception e) {
            log.error("更新产品信息失败", e);
            throw new RuntimeException("更新产品信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteProduct(String productId) {
        try {
            return this.removeById(productId);
        } catch (Exception e) {
            log.error("删除产品失败", e);
            throw new RuntimeException("删除产品失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteProducts(List<String> productIds) {
        try {
            return this.removeByIds(productIds);
        } catch (Exception e) {
            log.error("批量删除产品失败", e);
            throw new RuntimeException("批量删除产品失败: " + e.getMessage());
        }
    }

    @Override
    public List<ProductDto.BasicResp> getAvailableProducts() {
        try {
            return BeanUtil.copyToList(productMapper.selectList(Wrappers.emptyWrapper()), ProductDto.BasicResp.class);
        } catch (Exception e) {
            log.error("查询上架产品列表失败", e);
            throw new RuntimeException("查询上架产品列表失败: " + e.getMessage());
        }
    }

    @Override
    public List<ProductDto.BasicResp> getProductsByType(String type) {
        try {
            return BeanUtil.copyToList(productMapper.selectList(Wrappers.lambdaQuery(Product.class).eq(Product::getType, type)), ProductDto.BasicResp.class);
        } catch (Exception e) {
            log.error("根据类型查询产品列表失败", e);
            throw new RuntimeException("根据类型查询产品列表失败: " + e.getMessage());
        }
    }

    @Override
    public List<ProductDto.BasicResp> getProductsBySubject(String subject) {
        try {
            return BeanUtil.copyToList(productMapper.selectList(Wrappers.lambdaQuery(Product.class).eq(Product::getSubject, subject)), ProductDto.BasicResp.class);
        } catch (Exception e) {
            log.error("根据学科查询产品列表失败", e);
            throw new RuntimeException("根据学科查询产品列表失败: " + e.getMessage());
        }
    }

    @Override
    public List<ProductDto.BasicResp> getProductsByTag(String tag) {
        try {
            return BeanUtil.copyToList(productMapper.selectList(Wrappers.lambdaQuery(Product.class).in(Product::getTags, tag)), ProductDto.BasicResp.class);
        } catch (Exception e) {
            log.error("根据标签查询产品列表失败", e);
            throw new RuntimeException("根据标签查询产品列表失败: " + e.getMessage());
        }
    }

    @Override
    public List<ProductDto.BasicResp> getHotProducts(Integer limit) {
        try {
            return BeanUtil.copyToList(productMapper.selectList(Wrappers.lambdaQuery(Product.class).apply("limit " + limit)), ProductDto.BasicResp.class);
        } catch (Exception e) {
            log.error("查询热门产品列表失败", e);
            throw new RuntimeException("查询热门产品列表失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableProduct(String productId) {
        try {
            Product product = this.getById(productId);
            if (product == null) {
                throw new RuntimeException("产品不存在");
            }
            product.setStatus("上架");
            return this.updateById(product);
        } catch (Exception e) {
            log.error("上架产品失败", e);
            throw new RuntimeException("上架产品失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableProduct(String productId) {
        try {
            Product product = this.getById(productId);
            if (product == null) {
                throw new RuntimeException("产品不存在");
            }
            product.setStatus("下架");
            return this.updateById(product);
        } catch (Exception e) {
            log.error("下架产品失败", e);
            throw new RuntimeException("下架产品失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSalesCount(String productId, Integer quantity) {
        try {
            Product product = productMapper.selectById(productId);
            if (product == null || product.getDeleted()) {
                return false;
            }
            product.setSalesCount((product.getSalesCount() != null ? product.getSalesCount() : 0) + quantity);
            return productMapper.updateById(product) > 0;
        } catch (Exception e) {
            log.error("更新产品销售数量失败", e);
            throw new RuntimeException("更新产品销售数量失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStock(String productId, Integer quantity) {
        try {
            Product product = productMapper.selectById(productId);
            if (product == null || product.getDeleted()) {
                return false;
            }
            product.setStock((product.getStock() != null ? product.getStock() : 0) + quantity);
            return productMapper.updateById(product) > 0;
        } catch (Exception e) {
            log.error("更新产品库存失败", e);
            throw new RuntimeException("更新产品库存失败: " + e.getMessage());
        }
    }

    @Override
    public boolean checkStock(String productId, Integer quantity) {
        try {
            Product product = productMapper.selectById(productId);
            if (product == null || product.getDeleted()) {
                return false;
            }

            // 如果不限制库存，直接返回true
            if (product.getStockLimited() == null || !product.getStockLimited()) {
                return true;
            }

            // 检查库存是否充足
            int currentStock = product.getStock() != null ? product.getStock() : 0;
            return currentStock >= quantity;
        } catch (Exception e) {
            log.error("检查产品库存失败", e);
            return false;
        }
    }

    /**
     * 计算原价
     */
    private void calculateOriginalPrice(Product product) {
        if (product.getUnitPrice() != null && product.getQuantity() != null) {
            long basePrice = product.getUnitPrice() * product.getQuantity();
            long materialFee = (product.getHasMaterialFee() != null && product.getHasMaterialFee())
                    ? (product.getMaterialFee() != null ? product.getMaterialFee() : 0) : 0;
            product.setOriginalPrice(basePrice + materialFee);
        }
    }

    /**
     * 转换为基础响应DTO
     */
    private ProductDto.BasicResp convertToBasicResp(Product product) {
        ProductDto.BasicResp resp = new ProductDto.BasicResp();
        BeanUtil.copyProperties(product, resp);

        // 处理JSON字段
        if (CollUtil.isNotEmpty(product.getApplicableGrades())) {
            resp.setApplicableGrades(product.getApplicableGrades());
        }
        if (StrUtil.isNotBlank(product.getTags())) {
            resp.setTags(JSONUtil.toList(product.getTags(), String.class));
        }

        return resp;
    }

    /**
     * 转换为详细响应DTO
     */
    private ProductDto.DetailResp convertToDetailResp(Product product) {
        ProductDto.DetailResp resp = new ProductDto.DetailResp();
        BeanUtil.copyProperties(product, resp);

        // 处理JSON字段
        if (CollUtil.isNotEmpty(product.getApplicableGrades())) {
            resp.setApplicableGrades(product.getApplicableGrades());
        }
        if (StrUtil.isNotBlank(product.getDetailImages())) {
            resp.setDetailImages(JSONUtil.toList(product.getDetailImages(), String.class));
        }
        if (StrUtil.isNotBlank(product.getTags())) {
            resp.setTags(JSONUtil.toList(product.getTags(), String.class));
        }

        return resp;
    }
}
