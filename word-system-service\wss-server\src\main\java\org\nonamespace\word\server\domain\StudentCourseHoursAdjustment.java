package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nonamespace.word.server.entity.DataEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 学生课时调整历史记录实体类
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("student_course_hours_adjustment")
public class StudentCourseHoursAdjustment extends DataEntity {

    /**
     * 课时包ID
     */
    private String courseHoursId;

    /**
     * 学生ID
     */
    private String studentId;

    /**
     * 学科 (英语、数学、语文等)
     */
    private String subject;

    /**
     * 课型
     */
    private String specification;

    /**
     * 性质 (试听课、正式课)
     */
    private String nature;

    /**
     * 调整类型 (increase: 增加, decrease: 减少)
     */
    private String adjustmentType;

    /**
     * 调整课时数 (保留2位小数)
     */
    private BigDecimal adjustmentHours;

    /**
     * 购买课时调整数 (保留2位小数)
     */
    private BigDecimal purchasedHoursAdjustment;

    /**
     * 赠送课时调整数 (保留2位小数)
     */
    private BigDecimal giftHoursAdjustment;

    /**
     * 调整前总课时
     */
    private BigDecimal beforeTotalHours;

    /**
     * 调整后总课时
     */
    private BigDecimal afterTotalHours;

    /**
     * 调整前剩余课时
     */
    private BigDecimal beforeRemainingHours;

    /**
     * 调整后剩余课时
     */
    private BigDecimal afterRemainingHours;

    /**
     * 调整前购买课时
     */
    private BigDecimal beforePurchasedHours;

    /**
     * 调整后购买课时
     */
    private BigDecimal afterPurchasedHours;

    /**
     * 调整前赠送课时
     */
    private BigDecimal beforeGiftHours;

    /**
     * 调整后赠送课时
     */
    private BigDecimal afterGiftHours;

    /**
     * 调整原因
     */
    private String adjustmentReason;

    /**
     * 操作人ID
     */
    private String operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 调整时间
     */
    private Date adjustmentTime;
}
