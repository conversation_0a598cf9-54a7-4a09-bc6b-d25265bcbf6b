package org.nonamespace.word.rest.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.dto.management.booking.CourseBookingReviewDto;
import org.nonamespace.word.server.dto.sales.SalesGroupDto;
import org.nonamespace.word.server.dto.sales.SalesStaffDto;
import org.nonamespace.word.server.facade.ICourseBookingReviewFacade;
import org.nonamespace.word.server.facade.SalesGroupFacade;
import org.nonamespace.word.server.facade.SalesStaffFacade;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 预约课审核管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/course-booking-review")
@RequiredArgsConstructor
public class CourseBookingReviewController extends BaseController {

    private final ICourseBookingReviewFacade courseBookingReviewFacade;
    private final SalesGroupFacade salesGroupFacade;
    private final SalesStaffFacade salesStaffFacade;

    /**
     * 分页查询预约课申请列表（根据角色权限）
     */
    @PreAuthorize("@ss.hasAnyRoles('admin,hr,teaching_group_leader')")
    @GetMapping("/list")
    public AjaxResult list(CourseBookingReviewDto.GetListReq req) {
        try {
            log.info("查询预约课申请列表: req={}", req);

            IPage<CourseBookingReviewDto.ListResp> page = courseBookingReviewFacade.getCourseBookingReviewPage(req);
            return AjaxResult.success(page);
        } catch (Exception e) {
            log.error("查询预约课申请列表失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 根据ID查询预约课申请详情
     */
    // @PreAuthorize("@ss.hasAnyRoles('admin,hr,teaching_group_leader')")
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable String id) {
        try {
            log.info("查询预约课申请详情: id={}", id);
            
            CourseBookingReviewDto.DetailResp detail = courseBookingReviewFacade.getCourseBookingReviewDetail(id);
            return AjaxResult.success(detail);
        } catch (Exception e) {
            log.error("查询预约课申请详情失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 审核预约课申请（仅教学组长可操作）
     */
    @PreAuthorize("@ss.hasRole('teaching_group_leader')")
    @Log(title = "预约课申请审核", businessType = BusinessType.UPDATE)
    @PostMapping("/review")
    public AjaxResult review(@Valid @RequestBody CourseBookingReviewDto.ReviewReq req) {
        try {
            log.info("审核预约课申请: req={}", req);
            
            boolean success = courseBookingReviewFacade.reviewCourseBookingApplication(req);
            return success ? AjaxResult.success("审核成功") : AjaxResult.error("审核失败");
        } catch (Exception e) {
            log.error("审核预约课申请失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 批量审核预约课申请（仅教学组长可操作）
     */
    @PreAuthorize("@ss.hasRole('teaching_group_leader')")
    @Log(title = "预约课申请批量审核", businessType = BusinessType.UPDATE)
    @PostMapping("/batch-review")
    public AjaxResult batchReview(@Valid @RequestBody CourseBookingReviewDto.BatchReviewReq req) {
        try {
            log.info("批量审核预约课申请: req={}", req);
            
            CourseBookingReviewDto.BatchReviewResp result = courseBookingReviewFacade.batchReviewApplications(req);
            return AjaxResult.success("批量审核完成", result);
        } catch (Exception e) {
            log.error("批量审核预约课申请失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取审核统计信息
     */
    // @PreAuthorize("@ss.hasAnyRoles('admin,hr,teaching_group_leader')")
    @GetMapping("/stats")
    public AjaxResult getStats() {
        try {
            log.info("获取审核统计信息");
            
            CourseBookingReviewDto.StatsResp stats = courseBookingReviewFacade.getReviewStats();
            return AjaxResult.success(stats);
        } catch (Exception e) {
            log.error("获取审核统计信息失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取当前用户的审核权限信息
     */
    // @PreAuthorize("@ss.hasAnyRoles('admin,hr,teaching_group_leader')")
    @GetMapping("/permissions")
    public AjaxResult getPermissions() {
        try {
            log.info("获取当前用户审核权限信息");
            
            CourseBookingReviewDto.PermissionResp permissions = courseBookingReviewFacade.getCurrentUserPermissions();
            return AjaxResult.success(permissions);
        } catch (Exception e) {
            log.error("获取审核权限信息失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取可分配的教师列表（仅教学组长可查看）
     */
    // @PreAuthorize("@ss.hasRole('teaching_group_leader')")
    @GetMapping("/available-teachers")
    public AjaxResult getAvailableTeachers(@RequestParam(required = false) String teachingGroupId) {
        try {
            log.info("获取可分配教师列表: teachingGroupId={}", teachingGroupId);
            
            var teachers = courseBookingReviewFacade.getAvailableTeachers(teachingGroupId);
            return AjaxResult.success(teachers);
        } catch (Exception e) {
            log.error("获取可分配教师列表失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取销售组选项列表（用于下拉选择）
     */
    @PreAuthorize("@ss.hasAnyRoles('admin,hr,teaching_group_leader')")
    @GetMapping("/sales-group-options")
    public AjaxResult getSalesGroupOptions() {
        try {
            log.info("获取销售组选项列表");

            // 构建查询请求，只获取活跃的销售组
            SalesGroupDto.GetListReq req = new SalesGroupDto.GetListReq();
            req.setStatus("active");
            req.setPageNum(1);
            req.setPageSize(1000);

            IPage<SalesGroupDto.Resp> page = salesGroupFacade.getSalesGroupPage(req);

            // 转换为选项格式
            List<SalesGroupDto.OptionResp> options = page.getRecords().stream()
                    .map(group -> {
                        SalesGroupDto.OptionResp option = new SalesGroupDto.OptionResp();
                        option.setId(group.getId());
                        option.setName(group.getName());
                        option.setMemberCount(group.getMemberCount());
                        return option;
                    })
                    .collect(Collectors.toList());

            return AjaxResult.success(options);
        } catch (Exception e) {
            log.error("获取销售组选项列表失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取销售人员选项列表（用于下拉选择）
     */
    @PreAuthorize("@ss.hasAnyRoles('admin,hr,teaching_group_leader')")
    @GetMapping("/sales-staff-options")
    public AjaxResult getSalesStaffOptions(@RequestParam(required = false) String groupId) {
        try {
            log.info("获取销售人员选项列表: groupId={}", groupId);

            // 构建查询请求
            SalesStaffDto.GetListReq req = new SalesStaffDto.GetListReq();
            req.setStatus("active");
            req.setGroupId(groupId); // 支持按销售组过滤
            req.setPageNum(1);
            req.setPageSize(1000);

            IPage<SalesStaffDto.Resp> page = salesStaffFacade.getSalesStaffPage(req);

            // 转换为选项格式
            List<SalesStaffDto.OptionResp> options = page.getRecords().stream()
                    .map(staff -> {
                        SalesStaffDto.OptionResp option = new SalesStaffDto.OptionResp();
                        option.setId(staff.getId());
                        option.setName(staff.getSalesName());
                        option.setPhone(staff.getPhone());
                        option.setGroupName(staff.getGroupName());
                        return option;
                    })
                    .collect(Collectors.toList());

            return AjaxResult.success(options);
        } catch (Exception e) {
            log.error("获取销售人员选项列表失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }
}
