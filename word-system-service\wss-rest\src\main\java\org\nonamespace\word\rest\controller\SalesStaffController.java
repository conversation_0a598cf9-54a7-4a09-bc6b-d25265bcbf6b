package org.nonamespace.word.rest.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.dto.sales.SalesStaffDto;
import org.nonamespace.word.server.facade.SalesStaffFacade;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 销售人员管理Controller
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
@Slf4j
@RestController
@RequestMapping("/sales/staff")
@RequiredArgsConstructor
public class SalesStaffController extends BaseController {

    private final SalesStaffFacade salesStaffFacade;

    /**
     * 分页查询销售人员列表
     */
    @PreAuthorize("@ss.hasPermi('sales:staff:list')")
    @GetMapping("/list")
    public AjaxResult list(SalesStaffDto.GetListReq req) {
        try {
            IPage<SalesStaffDto.Resp> page = salesStaffFacade.getSalesStaffPage(req);
            return AjaxResult.success(page);
        } catch (Exception e) {
            log.error("查询销售人员列表失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 根据ID查询销售人员详情
     */
    @PreAuthorize("@ss.hasPermi('sales:staff:query')")
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable String id) {
        try {
            SalesStaffDto.Resp result = salesStaffFacade.getSalesStaffById(id);
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("查询销售人员详情失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 创建销售人员
     */
    @PreAuthorize("@ss.hasPermi('sales:staff:add')")
    @Log(title = "销售人员管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SalesStaffDto.CreateReq req) {
        try {
            String id = salesStaffFacade.createSalesStaff(req);
            return AjaxResult.success("创建成功", id);
        } catch (Exception e) {
            log.error("创建销售人员失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 更新销售人员
     */
    @PreAuthorize("@ss.hasPermi('sales:staff:edit')")
    @Log(title = "销售人员管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SalesStaffDto.UpdateReq req) {
        try {
            boolean success = salesStaffFacade.updateSalesStaff(req);
            return success ? AjaxResult.success("更新成功") : AjaxResult.error("更新失败");
        } catch (Exception e) {
            log.error("更新销售人员失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 删除销售人员
     */
    @PreAuthorize("@ss.hasPermi('sales:staff:remove')")
    @Log(title = "销售人员管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        try {
            boolean success = salesStaffFacade.deleteSalesStaff(ids);
            return success ? AjaxResult.success("删除成功") : AjaxResult.error("删除失败");
        } catch (Exception e) {
            log.error("删除销售人员失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取可用销售人员列表
     */
    @PreAuthorize("@ss.hasPermi('sales:staff:list')")
    @GetMapping("/available")
    public AjaxResult getAvailable(SalesStaffDto.GetAvailableReq req) {
        try {
            List<SalesStaffDto.AvailableResp> result = salesStaffFacade.getAvailableSalesStaff(req);
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("获取可用销售人员列表失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取销售人员统计信息
     */
    @PreAuthorize("@ss.hasPermi('sales:staff:stats')")
    @GetMapping("/stats")
    public AjaxResult getStats() {
        try {
            SalesStaffDto.StatsResp result = salesStaffFacade.getSalesStaffStats();
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("获取销售人员统计信息失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 重置销售人员密码
     */
    @PreAuthorize("@ss.hasPermi('sales:staff:resetPwd')")
    @Log(title = "销售人员管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/reset-password")
    public AjaxResult resetPassword(@PathVariable String id, @RequestParam(defaultValue = "654321") String newPassword) {
        try {
            boolean success = salesStaffFacade.resetSalesStaffPassword(id, newPassword);
            return success ? AjaxResult.success("重置密码成功") : AjaxResult.error("重置密码失败");
        } catch (Exception e) {
            log.error("重置销售人员密码失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 启用/停用销售人员
     */
    @PreAuthorize("@ss.hasPermi('sales:staff:edit')")
    @Log(title = "销售人员管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/status")
    public AjaxResult changeStatus(@PathVariable String id, @RequestParam String status) {
        try {
            boolean success = salesStaffFacade.changeSalesStaffStatus(id, status);
            return success ? AjaxResult.success("状态修改成功") : AjaxResult.error("状态修改失败");
        } catch (Exception e) {
            log.error("修改销售人员状态失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 分配销售到销售组
     */
    @PreAuthorize("@ss.hasPermi('sales:staff:assign')")
    @Log(title = "销售人员管理", businessType = BusinessType.UPDATE)
    @PostMapping("/assign")
    public AjaxResult assign(@Validated @RequestBody SalesStaffDto.AssignReq req) {
        try {
            boolean success = salesStaffFacade.assignSalesToGroup(req);
            return success ? AjaxResult.success("分配成功") : AjaxResult.error("分配失败");
        } catch (Exception e) {
            log.error("分配销售到销售组失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 批量分配销售到销售组
     */
    @PreAuthorize("@ss.hasPermi('sales:staff:assign')")
    @Log(title = "销售人员管理", businessType = BusinessType.UPDATE)
    @PostMapping("/batch-assign")
    public AjaxResult batchAssign(@Validated @RequestBody SalesStaffDto.BatchAssignReq req) {
        try {
            boolean success = salesStaffFacade.batchAssignSalesToGroup(req);
            return success ? AjaxResult.success("批量分配成功") : AjaxResult.error("批量分配失败");
        } catch (Exception e) {
            log.error("批量分配销售到销售组失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 从销售组移除销售
     */
    @PreAuthorize("@ss.hasPermi('sales:staff:assign')")
    @Log(title = "销售人员管理", businessType = BusinessType.UPDATE)
    @DeleteMapping("/{salesId}/group/{groupId}")
    public AjaxResult removeFromGroup(@PathVariable String salesId, @PathVariable String groupId) {
        try {
            boolean success = salesStaffFacade.removeSalesFromGroup(salesId, groupId);
            return success ? AjaxResult.success("移除成功") : AjaxResult.error("移除失败");
        } catch (Exception e) {
            log.error("从销售组移除销售失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取销售人员选项列表（用于下拉选择）
     */
//    @PreAuthorize("@ss.hasPermi('sales:staff:list')")
    @GetMapping("/options")
    public AjaxResult getOptions(@RequestParam(required = false) String groupId) {
        try {
            log.info("获取销售人员选项列表: groupId={}", groupId);

            // 构建查询请求
            SalesStaffDto.GetListReq req = new SalesStaffDto.GetListReq();
            req.setStatus("active");
            req.setGroupId(groupId); // 如果指定了销售组，只返回该组的成员
            req.setPageNum(1);
            req.setPageSize(1000); // 获取所有活跃的销售人员

            IPage<SalesStaffDto.Resp> page = salesStaffFacade.getSalesStaffPage(req);

            // 转换为选项格式
            List<SalesStaffDto.OptionResp> options = page.getRecords().stream()
                    .map(staff -> {
                        SalesStaffDto.OptionResp option = new SalesStaffDto.OptionResp();
                        option.setId(staff.getId());
                        option.setName(staff.getSalesName());
                        option.setPhone(staff.getPhone());
                        option.setGroupName(staff.getGroupName());
                        return option;
                    })
                    .collect(java.util.stream.Collectors.toList());

            log.info("获取销售人员选项列表成功: count={}", options.size());
            return AjaxResult.success(options);
        } catch (Exception e) {
            log.error("获取销售人员选项列表失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }
}
