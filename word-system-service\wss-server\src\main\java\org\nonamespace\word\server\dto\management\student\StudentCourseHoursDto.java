package org.nonamespace.word.server.dto.management.student;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 学生课时管理DTO
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
public class StudentCourseHoursDto {

    /**
     * 课时查询请求
     */
    @Data
    public static class QueryRequest {
        /**
         * 学生姓名
         */
        private String studentName;

        /**
         * 学生手机号
         */
        private String studentPhone;

        /**
         * 学科
         */
        private String subject;

        /**
         * 课型
         */
        private String specification;

        /**
         * 性质（正式课、试听课）
         */
        private String nature;

        /**
         * 状态
         */
        private String status;

        /**
         * 老师姓名
         */
        private String teacherName;

        /**
         * 老师手机号
         */
        private String teacherPhone;

        /**
         * 页码
         */
        private Integer pageNum = 1;

        /**
         * 页大小
         */
        private Integer pageSize = 10;
    }

    /**
     * 课时记录响应
     */
    @Data
    public static class CourseHoursResponse {
        /**
         * 记录ID
         */
        private String id;

        /**
         * 学生ID
         */
        private String studentId;

        /**
         * 学生姓名
         */
        private String studentName;

        /**
         * 学生手机号
         */
        private String studentPhone;

        /**
         * 学科
         */
        private String subject;

        /**
         * 课型
         */
        private String specification;

        /**
         * 性质
         */
        private String nature;

        /**
         * 总课时数
         */
        private BigDecimal totalHours;

        /**
         * 剩余课时数
         */
        private BigDecimal remainingHours;

        /**
         * 购买课时数
         */
        private BigDecimal purchasedHours;

        /**
         * 赠送课时数
         */
        private BigDecimal giftHours;

        /**
         * 已消耗总课时数
         */
        private BigDecimal consumedTotalHours;

        /**
         * 已消耗购买课时数
         */
        private BigDecimal consumedPurchasedHours;

        /**
         * 已消耗赠送课时数
         */
        private BigDecimal consumedGiftHours;

        /**
         * 剩余购买课时数
         */
        private BigDecimal remainingPurchasedHours;

        /**
         * 剩余赠送课时数
         */
        private BigDecimal remainingGiftHours;

        /**
         * 单价
         */
        private BigDecimal unitPrice;

        /**
         * 状态
         */
        private String status;

        /**
         * 老师信息（姓名(手机号)）
         */
        private String teacherInfo;

        /**
         * 创建时间
         */
        private Date createTime;

        /**
         * 更新时间
         */
        private Date updateTime;
    }

    /**
     * 课时调整请求
     */
    @Data
    public static class AdjustRequest {
        /**
         * 课时包ID
         */
        @NotBlank(message = "课时包ID不能为空")
        private String courseHoursId;

        /**
         * 学生ID
         */
        @NotNull(message = "学生ID不能为空")
        private String studentId;

        /**
         * 学科
         */
        @NotBlank(message = "学科不能为空")
        private String subject;

        /**
         * 课型
         */
        @NotBlank(message = "课型不能为空")
        private String specification;

        /**
         * 购买课时调整数（正数为增加，负数为减少）
         */
        private BigDecimal purchasedHoursAdjustment = BigDecimal.ZERO;

        /**
         * 赠送课时调整数（正数为增加，负数为减少）
         */
        private BigDecimal giftHoursAdjustment = BigDecimal.ZERO;

        /**
         * 单价 (保留2位小数)
         */
        @DecimalMin(value = "0", message = "单价不能小于0")
        private BigDecimal unitPrice;

        /**
         * 调整原因
         */
        @NotBlank(message = "调整原因不能为空")
        private String adjustmentReason;
    }

    /**
     * 课时更新请求
     */
    @Data
    public static class UpdateRequest {
        /**
         * 课时包ID
         */
        @NotBlank(message = "课时包ID不能为空")
        private String courseHoursId;

        /**
         * 学生ID
         */
        @NotNull(message = "学生ID不能为空")
        private String studentId;

        /**
         * 学科
         */
        @NotBlank(message = "学科不能为空")
        private String subject;

        /**
         * 课型
         */
        @NotBlank(message = "课型不能为空")
        private String specification;

        /**
         * 购买课时数 (保留2位小数)
         */
        @NotNull(message = "购买课时数不能为空")
        @DecimalMin(value = "0", message = "购买课时数不能小于0")
        private BigDecimal purchasedHours;

        /**
         * 剩余购买课时数 (保留2位小数)
         */
        @NotNull(message = "剩余购买课时数不能为空")
        @DecimalMin(value = "0", message = "剩余购买课时数不能小于0")
        private BigDecimal remainingPurchasedHours;

        /**
         * 赠送课时数 (保留2位小数)
         */
        @NotNull(message = "赠送课时数不能为空")
        @DecimalMin(value = "0", message = "赠送课时数不能小于0")
        private BigDecimal giftHours;

        /**
         * 剩余赠送课时数 (保留2位小数)
         */
        @NotNull(message = "剩余赠送课时数不能为空")
        @DecimalMin(value = "0", message = "剩余赠送课时数不能小于0")
        private BigDecimal remainingGiftHours;

        /**
         * 单价 (保留2位小数)
         */
        @DecimalMin(value = "0", message = "单价不能小于0")
        private BigDecimal unitPrice;

        /**
         * 调整原因
         */
        @NotBlank(message = "调整原因不能为空")
        private String adjustmentReason;
    }

    /**
     * 录入课消请求
     */
    @Data
    public static class RecordConsumptionRequest {
        /**
         * 课时包ID
         */
        @NotBlank(message = "课时包ID不能为空")
        private String courseHoursId;

        /**
         * 学生ID
         */
        @NotNull(message = "学生ID不能为空")
        private String studentId;

        /**
         * 学科
         */
        @NotBlank(message = "学科不能为空")
        private String subject;

        /**
         * 课型
         */
        @NotBlank(message = "课型不能为空")
        private String specification;

        /**
         * 性质
         */
        @NotBlank(message = "性质不能为空")
        private String nature;

        /**
         * 消费课时数 (保留2位小数)
         */
        @NotNull(message = "消费课时数不能为空")
        @DecimalMin(value = "0.01", message = "消费课时数必须大于0")
        private BigDecimal consumedHours;

        /**
         * 老师ID（可选）
         */
        private String teacherId;

        /**
         * 课程ID（可选）
         */
        private String courseId;

        /**
         * 备注
         */
        @NotBlank(message = "备注不能为空")
        private String remark;
    }

    /**
     * 新增课时请求
     */
    @Data
    public static class AddCourseHoursRequest {
        /**
         * 学生ID
         */
        @NotNull(message = "学生ID不能为空")
        private String studentId;

        /**
         * 学科
         */
        @NotBlank(message = "学科不能为空")
        private String subject;

        /**
         * 课型
         */
        @NotBlank(message = "课型不能为空")
        private String specification;

        /**
         * 性质
         */
        @NotBlank(message = "性质不能为空")
        private String nature;

        /**
         * 购买课时数 (保留2位小数)
         */
        @NotNull(message = "购买课时数不能为空")
        @DecimalMin(value = "0", message = "购买课时数不能小于0")
        private BigDecimal purchasedHours;

        /**
         * 赠送课时数 (保留2位小数)
         */
        @NotNull(message = "赠送课时数不能为空")
        @DecimalMin(value = "0", message = "赠送课时数不能小于0")
        private BigDecimal giftHours;

        /**
         * 单价 (保留2位小数)
         */
        @NotNull(message = "单价不能为空")
        @DecimalMin(value = "0", message = "单价不能小于0")
        private BigDecimal unitPrice;

        /**
         * 原因说明
         */
        @NotBlank(message = "原因说明不能为空")
        private String reason;
    }

    /**
     * 课消记录查询请求
     */
    @Data
    public static class ConsumptionQueryRequest {
        /**
         * 学生ID
         */
        private String studentId;

        /**
         * 课时记录ID
         */
        private String courseHoursId;

        /**
         * 学生姓名
         */
        private String studentName;

        /**
         * 学生手机号
         */
        private String studentPhone;

        /**
         * 学科
         */
        private String subject;

        /**
         * 课型
         */
        private String specification;

        /**
         * 老师姓名
         */
        private String teacherName;

        /**
         * 课消开始时间
         */
        private Date startTime;

        /**
         * 课消结束时间
         */
        private Date endTime;

        /**
         * 状态
         */
        private String status;

        /**
         * 页码
         */
        private Integer pageNum = 1;

        /**
         * 页大小
         */
        private Integer pageSize = 10;
    }

    /**
     * 课消记录响应
     */
    @Data
    public static class ConsumptionResponse {
        /**
         * 记录ID
         */
        private String id;

        /**
         * 学生ID
         */
        private String studentId;

        /**
         * 学生姓名
         */
        private String studentName;

        /**
         * 学生手机号
         */
        private String studentPhone;

        /**
         * 学科
         */
        private String subject;

        /**
         * 课型
         */
        private String specification;

        /**
         * 性质
         */
        private String nature;

        /**
         * 课消课时数
         */
        private BigDecimal consumedHours;

        /**
         * 课消时间
         */
        private Date consumptionTime;

        /**
         * 课程ID
         */
        private String courseId;

        /**
         * 课时记录ID
         */
        private String courseHoursId;

        /**
         * 课时记录批次号
         */
        private String batchNo;

        /**
         * 老师ID
         */
        private String teacherId;

        /**
         * 老师姓名
         */
        private String teacherName;

        /**
         * 备注
         */
        private String remark;

        /**
         * 状态
         */
        private String status;

        /**
         * 创建时间
         */
        private Date createTime;
    }

    /**
     * 调整历史查询请求
     */
    @Data
    public static class AdjustmentHistoryQueryRequest {
        /**
         * 课时包ID
         */
        private String courseHoursId;

        /**
         * 学生ID
         */
        private String studentId;

        /**
         * 学生姓名
         */
        private String studentName;

        /**
         * 学生手机号
         */
        private String studentPhone;

        /**
         * 学科
         */
        private String subject;

        /**
         * 课型
         */
        private String specification;



        /**
         * 操作人姓名
         */
        private String operatorName;

        /**
         * 调整开始时间
         */
        private Date startTime;

        /**
         * 调整结束时间
         */
        private Date endTime;

        /**
         * 页码
         */
        private Integer pageNum = 1;

        /**
         * 页大小
         */
        private Integer pageSize = 10;
    }

    /**
     * 调整历史响应
     */
    @Data
    public static class AdjustmentHistoryResponse {
        /**
         * 记录ID
         */
        private String id;

        /**
         * 课时包ID
         */
        private String courseHoursId;

        /**
         * 学生ID
         */
        private String studentId;

        /**
         * 学生姓名
         */
        private String studentName;

        /**
         * 学生手机号
         */
        private String studentPhone;

        /**
         * 学科
         */
        private String subject;

        /**
         * 课型
         */
        private String specification;

        /**
         * 性质
         */
        private String nature;

        /**
         * 调整类型
         */
        private String adjustmentType;

        /**
         * 调整课时数
         */
        private BigDecimal adjustmentHours;

        /**
         * 购买课时调整数
         */
        private BigDecimal purchasedHoursAdjustment;

        /**
         * 赠送课时调整数
         */
        private BigDecimal giftHoursAdjustment;

        /**
         * 调整前总课时
         */
        private BigDecimal beforeTotalHours;

        /**
         * 调整后总课时
         */
        private BigDecimal afterTotalHours;

        /**
         * 调整前剩余课时
         */
        private BigDecimal beforeRemainingHours;

        /**
         * 调整后剩余课时
         */
        private BigDecimal afterRemainingHours;

        /**
         * 调整前购买课时
         */
        private BigDecimal beforePurchasedHours;

        /**
         * 调整后购买课时
         */
        private BigDecimal afterPurchasedHours;

        /**
         * 调整前赠送课时
         */
        private BigDecimal beforeGiftHours;

        /**
         * 调整后赠送课时
         */
        private BigDecimal afterGiftHours;

        /**
         * 调整原因
         */
        private String adjustmentReason;

        /**
         * 操作人ID
         */
        private String operatorId;

        /**
         * 操作人姓名
         */
        private String operatorName;

        /**
         * 调整时间
         */
        private Date adjustmentTime;

        /**
         * 创建时间
         */
        private Date createTime;
    }

    /**
     * 导出响应
     */
    @Data
    public static class ExportResponse {
        /**
         * 学生姓名
         */
        private String studentName;

        /**
         * 学生手机号
         */
        private String studentPhone;

        /**
         * 学科
         */
        private String subject;

        /**
         * 课型
         */
        private String specification;

        /**
         * 性质
         */
        private String nature;

        /**
         * 老师信息（姓名(手机号)）
         */
        private String teacherInfo;

        /**
         * 总课时
         */
        private BigDecimal totalHours;

        /**
         * 购买课时
         */
        private BigDecimal purchasedHours;

        /**
         * 赠送课时
         */
        private BigDecimal giftHours;

        /**
         * 剩余购买课时
         */
        private BigDecimal remainingPurchasedHours;

        /**
         * 剩余赠送课时
         */
        private BigDecimal remainingGiftHours;

        /**
         * 剩余课时
         */
        private BigDecimal remainingHours;

        /**
         * 已消耗课时
         */
        private BigDecimal consumedTotalHours;

        /**
         * 单价
         */
        private BigDecimal unitPrice;

        /**
         * 批次号
         */
        private String batchNo;

        /**
         * 创建时间
         */
        private Date createTime;
    }
}
