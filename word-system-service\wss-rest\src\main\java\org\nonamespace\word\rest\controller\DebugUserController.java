package org.nonamespace.word.rest.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysRoleService;
import com.ruoyi.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.domain.*;
import org.nonamespace.word.server.mapper.TeacherProfileMapper;
import org.nonamespace.word.server.service.ITeacherStudentRelationService;
import org.nonamespace.word.server.service.ITeachingGroupMemberService;
import org.nonamespace.word.server.service.ITeachingGroupService;
import org.nonamespace.word.server.service.UserStudentExtService;
import org.nonamespace.word.server.util.SystemDataQueryUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/debug/user")
@Slf4j
@Anonymous
public class DebugUserController extends BaseController {
    @Autowired
    private ISysUserService userService;
    @Autowired
    private ISysDeptService deptService;
    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ITeachingGroupService teachingGroupService;
    @Autowired
    private ITeachingGroupMemberService teachingGroupMemberService;
    @Autowired
    private TeacherProfileMapper teacherProfileMapper;

    @Autowired
    private ITeacherStudentRelationService teacherStudentRelationService;


    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private UserStudentExtService userStudentExtService;
    @Autowired
    private SystemDataQueryUtil systemDataQueryUtil;


    @RequestMapping("/import-student")
    public AjaxResult impportStudent(){
        SysDept dept = new SysDept();
//        dept.setDeptName("学生中心");
//        List<SysDept> sysDepts = deptService.selectDeptList(dept);
//        if(sysDepts.isEmpty()) {
//            throw new RuntimeException("未找到学生中心部门，请先创建");
//        }

//        Long deptId = sysDepts.getFirst().getDeptId();

        Long deptId=112L;

//        SysRole role = new SysRole();
//        role.setRoleName("学生");
//        List<SysRole> sysRoles = roleService.selectRoleList(role);
//        if(sysRoles.isEmpty()) {
//            throw new RuntimeException("未找到学生角色，请先创建");
//        }
//        Long roleId = sysRoles.getFirst().getRoleId();

        Long roleId = 4L; // 学生角色ID

        // 用hutool读取excel
        ExcelReader reader = ExcelUtil.getReader(new File("/Users/<USER>/Downloads/edu/学生列表.xlsx"));
        List<Map<String, Object>> rows = reader.readAll();
        List<SysUser> users = new ArrayList<>();
        rows.forEach(row -> {
            SysUser user = new SysUser();
            user.setUserName(((String) row.get("学生姓名")).trim());
            user.setNickName(((String) row.get("学生姓名")).trim());
            user.setPhonenumber((String.valueOf(row.get("手机号"))).trim());
            user.setRoleIds(CollUtil.list(false, roleId).toArray(new Long[]{}));
            user.setDeptId(deptId);
            user.setStatus("0");
            user.setPassword(SecurityUtils.encryptPassword("654321"));

            users.add(user);
        });

        Map<String, SysUser> userMap = userService.selectUserList(null).stream().collect(Collectors.toMap(SysUser::getUserName, x -> x, (a,b) -> a));

        for (SysUser user : users) {
            if(userMap.containsKey(user.getUserName())) {
                log.warn("用户 {} 已存在，跳过", user.getUserName());
                continue;
            }
            userService.insertUser(user);
        }

        return success("导入学生成功");
    }

    @RequestMapping("/import-teacher")
    public AjaxResult importTeacher(){
        transactionTemplate. execute(status -> {
            try {
//        SysDept dept = new SysDept();
//        dept.setDeptName("教学中心");
//        List<SysDept> sysDepts = deptService.selectDeptList(dept);
//        if(sysDepts.isEmpty()) {
//            throw new RuntimeException("未找到教学中心部门，请先创建");
//        }
//
//        Long deptId = sysDepts.getFirst().getDeptId();

                Long teacherDeptId = systemDataQueryUtil.getTeachingCenterDept().getDeptId();

//        SysRole role = new SysRole();
//        role.setRoleName("老师");
//        List<SysRole> sysRoles = roleService.selectRoleList(role);
//        if(sysRoles.isEmpty()) {
//            throw new RuntimeException("未找到老师角色，请先创建");
//        }
//        Long roleId = sysRoles.getFirst().getRoleId();

                Long teacherRoleId = systemDataQueryUtil.getTeacherRole().getRoleId();


                Map<String, SysDept> deptMap = deptService.selectDeptList(null).stream().filter(d -> d.getAncestors().startsWith("0,100,")).collect(Collectors.toMap(SysDept::getDeptName, x -> x));

                Map<String, String> userGroupMap = new HashMap<>();

                List<TeacherProfile> teacherProfiles = new ArrayList<>();

                // 用hutool读取excel
                ExcelReader reader = ExcelUtil.getReader(new File("/Users/<USER>/Downloads/edu/老师列表.xlsx"));
                List<Map<String, Object>> rows = reader.readAll();
                List<SysUser> users = new ArrayList<>();

                rows.forEach(row -> {
                    if(StrUtil.isEmpty((String) row.get("所属教学组"))){
                        return;
                    }
                    SysUser user = new SysUser();
                    user.setPhonenumber((String.valueOf(row.get("手机号"))).trim());
                    user.setUserName(user.getPhonenumber());
                    user.setNickName(((String) row.get("老师姓名")).trim());
                    user.setStatus("0");
                    log.info("正在处理用户: {}", user.getNickName());
                    String deptName = ((String) row.get("所属教学组")).replaceAll("\\s+", " ").trim();
                    user.setPassword(SecurityUtils.encryptPassword("654321"));

                    user.setNickName(user.getNickName().replaceAll("\\s+", "").trim());
                    user.setPhonenumber(user.getPhonenumber().replaceAll("\\s+", "").trim());


                    SysDept sysDept = deptMap.get(deptName);
                    if (sysDept == null) {
                        sysDept = new SysDept();
                        sysDept.setParentId(teacherDeptId);
                        sysDept.setDeptName(deptName);
                        sysDept.setStatus("0");
                        deptService.insertDept(sysDept);

                        deptMap.clear();
                        deptMap.putAll(
                                deptService.selectDeptList(null).stream().filter(d -> d.getAncestors().startsWith("0,100,")).collect(Collectors.toMap(SysDept::getDeptName, x -> x))
                        );


                        TeachingGroup teachingGroup = new TeachingGroup();
                        teachingGroup.setId(IdUtil.getSnowflakeNextIdStr());
                        teachingGroup.setName(deptName);
                        teachingGroup.setDeptId(deptMap.get(deptName).getDeptId());
                        teachingGroup.setStatus("active");
                        teachingGroupService.save(teachingGroup);

                        userGroupMap.put(user.getPhonenumber(), teachingGroup.getId());
                    } else {
                        String groupId = teachingGroupService.lambdaQuery().eq(TeachingGroup::getDeptId, sysDept.getDeptId())
                                .oneOpt()
                                .map(TeachingGroup::getId)
                                .orElse(null);
                        if (groupId == null) {
                            SysDept finalSysDept = sysDept;

                                TeachingGroup teachingGroup = new TeachingGroup();
                                teachingGroup.setId(IdUtil.getSnowflakeNextIdStr());
                                teachingGroup.setName(deptName);
                                teachingGroup.setDeptId(finalSysDept.getDeptId());
                                teachingGroup.setStatus("active");
                                teachingGroupService.save(teachingGroup);

                                groupId = teachingGroup.getId();

                            userGroupMap.put(user.getPhonenumber(), groupId);
                        }
                        userGroupMap.put(user.getPhonenumber(), groupId);

                    }

                    user.setRoleIds(CollUtil.list(false, teacherRoleId).toArray(new Long[]{}));
                    user.setDeptId(sysDept.getDeptId());


                    users.add(user);
                });

                Map<String, SysUser> userMap = userService.selectUserList(null).stream().collect(Collectors.toMap(SysUser::getUserName, Function.identity(), (a,b)-> a));

                for (SysUser user : users) {
                    log.info("正在导入用户: {}", user.getUserName());
                    if (userService.selectUserByPhone(user.getPhonenumber()) !=  null || userService.selectUserByUserName(user.getUserName()) !=  null) {
                        log.warn("用户 {} 已存在，跳过", user.getUserName());
                        continue;
                    }
                    userService.insertUser(user);

                    SysUser s = new SysUser();
                    s.setPhonenumber("12" + user.getPhonenumber().substring(2));
                    s.setUserName(s.getPhonenumber());
                    s.setNickName("学生1" + s.getPhonenumber().substring(5));
                    s.setStatus("0");
                    s.setPassword(SecurityUtils.encryptPassword("654321"));
                    s.setRoleIds(CollUtil.list(false, 3L).toArray(new Long[]{})); // 学生角色ID
                    s.setDeptId(126L);

                    userService.insertUser(s);

                    SysUser s2 = new SysUser();
                    s2.setPhonenumber("11" + user.getPhonenumber().substring(2));
                    s2.setUserName(s2.getPhonenumber());
                    s2.setNickName("学生2" + s2.getPhonenumber().substring(5));
                    s2.setStatus("0");
                    s2.setPassword(SecurityUtils.encryptPassword("654321"));
                    s2.setRoleIds(CollUtil.list(false, 3L).toArray(new Long[]{})); // 学生角色ID
                    s2.setDeptId(126L);

                    userService.insertUser(s2);

                    TeacherProfile teacherProfile = new TeacherProfile();
                    teacherProfile.setId(IdUtil.getSnowflakeNextIdStr());
                    teacherProfile.setRealName(user.getNickName());
                    teacherProfile.setStatus("active");
                    teacherProfile.setTeacherId(userService.selectUserByPhone(user.getPhonenumber()).getUserId().toString());

                    teacherProfiles.add(teacherProfile);

                    teacherProfileMapper.insert(teacherProfile);

                    TeachingGroupMember teachingGroupMember = new TeachingGroupMember();
                    teachingGroupMember.setId(IdUtil.getSnowflakeNextIdStr());
                    teachingGroupMember.setTeacherId(String.valueOf(teacherProfile.getTeacherId()));
                    teachingGroupMember.setGroupId(userGroupMap.get(user.getPhonenumber()));
                    teachingGroupMember.setRoleType("member");

                    teachingGroupMemberService.save(teachingGroupMember);

                    TeacherStudentRelation teacherStudentRelation = new TeacherStudentRelation();
                    teacherStudentRelation.setId(IdUtil.getSnowflakeNextIdStr());
                    teacherStudentRelation.setTeacherId(teacherProfile.getTeacherId());
                    teacherStudentRelation.setStudentId(s.getUserId().toString());
                    teacherStudentRelation.setStatus("active");
                    teacherStudentRelation.setRelationType("teaching");
                    teacherStudentRelation.setSubject("英语");
                    teacherStudentRelation.setSpecification("单词课");

                    teacherStudentRelationService.save(teacherStudentRelation);

                    teacherStudentRelation.setId(IdUtil.getSnowflakeNextIdStr());
                    teacherStudentRelation.setStudentId(s2.getUserId().toString());
                    teacherStudentRelationService.save(teacherStudentRelation);


                    UserStudentExt us = new UserStudentExt();
                    us.setId(IdUtil.getSnowflakeNextIdStr());
                    us.setStudentId(String.valueOf(s.getUserId()));
                    us.setName(s.getUserName());
                    us.setPhone(s.getPhonenumber());
                    us.setStatus("active");

                    userStudentExtService.save(us);

                    UserStudentExt us2 = new UserStudentExt();
                    us2.setId(IdUtil.getSnowflakeNextIdStr());
                    us2.setStudentId(String.valueOf(s2.getUserId()));
                    us2.setName(s2.getUserName());
                    us2.setPhone(s2.getPhonenumber());
                    us2.setStatus("active");

                    userStudentExtService.save(us2);


                }
            }catch (Exception e) {
                log.error("导入老师失败", e);
                status.setRollbackOnly();
                throw new RuntimeException("导入老师失败: " + e.getMessage());
            }
            return null;
        });
        return success("导入老师成功");

    }
}
