package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nonamespace.word.server.entity.DataEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 学生课消记录实体类
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("student_course_consumption")
public class StudentCourseConsumption extends DataEntity {

    /**
     * 学生ID
     */
    private String studentId;

    /**
     * 学科 (英语、数学、语文等)
     */
    private String subject;

    /**
     * 课型
     */
    private String specification;

    /**
     * 性质 (试听课、正式课)
     */
    private String nature;

    /**
     * 课消课时数 (保留2位小数)
     */
    private BigDecimal consumedHours;

    /**
     * 课消时间
     */
    private Date consumptionTime;

    /**
     * 课程ID
     */
    private String courseId;

    /**
     * 课时记录ID，记录课消是从哪一个课时记录中消耗的
     */
    private String courseHoursId;

    /**
     * 老师ID
     */
    private String teacherId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态 (active: 有效, cancelled: 已取消)
     */
    private String status;
}
