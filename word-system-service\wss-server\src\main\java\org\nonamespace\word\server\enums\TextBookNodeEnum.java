package org.nonamespace.word.server.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
public enum TextBookNodeEnum implements IEnum<Integer> {

    TEXT_BOOK(1, "词典"),
    UNIT(2, "单元"),
    WORD(3, "单词"),
    NULL(null,null);

    private final Integer value;
    private final String text;


    TextBookNodeEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    public static TextBookNodeEnum getByDefault( Integer value, TextBookNodeEnum defaultValue) {
        return Arrays.stream(values()).filter(e -> Objects.equals(e.getValue(),value)).findFirst().orElse(defaultValue);
    }

    public static TextBookNodeEnum getByValue(Integer value) {
        return getByDefault(value, NULL);
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
