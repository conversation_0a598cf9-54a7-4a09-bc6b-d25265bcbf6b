package org.nonamespace.word.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.nonamespace.word.server.domain.TeacherStudentRelation;

import java.util.List;

/**
 * 师生关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Mapper
public interface TeacherStudentRelationMapper extends BaseMapper<TeacherStudentRelation> {

    /**
     * 根据教师ID查询学生ID列表
     * 
     * @param teacherId 教师ID
     * @return 学生ID列表
     */
    List<String> selectStudentIdsByTeacher(@Param("teacherId") String teacherId);

    /**
     * 根据学生ID查询教师ID
     * 
     * @param studentId 学生ID
     * @return 教师ID
     */
    String selectTeacherIdByStudent(@Param("studentId") String studentId);

    /**
     * 分配学生给教师
     * 
     * @param teacherId 教师ID
     * @param studentIds 学生ID列表
     * @return 影响行数
     */
    int assignStudentsToTeacher(@Param("teacherId") String teacherId, @Param("studentIds") List<String> studentIds);

    /**
     * 取消师生关系
     * 
     * @param teacherId 教师ID
     * @param studentId 学生ID
     * @return 影响行数
     */
    int removeTeacherStudentRelation(@Param("teacherId") String teacherId, @Param("studentId") String studentId);

    /**
     * 取消学生的所有教师关系
     * 
     * @param studentId 学生ID
     * @return 影响行数
     */
    int removeAllTeachersByStudent(@Param("studentId") String studentId);

    /**
     * 取消教师的所有学生关系
     * 
     * @param teacherId 教师ID
     * @return 影响行数
     */
    int removeAllStudentsByTeacher(@Param("teacherId") String teacherId);

    /**
     * 检查师生关系是否存在
     * 
     * @param teacherId 教师ID
     * @param studentId 学生ID
     * @return 是否存在
     */
    boolean existsTeacherStudentRelation(@Param("teacherId") String teacherId, @Param("studentId") String studentId);
}
