package org.nonamespace.word.openai.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import org.nonamespace.word.openai.model.WordInfo;

import java.util.List;

/**
 * 硅基流动API
 *
 * <AUTHOR>
 * @date 2025/5/20 9:24
 */
public interface ISiliconflowService {

    List<WordInfo> generateWordMeanings(List<String> words) throws JsonProcessingException;

    List<WordInfo> enrich(List<WordInfo> words);


    List<WordInfo> enrichBasic(List<WordInfo> words);

    /**
     * 仅用于补全释义+ 单词基础信息
     * @param words
     * @return
     */
    List<WordInfo> enrichMeanings(List<WordInfo> words);

    /**
     * 仅用于补全例句
     * @param words
     * @return
     */
    List<WordInfo> enrichSentences(List<WordInfo> words);

}
