package org.nonamespace.word.server.dto.course;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
public class CourseDto {
    private String id;
    private String type;
    private String status;
    private org.nonamespace.word.server.dto.CourseInfoDto.Resp.Teacher teacher;
    private org.nonamespace.word.server.dto.CourseInfoDto.Resp.Student student;
    private Date scheduledStartTime;
    private Date scheduledEndTime;
    private Date actualStartTime;
    private Date actualEndTime;
    private Long durationMinutes;
    private org.nonamespace.word.server.dto.CourseInfoDto.Resp.Content content;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Teacher {
        private String id;
        private String name;
        private String avatar;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Student {
        private String id;
        private String name;
        private String avatar;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Content {
        private Long currentSectionIndex;
        private List<CourseSectionDto> sections;
    }
}
