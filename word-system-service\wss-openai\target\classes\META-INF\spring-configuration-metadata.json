{"groups": [{"name": "nebius", "type": "org.nonamespace.word.openai.config.NebiusConfig", "sourceType": "org.nonamespace.word.openai.config.NebiusConfig"}, {"name": "nebius.proxy", "type": "org.nonamespace.word.openai.config.NebiusConfig$Proxy", "sourceType": "org.nonamespace.word.openai.config.NebiusConfig", "sourceMethod": "getProxy()"}, {"name": "volcengine", "type": "org.nonamespace.word.openai.config.VolcengineConfig", "sourceType": "org.nonamespace.word.openai.config.VolcengineConfig"}, {"name": "volcengine.speech", "type": "org.nonamespace.word.openai.config.VolcengineConfig$Speech", "sourceType": "org.nonamespace.word.openai.config.VolcengineConfig", "sourceMethod": "getSpeech()"}, {"name": "volcengine.speech.voice-type", "type": "org.nonamespace.word.openai.config.VolcengineConfig$Speech$VoiceType", "sourceType": "org.nonamespace.word.openai.config.VolcengineConfig$Speech", "sourceMethod": "getVoiceType()"}, {"name": "xai", "type": "org.nonamespace.word.openai.config.XAIConfig", "sourceType": "org.nonamespace.word.openai.config.XAIConfig"}, {"name": "xai.proxy", "type": "org.nonamespace.word.openai.config.XAIConfig$Proxy", "sourceType": "org.nonamespace.word.openai.config.XAIConfig", "sourceMethod": "getProxy()"}], "properties": [{"name": "nebius.api-key", "type": "java.lang.String", "sourceType": "org.nonamespace.word.openai.config.NebiusConfig"}, {"name": "nebius.base-url", "type": "java.lang.String", "sourceType": "org.nonamespace.word.openai.config.NebiusConfig"}, {"name": "nebius.model", "type": "java.lang.String", "sourceType": "org.nonamespace.word.openai.config.NebiusConfig"}, {"name": "nebius.proxy.host", "type": "java.lang.String", "sourceType": "org.nonamespace.word.openai.config.NebiusConfig$Proxy"}, {"name": "nebius.proxy.port", "type": "java.lang.Integer", "sourceType": "org.nonamespace.word.openai.config.NebiusConfig$Proxy"}, {"name": "nebius.reasoning-effort", "type": "java.lang.String", "sourceType": "org.nonamespace.word.openai.config.NebiusConfig"}, {"name": "nebius.temperature", "type": "java.lang.Integer", "sourceType": "org.nonamespace.word.openai.config.NebiusConfig"}, {"name": "volcengine.base-url", "type": "java.lang.String", "sourceType": "org.nonamespace.word.openai.config.VolcengineConfig"}, {"name": "volcengine.speech.access-token", "type": "java.lang.String", "sourceType": "org.nonamespace.word.openai.config.VolcengineConfig$Speech"}, {"name": "volcengine.speech.appid", "type": "java.lang.String", "sourceType": "org.nonamespace.word.openai.config.VolcengineConfig$Speech"}, {"name": "volcengine.speech.cluster", "type": "java.lang.String", "sourceType": "org.nonamespace.word.openai.config.VolcengineConfig$Speech"}, {"name": "volcengine.speech.secretkey", "type": "java.lang.String", "sourceType": "org.nonamespace.word.openai.config.VolcengineConfig$Speech"}, {"name": "volcengine.speech.voice-type.uk", "type": "java.lang.String", "sourceType": "org.nonamespace.word.openai.config.VolcengineConfig$Speech$VoiceType"}, {"name": "volcengine.speech.voice-type.us", "type": "java.lang.String", "sourceType": "org.nonamespace.word.openai.config.VolcengineConfig$Speech$VoiceType"}, {"name": "xai.api-key", "type": "java.lang.String", "sourceType": "org.nonamespace.word.openai.config.XAIConfig"}, {"name": "xai.base-url", "type": "java.lang.String", "sourceType": "org.nonamespace.word.openai.config.XAIConfig"}, {"name": "xai.model", "type": "java.lang.String", "sourceType": "org.nonamespace.word.openai.config.XAIConfig"}, {"name": "xai.proxy.host", "type": "java.lang.String", "sourceType": "org.nonamespace.word.openai.config.XAIConfig$Proxy"}, {"name": "xai.proxy.port", "type": "java.lang.Integer", "sourceType": "org.nonamespace.word.openai.config.XAIConfig$Proxy"}, {"name": "xai.reasoning-effort", "type": "java.lang.String", "sourceType": "org.nonamespace.word.openai.config.XAIConfig"}, {"name": "xai.temperature", "type": "java.lang.Integer", "sourceType": "org.nonamespace.word.openai.config.XAIConfig"}], "hints": []}