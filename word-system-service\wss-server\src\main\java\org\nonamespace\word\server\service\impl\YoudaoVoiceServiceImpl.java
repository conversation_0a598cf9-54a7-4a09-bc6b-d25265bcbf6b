package org.nonamespace.word.server.service.impl;

import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.domain.WordFetchLog;
import org.nonamespace.word.server.mapper.WordFetchLogMapper;
import org.nonamespace.word.server.service.IYoudaoVoiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 有道词典语音服务实现
 */
@Slf4j
@Service
public class YoudaoVoiceServiceImpl implements IYoudaoVoiceService {

    @Value("${word.audio.save-path:audio}")
    private String audioSavePath;
    @Value("${word.audio.base-url}")
    private String audioBaseUrl;
    @Autowired
    private WordFetchLogMapper wordFetchLogMapper;

    private static final TimedCache<String, Boolean> WORD_FETCH_MAP = new TimedCache<String, Boolean>(10_000);

    @Override
    public String downloadWordAudio(String word, int type) {
        try {
            byte[] bytes = this.downloadWordAudioByte(word, type);

            if (bytes != null) {
                // 确保目录存在
                File directory = new File(audioSavePath, word);
                if (!directory.exists()) {
                    directory.mkdirs();
                }

                // 构建文件路径
                String fileName = String.format("%s_%d.mp3", word, type);
                String filePath = new File(directory, fileName).getPath();

                // 保存文件
                Files.write(Paths.get(filePath), bytes);

                log.info("音频文件已保存: {}", filePath);
                return filePath;
            }
            return null;

        } catch (Exception e) {
            log.error("下载保存音频文件失败:{}，type={}", word, type);
            throw new RuntimeException("下载音频文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String[] downloadWordAudios(String word) {
        String usAudioPath = downloadWordAudio(word, 0);  // 美式发音
        String ukAudioPath = downloadWordAudio(word, 1);  // 英式发音
        return new String[]{usAudioPath, ukAudioPath};
    }

    @Override
    public byte[] downloadWordAudioByte(String word, int type) {
        try {
            // 构建请求URL
            String url = String.format(audioBaseUrl + "?type=%d&audio=%s", type, URLEncoder.encode(word, StandardCharsets.UTF_8).replace("+", "%20"));
            // 创建请求头
            HttpHeaders headers = new HttpHeaders();

            // 如果非这些码，睡眠1秒，重新拉去一次
            Thread.sleep(1000);
            headers.setAccept(Collections.singletonList(MediaType.parseMediaType("audio/mpeg")));
            HttpResponse httpResponse = HttpUtil.createGet(url)
                    .header(headers)
                    .timeout(600000) // 设置10分钟超时（600秒 = 600000毫秒）
                    .execute();

            // 过去的爬取记录
            List<WordFetchLog> wordFetchLogs = wordFetchLogMapper.selectList(new QueryWrapper<WordFetchLog>().lambda().eq(WordFetchLog::getWord, word));
            if(CollUtil.isEmpty(wordFetchLogs)){
                WordFetchLog log = new WordFetchLog();
                log.setWord(word);
                wordFetchLogMapper.insert(log);
            }

            if (httpResponse.getStatus() == 200 && httpResponse.bodyBytes() != null && httpResponse.bodyBytes().length > 1024) {
                return httpResponse.bodyBytes();
            }
            if(httpResponse.getStatus() == 500){
                WordFetchLog log = new WordFetchLog();
                log.setWord(word);
                log.setYoudaoStatus(JSONUtil.toJsonStr(httpResponse.body()));
                wordFetchLogMapper.update(log, new QueryWrapper<WordFetchLog>().lambda().eq(WordFetchLog::getWord, word));
            }

            log.warn("单词[{}]获取音标异常：{}", word, JSONUtil.toJsonStr(httpResponse.body()));
            if(WORD_FETCH_MAP.get(word) == null && httpResponse.getStatus() != 500) {
                Thread.sleep(2000);
                WORD_FETCH_MAP.put(word, Boolean.TRUE);
                log.warn("单词[{}]重新获取一次", word);
                this.downloadWordAudioByte(word, type);
            }
            return null;
        } catch (Exception e) {
            log.error("下载音频文件失败:{}，type={}", word, type);
            throw new RuntimeException("下载音频文件失败: " + e.getMessage(), e);
        }
    }
}
