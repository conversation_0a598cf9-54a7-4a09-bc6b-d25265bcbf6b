# 产品管理前端Mock数据使用说明

## 概述

根据您的要求，产品管理前端部分现在使用mock数据，不依赖后端API。这样可以让前端功能独立开发和测试，后续可以轻松切换到真实的后端API。

## 已修改的文件

### 1. 产品管理页面
**文件**: `words-frontend/src/views/management/product/index.vue`

**主要修改**:
- 注释掉了真实API的导入
- 在data中添加了mockProductList数组，包含5个示例产品
- 修改了所有API调用方法，使用本地mock数据模拟操作
- 保持了原有的功能逻辑（增删改查、搜索、分页等）

**Mock数据包含**:
- 小学英语单词课程包 (¥2999.00)
- 初中数学基础课程 (¥3999.00)
- 高中语文阅读理解专项 (¥4999.00)
- 英语语法单次课程 (¥99.00, 下架状态)
- 数学教材配套练习册 (¥59.00)

### 2. 产品API文件
**文件**: `words-frontend/src/api/management/product.js`

**主要修改**:
- 注释掉了真实的API调用代码
- 添加了mock数据和模拟API函数
- 实现了getAvailableProductsApi、getProductsByTypeApi、getProductsBySubjectApi等方法
- 添加了模拟网络延迟（500ms）

### 3. 订单下单页面
**文件**: `words-frontend/src/views/management/order/index.vue`

**主要修改**:
- 使用mock版本的getAvailableProductsApi
- 移除了重复的mock数据定义
- 保持了原有的下单流程功能

## 功能特性

### 1. 产品管理页面功能
✅ **产品列表展示**: 显示所有mock产品数据
✅ **搜索功能**: 支持按产品名称、类型、学科、状态搜索
✅ **分页功能**: 模拟分页逻辑
✅ **新增产品**: 可以添加新产品到mock数据
✅ **编辑产品**: 可以修改现有产品信息
✅ **删除产品**: 可以删除产品（从mock数据中移除）
✅ **上架/下架**: 可以切换产品状态
✅ **价格显示**: 自动转换分为元显示

### 2. 订单下单页面功能
✅ **产品选择**: 显示所有上架的产品
✅ **产品卡片**: 显示产品图片、名称、价格、课时等信息
✅ **产品选中**: 支持点击选择产品
✅ **下单流程**: 完整的四步骤下单流程

### 3. Mock数据特点
- **真实性**: 数据结构与真实API保持一致
- **完整性**: 包含所有必要字段
- **多样性**: 涵盖不同类型、学科的产品
- **状态多样**: 包含上架和下架状态的产品

## 使用方法

### 1. 启动前端项目
```bash
cd words-frontend
npm run dev
```

### 2. 访问产品管理页面
- URL: `http://localhost:3000/management/product`
- 可以进行所有产品管理操作
- 数据变更会在当前会话中保持

### 3. 访问订单下单页面
- URL: `http://localhost:3000/management/order`
- 可以选择产品进行下单
- 只显示上架状态的产品

## Mock数据结构

```javascript
{
  id: '1',                                    // 产品ID
  name: '小学英语单词课程包',                    // 产品名称
  description: '适合小学生的英语单词学习课程...',  // 产品描述
  type: '课程包',                             // 产品类型
  subject: '英语',                           // 学科
  specification: '单词课',                    // 课型
  grade: '小学1-6年级',                       // 适用年级
  price: 299900,                            // 价格(分)
  hours: 20.0,                              // 课时数量
  status: '上架',                           // 状态
  coverImage: 'https://via.placeholder.com/300x200?text=英语课程', // 封面图
  sortOrder: 1,                             // 排序权重
  createTime: '2025-08-05 10:00:00',        // 创建时间
  updateTime: '2025-08-05 10:00:00'         // 更新时间
}
```

## 切换到真实API

当后端API准备就绪时，可以按以下步骤切换：

### 1. 恢复API文件
在 `words-frontend/src/api/management/product.js` 中：
```javascript
// 取消注释真实API代码
import request from '@/utils/request'

// 注释掉mock数据和函数
// const mockProducts = [...]
```

### 2. 恢复产品管理页面
在 `words-frontend/src/views/management/product/index.vue` 中：
```javascript
// 取消注释API导入
import { 
  getProductListApi, 
  getProductDetailApi, 
  createProductApi, 
  updateProductApi, 
  deleteProductApi,
  enableProductApi,
  disableProductApi
} from '@/api/management/product'

// 移除mockProductList数据
// 恢复原有的API调用方法
```

## 注意事项

### 1. 数据持久性
- Mock数据只在当前会话中有效
- 刷新页面会重置所有数据变更
- 如需持久化，可以考虑使用localStorage

### 2. 图片资源
- 当前使用placeholder图片
- 真实环境中需要配置图片上传和存储

### 3. 权限控制
- Mock版本跳过了权限验证
- 真实环境中需要配置相应的权限

### 4. 错误处理
- Mock版本简化了错误处理
- 真实环境中需要完善错误处理逻辑

## 开发建议

### 1. 数据一致性
确保mock数据结构与后端API返回的数据结构完全一致，便于后续切换。

### 2. 功能完整性
在mock环境中测试所有功能，确保逻辑正确。

### 3. 性能考虑
Mock版本添加了模拟延迟，更接近真实网络环境。

### 4. 测试覆盖
利用mock数据进行全面的功能测试，包括边界情况。

## 总结

现在产品管理前端已经完全使用mock数据，可以独立运行和测试。所有功能都已经实现，包括产品的增删改查、状态管理、搜索筛选等。当后端API准备就绪时，只需要简单的配置修改就可以切换到真实的API调用。
