package org.nonamespace.word.server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.misc.WssContext;
import org.nonamespace.word.server.domain.CourseBookingApplication;
import org.nonamespace.word.server.mapper.CourseBookingApplicationMapper;
import org.nonamespace.word.server.service.ICourseBookingApplicationService;
import org.springframework.stereotype.Service;

/**
 * 预约课申请数据层服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-29
 */
@Slf4j
@Service
public class CourseBookingApplicationServiceImpl extends ServiceImpl<CourseBookingApplicationMapper, CourseBookingApplication> implements ICourseBookingApplicationService {

    @Override
    public boolean remindTeachingGroupLeader(String applicationId, String operatorId) {
        return false;
//        try {
//            log.info("催促教学组长处理申请: applicationId={}, operatorId={}", applicationId, operatorId);
//
//            // 获取申请信息
//            CourseBookingApplication application = this.getById(applicationId);
//            if (application == null || application.getDeleted()) {
//                log.warn("申请不存在: applicationId={}", applicationId);
//                return false;
//            }
//
//            // 检查申请状态
//            if (!CourseBookingApplication.Status.PENDING.getCode().equals(application.getStatus())) {
//                log.warn("申请状态不是待处理，无法催促: applicationId={}, status={}", applicationId, application.getStatus());
//                return false;
//            }
//
//            // 检查申请时间是否超过4小时
//            LocalDateTime applyTime = application.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
//            LocalDateTime now = LocalDateTime.now();
//            if (applyTime.isAfter(now.minusHours(4))) {
//                log.warn("申请提交不足4小时，无法催促: applicationId={}, applyTime={}", applicationId, applyTime);
//                return false;
//            }
//
//            // 检查最后催促时间是否超过24小时
//            if (application.getLastRemindTime() != null) {
//                LocalDateTime lastRemindTime = application.getLastRemindTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
//                if (lastRemindTime.isAfter(now.minusHours(24))) {
//                    log.warn("24小时内已催促过，无法重复催促: applicationId={}, lastRemindTime={}", applicationId, lastRemindTime);
//                    return false;
//                }
//            }
//
//            // 发送催促微信通知
//            boolean notificationSuccess = wechatNotificationService.sendCourseBookingNotification(applicationId, "REMIND_LEADER");
//
//            // 更新催促记录
//            application.setLastRemindTime(new Date());
//            application.setRemindCount(application.getRemindCount() == null ? 1 : application.getRemindCount() + 1);
//            boolean updateSuccess = this.updateById(application);
//
//            if (updateSuccess) {
//                log.info("催促记录更新成功: applicationId={}, remindCount={}", applicationId, application.getRemindCount());
//            } else {
//                log.warn("催促记录更新失败: applicationId={}", applicationId);
//            }
//
//            return updateSuccess && notificationSuccess;
//
//        } catch (Exception e) {
//            log.error("催促教学组长失败", e);
//            return false;
//        }
    }

    @Override
    public boolean voidApplicationData(String applicationId, String operatorId, String voidReason) {
        try {
            log.info("作废申请数据操作: applicationId={}, operatorId={}, voidReason={}", applicationId, operatorId, voidReason);

            // 获取申请信息
            CourseBookingApplication application = this.getById(applicationId);
            if (application == null || application.getDeleted()) {
                log.warn("申请不存在: applicationId={}", applicationId);
                return false;
            }

            // 更新申请状态为已作废
            application.setStatus(CourseBookingApplication.Status.VOIDED.getCode());
            application.setRejectionReason(voidReason); // 复用拒绝原因字段存储作废原因
            application.setApprovalBy(operatorId);
            application.setApprovalTime(WssContext.now());
            application.setUpdateTime(WssContext.now());

            boolean success = this.updateById(application);

            if (success) {
                log.info("申请作废数据更新成功: applicationId={}", applicationId);
            } else {
                log.warn("申请作废数据更新失败: applicationId={}", applicationId);
            }

            return success;

        } catch (Exception e) {
            log.error("作废申请数据操作失败: applicationId={}, operatorId={}", applicationId, operatorId, e);
            return false;
        }
    }
}
