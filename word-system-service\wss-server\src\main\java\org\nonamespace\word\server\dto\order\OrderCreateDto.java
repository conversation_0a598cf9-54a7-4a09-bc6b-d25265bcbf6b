package org.nonamespace.word.server.dto.order;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;

import java.util.List;

@Data
public class OrderCreateDto {

    private String studentId;
    private String productId;

    private List<MultiTrxAmt> multiTrxAmts;

    /**
     * 分期支付dto
     */
    @Data
    public static class MultiTrxAmt {
        // 第几期
        private int idx;
        // 支付金额
        private Long amt;
    }

    /**
     * 校验分期金额，是否等于总金额
     * @param totalAmt  订单总金额
     * @return
     */
    public boolean isValidMultiTrxAmt(long totalAmt) {
        if(CollUtil.isEmpty(multiTrxAmts)) {
            return true;
        }

        // multiTrxAmts累加金额是否等于totalAmt
        long multiTrxAmtTotal = multiTrxAmts.stream().mapToLong(MultiTrxAmt::getAmt).sum();
        return multiTrxAmtTotal == totalAmt;
    }
}
