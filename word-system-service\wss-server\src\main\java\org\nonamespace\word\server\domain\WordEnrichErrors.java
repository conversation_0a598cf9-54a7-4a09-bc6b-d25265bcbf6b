package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import org.nonamespace.word.server.entity.BaseEntity;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "word_enrich_errors", autoResultMap = true)
public class WordEnrichErrors extends BaseEntity {

    private String word;
    private String response;
    private Date createTime;

    private Long millTimes;

    @TableField("enrich_stage")
    private String enrichStage;

}
