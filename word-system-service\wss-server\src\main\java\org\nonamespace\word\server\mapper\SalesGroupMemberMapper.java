package org.nonamespace.word.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.nonamespace.word.server.domain.SalesGroupMember;

/**
 * 销售组成员Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-28
 */
@Mapper
public interface SalesGroupMemberMapper extends BaseMapper<SalesGroupMember> {
    // 只提供基础的CRUD操作，复杂查询通过Service层的lambdaQuery()和MPJLambdaWrapper实现
}
