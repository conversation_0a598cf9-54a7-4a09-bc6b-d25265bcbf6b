package org.nonamespace.word.server.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.nonamespace.word.server.dto.curriculum.CurriculumUsersDto;
import org.nonamespace.word.server.dto.management.teachinggroup.TeacherDto;
import org.nonamespace.word.server.dto.management.teachermatch.TeacherMatchDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 教师管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface ITeacherManagementService {

    /**
     * 查询教师详细信息
     * 
     * @param teacherId 教师ID
     * @return 教师详细信息
     */
    TeacherDto.DetailResp getTeacherDetail(String teacherId);

    /**
     * 查询可分配的教师列表（未分配到任何教学组的教师）
     * 
     * @return 可分配教师列表
     */
    List<TeacherDto.AvailableResp> getAvailableTeachers();

    /**
     * 查询教师列表（支持搜索和分页）
     *
     * @param req 查询请求参数
     * @return 教师分页列表
     */
    IPage<TeacherDto.BasicResp> getTeachersPage(TeacherDto.GetListReq req);

    /**
     * 查询所有教师列表（用于选择组长、教务）
     *
     * @return 教师列表
     */
    List<TeacherDto.UserRoleResp> getAllTeachers();

    /**
     * 查询教师的时间表
     *
     * @param teacherId 教师ID
     * @return 时间表列表
     */
    List<TeacherDto.TimeSlotResp> getTeacherTimeSlots(String teacherId);

    /**
     * 批量查询教师时间段
     *
     * @param teacherIds 教师ID列表
     * @return 教师时间段映射表
     */
    Map<String, List<TeacherDto.TimeSlotResp>> getTeacherTimeSlotsMap(List<String> teacherIds);

    /**
     * 更新教师时间表（带权限检查）
     *
     * @param req 更新请求
     * @return 是否成功
     */
    boolean updateTeacherTimeSlotsWithPermissionCheck(TeacherDto.UpdateTimeSlotsReq req);

    /**
     * 更新教师时间表
     * 
     * @param req 更新请求
     * @return 是否成功
     */
    boolean updateTeacherTimeSlots(TeacherDto.UpdateTimeSlotsReq req);

    /**
     * 查询教师带教信息
     * 
     * @param teacherId 教师ID
     * @return 带教信息
     */
    TeacherDto.TeachingInfoResp getTeachingInfo(String teacherId);

    /**
     * 批量查询教师带教信息
     *
     * @param teacherIds 教师ID列表
     * @return 带教信息列表
     */
    List<TeacherDto.TeachingInfoResp> getTeachingInfoBatch(List<String> teacherIds);

    /**
     * 检查教师时间表最后更新时间
     *
     * @param teacherId 教师ID
     * @return 最后更新时间检查结果
     */
    TeacherDto.TimeSlotUpdateCheckResp checkTeacherTimeSlotUpdateTime(String teacherId);

    /**
     * 批量查询教师详细信息
     *
     * @param teacherIds 教师ID列表
     * @return 教师详细信息列表
     */
    List<TeacherDto.DetailResp> getTeacherDetailBatch(List<String> teacherIds);

    /**
     * 更新教师信息
     *
     * @param req 更新请求
     * @return 是否成功
     */
    boolean updateTeacherInfo(TeacherDto.UpdateTeacherReq req);

    /**
     * 更新教师暑期课上课时间
     *
     * @param req 更新请求
     * @return 是否成功
     */
    boolean updateTeacherSummerSchedule(TeacherDto.UpdateSummerScheduleReq req);

    /**
     * 创建教师
     *
     * @param req 创建请求
     * @return 教师ID
     */
    String createTeacher(TeacherDto.CreateTeacherReq req);

    /**
     * 获取教师课表
     *
     * @param teacherId 教师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 课表列表
     */
    List<TeacherDto.ScheduleResp> getTeacherSchedule(String teacherId, String startDate, String endDate);

    /**
     * 批量导入教师
     *
     * @param teacherDataList 教师数据列表
     * @return 导入结果
     */
    TeacherDto.ImportResult importTeachers(List<TeacherDto.ImportTeacherData> teacherDataList);

    /**
     * 导出教师列表
     *
     * @param req 查询条件
     * @return 教师导出数据
     */
    List<TeacherDto.ExportData> exportTeachers(TeacherDto.GetListReq req);

    /**
     * 更新教师状态
     *
     * @param teacherId 教师ID
     * @param status 状态（active/inactive）
     * @return 是否成功
     */
    boolean updateTeacherStatus(String teacherId, String status);

    /**
     * 分配学生给教师
     *
     * @param req 分配请求
     * @return 是否成功
     */
    boolean assignStudentsToTeacher(TeacherDto.AssignStudentsReq req);

    /**
     * 获取教师的学生列表
     *
     * @param teacherId 教师ID
     * @return 学生列表
     */
    List<TeacherDto.StudentResp> getTeacherStudents(String teacherId);

    /**
     * 删除教师
     *
     * @param teacherId 教师ID
     * @return 是否成功
     */
    boolean deleteTeacher(String teacherId);

    /**
     * 批量删除教师
     *
     * @param teacherIds 教师ID列表
     * @return 是否成功
     */
    boolean batchDeleteTeachers(List<String> teacherIds);

    /**
     * 重置教师密码
     *
     * @param teacherId 教师ID
     * @return 是否成功
     */
    boolean resetTeacherPassword(String teacherId);

    List<CurriculumUsersDto.User> teachers(CurriculumUsersDto.Req req);

    List<CurriculumUsersDto.User> students(CurriculumUsersDto.Req req);

    /**
     * 获取可选老师列表（根据权限控制）
     * 规则：1. admin和人力查询所有；2. 教学组长和教务查看所在组内老师；3. 老师只能查自己；4. 其他返回空
     *
     * @return 可选老师列表（包含id和name字段）
     */
    List<TeacherDto.SelectOption> teacherSelectLists();

    /**
     * 获取符合筛选条件的活跃教师列表（高性能版本）
     *
     * @param request 筛选条件
     * @return 教师基本信息列表
     */
    List<TeacherDto.BasicResp> getFilteredActiveTeachers(TeacherMatchDto.MatchTeachersReq request);

    Set<String> getManagedTeacherIds();
}
