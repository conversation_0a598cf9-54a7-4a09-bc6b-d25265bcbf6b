package org.nonamespace.word.thirdpart.esign.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.nonamespace.word.thirdpart.esign.model.inner.TemplateComponentPosition;

import java.util.List;

/**
 * 填写模板生成文件请求参数
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreateFileByTemplateInput {

    /**
     * 待填充的模板ID（通过【获取制作合同模板页面】接口获取）
     */
    private String docTemplateId;

    /**
     * 填充后生成的文件名称（可自定义文件名称）
     * 注：文件名称不可含有以下9个特殊字符：/ \\ : * \" < > | ？以及所有emoji表情
     * 文件名称长度限制不能超过100字符
     */
    private String fileName;

    /**
     * 控件列表（控件ID和控件Key二选一传值）
     */
    private List<ComponentInfo> components;

    /**
     * 是否校验PDF模板中必填控件，默认：false
     * false：不校验模板中必填控件（components必须传，可以传空数组）
     * true：校验模板中必填控件，必填控件不传值会报错
     * 补充说明：该参数只针对PDF模板生效，HTML模板不生效，即：HTML模板会强制校验必填控件
     */
    private Boolean requiredCheck = false;

    /**
     * 控件信息
     */
    @Data
    @AllArgsConstructor
    public static class ComponentInfo {
        /**
         * 控件ID（设置合同模板时由e签宝系统自动生成）
         */
        private String componentId;
        private Integer componentType;
        private String componentName;

        /**
         * 控件Key（设置合同模板时由用户自定义）
         */
        private String componentKey;

        /**
         * 控件填充值
         * 补充说明：
         * （1）可根据控件类型进行填充
         * （2）填充动态表格控件时，若需新增一行数据时 insertRow 参数值必须传 true
         */
        private String componentValue;

        private TemplateComponentPosition position;

        public ComponentInfo(String componentId, String componentValue, TemplateComponentPosition position) {
            this.componentId = componentId;
            this.componentValue = componentValue;
            this.position = position;
        }
    }
}