package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import org.nonamespace.word.server.entity.DataEntity;

/**
 * 词词汇关联: 关联词和具体单词，定义词汇级别等对象 textbook_item
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "词词汇关联表")
@TableName("textbook_item")
public class TextbookItem extends DataEntity
{

    /** 关联ID */
//    private String id;

    /** 词表ID (外键, 级联删除) */
    @Excel(name = "词表ID (外键, 级联删除)")
    private String textbookId;

    /** 节点类型(章节、单词) */
    @Excel(name = "节点类型(章节、单词)")
    private int nodeType;

    private String unitName;

    /** 父节点ID，同一个词表内pid=0 */
    @Excel(name = "父节点ID，同一个词表内pid=0")
    private String pid;

    /** 单词ID (外键, 级联删除) */
    @Excel(name = "单词ID (外键, 级联删除)")
    private String wordId;

    /** 讲解视频 */
    @Excel(name = "讲解视频")
    private String videoUrl;

    /** 显示顺序  */
    @Excel(name = "显示顺序 ")
    private int displayOrder;

}
