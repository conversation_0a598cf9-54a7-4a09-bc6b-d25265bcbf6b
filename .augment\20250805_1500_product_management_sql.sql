-- 产品管理相关数据库表创建脚本
-- 创建时间: 2025-08-05 16:00
-- 更新时间: 2025-08-05 16:00

-- 创建产品表
CREATE TABLE IF NOT EXISTS `product` (
  `id` varchar(64) NOT NULL COMMENT '产品ID',
  `name` varchar(255) NOT NULL COMMENT '产品名称',
  `description` text COMMENT '产品描述',
  `type` varchar(50) NOT NULL COMMENT '产品类型 (课程包、单次课程、教材等)',
  `subject` varchar(50) NOT NULL COMMENT '学科 (英语、数学、语文等)',
  `specification` varchar(100) COMMENT '课型 (单词课、题型课等)',
  `grade` varchar(50) COMMENT '适用年级',
  `price` bigint NOT NULL COMMENT '产品价格(分)',
  `hours` decimal(10,1) COMMENT '课时数量',
  `status` varchar(20) NOT NULL DEFAULT '上架' COMMENT '产品状态 (上架、下架)',
  `cover_image` varchar(500) COMMENT '产品封面图片URL',
  `detail_images` text COMMENT '产品详情图片URLs (JSON格式)',
  `sort_order` int DEFAULT 0 COMMENT '排序权重',
  `remark` text COMMENT '备注',
  `stock` int DEFAULT 0 COMMENT '库存数量',
  `stock_limited` tinyint(1) DEFAULT 0 COMMENT '是否限制库存 (0-不限制 1-限制)',
  `sales_count` int DEFAULT 0 COMMENT '销售数量',
  `tags` json COMMENT '产品标签 (JSON格式)',
  `content` longtext COMMENT '产品详情内容',
  `create_by` varchar(64) COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '删除标志 (0代表存在 1代表删除)',
  PRIMARY KEY (`id`),
  KEY `idx_product_type` (`type`),
  KEY `idx_product_subject` (`subject`),
  KEY `idx_product_status` (`status`),
  KEY `idx_product_sort` (`sort_order`),
  KEY `idx_product_sales` (`sales_count`),
  KEY `idx_product_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品信息表';

-- 插入示例产品数据
INSERT INTO `product` (`id`, `name`, `description`, `type`, `subject`, `specification`, `grade`, `price`, `hours`, `status`, `sort_order`, `stock`, `stock_limited`, `sales_count`, `tags`, `content`, `remark`) VALUES
('1', '小学英语单词课程包', '适合小学生的英语单词学习课程，包含基础词汇和常用句型', '课程包', '英语', '单词课', '小学1-6年级', 299900, 20.0, '上架', 1, 100, 1, 15, '["热门", "基础", "小学"]', '<h3>课程介绍</h3><p>本课程专为小学生设计，涵盖基础英语单词学习...</p>', '热门课程'),
('2', '初中数学基础课程', '初中数学基础知识点讲解，适合初一初二学生', '课程包', '数学', '基础课', '初中1-2年级', 399900, 30.0, '上架', 2, 50, 1, 8, '["基础", "数学", "初中"]', '<h3>课程介绍</h3><p>初中数学基础知识点全面讲解...</p>', '基础必修'),
('3', '高中语文阅读理解专项', '高中语文阅读理解技巧训练，提升阅读能力', '课程包', '语文', '阅读课', '高中1-3年级', 499900, 25.0, '上架', 3, 30, 1, 12, '["专项", "语文", "高中"]', '<h3>课程介绍</h3><p>高中语文阅读理解技巧专项训练...</p>', '专项提升'),
('4', '英语语法单次课程', '英语语法专项讲解，单次课程', '单次课程', '英语', '语法课', '初中1-3年级', 9900, 2.0, '上架', 4, 0, 0, 25, '["语法", "英语", "单次"]', '<h3>课程介绍</h3><p>英语语法专项讲解，单次课程...</p>', '语法专项'),
('5', '数学教材配套练习册', '数学教材配套的练习册，包含详细解答', '教材', '数学', '练习册', '小学3-6年级', 5900, NULL, '上架', 5, 200, 1, 35, '["教材", "练习册", "数学"]', '<h3>产品介绍</h3><p>数学教材配套练习册，包含详细解答...</p>', '教材配套'),
('6', '高考英语冲刺班', '高考英语最后冲刺，重点突破', '课程包', '英语', '冲刺课', '高中3年级', 899900, 40.0, '上架', 6, 20, 1, 5, '["高考", "冲刺", "英语"]', '<h3>课程介绍</h3><p>高考英语最后冲刺课程...</p>', '高考冲刺'),
('7', '小学数学思维训练', '培养数学思维，提升逻辑能力', '课程包', '数学', '思维课', '小学4-6年级', 399900, 24.0, '下架', 7, 0, 1, 0, '["思维", "数学", "小学"]', '<h3>课程介绍</h3><p>小学数学思维训练课程...</p>', '思维训练');

-- 为订单表添加产品ID字段（如果不存在）
-- ALTER TABLE `orders` ADD COLUMN `product_id` varchar(64) COMMENT '产品ID' AFTER `body`;
-- ALTER TABLE `orders` ADD KEY `idx_orders_product_id` (`product_id`);

-- 创建产品类型字典数据（可选）
-- INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES
-- ('产品类型', 'product_type', '0', 'admin', NOW(), '产品类型字典');

-- INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES
-- (1, '课程包', '课程包', 'product_type', '0', 'admin', NOW(), '课程包类型'),
-- (2, '单次课程', '单次课程', 'product_type', '0', 'admin', NOW(), '单次课程类型'),
-- (3, '教材', '教材', 'product_type', '0', 'admin', NOW(), '教材类型');

-- 创建学科字典数据（可选）
-- INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES
-- ('学科类型', 'subject_type', '0', 'admin', NOW(), '学科类型字典');

-- INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES
-- (1, '英语', '英语', 'subject_type', '0', 'admin', NOW(), '英语学科'),
-- (2, '数学', '数学', 'subject_type', '0', 'admin', NOW(), '数学学科'),
-- (3, '语文', '语文', 'subject_type', '0', 'admin', NOW(), '语文学科');
