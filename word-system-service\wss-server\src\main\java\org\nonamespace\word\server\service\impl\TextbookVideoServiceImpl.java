package org.nonamespace.word.server.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.utils.OssService;
import org.nonamespace.word.server.domain.TextbookItem;
import org.nonamespace.word.server.domain.Word;
import org.nonamespace.word.server.dto.TextbookVideoDto;
import org.nonamespace.word.server.service.ITextbookItemService;
import org.nonamespace.word.server.service.ITextbookVideoService;
import org.nonamespace.word.server.service.IWordService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 教材单词视频服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TextbookVideoServiceImpl implements ITextbookVideoService {

    private final ITextbookItemService textbookItemService;
    private final IWordService wordService;
    private final OssService ossService;

    @Override
    public TextbookVideoDto.VideoResp uploadVideo(TextbookVideoDto.UploadReq uploadReq) {
        // 1. 验证教材项是否存在
        TextbookItem textbookItem = textbookItemService.getById(uploadReq.getTextbookItemId());
        if (textbookItem == null) {
            throw new RuntimeException("教材项不存在");
        }

        // 2. 验证是否为单词节点
        if (textbookItem.getNodeType() != 1) { // 假设1表示单词节点
            throw new RuntimeException("只能为单词节点上传视频");
        }

        // 3. 上传视频文件
        MultipartFile videoFile = uploadReq.getVideoFile();
        if (videoFile == null || videoFile.isEmpty()) {
            throw new RuntimeException("视频文件不能为空");
        }

        // 验证文件格式
        String fileName = videoFile.getOriginalFilename();
        if (!isVideoFile(fileName)) {
            throw new RuntimeException("只支持上传视频文件格式：mp4, avi, mov, wmv");
        }

        try {
            // 构建OSS对象名称：textbook-video/教材项ID/文件名
            String objectName = "textbook-video/" + textbookItem.getId() + "/" + fileName;

            // 使用OSS服务上传视频
            String videoUrl = ossService.uploadFile(videoFile, objectName);

            // 4. 更新教材项的视频URL
            textbookItem.setVideoUrl(videoUrl);
            textbookItemService.updateById(textbookItem);

            // 5. 构建返回结果
            return buildVideoResp(textbookItem, videoUrl, fileName, videoFile.getSize());

        } catch (Exception e) {
            log.error("上传视频失败", e);
            throw new RuntimeException("上传视频失败: " + e.getMessage());
        }
    }

    @Override
    public TextbookVideoDto.VideoResp getVideoInfo(String textbookItemId) {
        TextbookItem textbookItem = textbookItemService.getById(textbookItemId);
        if (textbookItem == null) {
            throw new RuntimeException("教材项不存在");
        }

        if (!StrUtil.isNotEmpty(textbookItem.getVideoUrl())) {
            return null;
        }

        return buildVideoResp(textbookItem, textbookItem.getVideoUrl(), null, null);
    }

    @Override
    public List<TextbookVideoDto.VideoResp> getVideoList(TextbookVideoDto.QueryReq queryReq) {
        LambdaQueryWrapper<TextbookItem> wrapper = new LambdaQueryWrapper<>();
        
        // 只查询单词节点
        wrapper.eq(TextbookItem::getNodeType, 1);
        
        // 只查询有视频的项
        wrapper.isNotNull(TextbookItem::getVideoUrl);
        wrapper.ne(TextbookItem::getVideoUrl, "");

        // 添加查询条件
        if (StrUtil.isNotEmpty(queryReq.getTextbookId())) {
            wrapper.eq(TextbookItem::getTextbookId, queryReq.getTextbookId());
        }
        
        if (StrUtil.isNotEmpty(queryReq.getTextbookItemId())) {
            wrapper.eq(TextbookItem::getId, queryReq.getTextbookItemId());
        }
        
        if (StrUtil.isNotEmpty(queryReq.getWordId())) {
            wrapper.eq(TextbookItem::getWordId, queryReq.getWordId());
        }

        List<TextbookItem> textbookItems = textbookItemService.list(wrapper);
        
        return textbookItems.stream()
                .map(item -> buildVideoResp(item, item.getVideoUrl(), null, null))
                .collect(Collectors.toList());
    }

    @Override
    public boolean deleteVideo(TextbookVideoDto.DeleteReq deleteReq) {
        TextbookItem textbookItem = textbookItemService.getById(deleteReq.getTextbookItemId());
        if (textbookItem == null) {
            throw new RuntimeException("教材项不存在");
        }

        // 清空视频URL
        textbookItem.setVideoUrl(null);
        return textbookItemService.updateById(textbookItem);
    }

    @Override
    public boolean hasVideo(String textbookItemId) {
        TextbookItem textbookItem = textbookItemService.getById(textbookItemId);
        return textbookItem != null && StrUtil.isNotEmpty(textbookItem.getVideoUrl());
    }

    /**
     * 构建视频响应对象
     */
    private TextbookVideoDto.VideoResp buildVideoResp(TextbookItem textbookItem, String videoUrl, 
                                                      String fileName, Long fileSize) {
        TextbookVideoDto.VideoResp resp = new TextbookVideoDto.VideoResp();
        resp.setTextbookItemId(textbookItem.getId());
        resp.setWordId(textbookItem.getWordId());
        resp.setVideoUrl(videoUrl);
        resp.setVideoFileName(fileName);
        resp.setFileSize(fileSize);
        resp.setUploadTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        // 获取单词信息
        if (StrUtil.isNotEmpty(textbookItem.getWordId())) {
            Word word = wordService.getById(textbookItem.getWordId());
            if (word != null) {
                resp.setWord(word.getWord());
            }
        }

        return resp;
    }

    /**
     * 验证是否为视频文件
     */
    private boolean isVideoFile(String fileName) {
        if (!StrUtil.isNotEmpty(fileName)) {
            return false;
        }
        
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        return "mp4".equals(extension) || "avi".equals(extension) || 
               "mov".equals(extension) || "wmv".equals(extension);
    }
}
