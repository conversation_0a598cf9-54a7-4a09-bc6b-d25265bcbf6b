package org.nonamespace.word.server.enums;

/**
 * 复习计划状态枚举类
 * <p>
 * 该枚举类用于表示复习计划的不同状态，包括待开始、进行中、已完成和已跳过。
 * </p>
 */
public enum StudentWordProgressStatusEnum {
    //待开始, 进行中, 已完成, 已跳过
    MASTER("掌握"),
    MISTAKE("错误");
    private final String value;
    StudentWordProgressStatusEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static StudentWordProgressStatusEnum getByDefault(String value, StudentWordProgressStatusEnum defaultValue) {
        for (StudentWordProgressStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return defaultValue;
    }

}
