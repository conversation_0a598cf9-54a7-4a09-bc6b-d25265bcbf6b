package org.nonamespace.word.server.mapper.order;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseMapper;
import org.apache.ibatis.annotations.Param;
import org.nonamespace.word.server.domain.order.Orders;
import org.nonamespace.word.server.dto.order.OrderPageDto;

import java.util.Date;

/**
 * 订单表Mapper接口
 * 
 * <AUTHOR>
 */
public interface OrdersMapper extends MPJBaseMapper<Orders> {

    Page<OrderPageDto.Resp> selectOrdersByParam(Page<OrderPageDto.Resp> pageParam, OrderPageDto.Req req);


}