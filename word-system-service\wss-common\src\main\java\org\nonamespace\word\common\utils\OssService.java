package org.nonamespace.word.common.utils;

import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectResult;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.config.AliyunOssConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;
import java.util.UUID;

/**
 * 阿里云OSS工具类
 */
@Slf4j
@Component
public class OssService {

    @Autowired
    private OSS ossClient;

    /**
     *  获取OSS配置
     *
     * @return OSS配置
     */
    @Resource
    private AliyunOssConfig ossConfig;

    /**
     *
     * @param file
     * @param objectName
     * @return
     */
    public String uploadFile(MultipartFile file, String objectName) {
        try {
            // 上传文件
            PutObjectResult result = ossClient.putObject(
                    ossConfig.getBucketName(),
                    objectName,
                    file.getInputStream()
            );

            log.info("文件上传成功，ETag: {}", result.getETag());

            // 返回文件访问URL
            return ossConfig.getUrlPrefix() + objectName;
        } catch (OSSException oe) {
            log.error("OSS服务异常: {}", oe.getMessage(), oe);
            throw new RuntimeException("OSS服务异常: " + oe.getMessage());
        } catch (Exception e) {
            log.error("文件上传失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传字节数组到OSS
     *
     * @param data     字节数组
     * @param objectName 文件名
     * @return 文件访问URL
     */
    public String uploadBytes(byte[] data, String objectName) {
       return uploadBytes(data, objectName, null);
    }

    /**
     * 上传字节数组到OSS
     *
     * @param data     字节数组
     * @param objectName 文件名
     * @return 文件访问URL
     */
    public String uploadBytes(byte[] data, String objectName, ObjectMetadata metadata) {
        try {
            // 上传字节数组
            PutObjectResult result = metadata == null ? ossClient.putObject(
                    ossConfig.getBucketName(),
                    objectName,
                    new ByteArrayInputStream(data)
            ) : ossClient.putObject(
                    ossConfig.getBucketName(),
                    objectName,
                    new ByteArrayInputStream(data),
                    metadata
            );

            log.info("字节数组上传成功，ETag: {}", result.getETag());

            // 返回文件访问URL
            return ossConfig.getUrlPrefix() + objectName;
        } catch (OSSException oe) {
            log.error("OSS服务异常: {}", oe.getMessage(), oe);
            throw new RuntimeException("OSS服务异常: " + oe.getMessage());
        } catch (Exception e) {
            log.error("字节数组上传失败: {}", e.getMessage(), e);
            throw new RuntimeException("字节数组上传失败: " + e.getMessage());
        }
    }

    /**
     * 下载文件到本地
     *
     * @param objectName    OSS对象名称
     * @param localFilePath 本地文件路径
     * @return 是否下载成功
     */
    public boolean downloadFile(String objectName, String localFilePath) {
        try {
            // 确保目标目录存在
            Path parentDir = Paths.get(localFilePath).getParent();
            if (parentDir != null) {
                Files.createDirectories(parentDir);
            }

            // 下载文件
            ossClient.getObject(new GetObjectRequest(ossConfig.getBucketName(), objectName), new File(localFilePath));

            log.info("文件下载成功，保存路径: {}", localFilePath);
            return true;
        } catch (OSSException oe) {
            log.error("OSS服务异常: {}", oe.getMessage(), oe);
            return false;
        } catch (IOException e) {
            log.error("文件保存失败: {}", e.getMessage(), e);
            return false;
        } catch (Exception e) {
            log.error("文件下载失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取文件流
     *
     * @param objectName OSS对象名称
     * @return 文件输入流
     */
    public InputStream getFileStream(String objectName) {
        try {
            OSSObject ossObject = ossClient.getObject(ossConfig.getBucketName(), objectName);
            return ossObject.getObjectContent();
        } catch (OSSException oe) {
            log.error("OSS服务异常: {}", oe.getMessage(), oe);
            throw new RuntimeException("OSS服务异常: " + oe.getMessage());
        } catch (Exception e) {
            log.error("获取文件流失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取文件流失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件
     *
     * @param objectName OSS对象名称
     * @return 是否删除成功
     */

    public boolean deleteFile(String objectName) {
        try {
            ossClient.deleteObject(ossConfig.getBucketName(), objectName);
            log.info("文件删除成功: {}", objectName);
            return true;
        } catch (OSSException oe) {
            log.error("OSS服务异常: {}", oe.getMessage(), oe);
            return false;
        } catch (Exception e) {
            log.error("文件删除失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取文件上传签名URL
     *
     * @param dir      存储目录
     * @param fileName 文件名
     * @param expiration 过期时间（毫秒）
     * @return 上传签名URL
     */
    public String generateUploadSignedUrl(String dir, String fileName, long expiration) {
        try {
            // 生成唯一文件名
            String uniqueFileName = generateUniqueFileName(fileName);

            // 构建OSS文件路径
            String objectName = buildObjectName(dir, uniqueFileName);

            // 设置过期时彭间
            Date expirationDate = new Date(System.currentTimeMillis() + expiration);

            // 生成上传签名URL91
            URL signedUrl = ossClient.generatePresignedUrl(
                    ossConfig.getBucketName(),
                    objectName,
                    expirationDate,
                    HttpMethod.PUT
            );

            log.info("生成上传签名URL成功: {}", signedUrl.toString());

            // 返回签名URL和对象名称
            return signedUrl.toString() + "," + objectName;
        } catch (OSSException oe) {
            log.error("OSS服务异常: {}", oe.getMessage(), oe);
            throw new RuntimeException("OSS服务异常: " + oe.getMessage());
        } catch (Exception e) {
            log.error("生成上传签名URL失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成上传签名URL失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件下载签名URL
     *
     * @param objectName OSS对象名称
     * @param expiration 过期时间（毫秒）
     * @return 下载签名URL
     */
    public String generateDownloadSignedUrl(String objectName, long expiration) {
        try {
            // 设置过期时间
            Date expirationDate = new Date(System.currentTimeMillis() + expiration);

            // 生成下载签名URL
            URL signedUrl = ossClient.generatePresignedUrl(
                    ossConfig.getBucketName(),
                    objectName,
                    expirationDate,
                    HttpMethod.GET
            );

            log.info("生成下载签名URL成功: {}", signedUrl.toString());

            return signedUrl.toString();
        } catch (OSSException oe) {
            log.error("OSS服务异常: {}", oe.getMessage(), oe);
            throw new RuntimeException("OSS服务异常: " + oe.getMessage());
        } catch (Exception e) {
            log.error("生成下载签名URL失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成下载签名URL失败: " + e.getMessage());
        }
    }

    /**
     * 生成唯一文件名
     *
     * @param originalFileName 原始文件名
     * @return 唯一文件名
     */
    private String generateUniqueFileName(String originalFileName) {
        // 获取文件扩展名
        String extension = "";
        if (originalFileName.contains(".")) {
            extension = originalFileName.substring(originalFileName.lastIndexOf("."));
        }
        
        // 使用UUID生成唯一文件名
        return UUID.randomUUID().toString().replace("-", "") + extension;
    }

    /**
     * 构建OSS对象名称
     *
     * @param dir      存储目录
     * @param fileName 文件名
     * @return OSS对象名称
     */
    private String buildObjectName(String dir, String fileName) {
        if (dir == null || dir.trim().isEmpty()) {
            return fileName;
        }
        
        // 确保目录以/结尾
        if (!dir.endsWith("/")) {
            dir = dir + "/";
        }
        
        // 确保目录不以/开头
        if (dir.startsWith("/")) {
            dir = dir.substring(1);
        }
        
        return dir + fileName;
    }

    public static ObjectMetadata newFilenameMetadata(String fileName) {
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentDisposition("attachment; filename=\"" + fileName + "\"");
        metadata.setCacheControl("no-cache");
        metadata.setContentType("application/octet-stream");
        return metadata;
    }
}