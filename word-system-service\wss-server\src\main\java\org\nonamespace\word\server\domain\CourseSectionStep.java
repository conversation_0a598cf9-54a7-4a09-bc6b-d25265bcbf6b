package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ruoyi.common.annotation.Excel;
import org.nonamespace.word.server.entity.DataEntity;
import org.nonamespace.word.server.misc.mybatis.ListStringTypeHandler;

import java.util.Date;
import java.util.List;

/**
 * 课程学习步骤详细对象 course_section_step
 * 
 * <AUTHOR>
 * @date 2025-05-20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "course_section_step", autoResultMap = true)
public class CourseSectionStep extends DataEntity {

    /** 课程ID */
    @Excel(name = "课程ID")
    private String courseId;

    /** 环节ID */
    @Excel(name = "环节ID")
    private String sectionId;

    @Excel(name = "词表ID")
    private String textbookId;

    @Excel(name = "词表项ID")
    private String textbookItemId;

    /** 单词ID */
    @Excel(name = "单词ID")
    private String wordId;

    /** 类型 (例如: 单词测试、单词讲解、句子翻译、句子翻译讲解、视频讲解、句子排序) */
    @Excel(name = "类型 (例如: 单词测试、单词讲解、句子翻译、句子翻译讲解、视频讲解、句子排序)")
    private String type;

    /** 该步骤在当前单词学习流程中的顺序号 */
    @Excel(name = "该步骤在当前单词学习流程中的顺序号")
    private Long orderIndex;

    /** 状态 (待开始, 进行中, 已完成, 跳过) */
    @Excel(name = "状态 (待开始, 进行中, 已完成, 跳过)")
    private String status;

    /** 结果 (正确, 错误) */
    @Excel(name = "结果 (正确, 错误)")
    private String result;

    /** 单词文本 */
    @Excel(name = "单词文本")
    private String word;

    /** 句子文本 (例如填空题的句子) */
    @Excel(name = "句子文本 (例如填空题的句子)")
    private String sentence;

    /** 打乱的词组列表 (例如句子排序题的打乱词组) */
    @Excel(name = "打乱的词组列表 (例如句子排序题的打乱词组)")
    @TableField(typeHandler = ListStringTypeHandler.class)
    private List<String> ordering;

    /** 步骤的选项或打乱项 (例如选择题选项列表或句子排序的打乱词组) */
    @Excel(name = "步骤的选项或打乱项 (例如选择题选项列表或句子排序的打乱词组)")
    @TableField(typeHandler = ListStringTypeHandler.class)
    private List<String> optionsData;

    /** 正确答案 (例如选择题的选项) */
    @Excel(name = "正确答案 (例如选择题的选项)")
    private String answer;

    /** 学生答案(选项) */
    @Excel(name = "学生答案(选项)")
    private String studentAnswer;

    /** 步骤开始时间 */
    @Excel(name = "步骤开始时间")
    private Date startTime;

    /** 步骤结束时间 */ 
    @Excel(name = "步骤结束时间")
    private Date endTime;
}
