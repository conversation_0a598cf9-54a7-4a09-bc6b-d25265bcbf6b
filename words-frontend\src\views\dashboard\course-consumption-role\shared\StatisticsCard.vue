<template>
  <el-card class="statistics-card" shadow="hover">
    <div class="card-header">
      <div class="card-icon" :style="{ backgroundColor: iconColor }">
        <el-icon>
          <component :is="icon" />
        </el-icon>
      </div>
      <div class="card-title">
        <h3>{{ title }}</h3>
      </div>
    </div>
    
    <div class="card-content">
      <!-- 单值显示模式 -->
      <div v-if="!multiValues" class="main-value">
        {{ formatValue(value) }}
        <span v-if="unit" class="unit">{{ unit }}</span>
      </div>

      <!-- 多值显示模式 -->
      <div v-else class="multi-values">
        <div v-for="(item, index) in multiValues" :key="index" class="value-item">
          <div class="value-label">{{ item.label }}</div>
          <div class="value-number">
            {{ formatValue(item.value) }}
            <span v-if="item.unit" class="value-unit">{{ item.unit }}</span>
          </div>
        </div>
      </div>

      <div v-if="trend" class="trend-info">
        <el-icon :class="trendClass">
          <ArrowUp v-if="trend > 0" />
          <ArrowDown v-if="trend < 0" />
          <Minus v-if="trend === 0" />
        </el-icon>
        <span :class="trendClass">{{ Math.abs(trend) }}%</span>
        <span class="trend-text">{{ trendText }}</span>
      </div>

      <div v-if="extraInfo" class="extra-info">
        <span v-for="(info, index) in extraInfo" :key="index" class="info-item">
          <span class="info-label">{{ info.label }}:</span>
          <span class="info-value">{{ formatValue(info.value) }}{{ info.unit || '' }}</span>
          <span v-if="index < extraInfo.length - 1" class="info-separator">|</span>
        </span>
      </div>
    </div>
  </el-card>
</template>

<script>
export default {
  name: 'StatisticsCard',
  props: {
    title: {
      type: String,
      required: true
    },
    subtitle: {
      type: String,
      default: ''
    },
    value: {
      type: [Number, String],
      default: 0
    },
    unit: {
      type: String,
      default: ''
    },
    multiValues: {
      type: Array,
      default: null
    },
    icon: {
      type: [String, Object],
      required: true
    },
    iconColor: {
      type: String,
      default: '#409EFF'
    },
    trend: {
      type: Number,
      default: null
    },
    trendText: {
      type: String,
      default: '较上期'
    },
    extraInfo: {
      type: Array,
      default: () => []
    },
    precision: {
      type: Number,
      default: 2
    }
  },
  computed: {
    trendClass() {
      if (this.trend > 0) return 'trend-up'
      if (this.trend < 0) return 'trend-down'
      return 'trend-flat'
    }
  },
  methods: {
    formatValue(value) {
      if (value === null || value === undefined) return '0'
      
      const num = Number(value)
      if (isNaN(num)) return value
      
      // 如果是整数，不显示小数点
      if (num % 1 === 0) {
        return num.toLocaleString()
      }
      
      // 如果是小数，根据precision显示
      return num.toFixed(this.precision)
    }
  }
}
</script>

<style scoped>
.statistics-card {
  min-height: 140px;
  border: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  overflow: visible;
}

.statistics-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.statistics-card :deep(.el-card__body) {
  padding: 16px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: white;
  font-size: 20px;
}

.card-title h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 1.2;
}

.card-subtitle {
  margin: 4px 0 0 0;
  font-size: 12px;
  color: #909399;
  line-height: 1;
}

.card-content {
  flex: 1;
}

.main-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 8px;
}

.unit {
  font-size: 14px;
  font-weight: 400;
  color: #909399;
  margin-left: 4px;
}

.multi-values {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 12px;
  margin-bottom: 8px;
}

.value-item {
  text-align: center;
}

.value-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
  line-height: 1.2;
}

.value-number {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.value-unit {
  font-size: 10px;
  font-weight: 400;
  color: #909399;
  margin-left: 2px;
}

.trend-info {
  display: flex;
  align-items: center;
  font-size: 12px;
  margin-bottom: 8px;
}

.trend-up {
  color: #67C23A;
}

.trend-down {
  color: #F56C6C;
}

.trend-flat {
  color: #909399;
}

.trend-text {
  color: #909399;
  margin-left: 4px;
}

.extra-info {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: auto;
  padding-top: 8px;
  font-size: 12px;
  line-height: 1.4;
}

.info-item {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.info-label {
  color: #909399;
}

.info-value {
  color: #606266;
  font-weight: 500;
}

.info-separator {
  color: #DCDFE6;
  margin: 0 4px;
}
</style>
