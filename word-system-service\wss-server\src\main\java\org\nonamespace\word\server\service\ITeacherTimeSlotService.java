package org.nonamespace.word.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.nonamespace.word.server.domain.TeacherTimeSlot;
import org.nonamespace.word.server.dto.management.teachinggroup.TeacherDto;

import java.util.List;

/**
 * 教师时间表Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface ITeacherTimeSlotService extends IService<TeacherTimeSlot> {

    /**
     * 查询教师的时间表
     *
     * @param teacherId 教师ID
     * @return 时间表列表
     */
    List<TeacherDto.TimeSlotResp> selectByTeacherId(String teacherId);

    /**
     * 批量更新教师时间表
     *
     * @param teacherId 教师ID
     * @param timeSlots 时间表列表
     * @return 影响行数
     */
    int updateBatchByTeacherId(String teacherId, List<TeacherDto.TimeSlotResp> timeSlots);

    /**
     * 删除教师的所有时间表
     *
     * @param teacherId 教师ID
     * @return 影响行数
     */
    int deleteByTeacherId(String teacherId);

    /**
     * 批量插入教师时间表
     *
     * @param timeSlots 时间表列表
     * @return 影响行数
     */
    int insertBatch(List<TeacherTimeSlot> timeSlots);

    /**
     * 批量查询教师时间段
     *
     * @param teacherIds 教师ID列表
     * @return 时间段列表
     */
    List<TeacherDto.TimeSlotResp> selectByTeacherIds(List<String> teacherIds);

    /**
     * 更新教师时间表（完整替换）
     *
     * @param req 更新请求
     * @return 是否成功
     */
    boolean updateTeacherTimeSlots(TeacherDto.UpdateTimeSlotsReq req);

    /**
     * 获取教师可用时间段
     *
     * @param teacherId 教师ID
     * @return 可用时间段列表
     */
    List<TeacherDto.AvailableTimeSlotsDto> getAvailableTimeSlots(String teacherId);
}
