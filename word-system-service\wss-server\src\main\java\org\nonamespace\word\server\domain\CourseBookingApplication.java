package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nonamespace.word.server.entity.DataEntity;
import org.nonamespace.word.server.misc.mybatis.ListStringTypeHandler;

import java.util.Date;
import java.util.List;

/**
 * 预约课申请实体类
 * 
 * <AUTHOR>
 * @date 2025-06-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "course_booking_application", autoResultMap = true)
public class CourseBookingApplication extends DataEntity {

    /**
     * 学生ID
     */
    @TableField("student_id")
    private String studentId;

    /**
     * 申请销售ID
     */
    @TableField("sales_id")
    private String salesId;

    /**
     * 销售组ID
     */
    @TableField("sales_group_id")
    private String salesGroupId;

    /**
     * 学科
     */
    @TableField("subject")
    private String subject;

    /**
     * 课型
     */
    @TableField("specification")
    private String specification;

    /**
     * 性质
     */
    @TableField(value = "nature", exist = false)
    private String nature;

    /**
     * 年级
     */
    @TableField(value = "grade", exist = false)
    private String grade;

//    /**
//     * 候选老师列表(JSON)
//     */
//    @TableField(value = "teacher_candidates", exist = false)
//    private String teacherCandidates;

//    /**
//     * 教学组ID
//     */
//    @TableField("teaching_group_id")
//    private String teachingGroupId;

    /**
     * 首选时间段(JSON格式)
     */
    @TableField(value = "preferred_time_slots", typeHandler = JacksonTypeHandler.class)
    private List<PreferredTimeSlot> preferredTimeSlots;

//    /**
//     * 申请原因
//     */
//    @TableField(value = "apply_reason", exist = false)
//    private String applyReason;

    /**
     * 状态(pending/confirmed/rejected)
     */
    @TableField("status")
    private String status;

//    /**
//     * 申请时间
//     */
//    @TableField(value = "apply_time", exist = false)
//    private Date applyTime;
//
//    /**
//     * 最后催促时间
//     */
//    @TableField(value = "last_remind_time", exist = false)
//    private Date lastRemindTime;
//
//    /**
//     * 催促次数
//     */
//    @TableField(value = "remind_count", exist = false)
//    private Integer remindCount;
//
//    /**
//     * 确认的老师ID
//     */
//    @TableField(value = "confirm_teacher_id", exist = false)
//    private String confirmTeacherId;
//
//    /**
//     * 确认时间
//     */
//    @TableField(value = "confirm_time", exist = false)
//    private Date confirmTime;
//
//    /**
//     * 确认人ID(教学组长)
//     */
//    @TableField(value = "confirm_by", exist = false)
//    private String confirmBy;
//
//    /**
//     * 自动分配的课时包ID
//     */
//    @TableField(value = "auto_assigned_course_hour_id", exist = false)
//    private String autoAssignedCourseHourId;

    /**
     * 候选老师列表 (JSON格式)
     */
    @TableField(value = "preferred_teachers", typeHandler = ListStringTypeHandler.class)
    private List<String> preferredTeachers;

    @TableField(value = "preferred_teaching_group_ids", typeHandler = ListStringTypeHandler.class)
    private List<String> preferredTeachingGroupIds;

    /**
     * 申请说明
     */
    @TableField("application_reason")
    private String applicationReason;

    /**
     * 确认的老师ID
     */
    @TableField("approved_teacher_id")
    private String approvedTeacherId;


    @TableField("approved_teaching_group_id")
    private String approvedTeachingGroupId;

    @TableField("approved_time_slot")
    private String approvedTimeSlot; // 确认的时间段 (JSON字符串)

    /**
     * 审批人ID
     */
    @TableField("approval_by")
    private String approvalBy;

    /**
     * 审批时间
     */
    @TableField("approval_time")
    private Date approvalTime;

    /**
     * 拒绝原因
     */
    @TableField("rejection_reason")
    private String rejectionReason;

    /**
     * 课时包ID
     */
    @TableField("course_hours_package_id")
    private String courseHoursPackageId;

    /**
     * 试听课ID
     */
    @TableField("trial_course_id")
    private String trialCourseId;

    /**
     * 试听课日期
     */
    @TableField("trial_class_date")
    private Date trialClassDate;

    /**
     * 试听课开始时间
     */
    @TableField("trial_class_start_time")
    private java.sql.Time trialClassStartTime;

    /**
     * 试听课结束时间
     */
    @TableField("trial_class_end_time")
    private java.sql.Time trialClassEndTime;

    /**
     * 首选时间段内部类
     */
    @Data
    public static class PreferredTimeSlot {
        private Integer weekday; // 星期几 (1-7, 1=周一, 7=周日)
        private String startTime; // 开始时间 (HH:mm格式)
        private String endTime; // 结束时间 (HH:mm格式)
        private Integer priority; // 优先级 (1-3, 1=最优先)
    }

    /**
     * 试听课时间内部类
     */
    @Data
    public static class TrialClassTime {
        private Date date;        // 试听课日期
        private String startTime; // 开始时间 (HH:mm格式)
        private String endTime;   // 结束时间 (HH:mm格式)
    }

    /**
     * 申请状态枚举（现在直接使用中文作为code）
     */
    public enum Status {
        PENDING("待审核", "待审核"),
        APPROVED("已通过", "已通过"),
        REJECTED("已拒绝", "已拒绝"),
        CANCELLED("已取消", "已取消"),
        WITHDRAWN("已撤回", "已撤回"),
        VOIDED("已作废", "已作废");

        private final String code;
        private final String description;

        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static Status fromCode(String code) {
            for (Status status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            // 兼容旧的英文状态
            switch (code) {
                case "pending": return PENDING;
                case "approved":
                case "confirmed": return APPROVED;
                case "rejected": return REJECTED;
                case "cancelled": return CANCELLED;
                case "voided": return VOIDED;
                default:
                    throw new IllegalArgumentException("Unknown status code: " + code);
            }
        }
    }
}
