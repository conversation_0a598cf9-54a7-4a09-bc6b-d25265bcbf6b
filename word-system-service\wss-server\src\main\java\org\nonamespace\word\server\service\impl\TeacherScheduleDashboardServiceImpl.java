package org.nonamespace.word.server.service.impl;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.domain.Course;
import org.nonamespace.word.server.domain.TeacherProfile;
import org.nonamespace.word.server.domain.TeachingGroup;
import org.nonamespace.word.server.domain.TeachingGroupMember;
import org.nonamespace.word.server.dto.management.teacherschedule.TeacherScheduleDashboardDto;
import org.nonamespace.word.server.dto.management.teachinggroup.TeacherDto;
import org.nonamespace.word.server.service.*;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 老师可排课时间看板服务实现类
 * 
 * <AUTHOR>
 * @since 2025-06-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TeacherScheduleDashboardServiceImpl implements ITeacherScheduleDashboardService {

    private final ITeacherManagementService teacherManagementService;
    private final ICourseService courseService;
    private final ITeachingGroupMemberService teachingGroupMemberService;
    private final ITeachingGroupService teachingGroupService;
    private final ITeacherProfileService teacherProfileService;

    @Override
    public TeacherScheduleDashboardDto.DashboardResp getDashboard(TeacherScheduleDashboardDto.QueryReq request) {
        log.info("获取老师可排课时间看板: startDate={}, endDate={}, groupIds={}", 
            request.getStartDate(), request.getEndDate(), request.getGroupIds());

        // 验证日期范围
        if (!validateDateRange(request.getStartDate(), request.getEndDate())) {
            throw new IllegalArgumentException("日期范围无效：不能超过31天，且开始日期不能早于今天");
        }

        LocalDate startDate = LocalDate.parse(request.getStartDate());
        LocalDate endDate = LocalDate.parse(request.getEndDate());

        // 获取教师数据
        List<TeacherDto.BasicResp> teachers = getTeachers(request.getGroupIds());
        if (teachers.isEmpty()) {
            return createEmptyDashboard(request.getStartDate(), request.getEndDate());
        }

        // 批量获取教师时间段和已排课程
        Map<String, List<TeacherDto.TimeSlotResp>> teacherTimeSlotsMap = getTeacherTimeSlots(teachers);
        Map<String, List<Course>> teacherScheduledCoursesMap = getTeacherScheduledCourses(teachers, startDate, endDate);

        // 计算每个教师每天的可排课课次
        Map<String, Map<LocalDate, Integer>> teacherDailySlotsMap = calculateTeacherDailySlots(
            teachers, teacherTimeSlotsMap, teacherScheduledCoursesMap, startDate, endDate);

        // 构建响应数据
        return buildDashboardResponse(request, teachers, teacherDailySlotsMap, startDate, endDate);
    }

    @Override
    public Map<String, String> getQuickDateRange(TeacherScheduleDashboardDto.QuickDateRange quickRange) {
        LocalDate today = LocalDate.now();
        LocalDate startDate;
        LocalDate endDate;

        switch (quickRange) {
            case TODAY:
                startDate = today;
                endDate = today;
                break;
            case TOMORROW:
                startDate = today.plusDays(1);
                endDate = startDate;
                break;
            case THIS_WEEK:
                startDate = today.with(TemporalAdjusters.nextOrSame(DayOfWeek.MONDAY));
                endDate = startDate.plusDays(6);
                break;
            case NEXT_WEEK:
                startDate = today.with(TemporalAdjusters.next(DayOfWeek.MONDAY));
                endDate = startDate.plusDays(6);
                break;
            case THIS_MONTH:
                startDate = today.with(TemporalAdjusters.firstDayOfMonth());
                if (startDate.isBefore(today)) {
                    startDate = today;
                }
                endDate = today.with(TemporalAdjusters.lastDayOfMonth());
                break;
            case NEXT_MONTH:
                startDate = today.plusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
                endDate = startDate.with(TemporalAdjusters.lastDayOfMonth());
                break;
            default:
                throw new IllegalArgumentException("不支持的快捷日期范围: " + quickRange);
        }

        Map<String, String> result = new HashMap<>();
        result.put("startDate", startDate.toString());
        result.put("endDate", endDate.toString());
        return result;
    }

    @Override
    public Integer calculateTeacherAvailableSlots(String teacherId, LocalDate date) {
        // 获取教师时间段
        List<TeacherDto.TimeSlotResp> timeSlots = teacherManagementService.getTeacherTimeSlots(teacherId);
        if (timeSlots.isEmpty()) {
            return 0;
        }

        // 获取当天已排课程
        List<Course> scheduledCourses = courseService.lambdaQuery()
                // 查出除content字段外的所有字段
                .select(Course.class, c -> !c.getColumn().equals("content"))
                .eq(Course::getTeacherId, teacherId)
                .eq(Course::getDeleted, false)
                .in(Course::getCourseStatus, Arrays.asList("待开始", "进行中"))
                .apply("DATE(scheduled_start_time) = {0}", date.toString())
                .list();

        return calculateAvailableSlotsForDate(timeSlots, scheduledCourses, date);
    }

    @Override
    public boolean validateDateRange(String startDate, String endDate) {
        try {
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);
            LocalDate today = LocalDate.now();

            // 开始日期不能早于今天
            if (start.isBefore(today)) {
                return false;
            }

            // 结束日期不能早于开始日期
            if (end.isBefore(start)) {
                return false;
            }

            // 日期范围不能超过31天
            long daysBetween = ChronoUnit.DAYS.between(start, end) + 1;
            return daysBetween <= 31;

        } catch (Exception e) {
            log.warn("日期格式错误: startDate={}, endDate={}", startDate, endDate, e);
            return false;
        }
    }

    /**
     * 查询已加入教学组且状态为active的教师列表（专用于可排课资源统计）
     *
     * @param groupIds 教学组ID列表，如果为空则查询所有教学组的教师
     * @return 符合条件的教师列表
     */
    private List<TeacherDto.BasicResp> getActiveTeachersInGroups(List<String> groupIds) {
        log.info("查询已加入教学组且状态为active的教师列表: groupIds={}", groupIds);

        // 构建关联查询
        MPJLambdaWrapper<TeacherProfile> wrapper = new MPJLambdaWrapper<TeacherProfile>()
                // 选择教师基本信息字段
                .select(TeacherProfile::getTeacherId, TeacherProfile::getRealName, TeacherProfile::getNickName,
                        TeacherProfile::getPhonenumber, TeacherProfile::getAvatar)
                // 内连接教学组成员表，确保只查询已加入教学组的教师
                .innerJoin(TeachingGroupMember.class, TeachingGroupMember::getTeacherId, TeacherProfile::getTeacherId,
                        ext -> ext.eq(TeachingGroupMember::getDeleted, false)
                                .eq(TeachingGroupMember::getStatus, "active"))
                // 基础筛选条件
                .eq(TeacherProfile::getDeleted, false);
                // 注意：根据新需求，不对teacher_profile.status进行过滤

        // 如果指定了教学组，添加教学组过滤条件
        if (groupIds != null && !groupIds.isEmpty()) {
            wrapper.in(TeachingGroupMember::getGroupId, groupIds);
        }

        // 执行查询
        List<TeacherProfile> teachers = ((org.nonamespace.word.server.mapper.TeacherProfileMapper) teacherProfileService.getBaseMapper())
                .selectJoinList(TeacherProfile.class, wrapper);

        log.info("查询到符合条件的教师数量: {}", teachers.size());

        // 转换为响应对象
        return teachers.stream().map(tp -> {
            TeacherDto.BasicResp basicResp = new TeacherDto.BasicResp();
            basicResp.setId(String.valueOf(tp.getTeacherId()));
            basicResp.setName(tp.getRealName() != null ? tp.getRealName() : tp.getNickName());
            basicResp.setPhone(tp.getPhonenumber());
            basicResp.setEmail(""); // 暂时为空
            basicResp.setAvatar(tp.getAvatar());
            return basicResp;
        }).collect(Collectors.toList());
    }

    /**
     * 获取教师列表（使用新的专用查询方法）
     */
    private List<TeacherDto.BasicResp> getTeachers(List<String> groupIds) {
        // 使用专用的查询方法，仅统计已加入教学组且状态为active的教师
        return getActiveTeachersInGroups(groupIds);
    }



    /**
     * 批量获取教师时间段
     */
    private Map<String, List<TeacherDto.TimeSlotResp>> getTeacherTimeSlots(List<TeacherDto.BasicResp> teachers) {
        List<String> teacherIds = teachers.stream()
            .map(TeacherDto.BasicResp::getId)
            .collect(Collectors.toList());

        return teacherManagementService.getTeacherTimeSlotsMap(teacherIds);
    }

    /**
     * 批量获取教师已排课程
     */
    private Map<String, List<Course>> getTeacherScheduledCourses(
            List<TeacherDto.BasicResp> teachers, LocalDate startDate, LocalDate endDate) {

        List<String> teacherIds = teachers.stream()
                .map(TeacherDto.BasicResp::getId)
                .collect(Collectors.toList());

        List<Course> allCourses = courseService.lambdaQuery()
                // 查出除content字段外的所有字段
                .select(Course.class, c -> !c.getColumn().equals("content"))
                .in(Course::getTeacherId, teacherIds)
                .eq(Course::getDeleted, false)
                .in(Course::getCourseStatus, Arrays.asList("待开始", "进行中"))
                .ge(Course::getScheduledStartTime, startDate.atStartOfDay())
                .le(Course::getScheduledStartTime, endDate.atTime(23, 59, 59))
                .list();

        return allCourses.stream()
                .collect(Collectors.groupingBy(Course::getTeacherId));
    }

    /**
     * 计算每个教师每天的可排课课次
     */
    private Map<String, Map<LocalDate, Integer>> calculateTeacherDailySlots(
            List<TeacherDto.BasicResp> teachers,
            Map<String, List<TeacherDto.TimeSlotResp>> teacherTimeSlotsMap,
            Map<String, List<Course>> teacherScheduledCoursesMap,
            LocalDate startDate, LocalDate endDate) {

        Map<String, Map<LocalDate, Integer>> result = new HashMap<>();

        for (TeacherDto.BasicResp teacher : teachers) {
            String teacherId = teacher.getId();
            Map<LocalDate, Integer> dailySlots = new HashMap<>();

            List<TeacherDto.TimeSlotResp> timeSlots = teacherTimeSlotsMap.getOrDefault(teacherId, new ArrayList<>());
            List<Course> scheduledCourses = teacherScheduledCoursesMap.getOrDefault(teacherId, new ArrayList<>());

            // 计算每一天的可排课课次
            LocalDate currentDate = startDate;
            while (!currentDate.isAfter(endDate)) {
                Integer slots = calculateAvailableSlotsForDate(timeSlots, scheduledCourses, currentDate);
                dailySlots.put(currentDate, slots);
                currentDate = currentDate.plusDays(1);
            }

            result.put(teacherId, dailySlots);
        }

        return result;
    }

    /**
     * 计算指定日期的可排课课次
     */
    private Integer calculateAvailableSlotsForDate(
            List<TeacherDto.TimeSlotResp> timeSlots, 
            List<Course> scheduledCourses, 
            LocalDate date) {

        int weekday = date.getDayOfWeek().getValue(); // 1=周一, 7=周日

        // 获取当天的可用时间段
        List<TeacherDto.TimeSlotResp> dayTimeSlots = timeSlots.stream()
            .filter(slot -> slot.getWeekday() == weekday && "available".equals(slot.getStatus()))
            .collect(Collectors.toList());

        if (dayTimeSlots.isEmpty()) {
            return 0;
        }

        // 获取当天的已排课程
        List<Course> dayCourses = scheduledCourses.stream()
            .filter(course -> {
                LocalDate courseDate = course.getScheduledStartTime().toInstant()
                    .atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                return courseDate.equals(date);
            })
            .collect(Collectors.toList());

        // 计算总的可用分钟数
        int totalAvailableMinutes = 0;

        for (TeacherDto.TimeSlotResp timeSlot : dayTimeSlots) {
            int slotMinutes = calculateTimeSlotMinutes(timeSlot);
            
            // 减去已排课程占用的时间
            for (Course course : dayCourses) {
                int occupiedMinutes = calculateOccupiedMinutes(timeSlot, course);
                slotMinutes -= occupiedMinutes;
            }

            totalAvailableMinutes += Math.max(0, slotMinutes);
        }

        // 转换为课次：每60分钟算1课次
        return totalAvailableMinutes / 60;
    }

    /**
     * 计算时间段的分钟数
     */
    private int calculateTimeSlotMinutes(TeacherDto.TimeSlotResp timeSlot) {
        try {
            String[] startParts = timeSlot.getStartTime().split(":");
            String[] endParts = timeSlot.getEndTime().split(":");
            
            int startMinutes = Integer.parseInt(startParts[0]) * 60 + Integer.parseInt(startParts[1]);
            int endMinutes = Integer.parseInt(endParts[0]) * 60 + Integer.parseInt(endParts[1]);
            
            return endMinutes - startMinutes;
        } catch (Exception e) {
            log.warn("时间格式错误: startTime={}, endTime={}", timeSlot.getStartTime(), timeSlot.getEndTime(), e);
            return 0;
        }
    }

    /**
     * 计算课程在时间段中占用的分钟数
     */
    private int calculateOccupiedMinutes(TeacherDto.TimeSlotResp timeSlot, Course course) {
        // 这里需要实现时间段重叠计算逻辑
        // 简化实现：如果课程时间与时间段有重叠，则按课程时长计算
        Long durationMinutes = course.getDurationMinutes();
        return durationMinutes != null ? durationMinutes.intValue() : 0;
    }

    /**
     * 构建看板响应数据
     */
    private TeacherScheduleDashboardDto.DashboardResp buildDashboardResponse(
            TeacherScheduleDashboardDto.QueryReq request,
            List<TeacherDto.BasicResp> teachers,
            Map<String, Map<LocalDate, Integer>> teacherDailySlotsMap,
            LocalDate startDate, LocalDate endDate) {

        TeacherScheduleDashboardDto.DashboardResp response = new TeacherScheduleDashboardDto.DashboardResp();
        response.setStartDate(request.getStartDate());
        response.setEndDate(request.getEndDate());

        // 构建总体统计
        response.setOverallStats(buildOverallStats(teachers, teacherDailySlotsMap, startDate, endDate));

        // 构建教学组统计
        response.setGroupStats(buildGroupStats(request.getGroupIds(), teachers, teacherDailySlotsMap, startDate, endDate));

        // 构建每日统计
        response.setDailyStats(buildDailyStats(teachers, teacherDailySlotsMap, startDate, endDate));

        // 构建每周统计
        response.setWeeklyStats(buildWeeklyStats(teachers, teacherDailySlotsMap, startDate, endDate));

        return response;
    }

    /**
     * 构建总体统计
     */
    private TeacherScheduleDashboardDto.OverallStats buildOverallStats(
            List<TeacherDto.BasicResp> teachers,
            Map<String, Map<LocalDate, Integer>> teacherDailySlotsMap,
            LocalDate startDate, LocalDate endDate) {

        TeacherScheduleDashboardDto.OverallStats stats = new TeacherScheduleDashboardDto.OverallStats();
        stats.setTotalTeachers(teachers.size());
        stats.setTotalDays((int) ChronoUnit.DAYS.between(startDate, endDate) + 1);

        // 计算总可排课课次
        int totalSlots = teacherDailySlotsMap.values().stream()
            .mapToInt(dailySlots -> dailySlots.values().stream().mapToInt(Integer::intValue).sum())
            .sum();

        stats.setTotalAvailableSlots(totalSlots);

        // 计算平均课次
        if (teachers.size() > 0) {
            double average = (double) totalSlots / teachers.size();
            stats.setAverageSlotsPerTeacher(BigDecimal.valueOf(average).setScale(2, RoundingMode.HALF_UP).doubleValue());
        } else {
            stats.setAverageSlotsPerTeacher(0.0);
        }

        return stats;
    }

    /**
     * 构建教学组统计
     */
    private List<TeacherScheduleDashboardDto.GroupStats> buildGroupStats(
            List<String> groupIds,
            List<TeacherDto.BasicResp> teachers,
            Map<String, Map<LocalDate, Integer>> teacherDailySlotsMap,
            LocalDate startDate, LocalDate endDate) {

        List<TeacherScheduleDashboardDto.GroupStats> groupStatsList = new ArrayList<>();

        // 获取所有教学组信息
        Map<String, String> groupNameMap = getGroupNameMap();

        // 如果没有指定教学组，获取所有教学组
        List<String> targetGroupIds = groupIds;
        if (targetGroupIds == null || targetGroupIds.isEmpty()) {
            targetGroupIds = new ArrayList<>(groupNameMap.keySet());
        }

        // 获取教师与教学组的关系
        Map<String, String> teacherGroupMap = getTeacherGroupMap(teachers);

        // 按教学组分组统计
        for (String groupId : targetGroupIds) {
            TeacherScheduleDashboardDto.GroupStats groupStats = new TeacherScheduleDashboardDto.GroupStats();
            groupStats.setGroupId(groupId);
            groupStats.setGroupName(groupNameMap.getOrDefault(groupId, "未知教学组"));

            // 筛选该教学组的教师
            List<TeacherDto.BasicResp> groupTeachers = teachers.stream()
                .filter(teacher -> groupId.equals(teacherGroupMap.get(teacher.getId())))
                .collect(Collectors.toList());

            groupStats.setTeacherCount(groupTeachers.size());

            // 计算该教学组的总课次
            int totalSlots = 0;
            List<TeacherScheduleDashboardDto.TeacherStats> teacherStatsList = new ArrayList<>();

            for (TeacherDto.BasicResp teacher : groupTeachers) {
                Map<LocalDate, Integer> dailySlots = teacherDailySlotsMap.get(teacher.getId());
                if (dailySlots != null) {
                    int teacherTotalSlots = dailySlots.values().stream().mapToInt(Integer::intValue).sum();
                    totalSlots += teacherTotalSlots;

                    // 构建教师统计
                    TeacherScheduleDashboardDto.TeacherStats teacherStats = new TeacherScheduleDashboardDto.TeacherStats();
                    teacherStats.setTeacherId(teacher.getId());
                    teacherStats.setTeacherName(teacher.getName());
                    teacherStats.setTotalAvailableSlots(teacherTotalSlots);

                    // 构建每日课次
                    List<TeacherScheduleDashboardDto.DailyTeacherSlots> dailyTeacherSlots = new ArrayList<>();
                    LocalDate currentDate = startDate;
                    while (!currentDate.isAfter(endDate)) {
                        TeacherScheduleDashboardDto.DailyTeacherSlots daySlots = new TeacherScheduleDashboardDto.DailyTeacherSlots();
                        daySlots.setDate(currentDate.toString());
                        daySlots.setWeekday(currentDate.getDayOfWeek().getValue());
                        daySlots.setAvailableSlots(dailySlots.getOrDefault(currentDate, 0));
                        dailyTeacherSlots.add(daySlots);
                        currentDate = currentDate.plusDays(1);
                    }
                    teacherStats.setDailySlots(dailyTeacherSlots);
                    teacherStatsList.add(teacherStats);
                }
            }

            groupStats.setTotalAvailableSlots(totalSlots);
            groupStats.setTeachers(teacherStatsList);

            // 计算平均课次
            if (groupTeachers.size() > 0) {
                double average = (double) totalSlots / groupTeachers.size();
                groupStats.setAverageSlotsPerTeacher(BigDecimal.valueOf(average).setScale(2, RoundingMode.HALF_UP).doubleValue());
            } else {
                groupStats.setAverageSlotsPerTeacher(0.0);
            }

            groupStatsList.add(groupStats);
        }

        return groupStatsList;
    }

    /**
     * 获取教学组名称映射
     */
    private Map<String, String> getGroupNameMap() {
        try {
            // 调用教学组服务获取所有教学组信息
            List<TeachingGroup> teachingGroups = teachingGroupService.lambdaQuery()
                .eq(TeachingGroup::getDeleted, false)
                .eq(TeachingGroup::getStatus, "active")
                .list();

            Map<String, String> groupMap = new HashMap<>();
            for (TeachingGroup group : teachingGroups) {
                groupMap.put(group.getId(), group.getName());
            }

            // 添加默认组
            groupMap.put("default", "未分组");

            return groupMap;
        } catch (Exception e) {
            log.warn("获取教学组信息失败", e);
            // 返回默认组映射
            Map<String, String> defaultMap = new HashMap<>();
            defaultMap.put("default", "未分组");
            return defaultMap;
        }
    }

    /**
     * 获取教师与教学组的关系映射
     */
    private Map<String, String> getTeacherGroupMap(List<TeacherDto.BasicResp> teachers) {
        try {
            Map<String, String> teacherGroupMap = new HashMap<>();

            // 获取所有教师的教学组关系
            List<String> teacherIds = teachers.stream()
                .map(TeacherDto.BasicResp::getId)
                .collect(Collectors.toList());



            if (!teacherIds.isEmpty()) {
                List<TeachingGroupMember> members = teachingGroupMemberService.lambdaQuery()
                    .in(TeachingGroupMember::getTeacherId, teacherIds)
                    .eq(TeachingGroupMember::getStatus, "active")
                    .eq(TeachingGroupMember::getDeleted, false)
                    .list();

                for (TeachingGroupMember member : members) {
                    teacherGroupMap.put(member.getTeacherId(), member.getGroupId());
                }
            }

            // 为没有教学组的教师分配默认组
            for (TeacherDto.BasicResp teacher : teachers) {
                if (!teacherGroupMap.containsKey(teacher.getId())) {
                    teacherGroupMap.put(teacher.getId(), "default");
                }
            }
            return teacherGroupMap;
        } catch (Exception e) {
            log.warn("获取教师教学组关系失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 构建每日统计
     */
    private List<TeacherScheduleDashboardDto.DailyStats> buildDailyStats(
            List<TeacherDto.BasicResp> teachers,
            Map<String, Map<LocalDate, Integer>> teacherDailySlotsMap,
            LocalDate startDate, LocalDate endDate) {

        List<TeacherScheduleDashboardDto.DailyStats> dailyStats = new ArrayList<>();

        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            TeacherScheduleDashboardDto.DailyStats dayStats = new TeacherScheduleDashboardDto.DailyStats();
            dayStats.setDate(currentDate.toString());
            dayStats.setWeekday(currentDate.getDayOfWeek().getValue());
            dayStats.setWeekdayName(getWeekdayName(currentDate.getDayOfWeek()));

            // 计算当天总课次和有课时的教师数
            int totalSlots = 0;
            int availableTeachers = 0;

            for (TeacherDto.BasicResp teacher : teachers) {
                Integer slots = teacherDailySlotsMap.get(teacher.getId()).get(currentDate);
                if (slots != null && slots > 0) {
                    totalSlots += slots;
                    availableTeachers++;
                }
            }

            dayStats.setTotalAvailableSlots(totalSlots);
            dayStats.setAvailableTeachers(availableTeachers);

            if (availableTeachers > 0) {
                double average = (double) totalSlots / availableTeachers;
                dayStats.setAverageSlotsPerAvailableTeacher(
                    BigDecimal.valueOf(average).setScale(2, RoundingMode.HALF_UP).doubleValue());
            } else {
                dayStats.setAverageSlotsPerAvailableTeacher(0.0);
            }

            dayStats.setGroupStats(buildDailyGroupStats(teachers, teacherDailySlotsMap, currentDate));

            dailyStats.add(dayStats);
            currentDate = currentDate.plusDays(1);
        }

        return dailyStats;
    }

    /**
     * 构建每日教学组统计
     */
    private List<TeacherScheduleDashboardDto.DailyGroupStats> buildDailyGroupStats(
            List<TeacherDto.BasicResp> teachers,
            Map<String, Map<LocalDate, Integer>> teacherDailySlotsMap,
            LocalDate date) {

        List<TeacherScheduleDashboardDto.DailyGroupStats> dailyGroupStats = new ArrayList<>();

        // 获取教学组信息
        Map<String, String> groupNameMap = getGroupNameMap();
        Map<String, String> teacherGroupMap = getTeacherGroupMap(teachers);



        // 按教学组统计当日数据
        Map<String, List<TeacherDto.BasicResp>> groupTeachersMap = teachers.stream()
            .collect(Collectors.groupingBy(teacher ->
                teacherGroupMap.getOrDefault(teacher.getId(), "default")));



        for (Map.Entry<String, List<TeacherDto.BasicResp>> entry : groupTeachersMap.entrySet()) {
            String groupId = entry.getKey();
            List<TeacherDto.BasicResp> groupTeachers = entry.getValue();

            TeacherScheduleDashboardDto.DailyGroupStats groupStats = new TeacherScheduleDashboardDto.DailyGroupStats();
            groupStats.setGroupId(groupId);
            groupStats.setGroupName(groupNameMap.getOrDefault(groupId, "未知教学组"));

            // 计算当日该教学组的课次和教师数
            int totalSlots = 0;
            int availableTeachers = 0;

            for (TeacherDto.BasicResp teacher : groupTeachers) {
                Map<LocalDate, Integer> dailySlots = teacherDailySlotsMap.get(teacher.getId());
                if (dailySlots != null) {
                    Integer slots = dailySlots.get(date);
                    if (slots != null && slots > 0) {
                        totalSlots += slots;
                        availableTeachers++;
                    }
                }
            }

            groupStats.setAvailableSlots(totalSlots);
            groupStats.setAvailableTeachers(availableTeachers);



            dailyGroupStats.add(groupStats);
        }


        return dailyGroupStats;
    }

    /**
     * 构建每周统计
     */
    private List<TeacherScheduleDashboardDto.WeeklyStats> buildWeeklyStats(
            List<TeacherDto.BasicResp> teachers,
            Map<String, Map<LocalDate, Integer>> teacherDailySlotsMap,
            LocalDate startDate, LocalDate endDate) {

        // 简化实现：暂时返回空列表，后续可以扩展
        return new ArrayList<>();
    }

    /**
     * 获取星期几名称
     */
    private String getWeekdayName(DayOfWeek dayOfWeek) {
        switch (dayOfWeek) {
            case MONDAY: return "周一";
            case TUESDAY: return "周二";
            case WEDNESDAY: return "周三";
            case THURSDAY: return "周四";
            case FRIDAY: return "周五";
            case SATURDAY: return "周六";
            case SUNDAY: return "周日";
            default: return "";
        }
    }

    @Override
    public TeacherScheduleDashboardDto.GroupTrendResp getGroupTrend(TeacherScheduleDashboardDto.GroupTrendReq request) {
        try {
            log.info("获取教学组趋势数据: groupId={}, startDate={}, endDate={}",
                    request.getGroupId(), request.getStartDate(), request.getEndDate());

            LocalDate startDate = LocalDate.parse(request.getStartDate());
            LocalDate endDate = LocalDate.parse(request.getEndDate());

            // 获取教学组信息
            Map<String, String> groupNameMap = getGroupNameMap();
            String groupName = groupNameMap.getOrDefault(request.getGroupId(), "未知教学组");

            // 获取该教学组的教师列表
            List<TeacherDto.BasicResp> groupTeachers = getGroupTeachers(request.getGroupId());

            if (groupTeachers.isEmpty()) {
                log.warn("教学组{}没有教师", request.getGroupId());
                return createEmptyGroupTrend(request.getGroupId(), groupName, request.getStartDate(), request.getEndDate());
            }

            // 批量获取教师时间段数据（避免循环查表）
            Map<String, List<TeacherDto.TimeSlotResp>> teacherTimeSlotsMap = getTeacherTimeSlots(groupTeachers);
            Map<String, List<Course>> teacherScheduledCoursesMap = getTeacherScheduledCourses(groupTeachers, startDate, endDate);

            Map<String, Map<LocalDate, Integer>> teacherDailySlotsMap =
                calculateTeacherDailySlots(groupTeachers, teacherTimeSlotsMap, teacherScheduledCoursesMap, startDate, endDate);

            // 计算每日数据
            List<TeacherScheduleDashboardDto.GroupTrendDailyData> dailyData = new ArrayList<>();
            int totalSlots = 0;
            int peakSlots = 0;

            for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                TeacherScheduleDashboardDto.GroupTrendDailyData dayData =
                    buildGroupTrendDailyDataBatch(groupTeachers, teacherDailySlotsMap, date);
                dailyData.add(dayData);

                totalSlots += dayData.getAvailableSlots();
                peakSlots = Math.max(peakSlots, dayData.getAvailableSlots());
            }

            // 构建响应
            TeacherScheduleDashboardDto.GroupTrendResp response = new TeacherScheduleDashboardDto.GroupTrendResp();
            response.setGroupId(request.getGroupId());
            response.setGroupName(groupName);
            response.setStartDate(request.getStartDate());
            response.setEndDate(request.getEndDate());
            response.setTotalSlots(totalSlots);
            response.setAverageSlots(dailyData.isEmpty() ? 0.0 : (double) totalSlots / dailyData.size());
            response.setPeakSlots(peakSlots);
            response.setTeacherCount(groupTeachers.size());
            response.setDailyData(dailyData);

            log.info("教学组趋势数据获取成功: groupId={}, totalSlots={}, teacherCount={}",
                    request.getGroupId(), totalSlots, groupTeachers.size());

            return response;
        } catch (Exception e) {
            log.error("获取教学组趋势数据失败", e);
            throw new RuntimeException("获取教学组趋势数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取指定教学组的教师列表（使用专用查询方法）
     */
    private List<TeacherDto.BasicResp> getGroupTeachers(String groupId) {
        try {
            // 使用专用的查询方法，仅统计已加入教学组且状态为active的教师
            return getActiveTeachersInGroups(Collections.singletonList(groupId));
        } catch (Exception e) {
            log.error("获取教学组教师列表失败: groupId={}", groupId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 构建教学组趋势每日数据（批量版本，避免循环查表）
     */
    private TeacherScheduleDashboardDto.GroupTrendDailyData buildGroupTrendDailyDataBatch(
            List<TeacherDto.BasicResp> teachers,
            Map<String, Map<LocalDate, Integer>> teacherDailySlotsMap,
            LocalDate date) {

        TeacherScheduleDashboardDto.GroupTrendDailyData dayData = new TeacherScheduleDashboardDto.GroupTrendDailyData();
        dayData.setDate(date.toString());
        dayData.setWeekdayName(getWeekdayName(date.getDayOfWeek()));

        int totalSlots = 0;
        int availableTeachers = 0;

        for (TeacherDto.BasicResp teacher : teachers) {
            Map<LocalDate, Integer> dailySlots = teacherDailySlotsMap.get(teacher.getId());
            if (dailySlots != null) {
                Integer slots = dailySlots.get(date);
                if (slots != null && slots > 0) {
                    totalSlots += slots;
                    availableTeachers++;
                }
            }
        }

        dayData.setAvailableSlots(totalSlots);
        dayData.setAvailableTeachers(availableTeachers);
        dayData.setAverageSlots(availableTeachers > 0 ? (double) totalSlots / availableTeachers : 0.0);

        // 计算利用率（假设每个教师最多可以排8课次）
        int maxPossibleSlots = teachers.size() * 8;
        dayData.setUtilization(maxPossibleSlots > 0 ? (double) totalSlots / maxPossibleSlots * 100 : 0.0);

        return dayData;
    }

    /**
     * 创建空的教学组趋势数据
     */
    private TeacherScheduleDashboardDto.GroupTrendResp createEmptyGroupTrend(
            String groupId, String groupName, String startDate, String endDate) {

        TeacherScheduleDashboardDto.GroupTrendResp response = new TeacherScheduleDashboardDto.GroupTrendResp();
        response.setGroupId(groupId);
        response.setGroupName(groupName);
        response.setStartDate(startDate);
        response.setEndDate(endDate);
        response.setTotalSlots(0);
        response.setAverageSlots(0.0);
        response.setPeakSlots(0);
        response.setTeacherCount(0);
        response.setDailyData(new ArrayList<>());

        return response;
    }

    /**
     * 创建空的看板数据
     */
    private TeacherScheduleDashboardDto.DashboardResp createEmptyDashboard(String startDate, String endDate) {
        TeacherScheduleDashboardDto.DashboardResp response = new TeacherScheduleDashboardDto.DashboardResp();
        response.setStartDate(startDate);
        response.setEndDate(endDate);

        TeacherScheduleDashboardDto.OverallStats overallStats = new TeacherScheduleDashboardDto.OverallStats();
        overallStats.setTotalTeachers(0);
        overallStats.setTotalAvailableSlots(0);
        overallStats.setAverageSlotsPerTeacher(0.0);
        overallStats.setTotalDays(0);
        response.setOverallStats(overallStats);

        response.setGroupStats(new ArrayList<>());
        response.setDailyStats(new ArrayList<>());
        response.setWeeklyStats(new ArrayList<>());

        return response;
    }

    @Override
    public TeacherScheduleDashboardDto.DayTeacherDetailResp getDayTeacherDetail(TeacherScheduleDashboardDto.DayTeacherDetailReq request) {
        log.info("获取指定日期教师详细数据: date={}, groupIds={}", request.getDate(), request.getGroupIds());

        try {
            // 参数验证
            if (request.getDate() == null || request.getDate().trim().isEmpty()) {
                throw new IllegalArgumentException("日期不能为空");
            }

            LocalDate targetDate = LocalDate.parse(request.getDate());

            // 获取教师数据
            List<TeacherDto.BasicResp> teachers = getTeachers(request.getGroupIds());
            if (teachers.isEmpty()) {
                return createEmptyDayTeacherDetail(request.getDate());
            }

            // 批量获取教师时间段和已排课程
            Map<String, List<TeacherDto.TimeSlotResp>> teacherTimeSlotsMap = getTeacherTimeSlots(teachers);
            Map<String, List<Course>> teacherScheduledCoursesMap = getTeacherScheduledCourses(teachers, targetDate, targetDate);

            // 获取教学组信息
            Map<String, String> groupNameMap = getGroupNameMap();

            // 构建响应数据
            return buildDayTeacherDetailResponse(request, teachers, teacherTimeSlotsMap, teacherScheduledCoursesMap, groupNameMap, targetDate);

        } catch (Exception e) {
            log.error("获取指定日期教师详细数据失败", e);
            throw new RuntimeException("获取指定日期教师详细数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建指定日期教师详细数据响应
     */
    private TeacherScheduleDashboardDto.DayTeacherDetailResp buildDayTeacherDetailResponse(
            TeacherScheduleDashboardDto.DayTeacherDetailReq request,
            List<TeacherDto.BasicResp> teachers,
            Map<String, List<TeacherDto.TimeSlotResp>> teacherTimeSlotsMap,
            Map<String, List<Course>> teacherScheduledCoursesMap,
            Map<String, String> groupNameMap,
            LocalDate targetDate) {

        TeacherScheduleDashboardDto.DayTeacherDetailResp response = new TeacherScheduleDashboardDto.DayTeacherDetailResp();
        response.setDate(request.getDate());
        response.setWeekdayName(getWeekdayName(targetDate.getDayOfWeek()));

        // 构建教师详细信息列表
        List<TeacherScheduleDashboardDto.DayTeacherInfo> teacherInfos = new ArrayList<>();
        Map<String, TeacherScheduleDashboardDto.DayGroupSummary> groupSummaryMap = new HashMap<>();

        int totalAvailableSlots = 0;
        int availableTeachersCount = 0;

        for (TeacherDto.BasicResp teacher : teachers) {
            String teacherId = teacher.getId();
            List<TeacherDto.TimeSlotResp> timeSlots = teacherTimeSlotsMap.getOrDefault(teacherId, new ArrayList<>());
            List<Course> scheduledCourses = teacherScheduledCoursesMap.getOrDefault(teacherId, new ArrayList<>());

            // 计算该教师在目标日期的可排课课次
            Integer availableSlots = calculateAvailableSlotsForDate(timeSlots, scheduledCourses, targetDate);

            // 构建教师信息
            TeacherScheduleDashboardDto.DayTeacherInfo teacherInfo = new TeacherScheduleDashboardDto.DayTeacherInfo();
            teacherInfo.setTeacherId(teacherId);
            teacherInfo.setTeacherName(teacher.getName());
            teacherInfo.setTeacherPhone(teacher.getPhone());
            teacherInfo.setTeacherEmail(teacher.getEmail());
            teacherInfo.setTeacherAvatar(teacher.getAvatar());
            teacherInfo.setAvailableSlots(availableSlots);
            teacherInfo.setScheduledCoursesCount(scheduledCourses.size());
            // 设置已排课程列表（简化版本，只包含基本信息）
            List<Object> scheduledCoursesList = scheduledCourses.stream()
                    .map(course -> {
                        Map<String, Object> courseInfo = new HashMap<>();
                        courseInfo.put("id", course.getId());
                        courseInfo.put("subject", course.getSubject());
                        courseInfo.put("startTime", course.getScheduledStartTime());
                        courseInfo.put("endTime", course.getScheduledEndTime());
                        return courseInfo;
                    })
                    .collect(Collectors.toList());
            teacherInfo.setScheduledCourses(scheduledCoursesList);

            // 获取教师的教学组信息
            String groupId = getTeacherGroupId(teacherId);
            String groupName = groupNameMap.getOrDefault(groupId, "未分组");
            teacherInfo.setGroupId(groupId);
            teacherInfo.setGroupName(groupName);

            // 构建时间段详情
            List<TeacherScheduleDashboardDto.DayTimeSlotInfo> dayTimeSlots = buildDayTimeSlots(timeSlots, scheduledCourses, targetDate);
            teacherInfo.setTimeSlots(dayTimeSlots);

            teacherInfos.add(teacherInfo);

            // 统计数据
            totalAvailableSlots += availableSlots;
            if (availableSlots > 0) {
                availableTeachersCount++;
            }

            // 构建教学组汇总
            if (groupId != null && !groupId.isEmpty()) {
                TeacherScheduleDashboardDto.DayGroupSummary groupSummary = groupSummaryMap.computeIfAbsent(groupId, k -> {
                    TeacherScheduleDashboardDto.DayGroupSummary summary = new TeacherScheduleDashboardDto.DayGroupSummary();
                    summary.setGroupId(groupId);
                    summary.setGroupName(groupName);
                    summary.setTeacherCount(0);
                    summary.setAvailableSlots(0);
                    return summary;
                });
                groupSummary.setTeacherCount(groupSummary.getTeacherCount() + 1);
                groupSummary.setAvailableSlots(groupSummary.getAvailableSlots() + availableSlots);
            }
        }

        response.setTeachers(teacherInfos);

        // 构建汇总信息
        TeacherScheduleDashboardDto.DayTeacherSummary summary = new TeacherScheduleDashboardDto.DayTeacherSummary();
        summary.setTotalTeachers(teachers.size());
        summary.setAvailableTeachers(availableTeachersCount);
        summary.setTotalSlots(totalAvailableSlots);
        summary.setAverageSlots(teachers.isEmpty() ? 0.0 : (double) totalAvailableSlots / teachers.size());
        summary.setGroupSummaries(new ArrayList<>(groupSummaryMap.values()));

        response.setSummary(summary);

        return response;
    }

    /**
     * 构建指定日期的时间段详情
     */
    private List<TeacherScheduleDashboardDto.DayTimeSlotInfo> buildDayTimeSlots(
            List<TeacherDto.TimeSlotResp> timeSlots,
            List<Course> scheduledCourses,
            LocalDate targetDate) {

        List<TeacherScheduleDashboardDto.DayTimeSlotInfo> dayTimeSlots = new ArrayList<>();

        // 获取目标日期对应的星期几
        int targetWeekday = targetDate.getDayOfWeek().getValue();

        // 筛选出目标日期的时间段
        List<TeacherDto.TimeSlotResp> targetDaySlots = timeSlots.stream()
                .filter(slot -> slot.getWeekday() != null && slot.getWeekday().equals(targetWeekday))
                .collect(Collectors.toList());

        // 获取已排课程的时间段
        Set<String> scheduledTimeRanges = scheduledCourses.stream()
                .filter(course -> course.getScheduledStartTime() != null && course.getScheduledEndTime() != null)
                .map(course -> {
                    LocalTime startTime = course.getScheduledStartTime().toInstant()
                            .atZone(ZoneId.systemDefault()).toLocalTime();
                    LocalTime endTime = course.getScheduledEndTime().toInstant()
                            .atZone(ZoneId.systemDefault()).toLocalTime();
                    return startTime.toString() + "-" + endTime.toString();
                })
                .collect(Collectors.toSet());

        // 构建时间段详情
        for (TeacherDto.TimeSlotResp slot : targetDaySlots) {
            TeacherScheduleDashboardDto.DayTimeSlotInfo dayTimeSlot = new TeacherScheduleDashboardDto.DayTimeSlotInfo();
            dayTimeSlot.setTimeSlotId(slot.getId());
            dayTimeSlot.setStartTime(slot.getStartTime());
            dayTimeSlot.setEndTime(slot.getEndTime());
            dayTimeSlot.setRemark(slot.getRemark());

            // 判断时间段状态
            String timeRange = slot.getStartTime() + "-" + slot.getEndTime();
            if (scheduledTimeRanges.contains(timeRange)) {
                dayTimeSlot.setStatus("scheduled");
            } else {
                dayTimeSlot.setStatus("available");
            }

            dayTimeSlots.add(dayTimeSlot);
        }

        return dayTimeSlots;
    }

    /**
     * 获取教师的教学组ID
     */
    private String getTeacherGroupId(String teacherId) {
        try {
            // 查询教师所属的教学组
            List<TeachingGroupMember> members = teachingGroupMemberService.lambdaQuery()
                    .eq(TeachingGroupMember::getTeacherId, teacherId)
                    .eq(TeachingGroupMember::getDeleted, false)
                    .list();

            if (!members.isEmpty()) {
                return members.get(0).getGroupId();
            }
        } catch (Exception e) {
            log.warn("获取教师教学组失败: teacherId={}", teacherId, e);
        }
        return null;
    }

    /**
     * 创建空的指定日期教师详细数据响应
     */
    private TeacherScheduleDashboardDto.DayTeacherDetailResp createEmptyDayTeacherDetail(String date) {
        TeacherScheduleDashboardDto.DayTeacherDetailResp response = new TeacherScheduleDashboardDto.DayTeacherDetailResp();
        response.setDate(date);

        try {
            LocalDate targetDate = LocalDate.parse(date);
            response.setWeekdayName(getWeekdayName(targetDate.getDayOfWeek()));
        } catch (Exception e) {
            response.setWeekdayName("未知");
        }

        // 空汇总信息
        TeacherScheduleDashboardDto.DayTeacherSummary summary = new TeacherScheduleDashboardDto.DayTeacherSummary();
        summary.setTotalTeachers(0);
        summary.setAvailableTeachers(0);
        summary.setTotalSlots(0);
        summary.setAverageSlots(0.0);
        summary.setGroupSummaries(new ArrayList<>());
        response.setSummary(summary);

        response.setTeachers(new ArrayList<>());

        return response;
    }
}
