package org.nonamespace.word.server.domain;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.*;
import org.nonamespace.word.server.entity.DataEntity;

import java.util.Date;

/**
 * 排课记录表
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "course_schedule_info", autoResultMap = true)
public class CourseScheduleInfo extends DataEntity {

    private String teacherId;

    private String studentId;

    private String type;

    @TableField("course_type")
    private String courseType;

    private Date beginDate;
    private Date endDate;
    private Integer totalLessons;
    private String subject;
    private String specification;


    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONObject scheduleData;

}
