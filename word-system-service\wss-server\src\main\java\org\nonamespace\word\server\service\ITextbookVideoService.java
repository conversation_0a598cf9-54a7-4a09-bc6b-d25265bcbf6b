package org.nonamespace.word.server.service;

import org.nonamespace.word.server.dto.TextbookVideoDto;

import java.util.List;

/**
 * 教材单词视频服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface ITextbookVideoService {

    /**
     * 上传单词视频
     * 
     * @param uploadReq 上传请求
     * @return 视频信息
     */
    TextbookVideoDto.VideoResp uploadVideo(TextbookVideoDto.UploadReq uploadReq);

    /**
     * 获取单词视频信息
     * 
     * @param textbookItemId 教材项ID
     * @return 视频信息
     */
    TextbookVideoDto.VideoResp getVideoInfo(String textbookItemId);

    /**
     * 查询视频列表
     * 
     * @param queryReq 查询请求
     * @return 视频列表
     */
    List<TextbookVideoDto.VideoResp> getVideoList(TextbookVideoDto.QueryReq queryReq);

    /**
     * 删除单词视频
     * 
     * @param deleteReq 删除请求
     * @return 是否成功
     */
    boolean deleteVideo(TextbookVideoDto.DeleteReq deleteReq);

    /**
     * 检查视频是否存在
     * 
     * @param textbookItemId 教材项ID
     * @return 是否存在
     */
    boolean hasVideo(String textbookItemId);
}
