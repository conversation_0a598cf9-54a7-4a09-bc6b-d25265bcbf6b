package org.nonamespace.word.server.dto.sales;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * 销售组相关DTO
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
public class SalesGroupDto {

    /**
     * 获取销售组列表请求参数
     */
    @Data
    public static class GetListReq {
        private Integer pageNum = 1;
        private Integer pageSize = 20;
        private String name;
        private String status;
        private String leaderId;
    }

    /**
     * 创建销售组请求参数
     */
    @Data
    public static class CreateReq {
        @NotBlank(message = "销售组名称不能为空")
        @Size(min = 2, max = 50, message = "销售组名称长度在 2 到 50 个字符")
        private String name;

        @Size(max = 200, message = "描述长度不能超过 200 个字符")
        private String description;

        private String leaderId;
        private Long deptId; // 关联的部门ID
        private String status; // 状态 (active: 活跃, inactive: 停用)
    }

    /**
     * 更新销售组请求参数
     */
    @Data
    public static class UpdateReq {
        @NotBlank(message = "销售组ID不能为空")
        private String id;

        @NotBlank(message = "销售组名称不能为空")
        @Size(min = 2, max = 50, message = "销售组名称长度在 2 到 50 个字符")
        private String name;

        @Size(max = 200, message = "描述长度不能超过 200 个字符")
        private String description;

        private String leaderId;
        private Long deptId; // 关联的部门ID
        private String status; // 状态 (active: 活跃, inactive: 停用)
    }

    /**
     * 销售组响应数据
     */
    @Data
    public static class Resp {
        private String id;
        private String name;
        private String description;
        private String leaderId;
        private String leaderName;
        private String leaderPhone;
        private Long deptId;
        private String deptName;
        private Integer memberCount;
        private String status;
        private Date createTime;
        private Date updateTime;
    }

    /**
     * 销售组统计信息
     */
    @Data
    public static class StatsResp {
        private Integer totalGroups;
        private Integer activeGroups;
        private Integer totalSales;
        private Integer unassignedSales;
    }

    /**
     * 添加成员请求参数
     */
    @Data
    public static class AddMembersReq {
        @NotBlank(message = "销售组ID不能为空")
        private String groupId;
        
        private List<String> salesIds;
    }

    /**
     * 移除成员请求参数
     */
    @Data
    public static class RemoveMemberReq {
        @NotBlank(message = "销售组ID不能为空")
        private String groupId;
        
        @NotBlank(message = "销售人员ID不能为空")
        private String salesId;
    }

    /**
     * 设置组长请求参数
     */
    @Data
    public static class SetLeaderReq {
        @NotBlank(message = "销售组ID不能为空")
        private String groupId;
        
        @NotBlank(message = "组长ID不能为空")
        private String leaderId;
    }

    /**
     * 销售组成员响应数据
     */
    @Data
    public static class MemberResp {
        private String id;
        private String groupId;
        private String salesId;
        private String salesName;
        private String salesPhone;
        private String roleType;
        private String status;
        private Date joinTime;
        private Date createTime;
    }

    /**
     * 获取销售组成员列表请求参数
     */
    @Data
    public static class GetMembersReq {
        private Integer pageNum = 1;
        private Integer pageSize = 20;
        private String groupId;
        private String salesName;
        private String roleType;
        private String status;
    }

    /**
     * 销售组选项响应（用于下拉选择）
     */
    @Data
    public static class OptionResp {
        private String id;
        private String name;
        private Integer memberCount;
    }
}
