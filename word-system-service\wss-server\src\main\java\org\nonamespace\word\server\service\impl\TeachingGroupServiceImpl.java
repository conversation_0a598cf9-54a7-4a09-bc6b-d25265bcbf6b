package org.nonamespace.word.server.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.domain.TeacherProfile;
import org.nonamespace.word.server.domain.TeachingGroup;
import org.nonamespace.word.server.domain.TeachingGroupMember;
import org.nonamespace.word.server.dto.management.teachinggroup.TeachingGroupDto;
import org.nonamespace.word.server.mapper.TeachingGroupMapper;
import org.nonamespace.word.server.service.ITeacherProfileService;
import org.nonamespace.word.server.service.ITeachingGroupMemberService;
import org.nonamespace.word.server.service.ITeachingGroupService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 教学组Service实现类
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Slf4j
@Service
public class TeachingGroupServiceImpl extends ServiceImpl<TeachingGroupMapper, TeachingGroup> implements ITeachingGroupService {

    @Autowired
    private ITeachingGroupMemberService teachingGroupMemberService;

    @Autowired
    @Lazy
    private ITeacherProfileService teacherProfileService;

    @Override
    public IPage<TeachingGroupDto.Resp> selectTeachingGroupPage(TeachingGroupDto.GetListReq req) {
        Page<TeachingGroup> page = new Page<>(req.getPageNum(), req.getPageSize());

        // 查询教学组基本信息
        IPage<TeachingGroup> groupPage = lambdaQuery()
                .eq(TeachingGroup::getDeleted, false)
                .like(StrUtil.isNotEmpty(req.getName()), TeachingGroup::getName, req.getName())
                .eq(StrUtil.isNotEmpty(req.getStatus()), TeachingGroup::getStatus, req.getStatus())
                // .eq(StrUtil.isNotEmpty(req.getDeptId()), TeachingGroup::getDeptId, req.getDeptId()) // 暂时注释，如果DTO中没有此字段
                .orderByDesc(TeachingGroup::getCreateTime)
                .page(page);

        // 批量查询成员信息
        List<String> groupIds = groupPage.getRecords().stream()
                .map(TeachingGroup::getId)
                .collect(Collectors.toList());

        final Map<String, Long> memberCountMap;
        final Map<String, String> leaderNameMap;
        final Map<String, String> adminNameMap;

        if (!groupIds.isEmpty()) {
            leaderNameMap = new HashMap<>();
            adminNameMap = new HashMap<>();
            // 查询成员数量
            List<TeachingGroupMember> members = teachingGroupMemberService.lambdaQuery()
                    .in(TeachingGroupMember::getGroupId, groupIds)
                    .eq(TeachingGroupMember::getDeleted, false)
                    .eq(TeachingGroupMember::getStatus, "active")
                    .list();

            memberCountMap = members.stream()
                    .collect(Collectors.groupingBy(
                            TeachingGroupMember::getGroupId,
                            Collectors.counting()
                    ));

            // 查询组长和教务信息
            Set<String> teacherIds = groupPage.getRecords().stream()
                    .flatMap(group -> Stream.of(group.getLeaderId(), group.getAdminId()))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            if (!teacherIds.isEmpty()) {
                List<TeacherProfile> teachers = teacherProfileService.lambdaQuery()
                        .in(TeacherProfile::getTeacherId, teacherIds)
                        .eq(TeacherProfile::getDeleted, false)
                        .list();

                Map<String, String> teacherNameMap = teachers.stream()
                        .collect(Collectors.toMap(
                                TeacherProfile::getTeacherId,
                                teacher -> teacher.getNickName() != null ? teacher.getNickName() : teacher.getRealName(),
                                (existing, replacement) -> existing
                        ));

                for (TeachingGroup group : groupPage.getRecords()) {
                    if (group.getLeaderId() != null) {
                        leaderNameMap.put(group.getId(), teacherNameMap.get(group.getLeaderId()));
                    }
                    if (group.getAdminId() != null) {
                        adminNameMap.put(group.getId(), teacherNameMap.get(group.getAdminId()));
                    }
                }
            }
        } else {
            memberCountMap = new HashMap<>();
            leaderNameMap = new HashMap<>();
            adminNameMap = new HashMap<>();
        }

        // 转换为响应对象
        List<TeachingGroupDto.Resp> respList = groupPage.getRecords().stream().map(group -> {
            TeachingGroupDto.Resp resp = new TeachingGroupDto.Resp();
            resp.setId(group.getId());
            resp.setName(group.getName());
            resp.setDescription(group.getDescription());
            resp.setDeptId(group.getDeptId());
            resp.setLeaderId(group.getLeaderId() != null ? String.valueOf(group.getLeaderId()) : null);
            resp.setLeaderName(leaderNameMap.get(group.getId()));
            resp.setAdminId(group.getAdminId() != null ? String.valueOf(group.getAdminId()) : null);
            resp.setAdminName(adminNameMap.get(group.getId()));
            resp.setMemberCount(memberCountMap.getOrDefault(group.getId(), 0L).intValue());
            resp.setStatus(group.getStatus());
            resp.setRemark(group.getRemark());
            resp.setCreateTime(group.getCreateTime());
            resp.setUpdateTime(group.getUpdateTime());
            return resp;
        }).collect(Collectors.toList());

        IPage<TeachingGroupDto.Resp> result = new Page<>(groupPage.getCurrent(), groupPage.getSize(), groupPage.getTotal());
        result.setRecords(respList);
        return result;
    }

    @Override
    public TeachingGroupDto.Resp selectTeachingGroupById(String id) {
        TeachingGroup group = getById(id);
        if (group == null || group.getDeleted()) {
            return null;
        }

        TeachingGroupDto.Resp resp = new TeachingGroupDto.Resp();
        resp.setId(group.getId());
        resp.setName(group.getName());
        resp.setDescription(group.getDescription());
        resp.setDeptId(group.getDeptId());
        resp.setLeaderId(group.getLeaderId() != null ? String.valueOf(group.getLeaderId()) : null);
        resp.setAdminId(group.getAdminId() != null ? String.valueOf(group.getAdminId()) : null);
        resp.setStatus(group.getStatus());
        resp.setRemark(group.getRemark());
        resp.setCreateTime(group.getCreateTime());
        resp.setUpdateTime(group.getUpdateTime());

        // 查询成员数量
        long memberCount = teachingGroupMemberService.lambdaQuery()
                .eq(TeachingGroupMember::getGroupId, id)
                .eq(TeachingGroupMember::getDeleted, false)
                .eq(TeachingGroupMember::getStatus, "active")
                .count();
        resp.setMemberCount((int) memberCount);

        // 查询组长和教务姓名
        if (group.getLeaderId() != null || group.getAdminId() != null) {
            Set<String> teacherIds = Stream.of(group.getLeaderId(), group.getAdminId())
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            List<TeacherProfile> teachers = teacherProfileService.lambdaQuery()
                    .in(TeacherProfile::getTeacherId, teacherIds)
                    .eq(TeacherProfile::getDeleted, false)
                    .list();

            Map<String, String> teacherNameMap = teachers.stream()
                    .collect(Collectors.toMap(
                            TeacherProfile::getTeacherId,
                            teacher -> teacher.getNickName() != null ? teacher.getNickName() : teacher.getRealName(),
                            (existing, replacement) -> existing
                    ));

            if (group.getLeaderId() != null) {
                resp.setLeaderName(teacherNameMap.get(group.getLeaderId()));
            }
            if (group.getAdminId() != null) {
                resp.setAdminName(teacherNameMap.get(group.getAdminId()));
            }
        }

        return resp;
    }

    @Override
    public boolean createTeachingGroup(TeachingGroupDto.CreateReq req) {
        try {
            log.debug("创建教学组: name={}", req.getName());

            TeachingGroup teachingGroup = new TeachingGroup();
            teachingGroup.setId(IdUtil.getSnowflakeNextIdStr());
            teachingGroup.setName(req.getName());
            teachingGroup.setDescription(req.getDescription());
            teachingGroup.setDeptId(req.getDeptId());

            if(StrUtil.isNotEmpty(req.getLeaderId())) {
                teachingGroup.setLeaderId(req.getLeaderId());
            }
            if(StrUtil.isNotEmpty(req.getAdminId())) {
                teachingGroup.setAdminId(req.getAdminId());
            }

            teachingGroup.setRemark(req.getRemark());
            teachingGroup.setStatus("active");
            teachingGroup.setMemberCount(0);
            teachingGroup.setCreateTime(new Date());
            teachingGroup.setUpdateTime(new Date());

            boolean result = save(teachingGroup);

            if (result) {
                // 自动添加组长和教务到教学组成员表
                autoAddLeaderAndAdminToGroup(teachingGroup.getId(), req.getLeaderId(), req.getAdminId());

                log.info("教学组创建成功: id={}, name={}", teachingGroup.getId(), teachingGroup.getName());
            } else {
                log.error("教学组保存失败: name={}", req.getName());
            }

            return result;
        } catch (Exception e) {
            log.error("创建教学组失败: name={}", req.getName(), e);
            throw new RuntimeException("创建教学组失败: " + e.getMessage());
        }
    }

    @Override
        public boolean updateTeachingGroup(TeachingGroupDto.UpdateReq req) {
        try {
            TeachingGroup teachingGroup = getById(req.getId());
            if (teachingGroup == null) {
                throw new RuntimeException("教学组不存在");
            }
            
            // 记录原来的组长和教务ID
            String oldLeaderId = teachingGroup.getLeaderId();
            String oldAdminId = teachingGroup.getAdminId();

            BeanUtils.copyProperties(req, teachingGroup);
            if(StrUtil.isNotEmpty(req.getLeaderId())) {
                teachingGroup.setLeaderId(req.getLeaderId());
            }
            if(StrUtil.isNotEmpty(req.getAdminId())) {
                teachingGroup.setAdminId(req.getAdminId());
            }
            teachingGroup.setUpdateTime(new Date());

            boolean updateResult = updateById(teachingGroup);

            if (updateResult) {
                // 处理组长和教务的成员关系变更
                handleLeaderAndAdminMembershipChange(req.getId(), oldLeaderId, oldAdminId, req.getLeaderId(), req.getAdminId());
            }

            return updateResult;
        } catch (Exception e) {
            log.error("更新教学组失败", e);
            throw new RuntimeException("更新教学组失败: " + e.getMessage());
        }
    }

    @Override
        public boolean deleteTeachingGroup(String id) {
        try {
            // 先删除教学组成员关系
            teachingGroupMemberService.removeAllMembersByGroupId(id);
            
            // 再删除教学组
            return removeById(id);
        } catch (Exception e) {
            log.error("删除教学组失败", e);
            throw new RuntimeException("删除教学组失败: " + e.getMessage());
        }
    }

    @Override
    public boolean deleteTeachingGroups(List<String> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                return true;
            }

            // 批量删除教学组成员关系
            teachingGroupMemberService.lambdaUpdate()
                    .set(TeachingGroupMember::getDeleted, true)
                    .set(TeachingGroupMember::getUpdateTime, new Date())
                    .in(TeachingGroupMember::getGroupId, ids)
                    .update();

            // 批量删除教学组
            return removeByIds(ids);
        } catch (Exception e) {
            log.error("批量删除教学组失败: ids={}", ids, e);
            throw new RuntimeException("批量删除教学组失败: " + e.getMessage());
        }
    }

    @Override
    public TeachingGroupDto.StatsResp getTeachingGroupStats() {
        try {
            // 查询教学组统计信息
            long totalGroups = lambdaQuery()
                    .eq(TeachingGroup::getDeleted, false)
                    .count();

            long activeGroups = lambdaQuery()
                    .eq(TeachingGroup::getDeleted, false)
                    .eq(TeachingGroup::getStatus, "active")
                    .count();

            TeachingGroupDto.StatsResp resp = new TeachingGroupDto.StatsResp();
            resp.setTotalGroups((int) totalGroups);
            resp.setActiveGroups((int) activeGroups);

            // 如果DTO支持更多字段，可以在这里添加
            // 例如：非活跃组数量、总成员数等

            return resp;
        } catch (Exception e) {
            log.error("查询教学组统计信息失败", e);
            throw new RuntimeException("查询教学组统计信息失败: " + e.getMessage());
        }
    }

    @Override
        public boolean assignTeachers(TeachingGroupDto.AssignTeachersReq req) {
        try {
            // 检查教师是否已在其他教学组
            List<String> assignedTeachers = teachingGroupMemberService.checkAssignedTeachers(req.getTeacherIds());
            if (!assignedTeachers.isEmpty()) {
                throw new RuntimeException("部分教师已分配到其他教学组");
            }
            
            // 添加教师到教学组
            boolean success = teachingGroupMemberService.addTeachersToGroup(req.getGroupId(), req.getTeacherIds());
            
            if (success) {
                // 更新教学组成员数量
                updateMemberCount(req.getGroupId());
            }
            
            return success;
        } catch (Exception e) {
            log.error("分配教师失败", e);
            throw new RuntimeException("分配教师失败: " + e.getMessage());
        }
    }

    @Override
        public boolean removeTeachers(TeachingGroupDto.RemoveTeachersReq req) {
        try {
            boolean success = teachingGroupMemberService.removeTeachersFromGroup(req.getGroupId(), req.getTeacherIds());
            
            if (success) {
                // 更新教学组成员数量
                updateMemberCount(req.getGroupId());
            }
            
            return success;
        } catch (Exception e) {
            log.error("移除教师失败", e);
            throw new RuntimeException("移除教师失败: " + e.getMessage());
        }
    }

    @Override
    public void updateMemberCount(String groupId) {
        // 查询当前教学组的成员数量
        long memberCount = teachingGroupMemberService.lambdaQuery()
                .eq(TeachingGroupMember::getGroupId, groupId)
                .eq(TeachingGroupMember::getDeleted, false)
                .eq(TeachingGroupMember::getStatus, "active")
                .count();

        // 更新教学组的成员数量
        lambdaUpdate()
                .set(TeachingGroup::getMemberCount, (int) memberCount)
                .set(TeachingGroup::getUpdateTime, new Date())
                .eq(TeachingGroup::getId, groupId)
                .update();
    }

    /**
     * 自动添加组长和教务到教学组成员表
     *
     * @param groupId 教学组ID
     * @param leaderId 组长ID
     * @param adminId 教务ID
     */
    private void autoAddLeaderAndAdminToGroup(String groupId, String leaderId, String adminId) {
        try {
            List<TeachingGroupMember> membersToAdd = new ArrayList<>();
            Date now = new Date();

            // 添加组长
            if (StrUtil.isNotEmpty(leaderId)) {
                // 检查组长是否已经在成员表中
                boolean leaderExists = teachingGroupMemberService.lambdaQuery()
                        .eq(TeachingGroupMember::getGroupId, groupId)
                        .eq(TeachingGroupMember::getTeacherId, leaderId)
                        .eq(TeachingGroupMember::getDeleted, false)
                        .exists();

                if (!leaderExists) {
                    TeachingGroupMember leaderMember = new TeachingGroupMember();
                    leaderMember.setId(IdUtil.getSnowflakeNextIdStr());
                    leaderMember.setGroupId(groupId);
                    leaderMember.setTeacherId(leaderId);
                    leaderMember.setRoleType("leader");
                    leaderMember.setJoinTime(now);
                    leaderMember.setStatus("active");
                    leaderMember.setCreateTime(now);
                    leaderMember.setUpdateTime(now);
                    leaderMember.setDeleted(false);

                    membersToAdd.add(leaderMember);
                    log.info("准备添加组长到教学组成员: groupId={}, leaderId={}", groupId, leaderId);
                } else {
                    log.info("组长已存在于教学组成员中: groupId={}, leaderId={}", groupId, leaderId);
                }
            }

            // 添加教务
            if (StrUtil.isNotEmpty(adminId)) {
                // 检查教务是否已经在成员表中
                boolean adminExists = teachingGroupMemberService.lambdaQuery()
                        .eq(TeachingGroupMember::getGroupId, groupId)
                        .eq(TeachingGroupMember::getTeacherId, adminId)
                        .eq(TeachingGroupMember::getDeleted, false)
                        .exists();

                if (!adminExists) {
                    TeachingGroupMember adminMember = new TeachingGroupMember();
                    adminMember.setId(IdUtil.getSnowflakeNextIdStr());
                    adminMember.setGroupId(groupId);
                    adminMember.setTeacherId(adminId);
                    adminMember.setRoleType("admin");
                    adminMember.setJoinTime(now);
                    adminMember.setStatus("active");
                    adminMember.setCreateTime(now);
                    adminMember.setUpdateTime(now);
                    adminMember.setDeleted(false);

                    membersToAdd.add(adminMember);
                    log.info("准备添加教务到教学组成员: groupId={}, adminId={}", groupId, adminId);
                } else {
                    log.info("教务已存在于教学组成员中: groupId={}, adminId={}", groupId, adminId);
                }
            }

            // 批量保存成员
            if (!membersToAdd.isEmpty()) {
                boolean saveResult = teachingGroupMemberService.saveBatch(membersToAdd);
                if (saveResult) {
                    log.info("成功添加组长和教务到教学组成员: groupId={}, 添加数量={}", groupId, membersToAdd.size());
                    // 更新成员数量
                    updateMemberCount(groupId);
                } else {
                    log.error("添加组长和教务到教学组成员失败: groupId={}", groupId);
                }
            }

        } catch (Exception e) {
            log.error("自动添加组长和教务到教学组失败: groupId={}, leaderId={}, adminId={}", groupId, leaderId, adminId, e);
            // 不抛出异常，避免影响教学组创建的主流程
        }
    }

    /**
     * 处理组长和教务的成员关系变更
     *
     * @param groupId 教学组ID
     * @param oldLeaderId 原组长ID
     * @param oldAdminId 原教务ID
     * @param newLeaderId 新组长ID
     * @param newAdminId 新教务ID
     */
    private void handleLeaderAndAdminMembershipChange(String groupId, String oldLeaderId, String oldAdminId,
                                                     String newLeaderId, String newAdminId) {
        try {
            // 处理组长变更
            if (!Objects.equals(oldLeaderId, newLeaderId)) {
                // 移除原组长的leader角色，但保留为普通成员
                if (StrUtil.isNotEmpty(oldLeaderId)) {
                    teachingGroupMemberService.lambdaUpdate()
//                            .set(TeachingGroupMember::getRoleType, "member")
                            .set(TeachingGroupMember::getStatus, "inactive")
                            .set(TeachingGroupMember::getUpdateTime, new Date())
                            .eq(TeachingGroupMember::getGroupId, groupId)
                            .eq(TeachingGroupMember::getTeacherId, oldLeaderId)
                            .eq(TeachingGroupMember::getRoleType, "leader")
                            .eq(TeachingGroupMember::getDeleted, false)
                            .update();
                    log.info("原组长角色已更新为普通成员: groupId={}, oldLeaderId={}", groupId, oldLeaderId);
                }

                // 添加新组长
                if (StrUtil.isNotEmpty(newLeaderId)) {
                    autoAddLeaderAndAdminToGroup(groupId, newLeaderId, null);
                }
            }

            // 处理教务变更
            if (!Objects.equals(oldAdminId, newAdminId)) {
                // 移除原教务的admin角色，但保留为普通成员
                if (StrUtil.isNotEmpty(oldAdminId)) {
                    teachingGroupMemberService.lambdaUpdate()
//                            .set(TeachingGroupMember::getRoleType, "member")
                            .set(TeachingGroupMember::getStatus, "inactive")
                            .set(TeachingGroupMember::getUpdateTime, new Date())
                            .eq(TeachingGroupMember::getGroupId, groupId)
                            .eq(TeachingGroupMember::getTeacherId, oldAdminId)
                            .eq(TeachingGroupMember::getRoleType, "admin")
                            .eq(TeachingGroupMember::getDeleted, false)
                            .update();
                    log.info("原教务角色已更新为普通成员: groupId={}, oldAdminId={}", groupId, oldAdminId);
                }

                // 添加新教务
                if (StrUtil.isNotEmpty(newAdminId)) {
                    autoAddLeaderAndAdminToGroup(groupId, null, newAdminId);
                }
            }

        } catch (Exception e) {
            log.error("处理组长和教务成员关系变更失败: groupId={}", groupId, e);
            // 不抛出异常，避免影响教学组更新的主流程
        }
    }
}
