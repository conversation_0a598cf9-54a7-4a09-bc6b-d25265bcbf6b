package org.nonamespace.word.server.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.nonamespace.word.server.domain.Word;

import java.util.ArrayList;
import java.util.List;

/**
 * 教材单词模型类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TextbookWord {
    private String word;
    private String phonetic;
    private String syllables;
    private List<Word.Sentences> sentences = new ArrayList<>();
    private List<Word.Meanings.Pos> pods = new ArrayList<>();
}
