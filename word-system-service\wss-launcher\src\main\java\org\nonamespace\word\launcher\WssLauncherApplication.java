package org.nonamespace.word.launcher;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.util.Arrays;

@SpringBootApplication(scanBasePackages = {"org.nonamespace.word", "com.ruoyi"}, exclude = { DataSourceAutoConfiguration.class })
@MapperScan({"com.ruoyi.**.mapper", "org.nonamespace.**.mapper"})
//@EnableTransactionManagement
@EnableAspectJAutoProxy
@EnableScheduling
public class WssLauncherApplication {

    public static void main(String[] args) {

        //关闭热部署
        System.setProperty("spring.devtools.restart.enabled","false");
        ConfigurableApplicationContext context = SpringApplication.run(WssLauncherApplication.class, args);
        ConfigurableEnvironment environment = context.getEnvironment();
        String info = """
                
                ----------------------------------------------------------
                应用 '%s' 运行成功! 当前环境 '%s' !!! 端口 '[%s]' !!!
                ----------------------------------------------------------
                
                """;
        System.out.printf((info) + "%n",
                environment.getProperty("spring.application.name"),
                Arrays.toString(environment.getActiveProfiles()),
                environment.getProperty("server.port"));
    }

}
