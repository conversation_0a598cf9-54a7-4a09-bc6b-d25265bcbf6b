package org.nonamespace.word.rest.controller.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.rest.base.controller.BaseController;
import org.nonamespace.word.rest.dto.vo.UserNamePhoneVo;
import org.nonamespace.word.server.domain.Product;
import org.nonamespace.word.server.domain.order.Orders;
import org.nonamespace.word.server.domain.order.OrdersTrx;
import org.nonamespace.word.server.dto.order.OrderExportDto;
import org.nonamespace.word.server.dto.order.OrderPageDto;
import org.nonamespace.word.server.dto.order.OrderRefundDto;
import org.nonamespace.word.server.dto.order.OrderTrxExportDto;
import org.nonamespace.word.server.service.IUserService;
import org.nonamespace.word.server.service.order.IOrdersService;
import org.nonamespace.word.server.service.order.IOrdersTrxService;
import org.nonamespace.word.server.util.ExcelExportUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 订单管理控制器
 * 提供订单管理相关的接口
 */
@Slf4j
@RestController
@RequestMapping("/order-manager")
@RequiredArgsConstructor
public class OrderManagerController extends BaseController {

    private final IOrdersService ordersService;
    private final IOrdersTrxService ordersTrxService;
    private final IUserService userService;

    /**
     * 分页查询订单列表
     */
    @PreAuthorize("@ss.hasPermi('order:manager:list')")
    @GetMapping("/list")
    public TableDataInfo list(OrderPageDto.Req req) {
        log.info("分页查询订单列表请求参数: req={}", req);

        Page<OrderPageDto.Resp> page = ordersService.selectOrdersByParam(req);
        return getDataTable(page);
    }

    /**
     * 获取订单统计信息
     */
    @PreAuthorize("@ss.hasPermi('order:manager:statistics')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            // 总订单数
            long totalOrders = ordersService.count();
            statistics.put("totalOrders", totalOrders);
            
            // 各状态订单数量
            Map<String, Long> statusCount = new HashMap<>();
            statusCount.put("unpaid", ordersService.count(new LambdaQueryWrapper<Orders>().eq(Orders::getOrderStatus, "未支付")));
            statusCount.put("paid", ordersService.count(new LambdaQueryWrapper<Orders>().eq(Orders::getOrderStatus, "已支付")));
            statusCount.put("cancelled", ordersService.count(new LambdaQueryWrapper<Orders>().eq(Orders::getOrderStatus, "已取消")));
            statusCount.put("refunded", ordersService.count(new LambdaQueryWrapper<Orders>().eq(Orders::getOrderStatus, "已退款")));
            statistics.put("statusCount", statusCount);
            
            // 今日订单数
            LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
            LocalDateTime todayEnd = todayStart.plusDays(1);
            long todayOrders = ordersService.count(new LambdaQueryWrapper<Orders>()
                    .ge(Orders::getCreateTime, todayStart)
                    .lt(Orders::getCreateTime, todayEnd));
            statistics.put("todayOrders", todayOrders);
            
            // 本月订单数
            LocalDateTime monthStart = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
            long monthOrders = ordersService.count(new LambdaQueryWrapper<Orders>()
                    .ge(Orders::getCreateTime, monthStart));
            statistics.put("monthOrders", monthOrders);
            
            // 总交易金额（已支付订单）
            List<Orders> paidOrders = ordersService.list(new LambdaQueryWrapper<Orders>()
                    .eq(Orders::getOrderStatus, "已支付"));
            BigDecimal totalAmount = paidOrders.stream()
                    .map(order -> BigDecimal.valueOf(order.getTotalAmt()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            statistics.put("totalAmount", totalAmount.divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP));
            
            return AjaxResult.success(statistics);
        } catch (Exception e) {
            log.error("获取订单统计信息失败", e);
            return error("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 批量取消订单
     */
    @PreAuthorize("@ss.hasPermi('order:manager:batchCancel')")
    @Log(title = "批量取消订单", businessType = BusinessType.UPDATE)
    @PutMapping("/batch-cancel")
    public AjaxResult batchCancel(@RequestBody List<String> orderIds) {
        try {
            if (CollUtil.isEmpty(orderIds)) {
                return error("请选择要取消的订单");
            }
            
            int successCount = 0;
            int failCount = 0;
            StringBuilder errorMsg = new StringBuilder();
            
            for (String orderId : orderIds) {
                try {
                    ordersService.cancelOrder(orderId);
                    successCount++;
                } catch (Exception e) {
                    failCount++;
                    errorMsg.append("订单").append(orderId).append("取消失败: ").append(e.getMessage()).append("; ");
                    log.error("取消订单{}失败", orderId, e);
                }
            }
            
            String resultMsg = String.format("批量取消完成，成功%d个，失败%d个", successCount, failCount);
            if (failCount > 0) {
                resultMsg += "。失败原因: " + errorMsg.toString();
            }
            
            return AjaxResult.success(resultMsg);
        } catch (Exception e) {
            log.error("批量取消订单失败", e);
            return error("批量取消订单失败: " + e.getMessage());
        }
    }

    /**
     * 获取订单详情（管理员视图）
     */
    @PreAuthorize("@ss.hasPermi('order:manager:detail')")
    @GetMapping("/{orderId}/detail")
    public AjaxResult getOrderDetail(@PathVariable String orderId) {
        try {
            Orders order = ordersService.getById(orderId);
            if (order == null) {
                return error("订单不存在");
            }

            Map<String, Object> result = new HashMap<>();

            // 获取用户信息
            List<SysUser> users = userService.lambdaQuery().in(SysUser::getUserId, List.of(order.getSalerId(), order.getStudentId())).list();
            // 获取学生信息
            Map<String, SysUser> userMap = users.stream().collect(Collectors.toMap(k -> String.valueOf(k.getUserId()), v -> v));
            if(userMap.containsKey(order.getStudentId())) {
                SysUser sysUser = userMap.get(order.getStudentId());
                UserNamePhoneVo studentVo = new UserNamePhoneVo(sysUser.getUserName(), sysUser.getNickName(), sysUser.getPhonenumber());
                result.put("student", studentVo);
            }
            if(userMap.containsKey(order.getSalerId())) {
                SysUser sysUser = userMap.get(order.getSalerId());
                UserNamePhoneVo salerVo = new UserNamePhoneVo(sysUser.getUserName(), sysUser.getNickName(), sysUser.getPhonenumber());
                result.put("saler", salerVo);
            }

            // 获取产品信息
            Product product = order.getProducts();
            result.put("product", product);
            
            // 获取交易流水
            List<OrdersTrx> transactions = ordersTrxService.getByOrderId(orderId);

            result.put("order", order);
            result.put("transactions", transactions);
            
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("获取订单详情失败", e);
            return error("获取订单详情失败: " + e.getMessage());
        }
    }

    /**
     * 强制更新订单状态（管理员权限）
     */
    @PreAuthorize("@ss.hasPermi('order:manager:updateStatus')")
    @Log(title = "强制更新订单状态", businessType = BusinessType.UPDATE)
    @PutMapping("/{orderId}/status")
    public AjaxResult updateOrderStatus(@PathVariable String orderId, 
                                       @RequestParam String orderStatus,
                                       @RequestParam(required = false) String remark) {
        try {
            Orders order = ordersService.getById(orderId);
            if (order == null) {
                return error("订单不存在");
            }
            
            String oldStatus = order.getOrderStatus();
            order.setOrderStatus(orderStatus);
            if (StrUtil.isNotBlank(remark)) {
                order.setRemark(remark);
            }
            
            boolean updated = ordersService.updateById(order);
            if (updated) {
                log.info("管理员强制更新订单状态: orderId={}, oldStatus={}, newStatus={}, remark={}", 
                        orderId, oldStatus, orderStatus, remark);
                return AjaxResult.success("订单状态更新成功");
            } else {
                return error("订单状态更新失败");
            }
        } catch (Exception e) {
            log.error("强制更新订单状态失败", e);
            return error("更新订单状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取订单趋势数据（最近7天）
     */
    @PreAuthorize("@ss.hasPermi('order:manager:trend')")
    @GetMapping("/trend")
    public AjaxResult getOrderTrend() {
        try {
            Map<String, Object> trendData = new HashMap<>();
            
            // 最近7天的订单数量趋势
            LocalDateTime now = LocalDateTime.now();
            Map<String, Long> dailyOrders = new HashMap<>();
            
            for (int i = 6; i >= 0; i--) {
                LocalDateTime dayStart = now.minusDays(i).withHour(0).withMinute(0).withSecond(0).withNano(0);
                LocalDateTime dayEnd = dayStart.plusDays(1);
                
                long dayCount = ordersService.count(new LambdaQueryWrapper<Orders>()
                        .ge(Orders::getCreateTime, dayStart)
                        .lt(Orders::getCreateTime, dayEnd));
                
                String dateKey = dayStart.toLocalDate().toString();
                dailyOrders.put(dateKey, dayCount);
            }
            
            trendData.put("dailyOrders", dailyOrders);
            
            return AjaxResult.success(trendData);
        } catch (Exception e) {
            log.error("获取订单趋势数据失败", e);
            return error("获取趋势数据失败: " + e.getMessage());
        }
    }

    /**
     * 管理员订单退款（管理员权限）
     */
    @PreAuthorize("@ss.hasPermi('order:manager:refund')")
    @Log(title = "管理员订单退款", businessType = BusinessType.UPDATE)
    @PostMapping("/{orderId}/refund")
    public AjaxResult managerRefundOrder(@PathVariable String orderId, @RequestBody OrderRefundDto.RefundReq refundReq) {
        try {
            // 验证订单ID一致性
            if (!orderId.equals(refundReq.getOrderId())) {
                return error("订单ID不匹配");
            }

            ordersService.refundOrder(orderId, refundReq.getRefundAmount(), refundReq.getRefundReason());

            OrderRefundDto.RefundResp resp = new OrderRefundDto.RefundResp();
            resp.setOrderId(orderId);
            resp.setRefundAmount(refundReq.getRefundAmount());
            resp.setRefundReason(refundReq.getRefundReason());
            resp.setMessage("管理员订单退款成功");

            return AjaxResult.success("管理员订单退款成功", resp);
        } catch (Exception e) {
            log.error("管理员订单退款失败", e);
            return error("管理员订单退款失败: " + e.getMessage());
        }
    }

    /**
     * 管理员全额退款（管理员权限）
     */
    @PreAuthorize("@ss.hasPermi('order:manager:refund')")
    @Log(title = "管理员订单全额退款", businessType = BusinessType.UPDATE)
    @PostMapping("/{orderId}/full-refund")
    public AjaxResult managerFullRefundOrder(@PathVariable String orderId, @RequestBody OrderRefundDto.FullRefundReq refundReq) {
        try {
            // 验证订单ID一致性
            if (!orderId.equals(refundReq.getOrderId())) {
                return error("订单ID不匹配");
            }

            ordersService.fullRefundOrder(orderId, refundReq.getRefundReason());

            OrderRefundDto.RefundResp resp = new OrderRefundDto.RefundResp();
            resp.setOrderId(orderId);
            resp.setRefundReason(refundReq.getRefundReason());
            resp.setMessage("管理员订单全额退款成功");

            return AjaxResult.success("管理员订单全额退款成功", resp);
        } catch (Exception e) {
            log.error("管理员订单全额退款失败", e);
            return error("管理员订单全额退款失败: " + e.getMessage());
        }
    }

    /**
     * 管理员导出订单数据（管理员权限）
     */
    @PreAuthorize("@ss.hasPermi('order:manager:export')")
    @Log(title = "管理员导出订单数据", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void managerExportOrders(OrderExportDto.ExportReq req, HttpServletResponse response) {
        try {
            log.info("管理员开始导出订单数据: req={}", req);

            // 获取导出数据
            List<OrderExportDto.ExportResp> exportData = ordersService.exportOrders(req);

            if (exportData.isEmpty()) {
                response.setStatus(HttpServletResponse.SC_NO_CONTENT);
                return;
            }

            // 生成文件名
            String fileName = "订单数据_管理员导出_" + System.currentTimeMillis() + ".xlsx";

            // 创建表头映射
            LinkedHashMap<String, String> headers = ExcelExportUtil.createOrderExportHeaders();

            // 导出Excel
            ExcelExportUtil.exportToResponse(response, fileName, "订单数据", headers, exportData);

            log.info("管理员订单数据导出成功: fileName={}, count={}", fileName, exportData.size());

        } catch (Exception e) {
            log.error("管理员导出订单数据失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 管理员导出交易流水数据（管理员权限）
     */
    @PreAuthorize("@ss.hasPermi('order:manager:trx:export')")
    @Log(title = "管理员导出交易流水数据", businessType = BusinessType.EXPORT)
    @GetMapping("/trx/export")
    public void managerExportOrderTrx(OrderTrxExportDto.ExportReq req, HttpServletResponse response) {
        try {
            log.info("管理员开始导出交易流水数据: req={}", req);

            // 获取导出数据
            List<OrderTrxExportDto.ExportResp> exportData = ordersTrxService.exportOrderTrx(req);

            if (exportData.isEmpty()) {
                response.setStatus(HttpServletResponse.SC_NO_CONTENT);
                return;
            }

            // 生成文件名
            String fileName = "交易流水_管理员导出_" + System.currentTimeMillis() + ".xlsx";

            // 创建表头映射
            LinkedHashMap<String, String> headers = ExcelExportUtil.createOrderTrxExportHeaders();

            // 导出Excel
            ExcelExportUtil.exportToResponse(response, fileName, "交易流水", headers, exportData);

            log.info("管理员交易流水数据导出成功: fileName={}, count={}", fileName, exportData.size());

        } catch (Exception e) {
            log.error("管理员导出交易流水数据失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }
}
