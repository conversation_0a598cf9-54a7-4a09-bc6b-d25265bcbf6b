package org.nonamespace.word.server.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel
public class WssR<T> {


    public static final String DEF_ERROR_MESSAGE = "系统繁忙，请稍候再试";
    public static final String HYSTRIX_ERROR_MESSAGE = "当前系统访问人数较多，请求超时，请稍候再试";
    public static final String SESSION_INVALIDATE_MESSAGE = "用户信息失效，请重新登录";
    public static final String SIGNATURE_INVALIDATE_MESSAGE = "签名校验未通过";

    public static final String ILLEGAL_AUTH_MESSAGE = "非法授权！";
    public static final String VISIT_LIMIT_MESSAGE = "当前请求过于频繁，请稍候再试！";
    public static final String INVALID_PAEAMETER = "无效参数，请先进行校验";
    public static final String NULL_OR_DELETE = "该类型的数据不存在或已删除";
    public static final String BEIJIAN_ERROR_MESSAGE = "当前系统访问人数较多，请稍后重试";



    public static final int SUCCESS_CODE = 200;

    public static final int ERROR_CODE = 500;

    public static final int SESSION_INVALIDATE = 401;
    public static final int FAIL_CODE = 500;
    public static final int TIMEOUT_CODE = 500;
    public static final int ILLEGAL_AUTH  = 403;

    /**
     * 限流处理，请求过于频繁
     */
    public static final int VISIT_LIMIT  = 429;


    public static final WssR<Boolean> R_SUCCESS = new WssR<>();

    /**
     * 调用是否成功标识，0：成功，-1:系统繁忙
     */
    @ApiModelProperty("200 -正常返回 500 -服务器异常 40029 -企业微信code过期/非法")
    private int code = SUCCESS_CODE;

    /**
     * 调用结果
     */
    private T data;


    private String message = "ok";

    /**
     * 响应时间
     */
    private long timestamp = System.currentTimeMillis();

    private WssR() {
        super();
    }

    public WssR(int code, T data, String message) {
        this.code = code;
        this.data = data;
        this.message = message;
    }

    public static <E> WssR<E> result(int code, E data, String msg) {
        return new WssR<>(code, data, msg);
    }

    /**
     * 请求成功消息
     *
     * @param data 结果
     * @return RPC调用结果
     */
    public static <E> WssR<E> success(E data) {
        return new WssR<>(SUCCESS_CODE, data, "ok");
    }


    public static WssR<Boolean> success() {
        return R_SUCCESS;
    }

    /**
     * 请求成功方法 ，data返回值，msg提示信息
     *
     * @param data 结果
     * @param msg  消息
     * @return RPC调用结果
     */
    public static <E> WssR<E> success(E data, String msg) {
        return new WssR<>(SUCCESS_CODE, data, msg);
    }

    /**
     * 请求失败消息
     *
     * @param msg 消息
     * @return 调用结果
     */
    public static <E> WssR<E> fail(int code, String msg) {
        return new WssR<>(code, null, (msg == null || msg.isEmpty()) ? DEF_ERROR_MESSAGE : msg);
    }

    public static <E> WssR<E> fail(String msg) {
        return new WssR<>(FAIL_CODE, null, (msg == null || msg.isEmpty()) ? DEF_ERROR_MESSAGE : msg);
    }

    /**
     * 请求失败消息，根据异常类型，获取不同的提供消息
     *
     * @param throwable 异常
     * @return RPC调用结果
     */
    public static <E> WssR<E> fail(Throwable throwable) {
        return fail(FAIL_CODE, throwable != null ? throwable.getMessage() : DEF_ERROR_MESSAGE);
    }


    /**
     * 逻辑处理是否成功
     *
     * @return 是否成功
     */
    @JsonIgnore
    public Boolean getIsSuccess() {
        return this.code == SUCCESS_CODE;
    }

    /**
     * 逻辑处理是否失败
     */
    @JsonIgnore
    public Boolean getIsError() {
        return !getIsSuccess();
    }
}
