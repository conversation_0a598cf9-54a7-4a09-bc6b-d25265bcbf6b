package org.nonamespace.word.server.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.constants.OrderConstants;
import org.nonamespace.word.server.domain.order.OrdersTrx;
import org.nonamespace.word.server.dto.order.OrderTrxExportDto;
import org.nonamespace.word.server.mapper.order.OrdersTrxMapper;
import org.nonamespace.word.server.service.base.BaseService;
import org.nonamespace.word.server.service.order.IOrdersTrxService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单交易记录表Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class OrdersTrxServiceImpl extends BaseService<OrdersTrxMapper, OrdersTrx> implements IOrdersTrxService {

    @Override
    public List<OrdersTrx> getByOrderId(String orderId) {
        return this.lambdaQuery()
                .eq(OrdersTrx::getOrderId, orderId)
                .orderByAsc(OrdersTrx::getTrxIdx)
                .list();
    }

    @Override
    public List<OrdersTrx> getByOrderNo(String orderNo) {
        return this.lambdaQuery()
                .eq(OrdersTrx::getOrderNo, orderNo)
                .orderByDesc(OrdersTrx::getCreateTime)
                .list();
    }

    @Override
    public OrdersTrx getByCusTrxSeq(String cusTrxSeq) {
        return this.lambdaQuery()
                .eq(OrdersTrx::getCusTrxSeq, cusTrxSeq)
                .one();
    }

    @Override
    public List<OrdersTrx> getByTrxType(String trxType) {
        return this.lambdaQuery()
                .eq(OrdersTrx::getTrxType, trxType)
                .orderByDesc(OrdersTrx::getCreateTime)
                .list();
    }

    @Override
    public List<OrdersTrx> getByTrxStatus(String trxStatus) {
        return this.lambdaQuery()
                .eq(OrdersTrx::getTrxStatus, trxStatus)
                .orderByDesc(OrdersTrx::getCreateTime)
                .list();
    }

    @Override
    public List<OrdersTrx> getByPayType(String payType) {
        return this.lambdaQuery()
                .eq(OrdersTrx::getPayType, payType)
                .orderByDesc(OrdersTrx::getCreateTime)
                .list();
    }

    @Override
    public List<OrderTrxExportDto.ExportResp> exportOrderTrx(OrderTrxExportDto.ExportReq req) {
        log.info("开始导出交易流水数据: req={}", req);

        // 构建查询条件
        LambdaQueryWrapper<OrdersTrx> queryWrapper = buildTrxExportQuery(req);

        // 限制导出数量
        int maxCount = req.getMaxExportCount() != null ? req.getMaxExportCount() : 10000;
        queryWrapper.last("LIMIT " + maxCount);

        // 查询交易流水数据
        List<OrdersTrx> trxList = this.list(queryWrapper);

        if (CollUtil.isEmpty(trxList)) {
            log.info("没有符合条件的交易流水数据");
            return new ArrayList<>();
        }

        log.info("查询到交易流水数据: count={}", trxList.size());

        // 转换为导出格式
        List<OrderTrxExportDto.ExportResp> exportData = new ArrayList<>();
        for (OrdersTrx trx : trxList) {
            OrderTrxExportDto.ExportResp exportResp = convertToTrxExportResp(trx, req);
            exportData.add(exportResp);
        }

        log.info("交易流水数据导出完成: exportCount={}", exportData.size());
        return exportData;
    }

    /**
     * 构建交易流水导出查询条件
     */
    private LambdaQueryWrapper<OrdersTrx> buildTrxExportQuery(OrderTrxExportDto.ExportReq req) {
        LambdaQueryWrapper<OrdersTrx> queryWrapper = new LambdaQueryWrapper<>();

        // 基本查询条件
        queryWrapper.eq(StrUtil.isNotBlank(req.getOrderNo()), OrdersTrx::getOrderNo, req.getOrderNo())
                .eq(StrUtil.isNotBlank(req.getOrderId()), OrdersTrx::getOrderId, req.getOrderId())
                .eq(StrUtil.isNotBlank(req.getCusTrxSeq()), OrdersTrx::getCusTrxSeq, req.getCusTrxSeq())
                .eq(StrUtil.isNotBlank(req.getTrxType()), OrdersTrx::getTrxType, req.getTrxType())
                .eq(StrUtil.isNotBlank(req.getTrxStatus()), OrdersTrx::getTrxStatus, req.getTrxStatus())
                .eq(StrUtil.isNotBlank(req.getPayType()), OrdersTrx::getPayType, req.getPayType())
                .eq(req.getTrxIdx() != null, OrdersTrx::getTrxIdx, req.getTrxIdx())
                .eq(req.getTrxId() != null, OrdersTrx::getTrxId, req.getTrxId())
                .ge(req.getCreateTimeStart() != null, OrdersTrx::getCreateTime, req.getCreateTimeStart())
                .le(req.getCreateTimeEnd() != null, OrdersTrx::getCreateTime, req.getCreateTimeEnd())
                .ge(req.getMinTrxAmt() != null, OrdersTrx::getTrxAmt, req.getMinTrxAmt())
                .le(req.getMaxTrxAmt() != null, OrdersTrx::getTrxAmt, req.getMaxTrxAmt());

        // 排序
        String orderBy = StrUtil.isNotBlank(req.getOrderBy()) ? req.getOrderBy() : "create_time";
        String orderDirection = StrUtil.isNotBlank(req.getOrderDirection()) ? req.getOrderDirection() : "DESC";

        if ("ASC".equalsIgnoreCase(orderDirection)) {
            queryWrapper.orderByAsc(OrdersTrx::getCreateTime);
        } else {
            queryWrapper.orderByDesc(OrdersTrx::getCreateTime);
        }

        return queryWrapper;
    }

    /**
     * 转换为交易流水导出响应对象
     */
    private OrderTrxExportDto.ExportResp convertToTrxExportResp(OrdersTrx trx, OrderTrxExportDto.ExportReq req) {
        OrderTrxExportDto.ExportResp resp = new OrderTrxExportDto.ExportResp();

        // 基本交易信息
        resp.setTrxId(trx.getId());
        resp.setOrderNo(trx.getOrderNo());
        resp.setOrderId(trx.getOrderId());
        resp.setCusTrxSeq(trx.getCusTrxSeq());
        resp.setTrxIdx(trx.getTrxIdx());
        resp.setTrxType(trx.getTrxType());
        resp.setTrxTypeDesc(getTrxTypeDesc(trx.getTrxType()));
        resp.setTrxStatus(trx.getTrxStatus());
        resp.setTrxStatusDesc(getTrxStatusDesc(trx.getTrxStatus()));
        resp.setPayType(trx.getPayType());
        resp.setPayTypeDesc(getPayTypeDesc(trx.getPayType()));
        resp.setTrxAmtYuan(formatAmountToYuan(trx.getTrxAmt()));
        resp.setPlatformTrxId(trx.getTrxId());
        resp.setCreateTime(trx.getCreateTime());
        resp.setUpdateTime(trx.getUpdateTime());
        resp.setCreateBy(trx.getCreateBy());
        resp.setUpdateBy(trx.getUpdateBy());

        // 如果需要包含订单详情，这里可以查询订单信息
        // TODO: 优化为批量查询以提高性能
        if (req.getIncludeOrderDetails() != null && req.getIncludeOrderDetails()) {
            // 这里需要注入订单服务来获取订单详情
            // 暂时设置基本信息
            resp.setOrderBody("订单标题");
            resp.setOrderStatus("订单状态");
            resp.setOrderTotalAmtYuan("0.00");
        }

        // 如果需要包含学生信息
        if (req.getIncludeStudentInfo() != null && req.getIncludeStudentInfo()) {
            // 这里需要通过订单ID查询学生信息
            resp.setStudentName("学生姓名");
            resp.setStudentPhone("学生手机号");
            resp.setSalerName("销售员姓名");
            resp.setSalerPhone("销售员手机号");
        }

        return resp;
    }

    /**
     * 获取交易类型描述
     */
    private String getTrxTypeDesc(String trxType) {
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put(OrderConstants.TrxType.PAY, "支付");
        typeMap.put(OrderConstants.TrxType.REFUND, "退款");
        typeMap.put(OrderConstants.TrxType.CANCEL, "撤销");
        return typeMap.getOrDefault(trxType, trxType);
    }

    /**
     * 获取交易状态描述
     */
    private String getTrxStatusDesc(String trxStatus) {
        Map<String, String> statusMap = new HashMap<>();
        statusMap.put(OrderConstants.TrxStatus.UNPAID, "未付款");
        statusMap.put(OrderConstants.TrxStatus.PAID, "已付款");
        statusMap.put(OrderConstants.TrxStatus.REFUNDED, "已退款");
        statusMap.put(OrderConstants.TrxStatus.CANCELLED, "已取消");
        statusMap.put(OrderConstants.TrxStatus.PROCESSING, "处理中");
        statusMap.put(OrderConstants.TrxStatus.FAILED, "失败");
        return statusMap.getOrDefault(trxStatus, trxStatus);
    }

    /**
     * 获取支付类型描述
     */
    private String getPayTypeDesc(String payType) {
        if (StrUtil.isBlank(payType)) {
            return "";
        }
        Map<String, String> payTypeMap = new HashMap<>();
        payTypeMap.put("alipay", "支付宝");
        payTypeMap.put("wechat", "微信支付");
        payTypeMap.put("unionpay", "银联支付");
        payTypeMap.put("cash", "现金");
        payTypeMap.put("transfer", "转账");
        return payTypeMap.getOrDefault(payType, payType);
    }

    /**
     * 格式化金额为元（保留2位小数）
     */
    private String formatAmountToYuan(Long amountFen) {
        if (amountFen == null) {
            return "0.00";
        }
        return String.format("%.2f", amountFen / 100.0);
    }

}