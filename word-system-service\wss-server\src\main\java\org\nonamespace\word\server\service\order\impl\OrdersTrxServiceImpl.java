package org.nonamespace.word.server.service.order.impl;

import org.nonamespace.word.server.domain.order.OrdersTrx;
import org.nonamespace.word.server.mapper.order.OrdersTrxMapper;
import org.nonamespace.word.server.service.base.BaseService;
import org.nonamespace.word.server.service.order.IOrdersTrxService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 订单交易记录表Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class OrdersTrxServiceImpl extends BaseService<OrdersTrxMapper, OrdersTrx> implements IOrdersTrxService {

    @Override
    public List<OrdersTrx> getByOrderId(String orderId) {
        return this.lambdaQuery()
                .eq(OrdersTrx::getOrderId, orderId)
                .orderByDesc(OrdersTrx::getCreateTime)
                .list();
    }

    @Override
    public List<OrdersTrx> getByOrderNo(String orderNo) {
        return this.lambdaQuery()
                .eq(OrdersTrx::getOrderNo, orderNo)
                .orderByDesc(OrdersTrx::getCreateTime)
                .list();
    }

    @Override
    public OrdersTrx getByCusTrxSeq(String cusTrxSeq) {
        return this.lambdaQuery()
                .eq(OrdersTrx::getCusTrxSeq, cusTrxSeq)
                .one();
    }

    @Override
    public List<OrdersTrx> getByTrxType(String trxType) {
        return this.lambdaQuery()
                .eq(OrdersTrx::getTrxType, trxType)
                .orderByDesc(OrdersTrx::getCreateTime)
                .list();
    }

    @Override
    public List<OrdersTrx> getByTrxStatus(String trxStatus) {
        return this.lambdaQuery()
                .eq(OrdersTrx::getTrxStatus, trxStatus)
                .orderByDesc(OrdersTrx::getCreateTime)
                .list();
    }

    @Override
    public List<OrdersTrx> getByPayType(String payType) {
        return this.lambdaQuery()
                .eq(OrdersTrx::getPayType, payType)
                .orderByDesc(OrdersTrx::getCreateTime)
                .list();
    }

}