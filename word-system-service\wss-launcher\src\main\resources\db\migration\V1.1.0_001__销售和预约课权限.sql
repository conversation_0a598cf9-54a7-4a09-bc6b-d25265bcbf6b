-- =====================================================
-- 销售管理权限配置SQL脚本
-- 基于若依框架RBAC权限模型
-- 包含：角色配置、菜单配置、权限关联配置
-- =====================================================

-- 创建销售中心部门（如果不存在）
INSERT INTO sys_dept (dept_id, parent_id, ancestors, dept_name, order_num, status, del_flag, create_by, create_time)
SELECT nextval('sys_dept_dept_id_seq'), 100, '0,100', '销售中心', 4, '0', '0', 'admin', now()
    WHERE NOT EXISTS (SELECT 1 FROM sys_dept WHERE dept_name = '销售中心');

-- =====================================================
-- 1. 角色配置 (sys_role)
-- =====================================================

UPDATE words.sys_role
SET role_key='teaching_group_leader'
WHERE role_key='teacher-group-leader';

UPDATE words.sys_role
SET role_key='teaching_group_admin'
WHERE role_key='teacher-group-admin';


-- 新增销售总监角色
INSERT INTO words.sys_role (role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark) 
VALUES (9, '销售总监', 'sales_director', 10, '1', true, true, '0', '0', 'admin', NOW(), '销售总监，管理所有销售组和销售人员');

-- 新增销售组长角色  
INSERT INTO words.sys_role (role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark)
VALUES (10, '销售组长', 'sales_group_leader', 11, '4', true, true, '0', '0', 'admin', NOW(), '销售组长，管理本组销售人员和学生');

-- 更新现有销售角色的配置
UPDATE words.sys_role 
SET role_key='sales', role_sort = 12, data_scope = '5', remark = '销售人员，管理自己的学生和申请预约课'
WHERE role_key = 'sale';

-- =====================================================
-- 2. 菜单配置 (sys_menu) 
-- =====================================================

-- 2.1 销售管理主菜单
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3000, '销售管理', 0, 5, 'sales-management', NULL, '', 1, 0, 'M', '0', 0, '', 'money', 'admin', NOW(), '销售管理主菜单');

-- 2.2 销售组管理菜单
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3001, '销售组管理', 3000, 1, 'sales-group', 'management/sales-group/index', '', 1, 0, 'C', '0', 0, 'sales:group:list', 'peoples', 'admin', NOW(), '销售组管理页面');

-- 2.3 销售人员管理菜单
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3002, '销售人员管理', 3000, 2, 'sales-staff', 'management/sales-staff/index', '', 1, 0, 'C', '0', 0, 'sales:staff:list', 'user', 'admin', NOW(), '销售人员管理页面');

-- 2.4 销售学生管理菜单
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3003, '销售学生管理', 3000, 3, 'sales-student', 'management/sales-student/index', '', 1, 0, 'C', '0', 0, 'sales:student:list', 'education', 'admin', NOW(), '销售学生管理页面');

-- 2.5 预约课审核菜单
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3004, '预约课审核', 3000, 4, 'course-booking', 'management/course-booking/index', '', 1, 0, 'C', '0', 0, 'course:booking:review', 'eye-open', 'admin', NOW(), '预约课审核页面');

-- =====================================================
-- 3. 销售组管理权限按钮 (menu_type = 'F')
-- =====================================================

-- 销售组查询
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
VALUES (3011, '销售组查询', 3001, 1, '', '', '', 1, 0, 'F', '0', 0, 'sales:group:query', '#', 'admin', NOW());

-- 销售组新增
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
VALUES (3012, '销售组新增', 3001, 2, '', '', '', 1, 0, 'F', '0', 0, 'sales:group:add', '#', 'admin', NOW());

-- 销售组修改
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
VALUES (3013, '销售组修改', 3001, 3, '', '', '', 1, 0, 'F', '0', 0, 'sales:group:edit', '#', 'admin', NOW());

-- 销售组删除
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
VALUES (3014, '销售组删除', 3001, 4, '', '', '', 1, 0, 'F', '0', 0, 'sales:group:remove', '#', 'admin', NOW());

-- 销售组成员
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
VALUES (3015, '销售组成员', 3001, 4, '', '', '', 1, 0, 'F', '0', 0, 'sales:group:member', '#', 'admin', NOW());


-- =====================================================
-- 4. 销售人员管理权限按钮
-- =====================================================

-- 销售人员查询
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
VALUES (3021, '销售人员查询', 3002, 1, '', '', '', 1, 0, 'F', '0', 0, 'sales:staff:query', '#', 'admin', NOW());

-- 销售人员新增
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
VALUES (3022, '销售人员新增', 3002, 2, '', '', '', 1, 0, 'F', '0', 0, 'sales:staff:add', '#', 'admin', NOW());

-- 销售人员修改
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
VALUES (3023, '销售人员修改', 3002, 3, '', '', '', 1, 0, 'F', '0', 0, 'sales:staff:edit', '#', 'admin', NOW());

-- 销售人员删除
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
VALUES (3024, '销售人员删除', 3002, 4, '', '', '', 1, 0, 'F', '0', 0, 'sales:staff:remove', '#', 'admin', NOW());


-- =====================================================
-- 5. 销售学生管理权限按钮
-- =====================================================

-- 销售学生查询
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
VALUES (3031, '销售学生查询', 3003, 1, '', '', '', 1, 0, 'F', '0', 0, 'sales:student:query', '#', 'admin', NOW());

-- 销售学生新增
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
VALUES (3032, '销售学生新增', 3003, 2, '', '', '', 1, 0, 'F', '0', 0, 'sales:student:add', '#', 'admin', NOW());

-- 销售学生修改
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
VALUES (3033, '销售学生修改', 3003, 3, '', '', '', 1, 0, 'F', '0', 0, 'sales:student:edit', '#', 'admin', NOW());

-- 销售学生删除
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
VALUES (3034, '销售学生删除', 3003, 4, '', '', '', 1, 0, 'F', '0', 0, 'sales:student:remove', '#', 'admin', NOW());

-- 学生分配销售
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
VALUES (3035, '学生分配销售', 3003, 5, '', '', '', 1, 0, 'F', '0', 0, 'sales:student:assign', '#', 'admin', NOW());

-- 销售学生导出
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
VALUES (3036, '销售学生导出', 3003, 6, '', '', '', 1, 0, 'F', '0', 0, 'sales:student:export', '#', 'admin', NOW());

-- 销售学生导入
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
VALUES (3037, '销售学生导入', 3003, 7, '', '', '', 1, 0, 'F', '0', 0, 'sales:student:import', '#', 'admin', NOW());

-- 销售学生预约课申请
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
VALUES (3038, '试听课申请', 3003, 8, '', '', '', 1, 0, 'F', '0', 0, 'sales:student:booking', '#', 'admin', NOW());


-- =====================================================
-- 6. 预约课审核权限按钮
-- =====================================================

-- 预约课查询
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
VALUES (3041, '预约课查询', 3004, 1, '', '', '', 1, 0, 'F', '0', 0, 'course:booking:query', '#', 'admin', NOW());

-- 预约课审核
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
VALUES (3042, '预约课审核', 3004, 2, '', '', '', 1, 0, 'F', '0', 0, 'course:booking:review', '#', 'admin', NOW());



-- 预约课审核
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (2080, '预约课审核', 2037, 4, 'course-booking', 'management/course-booking/index', '', 1, 0, 'C', '0', 0, 'course:booking:review', 'eye-open', 'admin', NOW(), '预约课审核页面');


-- 预约课查询
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
VALUES (2081, '预约课查询', 2080, 1, '', '', '', 1, 0, 'F', '0', 0, 'course:booking:query', '#', 'admin', NOW());

-- 预约课审核
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
VALUES (2082, '预约课审核', 2080, 2, '', '', '', 1, 0, 'F', '0', 0, 'course:booking:review', '#', 'admin', NOW());


INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
VALUES
    (3051, '老师导入', 2038, 5, '', '', '', 1, 0, 'F', '0', '0', 'management:teacher:import', '#', 'admin', NOW()),
    (3052, '老师导出', 2038, 6, '', '', '', 1, 0, 'F', '0', '0', 'management:teacher:export', '#', 'admin', NOW());
-- -- =====================================================
-- -- 7. 角色菜单关联配置 (sys_role_menu)
-- -- =====================================================

-- -- 7.1 超级管理员 (role_id=1) - 拥有所有销售管理权限
-- INSERT INTO words.sys_role_menu (role_id, menu_id) VALUES
-- (1, 3000), (1, 3001), (1, 3002), (1, 3003), (1, 3004),
-- (1, 3011), (1, 3012), (1, 3013), (1, 3014), (1, 3015),
-- (1, 3021), (1, 3022), (1, 3023), (1, 3024), (1, 3025), (1, 3026),
-- (1, 3031), (1, 3032), (1, 3033), (1, 3034), (1, 3035), (1, 3036), (1, 3037), (1, 3038),
-- (1, 3041), (1, 3042), (1, 3043), (1, 3044), (1, 3045);

-- -- 7.2 人力 (role_id=7) - 拥有销售组和销售人员管理权限
-- INSERT INTO words.sys_role_menu (role_id, menu_id) VALUES
-- (7, 3000), (7, 3001), (7, 3002), (7, 3003),
-- (7, 3011), (7, 3012), (7, 3013), (7, 3014), (7, 3015),
-- (7, 3021), (7, 3022), (7, 3023), (7, 3024), (7, 3025), (7, 3026),
-- (7, 3031), (7, 3032), (7, 3033), (7, 3034), (7, 3035), (7, 3036), (7, 3037), (7, 3038);

-- -- 7.3 销售总监 (role_id=9) - 拥有所有销售管理权限
-- INSERT INTO words.sys_role_menu (role_id, menu_id) VALUES
-- (9, 3000), (9, 3001), (9, 3002), (9, 3003), (9, 3004),
-- (9, 3011), (9, 3012), (9, 3013), (9, 3014), (9, 3015),
-- (9, 3021), (9, 3022), (9, 3023), (9, 3024), (9, 3025), (9, 3026),
-- (9, 3031), (9, 3032), (9, 3033), (9, 3034), (9, 3035), (9, 3036), (9, 3037), (9, 3038),
-- (9, 3041), (9, 3042), (9, 3043), (9, 3044), (9, 3045);

-- -- 7.4 销售组长 (role_id=10) - 拥有销售学生管理和预约课审核权限
-- INSERT INTO words.sys_role_menu (role_id, menu_id) VALUES
-- (10, 3000), (10, 3003), (10, 3004),
-- (10, 3031), (10, 3032), (10, 3033), (10, 3034), (10, 3035), (10, 3036), (10, 3037), (10, 3038),
-- (10, 3041), (10, 3042), (10, 3043), (10, 3044), (10, 3045);

-- -- 7.5 销售 (role_id=8) - 仅拥有销售学生管理权限（查看和基本操作）
-- INSERT INTO words.sys_role_menu (role_id, menu_id) VALUES
-- (8, 3000), (8, 3003),
-- (8, 3031), (8, 3032), (8, 3033), (8, 3038);

-- -- 7.6 教学组长 (role_id=5) - 拥有预约课审核权限
-- INSERT INTO words.sys_role_menu (role_id, menu_id) VALUES
-- (5, 3000), (5, 3004),
-- (5, 3041), (5, 3042), (5, 3043), (5, 3044), (5, 3045);

-- =====================================================
-- 8. 我的申请菜单配置（销售人员专用）
-- =====================================================

-- -- 我的申请主菜单
-- INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
-- VALUES (3050, '我的申请', 2059, 3, 'my-applications', 'me/sales/applications/index', '', 1, 0, 'C', '0', 0, 'sales:my:applications', 'form', 'admin', NOW(), '销售人员查看自己的预约课申请');

-- -- 我的申请权限按钮
-- INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
-- VALUES (3051, '查看我的申请', 3050, 1, '', '', '', 1, 0, 'F', '0', 0, 'sales:my:applications:query', '#', 'admin', NOW());

-- INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
-- VALUES (3052, '申请预约课', 3050, 2, '', '', '', 1, 0, 'F', '0', 0, 'sales:my:applications:add', '#', 'admin', NOW());

-- INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
-- VALUES (3053, '撤销申请', 3050, 3, '', '', '', 1, 0, 'F', '0', 0, 'sales:my:applications:cancel', '#', 'admin', NOW());

-- -- 为销售相关角色分配我的申请菜单权限
-- INSERT INTO words.sys_role_menu (role_id, menu_id) VALUES
-- (1, 3050), (1, 3051), (1, 3052), (1, 3053),
-- (9, 3050), (9, 3051), (9, 3052), (9, 3053),
-- (10, 3050), (10, 3051), (10, 3052), (10, 3053),
-- (8, 3050), (8, 3051), (8, 3052), (8, 3053);

-- =====================================================
-- 9. 数据权限配置说明
-- =====================================================

/*
数据权限说明：
1. 超级管理员 (admin): 全部数据权限 (data_scope='1')
2. 人力 (hr): 全部数据权限 (data_scope='1')
3. 销售总监 (sales_director): 全部数据权限 (data_scope='1')
4. 销售组长 (sales_group_leader): 本部门及以下数据权限 (data_scope='4')
5. 销售 (sale): 仅本人数据权限 (data_scope='5')
6. 教学组长 (teaching_group_leader): 本部门及以下数据权限 (data_scope='4')

权限控制逻辑：
- 销售总监：可以查看和管理所有销售组、销售人员、销售学生
- 销售组长：只能查看和管理本组的销售人员和学生
- 销售：只能查看和管理自己名下的学生
- 教学组长：可以审核所有预约课申请
*/

-- =====================================================
-- 10. 执行完成提示
-- =====================================================

-- 更新菜单ID序列（如果使用序列）
SELECT setval('words.sys_menu_menu_id_seq', 3100);

-- 更新角色ID序列（如果使用序列）
SELECT setval('words.sys_role_role_id_seq', 15);

-- 执行完成
SELECT '销售管理权限配置SQL执行完成！' as result;
