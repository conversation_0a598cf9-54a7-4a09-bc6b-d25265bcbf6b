package org.nonamespace.word.server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.domain.SalesGroup;
import org.nonamespace.word.server.domain.SalesGroupMember;
import org.nonamespace.word.server.mapper.SalesGroupMemberMapper;
import org.nonamespace.word.server.service.ISalesGroupMemberService;
import org.nonamespace.word.server.service.ISalesGroupService;
import org.springframework.stereotype.Service;

/**
 * 销售组成员数据层服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SalesGroupMemberServiceImpl extends ServiceImpl<SalesGroupMemberMapper, SalesGroupMember> implements ISalesGroupMemberService {
    // 纯数据层服务，只提供基础的CRUD操作

    private final ISalesGroupService salesGroupService;

    @Override
    public SalesGroup getSalesGroupBySalesId(String salesId) {
        SalesGroupMember member = lambdaQuery().eq(SalesGroupMember::getSalesId, salesId)
                .one();
        if (member == null) {
            return null;
        }

        return salesGroupService.lambdaQuery()
                .eq(SalesGroup::getId, member.getGroupId())
                .one();
    }
}
