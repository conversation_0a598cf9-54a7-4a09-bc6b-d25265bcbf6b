//package org.nonamespace.word.rest.controller;
//
//import java.util.List;
//
//import jakarta.servlet.http.HttpServletResponse;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.PutMapping;
//import org.springframework.web.bind.annotation.DeleteMapping;
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//import com.ruoyi.common.annotation.Log;
//import com.ruoyi.common.core.controller.BaseController;
//import com.ruoyi.common.core.domain.AjaxResult;
//import com.ruoyi.common.enums.BusinessType;
//import org.nonamespace.word.server.domain.HistoryTextbook;
//import org.nonamespace.word.server.service.IHistoryTextbookService;
//import com.ruoyi.common.utils.poi.ExcelUtil;
//
///**
// * 词历史: 存储词的修改历史版本Controller
// * 
// * <AUTHOR>
// * @date 2025-05-13
// */
//@RestController
//@RequestMapping("/word/textbook")
//public class HistoryTextbookController extends BaseController
//{
//    @Autowired
//    private IHistoryTextbookService historyTextbookService;
//
//
//    /**
//     * 导出词历史: 存储词的修改历史版本列表
//     */
//    @PreAuthorize("@ss.hasPermi('word:textbook:export')")
//    @Log(title = "词历史: 存储词的修改历史版本", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, HistoryTextbook historyTextbook)
//    {
//        List<HistoryTextbook> list = historyTextbookService.selectHistoryTextbookList(historyTextbook);
//        ExcelUtil<HistoryTextbook> util = new ExcelUtil<HistoryTextbook>(HistoryTextbook.class);
//        util.exportExcel(response, list, "词历史: 存储词的修改历史版本数据");
//    }
//
//    /**
//     * 获取词历史: 存储词的修改历史版本详细信息
//     */
//    @PreAuthorize("@ss.hasPermi('word:textbook:query')")
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") String id)
//    {
//        return success(historyTextbookService.selectHistoryTextbookById(id));
//    }
//
//    /**
//     * 新增词历史: 存储词的修改历史版本
//     */
//    @PreAuthorize("@ss.hasPermi('word:textbook:add')")
//    @Log(title = "词历史: 存储词的修改历史版本", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody HistoryTextbook historyTextbook)
//    {
//        return toAjax(historyTextbookService.insertHistoryTextbook(historyTextbook));
//    }
//
//    /**
//     * 修改词历史: 存储词的修改历史版本
//     */
//    @PreAuthorize("@ss.hasPermi('word:textbook:edit')")
//    @Log(title = "词历史: 存储词的修改历史版本", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody HistoryTextbook historyTextbook)
//    {
//        return toAjax(historyTextbookService.updateHistoryTextbook(historyTextbook));
//    }
//
//    /**
//     * 删除词历史: 存储词的修改历史版本
//     */
//    @PreAuthorize("@ss.hasPermi('word:textbook:remove')")
//    @Log(title = "词历史: 存储词的修改历史版本", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable String[] ids)
//    {
//        return toAjax(historyTextbookService.deleteHistoryTextbookByIds(ids));
//    }
//}
