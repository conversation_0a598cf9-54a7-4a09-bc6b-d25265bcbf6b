package org.nonamespace.word.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.nonamespace.word.server.domain.TeacherTimeSlot;
import org.nonamespace.word.server.dto.management.teachinggroup.TeacherDto;

import java.util.List;

/**
 * 教师时间表Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Mapper
public interface TeacherTimeSlotMapper extends BaseMapper<TeacherTimeSlot> {

    /**
     * 查询教师的时间表
     *
     * @param teacherId 教师ID
     * @return 时间表列表
     */
    List<TeacherDto.TimeSlotResp> selectByTeacherId(@Param("teacherId") String teacherId);

    /**
     * 批量更新教师时间表
     *
     * @param teacherId 教师ID
     * @param timeSlots 时间表列表
     * @return 影响行数
     */
    int updateBatchByTeacherId(@Param("teacherId") String teacherId, @Param("timeSlots") List<TeacherDto.TimeSlotResp> timeSlots);

    /**
     * 删除教师的所有时间表
     *
     * @param teacherId 教师ID
     * @return 影响行数
     */
    int deleteByTeacherId(@Param("teacherId") String teacherId);

    /**
     * 批量插入教师时间表
     *
     * @param timeSlots 时间表列表
     * @return 影响行数
     */
int insertBatch(@Param("timeSlots") List<TeacherTimeSlot> timeSlots);

    /**
     * 批量查询教师时间段
     *
     * @param teacherIds 教师ID列表
     * @return 时间段列表
     */
    List<TeacherDto.TimeSlotResp> selectByTeacherIds(@Param("teacherIds") List<String> teacherIds);
}
