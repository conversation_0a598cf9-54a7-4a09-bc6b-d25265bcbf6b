package org.nonamespace.word.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.misc.WssContext;
import org.nonamespace.word.server.domain.*;
import org.nonamespace.word.server.dto.course.CourseQueryDto;
import org.nonamespace.word.server.service.*;
import org.nonamespace.word.server.util.SystemDataQueryUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 课程查询Service实现类
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
@Slf4j
@Service
public class CourseQueryServiceImpl implements ICourseQueryService {

    @Autowired
    private ICourseService courseService;
    
    @Autowired
    private IReviewScheduleService reviewScheduleService;
    
    @Autowired
    private IStudentCourseConsumptionService studentCourseConsumptionService;
    
    @Autowired
    private SystemDataQueryUtil systemDataQueryUtil;
    @Autowired
    private UserStudentExtService userStudentExtService;
    @Autowired
    private TeacherProfileServiceImpl teacherProfileServiceImpl;
    @Autowired
    private TeacherManagementServiceImpl teacherManagementServiceImpl;

    @Override
    public Page<CourseQueryDto.QueryResponse> queryCourses(CourseQueryDto.QueryRequest request) {
        log.info("开始查询课程信息，查询条件: {}", request);
        
        // 创建分页对象
        Page<Course> page = new Page<>(request.getPageNum(), request.getPageSize());
        
        // 构建查询条件
        MPJLambdaWrapper<Course> wrapper = buildQueryWrapper(request);
        
        // 执行查询
        Page<Course> coursePage = ((org.nonamespace.word.server.mapper.CourseMapper) courseService.getBaseMapper()).selectJoinPage(page, Course.class, wrapper);
        
        // 转换结果
        Page<CourseQueryDto.QueryResponse> resultPage = new Page<>();
        resultPage.setCurrent(coursePage.getCurrent());
        resultPage.setSize(coursePage.getSize());
        resultPage.setTotal(coursePage.getTotal());
        resultPage.setPages(coursePage.getPages());
        
        List<CourseQueryDto.QueryResponse> responseList = coursePage.getRecords().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
        
        // 批量查询抗遗忘状态
        enrichAntiForgetStatus(responseList);
        
        // 批量查询课消信息
        enrichCourseConsumption(responseList);
        
        // 设置操作权限
        enrichOperationPermissions(responseList);

        enrichUserNames(responseList);
        
        resultPage.setRecords(responseList);
        
        log.info("课程查询完成，共查询到 {} 条记录", resultPage.getTotal());
        return resultPage;
    }

    @Override
//    @Cacheable(value = "courseStatistics",
//               key = "T(java.util.Objects).hash(#request.subject, #request.specification, #request.type, #request.courseType, #request.courseStatuses, #request.teacherId, #request.studentId, #request.startDate, #request.endDate, #request.antiForgetCompleted, #request.exceptionTypes, #request.hasCourseConsumption) + ':' + T(com.ruoyi.common.utils.SecurityUtils).getUserId()",
//               unless = "#result == null")
    public CourseQueryDto.StatisticsResponse getStatistics(CourseQueryDto.QueryRequest request) {
        log.info("开始统计课程信息，查询条件: {}", request);

        CourseQueryDto.StatisticsResponse statistics = new CourseQueryDto.StatisticsResponse();

        try {
            // 使用数据库聚合查询直接计算统计信息，避免加载大量数据到内存
            statistics = calculateStatisticsWithAggregation(request);

            log.info("课程统计完成: {}", statistics);
            return statistics;
        } catch (Exception e) {
            log.error("统计课程信息失败", e);
            // 返回空统计作为降级方案
            statistics.setTotalStudents(0L);
            statistics.setTotalCourses(0L);
            statistics.setCompletedCourses(0L);
            statistics.setCancelledCourses(0L);
            statistics.setRescheduledCourses(0L);
            statistics.setAntiForgetProgress("0/0");
            statistics.setTotalCourseConsumption(BigDecimal.ZERO);
            return statistics;
        }
    }

    /**
     * 构建查询条件
     */
    private MPJLambdaWrapper<Course> buildQueryWrapper(CourseQueryDto.QueryRequest request) {
        MPJLambdaWrapper<Course> wrapper = new MPJLambdaWrapper<>();
        
        // 基础字段选择
        wrapper.selectAll(Course.class)
                .selectAs(UserStudentExt::getName, CourseQueryDto.QueryResponse::getStudentName)
                .selectAs(TeacherProfile::getNickName, CourseQueryDto.QueryResponse::getTeacherName);
        
        // 关联学生表
        wrapper.leftJoin(UserStudentExt.class, UserStudentExt::getStudentId, Course::getStudentId);
        
        // 关联教师表
        wrapper.leftJoin(TeacherProfile.class, TeacherProfile::getTeacherId, Course::getTeacherId);
        
        // 应用查询条件
        applyQueryConditions(wrapper, request);
        
        // 应用数据权限
        applyDataPermissions(wrapper);
        
        // 排序
        if (StrUtil.isNotEmpty(request.getOrderBy())) {
            if ("desc".equalsIgnoreCase(request.getOrderDirection())) {
                wrapper.orderByDesc(Course::getScheduledStartTime);
            } else {
                wrapper.orderByAsc(Course::getScheduledStartTime);
            }
        }
        
        return wrapper;
    }

    /**
     * 应用查询条件
     */
    private void applyQueryConditions(MPJLambdaWrapper<Course> wrapper, CourseQueryDto.QueryRequest request) {
        // 学科条件
        if (StrUtil.isNotEmpty(request.getSubject())) {
            wrapper.eq(Course::getSubject, request.getSubject());
        }
        
        // 课型条件
        if (StrUtil.isNotEmpty(request.getSpecification())) {
            wrapper.eq(Course::getSpecification, request.getSpecification());
        }
        
        // 类型条件
        if (StrUtil.isNotEmpty(request.getType())) {
            wrapper.eq(Course::getType, request.getType());
        }
        
        // 性质条件
        if (StrUtil.isNotEmpty(request.getCourseType())) {
            wrapper.eq(Course::getCourseType, request.getCourseType());
        }
        
        // 课程状态条件
        if (!CollectionUtils.isEmpty(request.getCourseStatuses())) {
            wrapper.in(Course::getCourseStatus, request.getCourseStatuses());
        }
        
        // 老师条件
        if (StrUtil.isNotEmpty(request.getTeacherId())) {
            wrapper.eq(Course::getTeacherId, request.getTeacherId());
        }
        if (StrUtil.isNotEmpty(request.getTeacherName())) {
            wrapper.like(TeacherProfile::getNickName, request.getTeacherName());
        }
        
        // 学生条件
        if (StrUtil.isNotEmpty(request.getStudentId())) {
            wrapper.eq(Course::getStudentId, request.getStudentId());
        }
        if (StrUtil.isNotEmpty(request.getStudentName())) {
            wrapper.like(UserStudentExt::getName, request.getStudentName());
        }
        
        // 日期条件
        if (request.getStartDate() != null) {
            wrapper.ge(Course::getScheduledStartTime, request.getStartDate());
        }
        if (request.getEndDate() != null) {
            wrapper.le(Course::getScheduledStartTime, request.getEndDate());
        }

        // 异常状态
        if(CollUtil.isNotEmpty(request.getExceptionTypes())){
            applyArrayFieldFilter(wrapper, "exception_types", request.getExceptionTypes());
        }

        // 抗遗忘完成状态
        if (request.getAntiForgetCompleted() != null) {
            applyAntiForgetCompletedFilter(wrapper, request.getAntiForgetCompleted());
        }
    }

    /**
     * 应用数据权限
     */
    private void applyDataPermissions(MPJLambdaWrapper<Course> wrapper) {
        // 根据用户角色应用数据权限
        if (systemDataQueryUtil.isAdminOrHr()) {
            // 管理员和HR可以查看所有课程
            return;
        }

        if (systemDataQueryUtil.isStudent()) {
            // 学生只能查看自己的课程
            wrapper.eq(Course::getStudentId, systemDataQueryUtil.getCurrentUserId());
            return;
        }

        Set<String> teacherIds = new HashSet<>();


        if (systemDataQueryUtil.isTeacherGroupManager()) {
            teacherIds.addAll(teacherManagementServiceImpl.getManagedTeacherIds());
        }


        if (systemDataQueryUtil.isTeacher()) {
            teacherIds.add(systemDataQueryUtil.getCurrentUserId());
        }

        if(CollUtil.isNotEmpty(teacherIds)){
            wrapper.in(Course::getTeacherId, teacherIds);
        }else{
            // 没有可看的老师，直接返回空结果
            wrapper.eq(Course::getId, "");
        }

    }

    /**
     * 转换为响应对象
     */
    private CourseQueryDto.QueryResponse convertToResponse(Course course) {
        CourseQueryDto.QueryResponse response = new CourseQueryDto.QueryResponse();
        
        response.setId(course.getId());
        response.setCourseDate(course.getScheduledStartTime());
        response.setSubject(course.getSubject());
        response.setSpecification(course.getSpecification());
        response.setType(course.getType());
        response.setCourseType(course.getCourseType());
        response.setCourseStatus(course.getCourseStatus());
        response.setTeacherId(course.getTeacherId());
        response.setStudentId(course.getStudentId());
        response.setScheduledStartTime(course.getScheduledStartTime());
        response.setScheduledEndTime(course.getScheduledEndTime());
        response.setActualStartTime(course.getActualStartTime());
        response.setActualEndTime(course.getActualEndTime());
        response.setExceptionTypes(course.getExceptionTypes());

        // 计算时长
        if (course.getScheduledStartTime() != null && course.getScheduledEndTime() != null) {
            long duration = (course.getScheduledEndTime().getTime() - course.getScheduledStartTime().getTime()) / (1000 * 60);
            response.setScheduledDuration(duration);
        }
        
        if (course.getActualStartTime() != null && course.getActualEndTime() != null) {
            long duration = (course.getActualEndTime().getTime() - course.getActualStartTime().getTime()) / (1000 * 60);
            response.setActualDuration(Math.max(duration, 1));
        }
        
        return response;
    }


    /**
     * 批量查询并设置用户姓名
     * @param responseList
     */
    private void enrichUserNames(List<CourseQueryDto.QueryResponse> responseList) {
        if (CollectionUtils.isEmpty(responseList)) {
            return;
        }

        // 获取所有学生ID和教师ID
        Set<String> studentIds = responseList.stream()
                .map(CourseQueryDto.QueryResponse::getStudentId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Set<String> teacherIds = responseList.stream()
                .map(CourseQueryDto.QueryResponse::getTeacherId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 批量查询学生姓名
        userStudentExtService.lambdaQuery().in(UserStudentExt::getStudentId, studentIds)
                .list().forEach(student -> {
                    responseList.stream()
                            .filter(response -> response.getStudentId().equals(student.getStudentId()))
                            .forEach(response -> response.setStudentName(student.getName()));
                });
        // 批量查询教师姓名
        teacherProfileServiceImpl.lambdaQuery().in(TeacherProfile::getTeacherId, teacherIds)
                .list().forEach(teacher -> {
                    responseList.stream()
                            .filter(response -> response.getTeacherId().equals(teacher.getTeacherId()))
                            .forEach(response -> response.setTeacherName(teacher.getNickName()));
                });
    }

    /**
     * 批量查询并设置抗遗忘状态
     */
    private void enrichAntiForgetStatus(List<CourseQueryDto.QueryResponse> responseList) {
        if (CollectionUtils.isEmpty(responseList)) {
            return;
        }

        // 获取所有课程ID
        List<String> courseIds = responseList.stream()
                .map(CourseQueryDto.QueryResponse::getId)
                .collect(Collectors.toList());

        // 批量查询复习计划
        List<ReviewSchedule> reviewSchedules = reviewScheduleService.lambdaQuery()
                .in(ReviewSchedule::getCourseId, courseIds)
                .list();

        // 按课程ID分组
        Map<String, List<ReviewSchedule>> reviewMap = reviewSchedules.stream()
                .collect(Collectors.groupingBy(ReviewSchedule::getCourseId));

        // 为每个课程设置抗遗忘状态
        for (CourseQueryDto.QueryResponse response : responseList) {
            List<ReviewSchedule> courseReviews = reviewMap.getOrDefault(response.getId(), Collections.emptyList());
            response.setAntiForgetStatus(buildAntiForgetStatus(courseReviews));
        }
    }

    /**
     * 构建抗遗忘状态
     */
    private CourseQueryDto.AntiForgetStatus buildAntiForgetStatus(List<ReviewSchedule> reviews) {
        CourseQueryDto.AntiForgetStatus status = new CourseQueryDto.AntiForgetStatus();

        Map<String, ReviewSchedule> reviewMap = reviews.stream()
                .collect(Collectors.toMap(ReviewSchedule::getReviewType, r -> r, (r1, r2) -> r1));

        status.setD2Status(buildReviewStatus(reviewMap.get("D2")));
        status.setD4Status(buildReviewStatus(reviewMap.get("D4")));
        status.setD7Status(buildReviewStatus(reviewMap.get("D7")));
        status.setD14Status(buildReviewStatus(reviewMap.get("D14")));
        status.setD21Status(buildReviewStatus(reviewMap.get("D21")));

        return status;
    }

    /**
     * 构建单个复习状态
     */
    private CourseQueryDto.ReviewStatus buildReviewStatus(ReviewSchedule review) {
        CourseQueryDto.ReviewStatus status = new CourseQueryDto.ReviewStatus();

        if (review == null) {
            status.setStatus("未开始");
            status.setColor("info");
        } else {
            status.setStatus(review.getStatus());
            switch (review.getStatus()) {
                case "已完成":
                    status.setColor("success");
                    break;
                case "进行中":
                    status.setColor("warning");
                    break;
                case "已跳过":
                    status.setColor("danger");
                    break;
                default:
                    status.setColor("info");
                    break;
            }
        }

        return status;
    }

    /**
     * 批量查询并设置课消信息
     */
    private void enrichCourseConsumption(List<CourseQueryDto.QueryResponse> responseList) {
        if (CollectionUtils.isEmpty(responseList)) {
            return;
        }

        // 获取所有课程ID
        List<String> courseIds = responseList.stream()
                .map(CourseQueryDto.QueryResponse::getId)
                .collect(Collectors.toList());

        // 批量查询课消记录
        List<StudentCourseConsumption> consumptions = studentCourseConsumptionService.lambdaQuery()
                .in(StudentCourseConsumption::getCourseId, courseIds)
                .eq(StudentCourseConsumption::getStatus, "active")
                .list();

        // 按课程ID分组并计算总课消
        Map<String, BigDecimal> consumptionMap = consumptions.stream()
                .collect(Collectors.groupingBy(
                        StudentCourseConsumption::getCourseId,
                        Collectors.reducing(BigDecimal.ZERO,
                                StudentCourseConsumption::getConsumedHours,
                                BigDecimal::add)
                ));

        // 为每个课程设置课消信息
        for (CourseQueryDto.QueryResponse response : responseList) {
            BigDecimal consumption = consumptionMap.getOrDefault(response.getId(), BigDecimal.ZERO);
            response.setCourseConsumption(consumption);
        }
    }

    /**
     * 设置操作权限
     */
    private void enrichOperationPermissions(List<CourseQueryDto.QueryResponse> responseList) {
        if (CollectionUtils.isEmpty(responseList)) {
            return;
        }

        String currentUserId = systemDataQueryUtil.getCurrentUserId();
        boolean isAdminOrHr = systemDataQueryUtil.isAdminOrHr();
        boolean isTeacher = systemDataQueryUtil.isTeacher();

        Set<String> managedTeacherIds = teacherManagementServiceImpl.getManagedTeacherIds();

        for (CourseQueryDto.QueryResponse response : responseList) {
            // 默认所有权限为false
            response.setCanStart(false);
            response.setCanCancel(false);
            response.setCanReschedule(false);
            response.setCanDownloadMaterial(false);
            response.setCanViewReport(false);
            response.setCanEnter(false);

            // 根据角色和课程状态设置权限
            if (isAdminOrHr || ( managedTeacherIds.contains(response.getTeacherId()))) {
                // 管理员和HR有所有权限
                // 组长和HR也有权限
                response.setCanStart(DateUtil.isSameDay(response.getCourseDate(), WssContext.now()) && "待开始".equals(response.getCourseStatus()) && StrUtil.endWith(response.getTeacherId(), systemDataQueryUtil.getCurrentUserId()));
                response.setCanCancel("待开始".equals(response.getCourseStatus()) || "进行中".equals(response.getCourseStatus()));
                response.setCanReschedule("待开始".equals(response.getCourseStatus()));
                response.setCanDownloadMaterial("已完成".equals(response.getCourseStatus()));
                response.setCanEnter("进行中".equalsIgnoreCase(response.getCourseStatus()) || "已完成".equals(response.getCourseStatus()));
//                response.setCanViewReport("已完成".equals(response.getCourseStatus()));
            } else if (isTeacher && currentUserId != null && currentUserId.equals(response.getTeacherId())) {
                // 教师只能操作自己的课程
                response.setCanStart(DateUtil.isSameDay(response.getCourseDate(), WssContext.now()) && "待开始".equals(response.getCourseStatus()) && StrUtil.endWith(response.getTeacherId(), systemDataQueryUtil.getCurrentUserId()));
                response.setCanCancel("待开始".equals(response.getCourseStatus()) || "进行中".equals(response.getCourseStatus()));
                response.setCanReschedule("待开始".equals(response.getCourseStatus()));
                response.setCanDownloadMaterial("已完成".equals(response.getCourseStatus()));
                response.setCanEnter("进行中".equalsIgnoreCase(response.getCourseStatus()) || "已完成".equals(response.getCourseStatus()));
//                response.setCanViewReport("已完成".equals(response.getCourseStatus()));
            }
        }
    }



    /**
     * 应用数组字段筛选，兼容JSON和VARCHAR数组两种类型（用于MPJLambdaWrapper）
     *
     * @param wrapper 查询包装器
     * @param fieldName 字段名
     * @param values 筛选值列表
     */
    private void applyArrayFieldFilter(MPJLambdaWrapper<Course> wrapper, String fieldName, List<String> values) {
        if (values == null || values.isEmpty()) {
            return;
        }
        String arrayStr = buildVarcharArrayString(values);
        wrapper.apply(fieldName + " && " + arrayStr);
    }

    /**
     * 应用抗遗忘完成状态筛选
     *
     * @param wrapper 查询包装器
     * @param antiForgetCompleted 抗遗忘是否完成
     */
    private void applyAntiForgetCompletedFilter(MPJLambdaWrapper<Course> wrapper, Boolean antiForgetCompleted) {
        if (antiForgetCompleted == null) {
            return;
        }

        if (antiForgetCompleted) {
            // 查询已完成的课程ID：所有复习计划都是"已完成"状态的课程
            wrapper.apply("""
                EXISTS (
                    SELECT 1 FROM review_schedule rs1
                    WHERE rs1.course_id = t.id
                    AND rs1.deleted = false
                )
                AND NOT EXISTS (
                    SELECT 1 FROM review_schedule rs2
                    WHERE rs2.course_id = t.id
                    AND rs2.deleted = false
                    AND rs2.status != '已完成'
                )
                """);
        } else {
            // 查询未完成的课程ID：没有复习计划或有未完成的复习计划
            wrapper.apply("""
                NOT EXISTS (
                    SELECT 1 FROM review_schedule rs1
                    WHERE rs1.course_id = t.id
                    AND rs1.deleted = false
                )
                OR EXISTS (
                    SELECT 1 FROM review_schedule rs2
                    WHERE rs2.course_id = t.id
                    AND rs2.deleted = false
                    AND rs2.status != '已完成'
                )
                """);
        }
    }



    /**
     * 构建PostgreSQL VARCHAR数组字符串
     *
     * @param values 字符串列表
     * @return PostgreSQL数组格式字符串，如 ARRAY['value1','value2']::VARCHAR[]
     */
    private String buildVarcharArrayString(List<String> values) {
        if (values == null || values.isEmpty()) {
            return "ARRAY[]::VARCHAR[]";
        }

        StringBuilder sb = new StringBuilder("ARRAY[");
        for (int i = 0; i < values.size(); i++) {
            if (i > 0) {
                sb.append(",");
            }
            // 转义单引号防止SQL注入
            String escapedValue = values.get(i).replace("'", "''");
            sb.append("'").append(escapedValue).append("'");
        }
        // 显式指定类型为 VARCHAR[] 以匹配数据库字段类型
        sb.append("]::VARCHAR[]");

        return sb.toString();
    }

    /**
     * 使用优化查询计算统计信息
     * 避免JOIN查询，只查询Course表，大幅提升性能
     */
    private CourseQueryDto.StatisticsResponse calculateStatisticsWithAggregation(CourseQueryDto.QueryRequest request) {
        log.info("使用优化查询计算统计信息（无JOIN）");

        CourseQueryDto.StatisticsResponse statistics = new CourseQueryDto.StatisticsResponse();

        // 构建统计专用的查询条件（不包含JOIN）
        MPJLambdaWrapper<Course> wrapper = buildStatisticsQueryWrapper(request);

        List<Course> courses = courseService.list(wrapper);

        if (CollectionUtils.isEmpty(courses)) {
            return getEmptyStatistics();
        }

        // 在内存中快速统计（数据量已大幅减少）
        statistics.setTotalStudents((long) courses.stream()
                .map(Course::getStudentId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet()).size());

        statistics.setTotalCourses((long) courses.size());

        // 按状态统计
        Map<String, Long> statusCounts = courses.stream()
                .collect(Collectors.groupingBy(Course::getCourseStatus, Collectors.counting()));

        statistics.setCompletedCourses(statusCounts.getOrDefault("已完成", 0L));
        statistics.setCancelledCourses(statusCounts.getOrDefault("停课", 0L));
        statistics.setRescheduledCourses(statusCounts.getOrDefault("调课", 0L));

        // 优化的抗遗忘和课消统计
        statistics.setAntiForgetProgress(calculateAntiForgetProgressOptimized(courses));
        statistics.setTotalCourseConsumption(calculateTotalCourseConsumptionOptimized(courses));

        return statistics;
    }

    /**
     * 构建统计专用的查询条件（不包含JOIN，性能优化）
     */
    private MPJLambdaWrapper<Course> buildStatisticsQueryWrapper(CourseQueryDto.QueryRequest request) {
        MPJLambdaWrapper<Course> wrapper = new MPJLambdaWrapper<>();

        // 只查询必要字段，不做JOIN
        wrapper.select(Course::getId, Course::getStudentId, Course::getCourseStatus, Course::getType);

        // 应用基础查询条件（跳过需要JOIN的条件）
        applyBasicQueryConditions(wrapper, request);

        // 应用数据权限
        applyDataPermissions(wrapper);

        return wrapper;
    }

    /**
     * 应用基础查询条件（不包含需要JOIN的条件）
     */
    private void applyBasicQueryConditions(MPJLambdaWrapper<Course> wrapper, CourseQueryDto.QueryRequest request) {
        // 学科条件
        if (StrUtil.isNotEmpty(request.getSubject())) {
            wrapper.eq(Course::getSubject, request.getSubject());
        }

        // 课型条件
        if (StrUtil.isNotEmpty(request.getSpecification())) {
            wrapper.eq(Course::getSpecification, request.getSpecification());
        }

        // 类型条件
        if (StrUtil.isNotEmpty(request.getType())) {
            wrapper.eq(Course::getType, request.getType());
        }

        // 性质条件
        if (StrUtil.isNotEmpty(request.getCourseType())) {
            wrapper.eq(Course::getCourseType, request.getCourseType());
        }

        // 课程状态条件
        if (!CollectionUtils.isEmpty(request.getCourseStatuses())) {
            wrapper.in(Course::getCourseStatus, request.getCourseStatuses());
        }

        // 老师条件（只支持ID，不支持姓名查询以避免JOIN）
        if (StrUtil.isNotEmpty(request.getTeacherId())) {
            wrapper.eq(Course::getTeacherId, request.getTeacherId());
        }

        // 学生条件（只支持ID，不支持姓名查询以避免JOIN）
        if (StrUtil.isNotEmpty(request.getStudentId())) {
            wrapper.eq(Course::getStudentId, request.getStudentId());
        }

        // 日期条件
        if (request.getStartDate() != null) {
            wrapper.ge(Course::getScheduledStartTime, request.getStartDate());
        }
        if (request.getEndDate() != null) {
            wrapper.le(Course::getScheduledStartTime, request.getEndDate());
        }

        // 异常状态
        if(CollUtil.isNotEmpty(request.getExceptionTypes())){
            applyArrayFieldFilter(wrapper, "exception_types", request.getExceptionTypes());
        }

        // 抗遗忘完成状态
        if (request.getAntiForgetCompleted() != null) {
            applyAntiForgetCompletedFilter(wrapper, request.getAntiForgetCompleted());
        }

        // 课消状态（暂时跳过，避免复杂查询）
        // if (request.getHasCourseConsumption() != null) {
        //     applyCourseConsumptionFilter(wrapper, request.getHasCourseConsumption());
        // }

        // 注意：teacherName 和 studentName 条件需要JOIN，在统计查询中跳过
        // 如果用户使用了这些条件，统计结果可能不完全准确，但性能会大幅提升
    }

    /**
     * 返回空统计数据
     */
    private CourseQueryDto.StatisticsResponse getEmptyStatistics() {
        CourseQueryDto.StatisticsResponse statistics = new CourseQueryDto.StatisticsResponse();
        statistics.setTotalStudents(0L);
        statistics.setTotalCourses(0L);
        statistics.setCompletedCourses(0L);
        statistics.setCancelledCourses(0L);
        statistics.setRescheduledCourses(0L);
        statistics.setAntiForgetProgress("0/0");
        statistics.setTotalCourseConsumption(BigDecimal.ZERO);
        return statistics;
    }





    /**
     * 优化的抗遗忘进度计算（只查询已完成学习课程的ID）
     */
    private String calculateAntiForgetProgressOptimized(List<Course> courses) {
        if (CollectionUtils.isEmpty(courses)) {
            return "0/0";
        }

        // 获取所有已完成的学习课程ID
        List<String> completedLearningCourseIds = courses.stream()
                .filter(course -> "已完成".equals(course.getCourseStatus()) && "学习课".equals(course.getType()))
                .map(Course::getId)
                .collect(Collectors.toList());

        if (completedLearningCourseIds.isEmpty()) {
            return "0/0";
        }

        // 批量查询这些课程的复习计划
        List<ReviewSchedule> reviewSchedules = reviewScheduleService.lambdaQuery()
                .in(ReviewSchedule::getCourseId, completedLearningCourseIds)
                .list();

        // 计算总数和已完成数
        long totalReviews = reviewSchedules.size();
        long completedReviews = reviewSchedules.stream()
                .filter(review -> "已完成".equals(review.getStatus()))
                .count();

        return completedReviews + "/" + totalReviews;
    }

    /**
     * 优化的课消总数计算（只查询课程ID）
     */
    private BigDecimal calculateTotalCourseConsumptionOptimized(List<Course> courses) {
        if (CollectionUtils.isEmpty(courses)) {
            return BigDecimal.ZERO;
        }

        // 获取所有课程ID
        List<String> courseIds = courses.stream()
                .map(Course::getId)
                .collect(Collectors.toList());

        // 批量查询课消记录并计算总和
        List<StudentCourseConsumption> consumptions = studentCourseConsumptionService.lambdaQuery()
                .in(StudentCourseConsumption::getCourseId, courseIds)
                .eq(StudentCourseConsumption::getStatus, "active")
                .list();

        return consumptions.stream()
                .map(StudentCourseConsumption::getConsumedHours)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

}
