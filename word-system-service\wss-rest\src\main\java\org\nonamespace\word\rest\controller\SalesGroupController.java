package org.nonamespace.word.rest.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.dto.sales.SalesGroupDto;
import org.nonamespace.word.server.facade.SalesGroupFacade;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 销售组管理Controller
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
@Slf4j
@RestController
@RequestMapping("/sales/group")
@RequiredArgsConstructor
public class SalesGroupController extends BaseController {

    private final SalesGroupFacade salesGroupFacade;

    /**
     * 分页查询销售组列表
     */
//    @PreAuthorize("@ss.hasPermi('sales:group:list')")
    @GetMapping("/list")
    public AjaxResult list(SalesGroupDto.GetListReq req) {
        try {
            IPage<SalesGroupDto.Resp> page = salesGroupFacade.getSalesGroupPage(req);
            return AjaxResult.success(page);
        } catch (Exception e) {
            log.error("查询销售组列表失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 根据ID查询销售组详情
     */
//    @PreAuthorize("@ss.hasPermi('sales:group:query')")
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable String id) {
        try {
            SalesGroupDto.Resp result = salesGroupFacade.getSalesGroupById(id);
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("查询销售组详情失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 创建销售组
     */
//    @PreAuthorize("@ss.hasPermi('sales:group:add')")
    @Log(title = "销售组管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SalesGroupDto.CreateReq req) {
        try {
            String id = salesGroupFacade.createSalesGroup(req);
            return AjaxResult.success("创建成功", id);
        } catch (Exception e) {
            log.error("创建销售组失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 更新销售组
     */
//    @PreAuthorize("@ss.hasPermi('sales:group:edit')")
    @Log(title = "销售组管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SalesGroupDto.UpdateReq req) {
        try {
            boolean success = salesGroupFacade.updateSalesGroup(req);
            return success ? AjaxResult.success("更新成功") : AjaxResult.error("更新失败");
        } catch (Exception e) {
            log.error("更新销售组失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 删除销售组
     */
//    @PreAuthorize("@ss.hasPermi('sales:group:remove')")
    @Log(title = "销售组管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable String id) {
        try {
            boolean success = salesGroupFacade.deleteSalesGroup(id);
            return success ? AjaxResult.success("删除成功") : AjaxResult.error("删除失败");
        } catch (Exception e) {
            log.error("删除销售组失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 批量删除销售组
     */
//    @PreAuthorize("@ss.hasPermi('sales:group:remove')")
    @Log(title = "销售组管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/batch")
    public AjaxResult removeBatch(@RequestBody List<String> ids) {
        try {
            boolean success = salesGroupFacade.deleteSalesGroups(ids);
            return success ? AjaxResult.success("批量删除成功") : AjaxResult.error("批量删除失败");
        } catch (Exception e) {
            log.error("批量删除销售组失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取销售组统计信息
     */
//    @PreAuthorize("@ss.hasPermi('sales:group:list')")
    @GetMapping("/stats")
    public AjaxResult getStats() {
        try {
            SalesGroupDto.StatsResp stats = salesGroupFacade.getSalesGroupStats();
            return AjaxResult.success(stats);
        } catch (Exception e) {
            log.error("获取销售组统计信息失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 分页查询销售组成员列表
     */
//    @PreAuthorize("@ss.hasPermi('sales:group:member:list')")
    @GetMapping("/members")
    public AjaxResult listMembers(SalesGroupDto.GetMembersReq req) {
        try {
            IPage<SalesGroupDto.MemberResp> page = salesGroupFacade.getSalesGroupMembersPage(req);
            return AjaxResult.success(page);
        } catch (Exception e) {
            log.error("查询销售组成员列表失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 查询销售组所有成员
     */
//    @PreAuthorize("@ss.hasPermi('sales:group:member:list')")
    @GetMapping("/{groupId}/members")
    public AjaxResult getMembers(@PathVariable String groupId) {
        try {
            List<SalesGroupDto.MemberResp> members = salesGroupFacade.getSalesGroupMembers(groupId);
            return AjaxResult.success(members);
        } catch (Exception e) {
            log.error("查询销售组成员失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 添加成员到销售组
     */
//    @PreAuthorize("@ss.hasPermi('sales:group:member:add')")
    @Log(title = "销售组成员管理", businessType = BusinessType.INSERT)
    @PostMapping("/members")
    public AjaxResult addMembers(@Validated @RequestBody SalesGroupDto.AddMembersReq req) {
        try {
            boolean success = salesGroupFacade.addSalesGroupMembers(req);
            return success ? AjaxResult.success("添加成员成功") : AjaxResult.error("添加成员失败");
        } catch (Exception e) {
            log.error("添加销售组成员失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 从销售组移除成员
     */
//    @PreAuthorize("@ss.hasPermi('sales:group:member:remove')")
    @Log(title = "销售组成员管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/members")
    public AjaxResult removeMember(@Validated @RequestBody SalesGroupDto.RemoveMemberReq req) {
        try {
            boolean success = salesGroupFacade.removeSalesGroupMember(req);
            return success ? AjaxResult.success("移除成员成功") : AjaxResult.error("移除成员失败");
        } catch (Exception e) {
            log.error("移除销售组成员失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 设置销售组组长
     */
//    @PreAuthorize("@ss.hasPermi('sales:group:leader:set')")
    @Log(title = "销售组组长管理", businessType = BusinessType.UPDATE)
    @PutMapping("/leader")
    public AjaxResult setLeader(@Validated @RequestBody SalesGroupDto.SetLeaderReq req) {
        try {
            boolean success = salesGroupFacade.setSalesGroupLeader(req);
            return success ? AjaxResult.success("设置组长成功") : AjaxResult.error("设置组长失败");
        } catch (Exception e) {
            log.error("设置销售组组长失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取销售组选项列表（用于下拉选择）
     */
//    @PreAuthorize("@ss.hasPermi('sales:group:list')")
    @GetMapping("/options")
    public AjaxResult getOptions() {
        try {
            log.info("获取销售组选项列表");

            // 构建查询请求，只获取活跃的销售组
            SalesGroupDto.GetListReq req = new SalesGroupDto.GetListReq();
            req.setStatus("active");
            req.setPageNum(1);
            req.setPageSize(1000); // 获取所有活跃的销售组

            IPage<SalesGroupDto.Resp> page = salesGroupFacade.getOptions(req);

            // 转换为选项格式
            List<SalesGroupDto.OptionResp> options = page.getRecords().stream()
                    .map(group -> {
                        SalesGroupDto.OptionResp option = new SalesGroupDto.OptionResp();
                        option.setId(group.getId());
                        option.setName(group.getName());
                        option.setMemberCount(group.getMemberCount());
                        return option;
                    })
                    .collect(java.util.stream.Collectors.toList());

            log.info("获取销售组选项列表成功: count={}", options.size());
            return AjaxResult.success(options);
        } catch (Exception e) {
            log.error("获取销售组选项列表失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

}
