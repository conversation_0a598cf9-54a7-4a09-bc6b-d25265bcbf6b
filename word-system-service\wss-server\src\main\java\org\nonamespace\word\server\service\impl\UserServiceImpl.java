package org.nonamespace.word.server.service.impl;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.nonamespace.word.server.domain.ViewUser;
import org.nonamespace.word.server.mapper.SeqIdMapper;
import org.nonamespace.word.server.mapper.UserMapper;
import org.nonamespace.word.server.mapper.ViewUserMapper;
import org.nonamespace.word.server.service.IUserService;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, SysUser> implements IUserService {

    public static final String DEFAULT_PASSWORD = "654321";

    private final SeqIdMapper seqIdMapper;
    private final ISysUserService sysUserService;
    private final ViewUserMapper viewUserMapper;

    @Override
    public boolean save(SysUser user){

        if(StrUtil.isEmpty(user.getUserName())){
            throw new RuntimeException("用户名不能为空");
        }
        if(StrUtil.isEmpty(user.getNickName())){
            throw new RuntimeException("昵称不能为空");
        }
        if(StrUtil.isEmpty(user.getPhonenumber())){
            throw new RuntimeException("手机号不能为空");
        }

        if(lambdaQuery().eq(SysUser::getDelFlag, "0")
                .and(q->
                        q.eq(SysUser::getPhonenumber, user.getPhonenumber())
                        .or()
                        .eq(SysUser::getPhonenumber, user.getPhonenumber())
                )
                .exists()) {
            throw new RuntimeException("用户名或手机号已被使用");
        }

        if(user.getUserId() == null){
            user.setUserId(nextUserId());
        }

        if(StrUtil.isEmpty(user.getPassword())){
            user.setPassword(SecurityUtils.encryptPassword(DEFAULT_PASSWORD));
        }

        if(user.getRoleId()!= null){
            if(user.getRoleIds() == null){
                user.setRoleIds(new Long[]{user.getRoleId()});
            }else{
                if (Arrays.stream(user.getRoleIds()).noneMatch(x->x.equals(user.getRoleId()))) {
                    user.setRoleIds(ArrayUtil.append(user.getRoleIds(), user.getRoleId()));
                }
            }
        }
        return sysUserService.insertUser(user) == 1;
    }

    @Override
    public Long nextUserId() {
        return seqIdMapper.getNextUserId();
    }

    @Override
    public boolean updateById(SysUser user) {

        user.setDelFlag(null);

        return update(user, new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getUserId, user.getUserId())
                .eq(SysUser::getDelFlag, "0")); // 只更新未删除的用户
    }

    /**
     * 重置密码
     * @param userId
     * @return
     */
    @Override
    public boolean resetPassword(Long userId) {

        SysUser user = getById(userId);
        if (user == null || "1".equals(user.getDelFlag())) {
            throw new RuntimeException("用户不存在或已被删除");
        }

        return lambdaUpdate().set(SysUser::getPassword, SecurityUtils.encryptPassword(DEFAULT_PASSWORD))
                .eq(SysUser::getUserId, userId)
                .update();
    }

    /**
     * 获取默认密码
     * @return 默认密码
     */
    @Override
    public String getDefaultPassword(){
        return DEFAULT_PASSWORD;
    }

    @Override
    public SysUser getById(Serializable id) {
        return lambdaQuery()
                .eq(SysUser::getUserId, id)
                .one();
    }

    @Cacheable(value = "systemData:permissions", key = "'allUser'", cacheManager = "sessionCacheManager")
    @Override
    public List<ViewUser> viewUsers() {
        long startTime = System.currentTimeMillis();
        List<ViewUser> viewUsers = viewUserMapper.selectList(new LambdaQueryWrapper<>());
        System.out.println("查询所有用户耗时: " + (System.currentTimeMillis() - startTime) + "ms");
        return viewUsers;
    }


    /**
     * 获取用户显示名称
     *
     * @param user 用户信息
     * @return 用户显示名称
     */
    public String getUserDisplayName(SysUser user) {
        if(user == null) {
            throw new RuntimeException("用户不存在");
        }
        return StrUtil.format("{}({})", user.getNickName(),  DesensitizedUtil.mobilePhone(user.getPhonenumber()));
    }

    public boolean isPhoneNumberExists(String phoneNumber) {
        if (StrUtil.isBlank(phoneNumber)) {
            return false;
        }
        return lambdaQuery()
                .eq(SysUser::getPhonenumber, phoneNumber)
                .eq(SysUser::getDelFlag, "0") // 只检查未删除的用户
                .exists();
    }

    /**
     * 检查手机号是否被其他用户使用（排除指定用户）
     *
     * @param phoneNumber 手机号
     * @param excludeUserId 要排除的用户ID
     * @return 是否被其他用户使用
     */
    public boolean isPhoneNumberUsedByOtherUser(String phoneNumber, Long excludeUserId) {
        if (StrUtil.isBlank(phoneNumber)) {
            return false;
        }

        return lambdaQuery()
                .eq(SysUser::getPhonenumber, phoneNumber)
                .ne(SysUser::getUserId, excludeUserId)
                .eq(SysUser::getDelFlag, "0") // 只检查未删除的用户
                .exists();
    }

    public List<SysUser> getUsers(LambdaQueryWrapper<SysUser> queryWrapper) {
        if (queryWrapper == null) {
            queryWrapper = new LambdaQueryWrapper<>();
        }
        queryWrapper.select(SysUser::getUserId, SysUser::getPhonenumber, SysUser::getNickName, SysUser::getAvatar, SysUser::getEmail, SysUser::getDeptId, SysUser::getSex, SysUser::getStatus);
        queryWrapper.eq(SysUser::getDelFlag, "0"); // 只查询未删除的用户
        List<SysUser> users = list(queryWrapper);
        return users;
    }
}
