package org.nonamespace.word.server.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.misc.WssContext;
import org.nonamespace.word.server.domain.StudentCourseHoursAdjustment;
import org.nonamespace.word.server.mapper.StudentCourseHoursAdjustmentMapper;
import org.nonamespace.word.server.service.IStudentCourseHoursAdjustmentService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 学生课时调整历史记录Service实现类
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@Service
public class StudentCourseHoursAdjustmentServiceImpl extends ServiceImpl<StudentCourseHoursAdjustmentMapper, StudentCourseHoursAdjustment> 
        implements IStudentCourseHoursAdjustmentService {

    @Override
    public StudentCourseHoursAdjustment recordAdjustment(String studentId, String subject, String specification, String nature,
                                                       String adjustmentType, BigDecimal adjustmentHours,
                                                       BigDecimal purchasedHoursAdjustment, BigDecimal giftHoursAdjustment,
                                                       BigDecimal beforeTotalHours, BigDecimal afterTotalHours,
                                                       BigDecimal beforeRemainingHours, BigDecimal afterRemainingHours,
                                                       BigDecimal beforePurchasedHours, BigDecimal afterPurchasedHours,
                                                       BigDecimal beforeGiftHours, BigDecimal afterGiftHours,
                                                       String adjustmentReason, String operatorId, String operatorName) {
        StudentCourseHoursAdjustment record = new StudentCourseHoursAdjustment();
        record.setId(IdUtil.getSnowflakeNextIdStr());
        record.setStudentId(studentId);
        record.setSubject(subject);
        record.setSpecification(specification);
        record.setNature(nature);
        record.setAdjustmentType(adjustmentType);
        record.setAdjustmentHours(adjustmentHours);
        record.setPurchasedHoursAdjustment(purchasedHoursAdjustment);
        record.setGiftHoursAdjustment(giftHoursAdjustment);
        record.setBeforeTotalHours(beforeTotalHours);
        record.setAfterTotalHours(afterTotalHours);
        record.setBeforeRemainingHours(beforeRemainingHours);
        record.setAfterRemainingHours(afterRemainingHours);
        record.setBeforePurchasedHours(beforePurchasedHours);
        record.setAfterPurchasedHours(afterPurchasedHours);
        record.setBeforeGiftHours(beforeGiftHours);
        record.setAfterGiftHours(afterGiftHours);
        record.setAdjustmentReason(adjustmentReason);
        record.setOperatorId(operatorId);
        record.setOperatorName(operatorName);
        record.setAdjustmentTime(WssContext.now());
        record.setCreateTime(WssContext.now());
        record.setUpdateTime(WssContext.now());
        record.setDeleted(false);

        save(record);
        log.info("记录课时调整历史: studentId={}, subject={}, specification={}, adjustmentType={}, purchasedAdjustment={}, giftAdjustment={}",
                studentId, subject, specification, adjustmentType, purchasedHoursAdjustment, giftHoursAdjustment);

        return record;
    }
}
