package org.nonamespace.word.server.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.nonamespace.word.server.domain.TeacherProfile;
import org.nonamespace.word.server.dto.management.teachinggroup.TeacherDto;
import org.nonamespace.word.server.dto.management.teachermatch.TeacherMatchDto;

import java.util.List;
import java.util.Set;

/**
 * 教师档案Service接口
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface ITeacherProfileService extends IService<TeacherProfile> {

    /**
     * 查询教师详细信息
     *
     * @param teacherId 教师ID
     * @return 教师详细信息
     */
    TeacherDto.DetailResp selectTeacherDetail(String teacherId);

    /**
     * 查询可分配的教师列表（未分配到任何教学组的教师）
     *
     * @return 可分配教师列表
     */
    List<TeacherDto.AvailableResp> selectAvailableTeachers();

    /**
     * 查询教师列表（支持搜索和分页）
     *
     * @param page 分页参数
     * @param req 查询条件
     * @param groupIds 教学组ID集合
     * @return 分页结果
     */
    IPage<TeacherDto.BasicResp> selectTeachersPage(Page<TeacherDto.BasicResp> page, TeacherDto.GetListReq req, Set<String> groupIds);

    /**
     * 查询所有教师列表（用于选择组长、教务）
     *
     * @return 教师列表
     */
    List<TeacherDto.UserRoleResp> selectAllTeachers();

    /**
     * 查询教师带教信息
     *
     * @param teacherId 教师ID
     * @return 带教信息
     */
    TeacherDto.TeachingInfoResp selectTeachingInfo(String teacherId);

    /**
     * 批量查询教师带教信息
     *
     * @param teacherIds 教师ID列表
     * @return 带教信息列表
     */
    List<TeacherDto.TeachingInfoResp> selectTeachingInfoBatch(List<String> teacherIds);

    /**
     * 批量查询教师详细信息
     *
     * @param teacherIds 教师ID列表
     * @return 教师详细信息列表
     */
    List<TeacherDto.DetailResp> selectTeacherDetailBatch(List<String> teacherIds);

    /**
     * 获取符合筛选条件的活跃教师列表（高性能版本）
     *
     * @param request 筛选条件
     * @return 教师基本信息列表
     */
    List<TeacherDto.BasicResp> selectFilteredActiveTeachers(TeacherMatchDto.MatchTeachersReq request);
}
