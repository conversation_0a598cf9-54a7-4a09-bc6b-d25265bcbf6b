package org.nonamespace.word.server.facade;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.nonamespace.word.server.dto.management.student.SalesStudentDto;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 销售版学生管理Facade接口
 * 
 * <AUTHOR>
 * @date 2025-06-29
 */
public interface ISalesStudentManagementFacade {

    /**
     * 分页查询学生列表（销售版）
     * 
     * @param req 查询请求
     * @return 学生分页列表
     */
    IPage<SalesStudentDto.BasicResp> getSalesStudentPage(SalesStudentDto.GetListReq req);

    /**
     * 查询学生详细信息（销售版）
     * 
     * @param studentId 学生ID
     * @return 学生详细信息
     */
    SalesStudentDto.DetailResp getSalesStudentDetail(String studentId);

    /**
     * 创建学生（销售版）
     * 
     * @param req 创建请求
     * @return 学生ID
     */
    String createSalesStudent(SalesStudentDto.CreateReq req);

    /**
     * 更新学生信息（销售版）
     * 
     * @param req 更新请求
     * @return 是否成功
     */
    boolean updateSalesStudent(SalesStudentDto.UpdateReq req);

    /**
     * 删除学生（销售版）
     * 
     * @param studentId 学生ID
     * @return 是否成功
     */
    boolean deleteSalesStudent(String studentId);

    /**
     * 批量删除学生（销售版）
     * 
     * @param studentIds 学生ID列表
     * @return 是否成功
     */
    boolean deleteSalesStudents(List<String> studentIds);

    /**
     * 导入学生（销售版）
     * 
     * @param file Excel文件
     * @param salesId 销售ID（可选，如果指定则自动分配给该销售）
     * @return 导入结果
     */
    SalesStudentDto.ImportResp importSalesStudents(MultipartFile file, String salesId);

    /**
     * 下载学生导入模板
     * 
     * @return 模板文件字节数组
     */
    byte[] downloadImportTemplate();

    /**
     * 分配学生给销售
     * 
     * @param req 分配请求
     * @return 是否成功
     */
    boolean assignStudentToSales(SalesStudentDto.AssignReq req);

    /**
     * 批量分配学生给销售
     *
     * @param req 批量分配请求
     * @return 是否成功
     */
    boolean batchAssignStudentsToSales(SalesStudentDto.BatchAssignReq req);

    /**
     * 智能分配学生给销售
     *
     * @param req 智能分配请求
     * @return 分配结果
     */
    SalesStudentDto.IntelligentAssignResp intelligentAssignStudents(SalesStudentDto.IntelligentAssignReq req);

    /**
     * 取消学生分配
     * 
     * @param studentId 学生ID
     * @return 是否成功
     */
    boolean unassignStudent(String studentId);

    /**
     * 获取销售版学生统计信息
     * 
     * @return 统计信息
     */
    SalesStudentDto.StatsResp getSalesStudentStats();

    /**
     * 获取可分配的学生列表
     * 
     * @return 可分配学生列表
     */
    List<SalesStudentDto.AvailableResp> getAvailableStudents();

    /**
     * 获取销售人员选项
     * 
     * @param groupId 销售组ID（可选）
     * @return 销售人员选项列表
     */
    List<SalesStudentDto.SalesOptionResp> getSalesOptions(String groupId);

    /**
     * 获取销售组选项
     *
     * @return 销售组选项列表
     */
    List<SalesStudentDto.GroupOptionResp> getSalesGroupOptions();

    /**
     * 从销售中移除学生
     *
     * @param req 移除请求
     * @return 是否成功
     */
    boolean removeStudentFromSales(SalesStudentDto.RemoveReq req);

    /**
     * 获取学生分配历史
     *
     * @param studentId 学生ID
     * @return 分配历史列表
     */
    List<SalesStudentDto.AssignmentHistoryResp> getStudentAssignmentHistory(String studentId);
}
