package org.nonamespace.word.server.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
public enum WordLearnStepTypeEnum {
    WORD_PRACTICE("单词测试"),
    WORD_PRACTICE_2("单词测试2"),
    WORD_EXPLAIN("单词讲解"),
    SENTENCE_TRANSLATE("句子翻译"),
    SENTENCE_TRANSLATE_EXPLAIN("句子翻译讲解"),
    VIDEO_EXPLAIN("视频讲解"),
    SENTENCE_ORDER("句子排序"),
    WORD_FILL_2("单词填空2"),
    WORD_FILL_3("单词填空3"),
    WORD_LISTEN("单词听写"),
    SENTENCE_FILL_3("句子填空3"),
    SENTENCE_FILL_4("句子填空4"),
    SENTENCE_FILL("句子填空"),
    WORD_TEST("单词测验");

    private final String value;

    WordLearnStepTypeEnum(String value) {
        this.value = value;
    }

    public static WordLearnStepTypeEnum typeOf(String type){
        return Arrays.stream(WordLearnStepTypeEnum.values()).filter(x -> x.getValue().equals(type)).findFirst().get();
    }

    /**
     * 获取所有学习步骤类型
     * @return List<WordLearnStepTypeEnum>
     */
    public static List<WordLearnStepTypeEnum> forLearn(){
        return Arrays.asList(WORD_PRACTICE, WORD_EXPLAIN, SENTENCE_TRANSLATE, SENTENCE_TRANSLATE_EXPLAIN, SENTENCE_ORDER);
    }

    /**
     * 获取下课复习步骤类型
     * @return List<WordLearnStepTypeEnum>
     */
    public static List<WordLearnStepTypeEnum> forEndClass(){
        return Arrays.asList(WORD_PRACTICE, WORD_EXPLAIN, SENTENCE_TRANSLATE, SENTENCE_TRANSLATE_EXPLAIN);
    }

    /**
     * 获取D2复习步骤类型
     * @return List<WordLearnStepTypeEnum>
     */
    public static List<WordLearnStepTypeEnum> forD2(){
        return Arrays.asList(WORD_PRACTICE, WORD_EXPLAIN, SENTENCE_TRANSLATE, SENTENCE_TRANSLATE_EXPLAIN);
    }

    /**
     * 获取D4复习步骤类型
     * @return List<WordLearnStepTypeEnum>
     */
    public static List<WordLearnStepTypeEnum> forD4() {
        return Arrays.asList(WORD_PRACTICE_2, WORD_EXPLAIN, SENTENCE_ORDER, SENTENCE_TRANSLATE_EXPLAIN);
    }

    /**
     * 获取D7复习步骤类型
     * @return List<WordLearnStepTypeEnum>
     */
    public static List<WordLearnStepTypeEnum> forD7() {
        return Arrays.asList(WORD_FILL_2, WORD_EXPLAIN, SENTENCE_FILL_3, SENTENCE_TRANSLATE_EXPLAIN);
    }

    /**
     * 获取D14复习步骤类型
     * @return List<WordLearnStepTypeEnum>
     */
    public static List<WordLearnStepTypeEnum> forD14() {
        return Arrays.asList(WORD_FILL_3, WORD_EXPLAIN, SENTENCE_FILL_4, SENTENCE_TRANSLATE_EXPLAIN);
    }

    /**
     * 获取D21复习步骤类型
     * @return List<WordLearnStepTypeEnum>
     */
    public static List<WordLearnStepTypeEnum> forD21() {
        return Arrays.asList(WORD_LISTEN, WORD_EXPLAIN, SENTENCE_ORDER, SENTENCE_TRANSLATE_EXPLAIN);
    }

    public static WordLearnStepTypeEnum getByDefault(String value, WordLearnStepTypeEnum defaultValue) {
        for (WordLearnStepTypeEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return defaultValue;
    }
}