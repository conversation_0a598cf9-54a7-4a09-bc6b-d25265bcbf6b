package org.nonamespace.word.server.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.nonamespace.word.common.misc.WssContext;
import org.nonamespace.word.server.domain.CourseSectionStep;
import org.nonamespace.word.server.dto.course.CourseReportDataDto;
import org.nonamespace.word.server.enums.WordLearnStepTypeEnum;
import org.nonamespace.word.server.mapper.CourseSectionStepMapper;
import org.nonamespace.word.server.service.ICourseSectionStepService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 课程学习步骤Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Service
public class CourseSectionStepServiceImpl extends ServiceImpl<CourseSectionStepMapper, CourseSectionStep> implements ICourseSectionStepService {

    @Override
    public List<CourseSectionStep> selectBySectionId(String sectionId) {
        return list(new LambdaQueryWrapper<CourseSectionStep>()
                .eq(CourseSectionStep::getSectionId, sectionId)
                .orderByAsc(CourseSectionStep::getOrderIndex));
    }

    @Override
    public List<CourseSectionStep> selectByWordId(String wordId) {
        return list(new LambdaQueryWrapper<CourseSectionStep>()
                .eq(CourseSectionStep::getWordId, wordId)
                .orderByAsc(CourseSectionStep::getOrderIndex));
    }

    @Override
    public boolean createStep(CourseSectionStep step) {
        step.setCreateTime(WssContext.now());
        step.setUpdateTime(WssContext.now());
        return save(step);
    }

    @Override
    public boolean updateStepResult(String stepId, String status, String result, String studentAnswer) {
        CourseSectionStep step = new CourseSectionStep();
        step.setId(stepId);
        step.setStatus(status);
        step.setResult(result);
        step.setStudentAnswer(studentAnswer);
        step.setUpdateTime(WssContext.now());
        
        if ("进行中".equals(status)) {
            step.setStartTime(WssContext.now());
        } else if ("已完成".equals(status) || "跳过".equals(status)) {
            step.setEndTime(WssContext.now());
        }
        
        return updateById(step);
    }

    @Override
    public List<CourseSectionStep> buildWordSteps(String courseId, String sectionId, String textbookItemId, List<WordLearnStepTypeEnum> stepTypes) {

        List<CourseSectionStep> steps = new ArrayList<>();
        int i = 0;
        for (WordLearnStepTypeEnum stepType : stepTypes) {
            CourseSectionStep step = new CourseSectionStep();
            step.setId(IdUtil.getSnowflakeNextIdStr());
            step.setCourseId(courseId);
            step.setSectionId(sectionId);
            step.setTextbookItemId(textbookItemId);
            step.setType(stepType.getValue());
            step.setOrderIndex((long) i++);
            step.setStatus("待开始");
            step.setCreateTime(WssContext.now());
            step.setUpdateTime(WssContext.now());

            steps.add(step);
        }

        return steps;
    }

    @Override
    public List<CourseReportDataDto> statisticCourseReportData(String courseId) {
        return this.baseMapper.statisticCourseReportData(courseId);
    }
}
