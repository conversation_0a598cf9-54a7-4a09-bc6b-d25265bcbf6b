package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.nonamespace.word.server.entity.DataEntity;

import java.util.Date;

/**
 * 学生积分交易流水表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("words.student_point_transaction")
public class StudentPointTransaction extends DataEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 学生ID
     */
    @TableField("student_id")
    private String studentId;

    /**
     * 交易类型 (奖励，消费)
     * 建议使用枚举类管理这些固定值
     */
    @TableField("transaction_type")
    private String transactionType;

    /**
     * 本次交易的积分变化量 (正数为增加, 负数为减少)
     */
    @TableField("points_change")
    private Long pointsChange;

    /**
     * 本次交易后，该学生的总可用积分余额
     */
    @TableField("points_balance_after")
    private Long pointsBalanceAfter;

    /**
     * 积分来源的大致分类（答题奖励，老师奖励，自学奖励）
     * 建议使用枚举类管理这些固定值
     */
    @TableField("source_category")
    private String sourceCategory;

    /**
     * 积分来源的子分类 () (可选)
     */
    @TableField("source_subcategory")
    private String sourceSubcategory;

    /**
     * 关联的源实体ID (stepId、courseId) (可选)
     */
    @TableField("source_entity_id")
    private String sourceEntityId;

    /**
     * 积分变化描述 (用户可见的描述)
     */
    @TableField("description")
    private String description;

    /**
     * 内部备注，例如调整原因，不对用户展示 (可选)
     */
    @TableField("notes_internal")
    private String notesInternal;

    /**
     * 交易实际记录时间
     * 数据库默认为 CURRENT_TIMESTAMP, MyBatis-Plus 插入时也可以自动填充
     */
    @TableField(value = "transaction_time", fill = FieldFill.INSERT) // 假设插入时填充
    private Date transactionTime;

}