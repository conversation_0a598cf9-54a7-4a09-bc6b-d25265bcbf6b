package org.nonamespace.word.server.dto.sales;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 销售人员管理DTO
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
public class SalesStaffDto {

    /**
     * 分页查询请求
     */
    @Data
    public static class GetListReq {
        /** 页码 */
        private Integer pageNum = 1;
        
        /** 页大小 */
        private Integer pageSize = 10;
        
        /** 销售人员姓名 */
        private String salesName;
        
        /** 手机号 */
        private String phone;
        
        /** 所属销售组ID */
        private String groupId;
        
        /** 状态 */
        private String status;
        
        /** 角色类型 */
        private String roleType;
        
        /** 部门ID */
        private Long deptId;
    }

    /**
     * 销售人员响应
     */
    @Data
    public static class Resp {
        /** ID */
        private String id;
        
        /** 用户ID */
        private String userId;
        
        /** 销售人员姓名 */
        private String salesName;

        /** 姓名 (前端兼容字段) */
        private String nickName;

        /** 手机号 */
        private String phone;

        /** 手机号 (前端兼容字段) */
        private String phonenumber;
        
        /** 邮箱 */
        private String email;

        /** 性别 (0: 男, 1: 女, 2: 未知) */
        private String sex;

        /** 所属销售组ID */
        private String groupId;
        
        /** 所属销售组名称 */
        private String groupName;
        
        /** 角色类型 */
        private String roleType;
        
        /** 角色名称 */
        private String roleName;
        
        /** 部门ID */
        private Long deptId;
        
        /** 部门名称 */
        private String deptName;
        
        /** 状态 */
        private String status;
        
        /** 负责学生数 */
        private Integer studentCount;
        
        /** 入职时间 */
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date joinTime;
        
        /** 创建时间 */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date createTime;
        
        /** 更新时间 */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date updateTime;
    }

    /**
     * 创建销售人员请求
     */
    @Data
    public static class CreateReq {
        /** 销售人员姓名 */
        @NotBlank(message = "销售人员姓名不能为空")
        private String salesName;

        /** 手机号 */
        @NotBlank(message = "手机号不能为空")
        private String phone;

        /** 邮箱 */
        private String email;

        /** 性别 (0: 男, 1: 女, 2: 未知) */
        private String sex;

        /** 所属销售组ID */
        private String groupId;

        /** 入职时间 */
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date joinTime;
    }

    /**
     * 更新销售人员请求
     */
    @Data
    public static class UpdateReq {
        /** ID */
        @NotBlank(message = "ID不能为空")
        private String id;

        /** 销售人员姓名 */
        @NotBlank(message = "销售人员姓名不能为空")
        private String salesName;

        /** 手机号 */
        @NotBlank(message = "手机号不能为空")
        private String phone;

        /** 邮箱 */
        private String email;

        /** 性别 (0: 男, 1: 女, 2: 未知) */
        private String sex;

        /** 所属销售组ID */
        private String groupId;

        /** 部门ID (可选，销售人员可以不指定部门) */
        private Long deptId;

        /** 状态 */
        private String status;

        /** 入职时间 */
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date joinTime;
    }

    /**
     * 获取可用销售人员请求
     */
    @Data
    public static class GetAvailableReq {
        /** 排除的销售组ID */
        private String excludeGroupId;
        
        /** 角色类型 */
        private String roleType;
        
        /** 部门ID */
        private Long deptId;
    }

    /**
     * 可用销售人员响应
     */
    @Data
    public static class AvailableResp {
        /** ID */
        private String id;
        
        /** 用户ID */
        private String userId;
        
        /** 销售人员姓名 */
        private String salesName;
        
        /** 手机号 */
        private String phone;
        
        /** 角色类型 */
        private String roleType;
        
        /** 角色名称 */
        private String roleName;
        
        /** 部门名称 */
        private String deptName;
        
        /** 负责学生数 */
        private Integer studentCount;
    }

    /**
     * 销售人员统计响应
     */
    @Data
    public static class StatsResp {
        /** 总销售人员数 */
        private Integer totalSales;
        
        /** 活跃销售人员数 */
        private Integer activeSales;
        
        /** 已分配销售人员数 */
        private Integer assignedSales;
        
        /** 未分配销售人员数 */
        private Integer unassignedSales;
        
        /** 组长数量 */
        private Integer leaderCount;
        
        /** 普通销售数量 */
        private Integer memberCount;
    }

    /**
     * 分配销售到销售组请求
     */
    @Data
    public static class AssignReq {
        /** 销售人员ID */
        @NotBlank(message = "销售人员ID不能为空")
        private String salesId;
        
        /** 销售组ID */
        @NotBlank(message = "销售组ID不能为空")
        private String groupId;
        
        /** 角色类型 */
        private String roleType = "member";
    }

    /**
     * 批量分配销售到销售组请求
     */
    @Data
    public static class BatchAssignReq {
        /** 销售人员ID列表 */
        @NotEmpty(message = "销售人员ID列表不能为空")
        private List<String> salesIds;

        /** 销售组ID */
        @NotBlank(message = "销售组ID不能为空")
        private String groupId;

        /** 角色类型 */
        private String roleType = "member";
    }

    /**
     * 销售人员选项响应（用于下拉选择）
     */
    @Data
    public static class OptionResp {
        private String id;
        private String name;
        private String phone;
        private String groupName;
    }
}
