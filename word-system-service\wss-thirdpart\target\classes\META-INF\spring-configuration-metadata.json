{"groups": [{"name": "allinpay", "type": "org.nonamespace.word.thirdpart.allinpay.config.AllinPayConfig", "sourceType": "org.nonamespace.word.thirdpart.allinpay.config.AllinPayConfig"}, {"name": "esign", "type": "org.nonamespace.word.thirdpart.esign.config.ESignConfig", "sourceType": "org.nonamespace.word.thirdpart.esign.config.ESignConfig"}], "properties": [{"name": "allinpay.appid", "type": "java.lang.String", "description": "应用ID", "sourceType": "org.nonamespace.word.thirdpart.allinpay.config.AllinPayConfig"}, {"name": "allinpay.base-url", "type": "java.lang.String", "description": "统一支付API", "sourceType": "org.nonamespace.word.thirdpart.allinpay.config.AllinPayConfig"}, {"name": "allinpay.connect-timeout", "type": "java.lang.Integer", "description": "连接超时时间（毫秒）", "sourceType": "org.nonamespace.word.thirdpart.allinpay.config.AllinPayConfig"}, {"name": "allinpay.cusid", "type": "java.lang.String", "description": "商户号", "sourceType": "org.nonamespace.word.thirdpart.allinpay.config.AllinPayConfig"}, {"name": "allinpay.front-url", "type": "java.lang.String", "description": "默认前端跳转地址", "sourceType": "org.nonamespace.word.thirdpart.allinpay.config.AllinPayConfig"}, {"name": "allinpay.notify-url", "type": "java.lang.String", "description": "默认通知地址", "sourceType": "org.nonamespace.word.thirdpart.allinpay.config.AllinPayConfig"}, {"name": "allinpay.orgid", "type": "java.lang.String", "description": "集团/代理商商户号", "sourceType": "org.nonamespace.word.thirdpart.allinpay.config.AllinPayConfig"}, {"name": "allinpay.private-key", "type": "java.lang.String", "description": "商户私钥", "sourceType": "org.nonamespace.word.thirdpart.allinpay.config.AllinPayConfig"}, {"name": "allinpay.public-key", "type": "java.lang.String", "description": "通联公钥", "sourceType": "org.nonamespace.word.thirdpart.allinpay.config.AllinPayConfig"}, {"name": "allinpay.read-timeout", "type": "java.lang.Integer", "description": "读取超时时间（毫秒）", "sourceType": "org.nonamespace.word.thirdpart.allinpay.config.AllinPayConfig"}, {"name": "allinpay.valid-time", "type": "java.lang.Integer", "sourceType": "org.nonamespace.word.thirdpart.allinpay.config.AllinPayConfig"}, {"name": "esign.app-id", "type": "java.lang.String", "description": "应用ID", "sourceType": "org.nonamespace.word.thirdpart.esign.config.ESignConfig"}, {"name": "esign.app-secret", "type": "java.lang.String", "description": "应用密钥", "sourceType": "org.nonamespace.word.thirdpart.esign.config.ESignConfig"}, {"name": "esign.base-url", "type": "java.lang.String", "description": "e签宝API基础地址", "sourceType": "org.nonamespace.word.thirdpart.esign.config.ESignConfig"}, {"name": "esign.connect-timeout", "type": "java.lang.Integer", "description": "连接超时时间（毫秒）", "sourceType": "org.nonamespace.word.thirdpart.esign.config.ESignConfig"}, {"name": "esign.doc-template-id", "type": "java.lang.String", "description": "合同模板ID", "sourceType": "org.nonamespace.word.thirdpart.esign.config.ESignConfig"}, {"name": "esign.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用", "sourceType": "org.nonamespace.word.thirdpart.esign.config.ESignConfig"}, {"name": "esign.read-timeout", "type": "java.lang.Integer", "description": "读取超时时间（毫秒）", "sourceType": "org.nonamespace.word.thirdpart.esign.config.ESignConfig"}, {"name": "esign.sdk-version", "type": "java.lang.String", "sourceType": "org.nonamespace.word.thirdpart.esign.config.ESignConfig"}], "hints": []}