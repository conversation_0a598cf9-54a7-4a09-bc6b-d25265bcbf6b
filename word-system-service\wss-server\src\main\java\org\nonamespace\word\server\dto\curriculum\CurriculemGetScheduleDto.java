package org.nonamespace.word.server.dto.curriculum;

import lombok.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
public class CurriculemGetScheduleDto {

    @Data
    public static class Req {
        private String teacherId;
        private String studentId;
        private String type;
        private String status;
        private Date startDate;
        private Date endDate;
        private String viewType; // "week", "month", "today", "future"

    }

    @Data
    public static class Course {
        private String id;
        private String type;
        private String courseType; // 课程性质：正式课、试听课
        private String teacherId;
        private String teacherName;
        private String studentId;
        private String studentName;
        private Date startTime;
        private Date endTime;
        private Long duration;
        private String status;
        private String subject;
        private String specification;
    }
}
