package org.nonamespace.word.common.utils;

import java.util.*;

public class SentenceInsertPipesUtils {

    /**
     * 在句子中插入|
     * @param sentence  句子
     * @param pipeCount  插入数量
     * @param c  插入字符
     * @return
     */
    // 用于存储已经使用过的插入位置组合
    private static final Set<List<Integer>> usedInsertionCombinations = new HashSet<>();

    public static List<String> insertPipesCount(String sentence, int resultCount, int pipeCount, char c) {
        List<String> results = new ArrayList<>();
        usedInsertionCombinations.clear(); // 清空历史记录

        // 找出所有空格的位置
        List<Integer> allSpaceIndices = new ArrayList<>();
        for (int i = 0; i < sentence.length(); i++) {
            if (sentence.charAt(i) == ' ') {
                allSpaceIndices.add(i);
            }
        }

        // 如果没有空格，直接返回原句的重复
        if (allSpaceIndices.isEmpty()) {
            return Collections.nCopies(resultCount, sentence);
        }

        // 计算实际可以插入的数量
        int actualPipeCount = Math.min(pipeCount, allSpaceIndices.size());

        // 生成指定数量的不重复结果
        while (results.size() < resultCount) {
            // 如果所有可能的组合都已用完，则跳出循环
            if (usedInsertionCombinations.size() >= combinationCount(allSpaceIndices.size(), actualPipeCount)) {
                break;
            }

            // 随机选择不同的空格位置
            List<Integer> shuffledSpaces = new ArrayList<>(allSpaceIndices);
            Collections.shuffle(shuffledSpaces);
            List<Integer> selectedIndices = shuffledSpaces.subList(0, actualPipeCount);
            Collections.sort(selectedIndices); // 排序以便比较组合是否相同

            // 检查这个组合是否已经使用过
            if (!usedInsertionCombinations.contains(selectedIndices)) {
                usedInsertionCombinations.add(selectedIndices);

                // 构建新字符串
                StringBuilder sb = new StringBuilder(sentence);
                // 从后往前插入，避免影响前面的索引
                for (int i = selectedIndices.size() - 1; i >= 0; i--) {
                    int index = selectedIndices.get(i);
                    sb.insert(index, c);
                }

                results.add(sb.toString());
            }
        }

        // 如果无法生成足够的不重复结果，用已生成的填充
        while (results.size() < resultCount) {
            results.add(results.get(results.size() % results.size()));
        }

        return results;
    }

    // 计算组合数 C(n,k)
    private static int combinationCount(int n, int k) {
        if (k > n) return 0;
        if (k == 0 || k == n) return 1;
        return combinationCount(n-1, k-1) + combinationCount(n-1, k);
    }


    public static void main(String[] args) {
        System.out.println(insertPipesCount("thit is my book as ss  dd ", 3, 2, '|'));
    }


}
