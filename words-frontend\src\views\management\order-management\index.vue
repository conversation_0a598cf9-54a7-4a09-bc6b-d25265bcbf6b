<template>
  <div class="order-management-container">
    <!-- 统计信息卡片 -->
    <div v-if="statistics" class="statistics-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.totalOrders }}</div>
              <div class="stat-label">总订单数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.todayOrders }}</div>
              <div class="stat-label">今日订单</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.monthOrders }}</div>
              <div class="stat-label">本月订单</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">¥{{ statistics.totalAmount }}</div>
              <div class="stat-label">总交易金额</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline label-width="80px">
        <el-form-item label="订单号">
          <el-input
            v-model="searchForm.orderNo"
            placeholder="请输入订单号"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="学生姓名">
          <el-input
            v-model="searchForm.studentName"
            placeholder="请输入学生姓名"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="销售员">
          <el-input
            v-model="searchForm.salerName"
            placeholder="请输入销售员姓名"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select
            v-model="searchForm.orderStatus"
            placeholder="请选择订单状态"
            clearable
            style="width: 150px"
          >
            <el-option label="未付款" value="未付款" />
            <el-option label="已全额支付" value="已全额支付" />
            <el-option label="已部分支付" value="已部分支付" />
            <el-option label="已取消" value="已取消" />
            <el-option label="已退款" value="已退款" />
          </el-select>
        </el-form-item>
        <el-form-item label="支付方式">
          <el-select
            v-model="searchForm.trxMethod"
            placeholder="请选择支付方式"
            clearable
            style="width: 150px"
          >
            <el-option label="一次性支付" value="一次性支付" />
            <el-option label="分期支付" value="分期支付" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新建订单
          </el-button>
          <el-button type="success" @click="handleExportOrders">
            <el-icon><Download /></el-icon>
            导出订单
          </el-button>
          <el-button type="info" @click="handleExportTrx">
            <el-icon><Document /></el-icon>
            导出流水
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 订单列表 -->
    <el-card class="table-card">
      <!-- 批量操作工具栏 -->
      <div class="batch-operations" style="margin-bottom: 16px;">
        <el-button 
          type="danger" 
          :disabled="selectedOrders.length === 0"
          @click="handleBatchCancel"
        >
          批量取消 ({{ selectedOrders.length }})
        </el-button>
      </div>

      <el-table
        v-loading="loading"
        :data="orderList"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="订单号" prop="no" width="220" />
        <el-table-column label="订单标题" prop="body" min-width="200" show-overflow-tooltip />
        <el-table-column label="学生姓名" prop="studentName" width="120" />
        <el-table-column label="销售员" prop="salerName" width="120" />
        <el-table-column label="订单金额" prop="totalAmt" width="120" align="center">
          <template #default="{ row }">
            ¥{{ (row.totalAmt / 100).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="已付款金额" prop="amtPaid" width="120" align="center">
          <template #default="{ row }">
            ¥{{ (row.amtPaid / 100).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="未付款金额" prop="amtUnpaid" width="120" align="center">
          <template #default="{ row }">
            ¥{{ (row.amtUnpaid / 100).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="支付方式" prop="trxMethod" width="120" align="center" />
        <el-table-column label="订单状态" prop="orderStatus" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.orderStatus)" size="small">
              {{ row.orderStatus }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="签署状态" prop="signStatus" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getSignStatusType(row.signStatus)" size="small">
            {{ row.signStatus || '未签署' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" width="180">
        <template #default="{ row }">
          {{ formatDateTime(row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="320" fixed="right">
        <template #default="{ row }">
          <el-button
            type="primary"
            link
            @click="handleView(row)"
          >
            查看详情
          </el-button>
          <el-button
            v-if="row.orderStatus === '未付款'"
            type="success"
            link
            @click="handlePay(row)"
          >
            支付
          </el-button>
          <el-button
              v-if="row.orderStatus === '已部分支付'"
              type="success"
              link
              @click="handlePay(row)"
          >
            继续支付
          </el-button>
          <el-button
            v-if="canSign(row)"
            type="warning"
            link
            @click="handleSign(row)"
          >
            合同签署
          </el-button>
          <el-button
            v-if="canRefund(row)"
            type="warning"
            link
            @click="handleRefund(row)"
          >
            退款
          </el-button>
          <el-button
            v-if="row.orderStatus === '未付款'"
            type="danger"
            link
            @click="handleCancel(row)"
          >
            取消订单
          </el-button>
        </template>
      </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.pageNum"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 订单详情对话框 -->
    <OrderDetailDialog
      v-model="detailOpen"
      :order-detail="orderDetail"
      @refresh="fetchData"
      @edit="handleEditOrder"
      @pay-transaction="handlePayTransaction"
    />

    <!-- 支付对话框 -->
    <PaymentDialog
      v-model="payOpen"
      :transaction="currentTransaction"
      @success="handlePaymentSuccess"
      @generate-qr="handleGenerateQR"
      @copy-link="handleCopyLink"
      @send-wechat="handleSendWechat"
    />

    <!-- 合同签署对话框 -->
    <ContractSignDialog
      v-model="contractSignVisible"
      :order-info="currentOrder"
      @success="handleContractSignSuccess"
    />

    <!-- 订单退款对话框 -->
    <OrderRefundDialog
      v-model="refundVisible"
      :order-info="currentOrder"
      @success="handleRefundSuccess"
    />

    <!-- 订单导出对话框 -->
    <OrderExportDialog
      v-model="orderExportVisible"
      :is-manager="true"
    />

    <!-- 交易流水导出对话框 -->
    <OrderTrxExportDialog
      v-model="trxExportVisible"
      :is-manager="true"
    />
  </div>
</template>

<script setup name="OrderManagement">
import {onMounted, reactive, ref} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {Plus, Refresh, Search, Download, Document} from '@element-plus/icons-vue'
import {useRouter} from 'vue-router'
import {formatDateTime} from '@/utils/date.js'
import {
  batchCancelOrdersApi,
  cancelOrderApi,
  generateQRCodeApi,
  getOrderManagerDetailApi,
  getOrderManagerListApi,
  getOrderStatisticsApi,
  getOrderTransactionsApi
} from '@/api/management/order-manager'
import ContractSignDialog from './components/ContractSignDialog.vue'
import OrderDetailDialog from './components/OrderDetailDialog.vue'
import PaymentDialog from './components/PaymentDialog.vue'
import OrderRefundDialog from './components/OrderRefundDialog.vue'
import OrderExportDialog from '@/components/OrderExport/OrderExportDialog.vue'
import OrderTrxExportDialog from '@/components/OrderExport/OrderTrxExportDialog.vue'

// 响应式数据
const loading = ref(false)
const orderList = ref([])
const detailOpen = ref(false)
const payOpen = ref(false)
const dateRange = ref([])
const selectedOrders = ref([]) // 新增：选中的订单
const statistics = ref(null) // 新增：统计信息
const router = useRouter();

// 搜索表单
const searchForm = reactive({
  orderNo: '',
  studentName: '',
  salerName: '',
  orderStatus: '',
  trxMethod: ''
})

// 分页数据
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

const orderDetail = ref(null)
const currentTransaction = ref(null)
const contractSignVisible = ref(false)
const currentOrder = ref(null)
const refundVisible = ref(false)
const orderExportVisible = ref(false)
const trxExportVisible = ref(false)

// 方法
const fetchData = async () => {
  try {
    loading.value = true
    
    // 构建查询参数
    const params = {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      orderNo: searchForm.orderNo,
      studentName: searchForm.studentName,
      salerName: searchForm.salerName,
      orderStatus: searchForm.orderStatus,
      trxMethod: searchForm.trxMethod
    }
    
    // 添加时间范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      params.beginTime = dateRange.value[0]
      params.endTime = dateRange.value[1]
    }
    
    const response = await getOrderManagerListApi(params)
    if (response.code === 200) {
      orderList.value = response.rows || []
      pagination.total = response.total || 0
    } else {
      ElMessage.error(response.msg || '获取订单列表失败')
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.pageNum = 1
  fetchData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    orderNo: '',
    studentName: '',
    salerName: '',
    orderStatus: '',
    trxMethod: ''
  })
  dateRange.value = []
  handleSearch()
}

const handleSelectionChange = (selection) => {
  selectedOrders.value = selection
}

// 新增：批量取消订单
const handleBatchCancel = async () => {
  if (selectedOrders.value.length === 0) {
    ElMessage.warning('请选择要取消的订单')
    return
  }
  
  try {
    await ElMessageBox.confirm('确认要批量取消选中的订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const orderIds = selectedOrders.value.map(order => order.id)
    const response = await batchCancelOrdersApi(orderIds)
    
    if (response.code === 200) {
      ElMessage.success(response.data || '批量取消成功')
      fetchData()
      selectedOrders.value = []
    } else {
      ElMessage.error(response.msg || '批量取消失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量取消订单失败:', error)
      ElMessage.error('批量取消订单失败')
    }
  }
}

// 新增：获取统计信息
const fetchStatistics = async () => {
  try {
    const response = await getOrderStatisticsApi()
    if (response.code === 200) {
      statistics.value = response.data
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
  }
}

const handleAdd = () => {
  // 跳转到订单创建页面
  router.push('/product/order')
}

const handleView = async (row) => {
  try {
    const response = await getOrderManagerDetailApi(row.id)
    if (response.code === 200) {
      orderDetail.value = response.data.order
      orderDetail.value.transactions = response.data.transactions
      orderDetail.value.saler = response.data.saler
      orderDetail.value.student = response.data.student
      orderDetail.value.product = response.data.product
      detailOpen.value = true
    } else {
      ElMessage.error(response.msg || '获取订单详情失败')
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败')
  }
}

const handlePay = async (row) => {
  // 获取交易流水
  const transactionsResponse = await getOrderTransactionsApi(row.id)
  const transaction = transactionsResponse.data
  if (transaction) {
    currentTransaction.value = transaction
    payOpen.value = true
  } else {
    ElMessage.error('未找到交易流水')
  }
}

const handleCancel = async (row) => {
  try {
    await ElMessageBox.confirm('确认要取消该订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await cancelOrderApi(row.id)
    ElMessage.success('取消成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消失败')
    }
  }
}

const handlePayTransaction = (transaction) => {
  currentTransaction.value = transaction
  payOpen.value = true
}



const getStatusType = (status) => {
  const statusMap = {
    '未付款': 'warning',
    '已付款': 'success',
    '已取消': 'danger',
    '已退款': 'info'
  }
  return statusMap[status] || 'info'
}

const getSignStatusType = (signStatus) => {
  const statusMap = {
    '未签署': 'warning',
    '已签署': 'success',
    '签署中': 'primary',
    '签署失败': 'danger'
  }
  return statusMap[signStatus] || 'warning'
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.pageNum = 1
  fetchData()
}

const handleCurrentChange = (page) => {
  pagination.pageNum = page
  fetchData()
}

const canSign = (row) => {
  // 判断是否可以签署合同：已付款且未签署
  return (row.orderStatus === '已全额支付' || row.orderStatus === '部分支付') &&
         row.signStatus === '未签署'
}

const canRefund = (row) => {
  // 判断是否可以退款：已付款状态
  return ['已全额支付', '已部分支付', '已付款'].includes(row.orderStatus)
}

const handleSign = (row) => {
  currentOrder.value = row
  contractSignVisible.value = true
}

const handleContractSignSuccess = () => {
  fetchData() // 刷新列表
}

// 处理退款
const handleRefund = (row) => {
  currentOrder.value = row
  refundVisible.value = true
}

// 处理退款成功
const handleRefundSuccess = () => {
  fetchData() // 刷新列表
  ElMessage.success('退款成功')
}

// 处理导出订单
const handleExportOrders = () => {
  orderExportVisible.value = true
}

// 处理导出交易流水
const handleExportTrx = () => {
  trxExportVisible.value = true
}

// 新增：处理编辑订单
const handleEditOrder = (orderDetail) => {
  // 跳转到订单编辑页面或打开编辑对话框
  router.push(`/management/order-edit/${orderDetail.id}`)
}

// 新增：处理支付成功
const handlePaymentSuccess = () => {
  fetchData() // 刷新订单列表
  ElMessage.success('支付成功')
}

// 新增：处理生成二维码
const handleGenerateQR = async (data) => {
  try {
    const response = await generateQRCodeApi(data.transaction.id)
    ElMessage.success('二维码生成成功')
  } catch (error) {
    ElMessage.error('生成二维码失败')
  }
}

// 新增：处理复制链接
const handleCopyLink = (link) => {
  ElMessage.success('支付链接已复制')
}

// 新增：处理发送微信
const handleSendWechat = (data) => {
  ElMessage.success('微信消息发送成功')
}

// 生命周期
onMounted(() => {
  fetchData()
  fetchStatistics()
})
</script>

<style scoped>
.order-detail {
  margin: 20px 0;
}

.transactions-section {
  margin-top: 30px;
}

.transactions-section h3 {
  margin-bottom: 15px;
  color: #333;
}



/* 新增统一样式 */
.order-management-container {
  padding: 20px;
}

.statistics-cards {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  
  .stat-content {
    padding: 20px 0;
  }
  
  .stat-number {
    font-size: 28px;
    font-weight: bold;
    color: #409EFF;
    margin-bottom: 8px;
  }
  
  .stat-label {
    font-size: 14px;
    color: #666;
  }
}

/* 订单详情对话框样式 */
.detail-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.order-title {
  font-weight: 500;
  color: #409EFF;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-avatar {
  background-color: #409EFF;
  color: white;
  font-size: 12px;
}

.amount-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.amount-label {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 8px;
}

.amount-value {
  font-size: 20px;
  font-weight: 600;
}

.amount-value.total {
  color: #409EFF;
}

.amount-value.paid {
  color: #67C23A;
}

.amount-value.unpaid {
  color: #F56C6C;
}

.progress-container {
  margin-top: 8px;
}

.progress-text {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  display: block;
}

.amount-text {
  font-weight: 500;
  color: #409EFF;
}

.timeline-content {
  padding: 8px 0;
}

.status-change {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.from-status {
  color: #909399;
  font-size: 14px;
}

.to-status {
  color: #409EFF;
  font-weight: 500;
  font-size: 14px;
}

.arrow-icon {
  color: #C0C4CC;
}

.status-remark {
  color: #606266;
  font-size: 13px;
  margin-bottom: 4px;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
}

.operator-info {
  color: #909399;
  font-size: 12px;
}

.remark-content {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409EFF;
  color: #606266;
  line-height: 1.6;
}

.search-card {
  margin-bottom: 20px;

  .el-form {
    .el-form-item {
      margin-bottom: 16px;
    }
  }
}

.table-card {
  .batch-operations {
    padding: 10px 0;
    border-bottom: 1px solid #ebeef5;
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
