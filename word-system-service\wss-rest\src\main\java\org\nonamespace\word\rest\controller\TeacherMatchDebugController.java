package org.nonamespace.word.rest.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.rest.base.controller.BaseController;
import org.nonamespace.word.server.domain.TeacherTimeSlot;
import org.nonamespace.word.server.dto.management.teachermatch.TeacherMatchDto;
import org.nonamespace.word.server.service.ITeacherTimeSlotService;
import org.nonamespace.word.server.util.TeacherMatchDebugUtil;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 教师匹配调试控制器
 * 
 * 用于调试教师时间段匹配问题
 */
@RestController
@RequestMapping("/debug/teacher-match")
@RequiredArgsConstructor
@Slf4j
public class TeacherMatchDebugController extends BaseController {

    private final ITeacherTimeSlotService teacherTimeSlotService;
    private final TeacherMatchDebugUtil debugUtil;

    /**
     * 调试时间段匹配逻辑
     */
    @PostMapping("/debug-time-slots")
    public AjaxResult debugTimeSlots(@RequestBody DebugRequest request) {
        try {
            log.info("开始调试时间段匹配: teacherId={}, studentTimeSlots={}", 
                request.getTeacherId(), request.getStudentTimeSlots().size());

            // 1. 验证学生时间段格式
            if (!debugUtil.validateStudentTimeSlots(request.getStudentTimeSlots())) {
                return error("学生时间段格式验证失败");
            }

            // 2. 查询教师时间段
            List<TeacherTimeSlot> teacherTimeSlots = teacherTimeSlotService.lambdaQuery()
                    .eq(TeacherTimeSlot::getTeacherId, request.getTeacherId())
                    .eq(TeacherTimeSlot::getDeleted, false)
                    .list();

            // 3. 执行调试
            debugUtil.debugTimeSlotMatching(
                request.getStudentTimeSlots(),
                teacherTimeSlots,
                request.getTeacherId(),
                request.getTeacherName()
            );

            // 4. 构建响应
            DebugResponse response = new DebugResponse();
            response.setTeacherId(request.getTeacherId());
            response.setTeacherName(request.getTeacherName());
            response.setStudentTimeSlots(request.getStudentTimeSlots());
            response.setTeacherTimeSlots(teacherTimeSlots);
            
            // 计算匹配结果
            int matchedCount = 0;
            for (TeacherMatchDto.StudentTimeSlot studentSlot : request.getStudentTimeSlots()) {
                boolean hasMatch = teacherTimeSlots.stream()
                    .anyMatch(teacherSlot -> isTimeSlotMatch(studentSlot, teacherSlot));
                if (hasMatch) {
                    matchedCount++;
                }
            }
            
            response.setMatchedTimeSlots(matchedCount);
            response.setTotalRequiredSlots(request.getStudentTimeSlots().size());
            response.setCanSatisfyAll(matchedCount == request.getStudentTimeSlots().size());

            return success(response);

        } catch (Exception e) {
            log.error("调试时间段匹配失败", e);
            return error("调试失败: " + e.getMessage());
        }
    }

    /**
     * 获取教师时间段统计信息
     */
    @GetMapping("/teacher-time-slots-stats")
    public AjaxResult getTeacherTimeSlotsStats() {
        try {
            // 查询所有教师时间段
            List<TeacherTimeSlot> allTimeSlots = teacherTimeSlotService.lambdaQuery()
                    .eq(TeacherTimeSlot::getDeleted, false)
                    .list();

            // 按状态分组统计
            Map<String, Long> statusStats = allTimeSlots.stream()
                    .collect(Collectors.groupingBy(
                        TeacherTimeSlot::getStatus,
                        Collectors.counting()
                    ));

            // 按星期几分组统计
            Map<Integer, Long> weekdayStats = allTimeSlots.stream()
                    .collect(Collectors.groupingBy(
                        TeacherTimeSlot::getWeekday,
                        Collectors.counting()
                    ));

            // 教师数量统计
            long totalTeachers = allTimeSlots.stream()
                    .map(TeacherTimeSlot::getTeacherId)
                    .distinct()
                    .count();

            StatsResponse response = new StatsResponse();
            response.setTotalTimeSlots(allTimeSlots.size());
            response.setTotalTeachers(totalTeachers);
            response.setStatusStats(statusStats);
            response.setWeekdayStats(weekdayStats);

            return success(response);

        } catch (Exception e) {
            log.error("获取教师时间段统计失败", e);
            return error("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 生成SQL调试信息
     */
    @PostMapping("/debug-sql")
    public AjaxResult debugSql(@RequestBody List<TeacherMatchDto.StudentTimeSlot> studentTimeSlots) {
        try {
            debugUtil.debugSqlConditions(studentTimeSlots);
            return success("SQL调试信息已输出到日志");
        } catch (Exception e) {
            log.error("生成SQL调试信息失败", e);
            return error("生成SQL调试信息失败: " + e.getMessage());
        }
    }

    /**
     * 检查时间段是否匹配
     */
    private boolean isTimeSlotMatch(TeacherMatchDto.StudentTimeSlot studentSlot, TeacherTimeSlot teacherSlot) {
        // 1. 检查星期几
        if (!studentSlot.getWeekday().equals(teacherSlot.getWeekday())) {
            return false;
        }
        
        // 2. 检查教师时间段状态
        if (teacherSlot.getDeleted() || !"available".equals(teacherSlot.getStatus())) {
            return false;
        }
        
        // 3. 检查时间是否有交集
        try {
            String studentStart = studentSlot.getStartTime();
            String studentEnd = studentSlot.getEndTime();
            String teacherStart = teacherSlot.getStartTime().toString();
            String teacherEnd = teacherSlot.getEndTime().toString();
            
            // 时间段有交集的条件：教师开始时间 <= 学生结束时间 AND 教师结束时间 >= 学生开始时间
            return teacherStart.compareTo(studentEnd) <= 0 && teacherEnd.compareTo(studentStart) >= 0;
        } catch (Exception e) {
            log.error("时间比较失败", e);
            return false;
        }
    }

    // 请求和响应类
    public static class DebugRequest {
        private String teacherId;
        private String teacherName;
        private List<TeacherMatchDto.StudentTimeSlot> studentTimeSlots;

        // getters and setters
        public String getTeacherId() { return teacherId; }
        public void setTeacherId(String teacherId) { this.teacherId = teacherId; }
        public String getTeacherName() { return teacherName; }
        public void setTeacherName(String teacherName) { this.teacherName = teacherName; }
        public List<TeacherMatchDto.StudentTimeSlot> getStudentTimeSlots() { return studentTimeSlots; }
        public void setStudentTimeSlots(List<TeacherMatchDto.StudentTimeSlot> studentTimeSlots) { this.studentTimeSlots = studentTimeSlots; }
    }

    public static class DebugResponse {
        private String teacherId;
        private String teacherName;
        private List<TeacherMatchDto.StudentTimeSlot> studentTimeSlots;
        private List<TeacherTimeSlot> teacherTimeSlots;
        private int matchedTimeSlots;
        private int totalRequiredSlots;
        private boolean canSatisfyAll;

        // getters and setters
        public String getTeacherId() { return teacherId; }
        public void setTeacherId(String teacherId) { this.teacherId = teacherId; }
        public String getTeacherName() { return teacherName; }
        public void setTeacherName(String teacherName) { this.teacherName = teacherName; }
        public List<TeacherMatchDto.StudentTimeSlot> getStudentTimeSlots() { return studentTimeSlots; }
        public void setStudentTimeSlots(List<TeacherMatchDto.StudentTimeSlot> studentTimeSlots) { this.studentTimeSlots = studentTimeSlots; }
        public List<TeacherTimeSlot> getTeacherTimeSlots() { return teacherTimeSlots; }
        public void setTeacherTimeSlots(List<TeacherTimeSlot> teacherTimeSlots) { this.teacherTimeSlots = teacherTimeSlots; }
        public int getMatchedTimeSlots() { return matchedTimeSlots; }
        public void setMatchedTimeSlots(int matchedTimeSlots) { this.matchedTimeSlots = matchedTimeSlots; }
        public int getTotalRequiredSlots() { return totalRequiredSlots; }
        public void setTotalRequiredSlots(int totalRequiredSlots) { this.totalRequiredSlots = totalRequiredSlots; }
        public boolean isCanSatisfyAll() { return canSatisfyAll; }
        public void setCanSatisfyAll(boolean canSatisfyAll) { this.canSatisfyAll = canSatisfyAll; }
    }

    public static class StatsResponse {
        private long totalTimeSlots;
        private long totalTeachers;
        private Map<String, Long> statusStats;
        private Map<Integer, Long> weekdayStats;

        // getters and setters
        public long getTotalTimeSlots() { return totalTimeSlots; }
        public void setTotalTimeSlots(long totalTimeSlots) { this.totalTimeSlots = totalTimeSlots; }
        public long getTotalTeachers() { return totalTeachers; }
        public void setTotalTeachers(long totalTeachers) { this.totalTeachers = totalTeachers; }
        public Map<String, Long> getStatusStats() { return statusStats; }
        public void setStatusStats(Map<String, Long> statusStats) { this.statusStats = statusStats; }
        public Map<Integer, Long> getWeekdayStats() { return weekdayStats; }
        public void setWeekdayStats(Map<Integer, Long> weekdayStats) { this.weekdayStats = weekdayStats; }
    }
}
