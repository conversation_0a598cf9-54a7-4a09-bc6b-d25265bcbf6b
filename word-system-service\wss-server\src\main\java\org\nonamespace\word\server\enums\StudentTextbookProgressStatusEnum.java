package org.nonamespace.word.server.enums;

/**
 * 复习计划状态枚举类
 * <p>
 * 该枚举类用于表示复习计划的不同状态，包括待开始、进行中、已完成和已跳过。
 * </p>
 */
public enum StudentTextbookProgressStatusEnum {
    //待开始, 进行中, 已完成, 已跳过
    WAIT_START("未学习"),
    IN_PROGRESS("学习中"),
    COMPLETED("已学习");
    private final String value;
    StudentTextbookProgressStatusEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static StudentTextbookProgressStatusEnum getByDefault(String value, StudentTextbookProgressStatusEnum defaultValue) {
        for (StudentTextbookProgressStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return defaultValue;
    }

}
