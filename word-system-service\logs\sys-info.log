21:09:17.100 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.1.Final
21:09:17.162 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarting,50] - [System|系统] - Starting WssLauncherApplication using Java 21.0.6 with PID 3976 (E:\idea_projects\words\word-system-service\wss-launcher\target\classes started by 79304 in E:\idea_projects\words\word-system-service)
21:09:17.162 [main] INFO  o.n.w.l.WssLauncherApplication - [logStartupProfileInfo,660] - [System|系统] - The following 1 profile is active: "dev"
21:09:28.807 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
21:09:30.245 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8080"]
21:09:30.247 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
21:09:30.247 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.24]
21:09:30.328 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
21:09:31.116 [main] INFO  o.n.w.c.w.f.LogMDCFilter - [init,26] - [System|系统] - LogMDCFilter 初始化完成
21:09:33.869 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - [System|系统] - {dataSource-1} inited
21:09:33.909 [main] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - [System|系统] - HHH000204: Processing PersistenceUnitInfo [name: default]
21:09:34.012 [main] INFO  o.hibernate.Version - [logVersion,44] - [System|系统] - HHH000412: Hibernate ORM core version 6.5.2.Final
21:09:34.061 [main] INFO  o.h.c.i.RegionFactoryInitiator - [initiateService,50] - [System|系统] - HHH000026: Second-level cache disabled
21:09:35.124 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,59] - [System|系统] - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
21:09:35.289 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
21:09:35.325 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
21:09:43.133 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,26] - [System|系统] - 线程池初始化中...
21:09:43.136 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,29] - [System|系统] - 线程池初始化完成...
21:09:44.263 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - [System|系统] - Using default implementation for ThreadExecutor
21:09:44.280 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - [System|系统] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
21:09:44.280 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - [System|系统] - Quartz Scheduler v.2.3.2 created.
21:09:44.280 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - [System|系统] - RAMJobStore initialized.
21:09:44.282 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - [System|系统] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

21:09:44.282 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - [System|系统] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
21:09:44.282 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - [System|系统] - Quartz scheduler version: 2.3.2
21:09:44.282 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - [System|系统] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@21e827c8
21:09:45.953 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8080"]
21:09:45.966 [main] INFO  o.q.c.QuartzScheduler - [start,547] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
21:09:45.980 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarted,56] - [System|系统] - Started WssLauncherApplication in 29.342 seconds (process running for 34.134)
21:10:09.748 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:10:12.673 [schedule-pool-1] INFO  sys-user - [run,55] - [System|系统] - [127.0.0.1]内网IP[admin][Success][登录成功]
21:10:19.367 [http-nio-8080-exec-10] INFO  o.n.w.r.c.o.OrderManagerController - [list,60] - [1|超级管理员] - 分页查询订单列表请求参数: req=OrderPageDto.Req(pageNum=1, pageSize=10, orderNo=null, studentName=null, salerName=null, orderStatus=null, trxMethod=null, beginTime=null, endTime=null)
21:14:36.452 [http-nio-8080-exec-18] INFO  o.n.w.r.c.o.OrderController - [createOrder,62] - [1|超级管理员] - 创建订单请求参数: OrderCreateDto(studentId=7209, productId=1953007841094787072, multiTrxAmts=null)
21:17:31.390 [http-nio-8080-exec-22] INFO  o.n.w.r.c.o.OrderController - [getOrderTransactions,159] - [1|超级管理员] - 查询订单交易流水请求参数: 1953444667104612352
21:17:31.735 [http-nio-8080-exec-23] INFO  o.n.w.t.a.u.AllinPaySignUtil - [sign,40] - [1|超级管理员] - [统一支付] - 签名前参数：appid=00000003&body=测试产品包&cusid=990440148166000&expiretime=20250807212231&front_url=https://your-domain.com/pay/success&notify_url=https://your-domain.com/allinpay/notify&randomstr=20250807211436625031683579201470800&remark=测试产品包待支付订单&reqsn=20250807211436625031683579201470800&signtype=RSA&trxamt=1&version=12
21:17:31.750 [http-nio-8080-exec-23] INFO  o.n.w.t.a.s.i.AllinPayServiceImpl - [executeNativepay,181] - [1|超级管理员] - [统一主扫支付] - 支付参数：{trxamt=1, reqsn=20250807211436625031683579201470800, body=测试产品包, remark=测试产品包待支付订单, expiretime=20250807212231, notify_url=https://your-domain.com/allinpay/notify, front_url=https://your-domain.com/pay/success, signtype=RSA, randomstr=20250807211436625031683579201470800, version=12, cusid=990440148166000, appid=00000003, sign=H2V+UAAM5C9UbeWGR05ra62TfVOLLZnx4NO2a2G5GDVMVEv7ADbhMitM1f00ku9F6GkTa5Lwa5bLSGHT5IgRDxAjTy27urHIr7mAecX+uXTxIwxdkVxNoLnhCIkNAJ1t12hoLwYe4RCrBQPa4CYVqswitbLgzUaKgVpDPT60SdM4Or8LVevhX5zgNcsiUeJ/+0NYlYdiOHX9QThEyQ+6mkrBToP/4DXgnxQ8bJ1d437tlFMsMKuowOqB+Z9ohEjlJJXM/jLZhtdkcYAb5ZreRRd5mquw0s/lVySipMhqzKIykwEV8Ar+eRifB2V0Y2VLA66Ov0ou3dZwAKfAY373Aw==}
21:17:32.057 [http-nio-8080-exec-23] INFO  o.n.w.t.a.s.i.AllinPayServiceImpl - [executeNativepay,190] - [1|超级管理员] - [统一主扫支付] - 接口返回：{"appid":"00000003","cusid":"990440148166000","errmsg":"生成收款码成功","payinfo":"https://syb.allinpay.com/apiweb/h5unionpay/unionnative?token=jVFUuUov90oI6t8JiGWpCSG1rTXhuObW9D7lzmBZlelZ6t8IJ0NGe0dU0xVpmgxCRQvFyfL96S5zA33KIgGf1E5SJz75tZNFNyVhO8dpJaAlx01rg0ZaInSjiMtM6kKzSreIoTqWsVUohxFx73a1nBBUDv","randomstr":"564348693824","reqsn":"20250807211436625031683579201470800","retcode":"SUCCESS","sign":"Nv18xC4MI/1PeELLnRwdHpW+3F7+bc37URKNlPEnel6YiFSN8HYf9lKX7Xp8mQhSSpFq1g5/2uT1Nh7hnqz+ycpjiR2p+4/PR3JJB86EnUyOBc1rcBZyohD1DXNJSMEX1l9w2QbiIptr5RprzsXYr0XA0sOxCJ1G3OgH+JRAkQE=","trxstatus":"0000"}
21:18:31.256 [http-nio-8080-exec-24] INFO  o.n.w.r.c.o.OrderController - [getOrderTransactions,159] - [1|超级管理员] - 查询订单交易流水请求参数: 1953444667104612352
21:19:04.986 [http-nio-8080-exec-25] INFO  o.n.w.t.a.u.AllinPaySignUtil - [sign,40] - [1|超级管理员] - [统一支付] - 签名前参数：appid=00000003&body=测试产品包&cusid=990440148166000&expiretime=20250807212404&front_url=https://your-domain.com/pay/success&notify_url=https://your-domain.com/allinpay/notify&randomstr=20250807211436625031683579201470800&remark=测试产品包待支付订单&reqsn=20250807211436625031683579201470800&signtype=RSA&trxamt=1&version=12
21:19:05.178 [http-nio-8080-exec-25] INFO  o.n.w.t.a.s.i.AllinPayServiceImpl - [executeNativepay,181] - [1|超级管理员] - [统一主扫支付] - 支付参数：{trxamt=1, reqsn=20250807211436625031683579201470800, body=测试产品包, remark=测试产品包待支付订单, expiretime=20250807212404, notify_url=https://your-domain.com/allinpay/notify, front_url=https://your-domain.com/pay/success, signtype=RSA, randomstr=20250807211436625031683579201470800, version=12, cusid=990440148166000, appid=00000003, sign=JPCUqCMiXEeWcJQNw1FuqqssYGAQEXkNKovjFS83lE/Y9cQRlB5Jxo91qRNtT0cVsVG3ogWUURkV2Tg9y0+WHQTZCxGqYXmNmQDmIborpd6za1yhcgDtOWd5ULxxMZSNbjWB1S2XqPrydMiyyymZ/q2lYeXyn6EK/kFPtu1AnGBdbbdkKOykU2E7Xwa3XaqshBHurMnoXHmyEzdJlwnQn5e+PjNdmK/tiiPKU4GF/h1lvxUXJLpknTqFMb7krXdPvIu+8gWLCGn3L0Fcq6ySuYJSjArETcgmbOP8Wz7ZjWDmFhOMagpQd+Fn2joukQUpir4aOfpDnuB9KNdt1TamrA==}
21:19:05.375 [http-nio-8080-exec-25] INFO  o.n.w.t.a.s.i.AllinPayServiceImpl - [executeNativepay,190] - [1|超级管理员] - [统一主扫支付] - 接口返回：{"appid":"00000003","cusid":"990440148166000","errmsg":"生成收款码成功","payinfo":"https://syb.allinpay.com/apiweb/h5unionpay/unionnative?token=jVFUuUov90oI6t8JiGWpCSG1rTXhuObW9D7lzmBZlelZ6t8IJ0NGe0dU0xVpmgxCRQvFyfL96S5zA33KIgGf1E5SJz75tZNFNyVhO8dpJaAlx01rg0ZaInSjiMtM6kKzSreIoTqWsVUohxFx73a1nBBUDv","randomstr":"908448954896","reqsn":"20250807211436625031683579201470800","retcode":"SUCCESS","sign":"ncoTvq2q1Yz8Qkjhj7y+wymNvS5lMrKBR5EZJCkQrOW//SnMme5QyuIyS8a0p80rfpY7CrxvHPhON0nCiTcxbWpGqHHecBjyeK0G7tsa1azv5bMWxOU6ZRSuNNMkFmsMlEBEI7aw5jzthdZ0dVh+HtH5NlJ8Clv3nl5ClwK2AiQ=","trxstatus":"0000"}
21:21:54.083 [http-nio-8080-exec-26] INFO  o.n.w.r.c.o.OrderManagerController - [list,60] - [1|超级管理员] - 分页查询订单列表请求参数: req=OrderPageDto.Req(pageNum=1, pageSize=10, orderNo=null, studentName=null, salerName=null, orderStatus=null, trxMethod=null, beginTime=null, endTime=null)
21:23:27.893 [http-nio-8080-exec-31] INFO  o.n.w.r.c.o.OrderController - [getOrderTransactions,159] - [1|超级管理员] - 查询订单交易流水请求参数: 1953444667104612352
21:23:30.093 [http-nio-8080-exec-32] INFO  o.n.w.r.c.o.OrderController - [getOrderTransactions,159] - [1|超级管理员] - 查询订单交易流水请求参数: 1953444667104612352
21:24:04.083 [http-nio-8080-exec-34] INFO  o.n.w.r.c.o.OrderController - [getOrderTransactions,159] - [1|超级管理员] - 查询订单交易流水请求参数: 1953444667104612352
21:47:07.441 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
21:47:08.483 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
21:47:08.484 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
21:47:08.485 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
21:47:08.491 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - [System|系统] - ====关闭后台任务任务线程池====
21:47:08.572 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - [System|系统] - {dataSource-1} closing ...
21:47:08.583 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - [System|系统] - {dataSource-1} closed
21:47:08.653 [SpringApplicationShutdownHook] INFO  o.n.w.c.w.f.LogMDCFilter - [destroy,58] - [System|系统] - LogMDCFilter 销毁完成
