<template>
  <div class="consumption-trend-chart">
    <div class="chart-header">
      <h3>{{ title }}</h3>
      <div class="chart-controls">
        <el-button-group>
          <el-button 
            :type="chartType === 'line' ? 'primary' : ''" 
            size="small"
            @click="chartType = 'line'"
          >
            线图
          </el-button>
          <el-button 
            :type="chartType === 'bar' ? 'primary' : ''" 
            size="small"
            @click="chartType = 'bar'"
          >
            柱图
          </el-button>
        </el-button-group>
      </div>
    </div>
    
    <div 
      ref="chartContainer" 
      class="chart-container"
      v-loading="loading"
    ></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'ConsumptionTrendChart',
  props: {
    title: {
      type: String,
      default: '课消趋势'
    },
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    height: {
      type: String,
      default: '400px'
    }
  },
  data() {
    return {
      chart: null,
      chartType: 'line'
    }
  },
  watch: {
    data: {
      handler() {
        this.$nextTick(() => {
          this.renderChart()
        })
      },
      deep: true
    },
    chartType() {
      this.renderChart()
    },
    loading(val) {
      if (!val) {
        this.$nextTick(() => {
          this.renderChart()
        })
      }
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeUnmount() {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initChart() {
      if (this.$refs.chartContainer) {
        this.chart = echarts.init(this.$refs.chartContainer)
        this.renderChart()
      }
    },
    
    renderChart() {
      if (!this.chart || !this.data || this.data.length === 0) return
      
      const xAxisData = this.data.map(item => item.weekLabel || item.label)
      const consumptionData = this.data.map(item => item.consumption || 0)
      const studentCountData = this.data.map(item => item.studentCount || 0)
      
      const option = {
        title: {
          show: false
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: (params) => {
            let result = `${params[0].axisValue}<br/>`
            params.forEach(param => {
              const unit = param.seriesName === '课消课时' ? '课时' : '人'
              result += `${param.marker}${param.seriesName}: ${param.value}${unit}<br/>`
            })
            return result
          }
        },
        legend: {
          data: ['课消课时', '学生数量'],
          top: 10
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: this.chartType === 'bar',
          data: xAxisData,
          axisLabel: {
            rotate: 45
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '课消课时',
            position: 'left',
            axisLabel: {
              formatter: '{value}课时'
            }
          },
          {
            type: 'value',
            name: '学生数量',
            position: 'right',
            axisLabel: {
              formatter: '{value}人'
            }
          }
        ],
        series: [
          {
            name: '课消课时',
            type: this.chartType,
            yAxisIndex: 0,
            data: consumptionData,
            smooth: true,
            itemStyle: {
              color: '#409EFF'
            },
            areaStyle: this.chartType === 'line' ? {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
                  { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
                ]
              }
            } : null
          },
          {
            name: '学生数量',
            type: this.chartType,
            yAxisIndex: 1,
            data: studentCountData,
            smooth: true,
            itemStyle: {
              color: '#67C23A'
            }
          }
        ]
      }
      
      this.chart.setOption(option, true)
    },
    
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style scoped>
.consumption-trend-chart {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.chart-container {
  height: v-bind(height);
  width: 100%;
}
</style>
