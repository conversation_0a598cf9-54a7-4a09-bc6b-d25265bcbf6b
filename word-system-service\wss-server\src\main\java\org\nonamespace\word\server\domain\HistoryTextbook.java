package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import org.nonamespace.word.server.entity.BaseEntity;

import java.util.Date;

/**
 * 词历史: 存储词的修改历史版本对象 history_textbook
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "词典修改历史表")
@TableName("history_textbook")
public class HistoryTextbook extends BaseEntity
{
    /** 词表ID */
    @Excel(name = "词表ID")
    @TableField("textbook_id")
    private String textbookId;

    /** 词表对象 */
    @Excel(name = "词表对象")
    @TableField("textbook_obj")
    private String textbookObj;

    /** 词表单词列表JSON数组对象 */
    @Excel(name = "词表单词列表JSON数组对象")
    @TableField("textbook_items_obj")
    private String textbookItemsObj;

    /** 修改描述 */
    @Excel(name = "修改描述")
    @TableField("change_description")
    private String changeDescription;

    /** 是否删除 */
    @Excel(name = "是否删除")
    @TableField("deleted")
    private boolean deleted = false;

    /** 修改前textbook的版本号 */
    @Excel(name = "修改前textbook的版本号")
    @TableField("version")
    private Long version;

    /** 修改用户ID */
    @Excel(name = "修改用户ID")
    @TableField("update_by")
    private String updateBy;

    /** 修改时间 */
    @Excel(name = "修改时间")
    @TableField("update_time")
    private Date updateTime;
}
