package org.nonamespace.word.rest.controller;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import jakarta.validation.Valid;
import org.nonamespace.word.server.domain.Word;
import org.nonamespace.word.server.dto.WordEditDto;
import org.nonamespace.word.server.dto.WordPageDto;
import org.nonamespace.word.server.service.IWordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 单词: 核心词库，存储所有单词的基本信息及例句Controller
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@RestController
@RequestMapping("/word/word")
//@Anonymous
public class WordController extends BaseController {

    @Autowired
    private IWordService wordService;

    /**
     * 查询单词
     * @return
     */
    @PreAuthorize("@ss.hasPermi('word:word:query')")
    @PostMapping("/page")
//    @Anonymous
    public TableDataInfo page(@RequestBody(required = false) WordPageDto.Req req) {
        /**
         * 判断是否有查询教材，或单元相关的信息.
         * 只要有一个不为空，那么就关联教材信息查询
         * @return
         */
        req.setQryTextbook(StrUtil.isNotBlank(req.getTextbookId()) || StrUtil.isNotBlank(req.getUnitId()) || StrUtil.isNotBlank(req.getTextBookType())
                || (req.getTextBookName() != null && StrUtil.isNotBlank(req.getTextBookName().getValue())));
        startPage();
        List<WordPageDto.Resp> list = wordService.page(req);
        return getDataTable(list);
    }


    /**
     * 获取单词: 核心词库，存储所有单词的基本信息及例句详细信息
     */
    @PreAuthorize("@ss.hasPermi('word:word:query')")
    @GetMapping(value = "/get/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(wordService.selectWordById(id));
    }

    /**
     * 新增单词: 核心词库，存储所有单词的基本信息及例句
     */
    @PreAuthorize("@ss.hasPermi('word:word:add')")
    @Log(title = "单词: 核心词库，存储所有单词的基本信息及例句", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Word word) {
        return toAjax(wordService.save(word));
    }

    /**
     * 修改单词: 核心词库，存储所有单词的基本信息及例句
     */
    @PreAuthorize("@ss.hasPermi('word:word:edit')")
//    @Log(title = "单词: 核心词库，存储所有单词的基本信息及例句", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@Valid @RequestBody WordEditDto.Req req) {
        // 校验单词是否存在，或者 是否有权限修改该单词
        String wordId = req.getId();
        Word word = wordService.selectWordById(wordId);
        if(word == null) {
            return AjaxResult.error("单词不存在，或没有权限修改，请联系管理员");
        }
        return toAjax(wordService.updateWord(req, word));
    }

    /**
     * 上传oss
     * @param id
     * @param files
     * @return
     */
    @PreAuthorize("@ss.hasPermi('word:word:edit')")
    @PostMapping("/upload/{id}")
    public AjaxResult upload(@PathVariable String id, @RequestParam("files") MultipartFile[] files) {
        return AjaxResult.success(wordService.uploadOss(id, files));
    }
}
