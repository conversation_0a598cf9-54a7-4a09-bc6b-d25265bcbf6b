# 订单管理合同签署功能集成

## 完成时间
2025年1月5日 17:30

## 功能概述

在订单管理列表页面集成了合同签署功能，利用项目中已有的e签宝电子合同接口实现订单合同的电子签署流程。

## 实现内容

### 1. 后端接口扩展

#### 新增订单合同签署接口
**文件**: `word-system-service/wss-rest/src/main/java/org/nonamespace/word/rest/controller/order/OrderController.java`

```java
/**
 * 发起合同签署
 */
@PreAuthorize("@ss.hasPermi('order:sign')")
@Log(title = "订单合同签署", businessType = BusinessType.UPDATE)
@PostMapping("/{orderId}/sign")
public AjaxResult initiateContractSign(@PathVariable String orderId) {
    try {
        eSignService.buildContractsFile(orderId);
        return AjaxResult.success("合同签署发起成功");
    } catch (Exception e) {
        log.error("发起合同签署失败", e);
        return error("发起合同签署失败: " + e.getMessage());
    }
}
```

**功能说明**：
- 接收订单ID参数
- 调用已有的e签宝服务生成合同文件并发起签署
- 返回签署结果

### 2. 前端API扩展

#### 新增合同签署API调用
**文件**: `words-frontend/src/api/management/order.js`

```javascript
// 发起合同签署
export function initiateContractSignApi(orderId) {
  return request({
    url: `/order/${orderId}/sign`,
    method: 'post'
  })
}
```

### 3. 订单管理页面功能增强

#### 3.1 表格列扩展
- **新增签署状态列**：显示订单的合同签署状态
- **操作列扩展**：增加"合同签署"按钮
- **列宽调整**：操作列宽度从240px调整为320px

#### 3.2 签署状态显示
```javascript
const getSignStatusType = (signStatus) => {
  const statusMap = {
    '未签署': 'warning',
    '已签署': 'success',
    '签署中': 'primary',
    '签署失败': 'danger'
  }
  return statusMap[signStatus] || 'warning'
}
```

#### 3.3 签署条件判断
```javascript
const canSign = (row) => {
  // 判断是否可以签署合同：已支付且未签署
  return (row.orderStatus === '已支付' || row.orderStatus === '部分支付') && 
         row.signStatus === '未签署'
}
```

### 4. 合同签署对话框组件

#### 新建组件
**文件**: `words-frontend/src/views/management/order-management/components/ContractSignDialog.vue`

**主要功能**：
1. **订单信息展示**：显示订单基本信息
2. **签署状态管理**：显示当前签署状态和进度
3. **签署链接操作**：
   - 复制签署链接到剪贴板
   - 发送微信通知给客户
   - 刷新签署状态
4. **签署发起**：支持首次发起和重新发起签署

**界面特性**：
- 响应式设计，适配不同屏幕尺寸
- 状态指示器，清晰显示签署进度
- 操作按钮，便于管理员操作
- 加载状态，提升用户体验

#### 组件结构
```vue
<template>
  <el-dialog title="合同签署" v-model="dialogVisible" width="600px">
    <!-- 订单信息展示 -->
    <div class="order-info">
      <el-descriptions :column="2" border>
        <!-- 订单详细信息 -->
      </el-descriptions>
    </div>
    
    <!-- 签署状态和操作 -->
    <div class="sign-status" v-if="signInfo">
      <el-alert :title="getSignStatusText(signInfo.status)" />
      <div class="sign-actions">
        <!-- 操作按钮 -->
      </div>
      <div class="sign-url" v-if="signInfo.signUrl">
        <!-- 签署链接展示 -->
      </div>
    </div>
  </el-dialog>
</template>
```

### 5. 业务流程

#### 5.1 签署条件检查
- 订单状态必须为"已支付"或"部分支付"
- 签署状态必须为"未签署"

#### 5.2 签署流程
1. **点击合同签署按钮** → 打开合同签署对话框
2. **查看订单信息** → 确认订单详情
3. **发起签署** → 调用后端接口生成合同和签署链接
4. **获取签署链接** → 系统返回e签宝签署链接
5. **通知客户** → 通过微信或其他方式发送链接给客户
6. **跟踪状态** → 实时查看签署进度

#### 5.3 状态管理
- **未签署** (warning) - 初始状态
- **签署中** (primary) - 已发起，等待客户签署
- **已签署** (success) - 签署完成
- **签署失败** (danger) - 签署过程中出现错误

### 6. 技术特点

#### 6.1 集成现有功能
- 充分利用项目中已有的e签宝接口
- 复用现有的订单管理基础设施
- 保持与现有代码风格的一致性

#### 6.2 用户体验优化
- 直观的状态显示和操作引导
- 便捷的链接复制和分享功能
- 实时的状态更新和反馈

#### 6.3 错误处理
- 完善的异常捕获和用户提示
- 支持重新发起签署功能
- 状态回滚和恢复机制

## 使用说明

### 管理员操作流程
1. 在订单管理列表中找到已支付的订单
2. 点击"合同签署"按钮
3. 在弹出的对话框中查看订单信息
4. 点击"发起签署"按钮
5. 复制生成的签署链接
6. 通过微信或其他方式发送给客户
7. 跟踪签署状态直至完成

### 客户操作流程
1. 接收管理员发送的签署链接
2. 点击链接进入e签宝签署页面
3. 按照页面提示完成电子签名
4. 签署完成后系统自动更新状态

## 后续优化建议

1. **状态同步**：实现与e签宝的状态回调同步
2. **批量操作**：支持批量发起合同签署
3. **模板管理**：支持不同类型订单使用不同合同模板
4. **签署提醒**：定时提醒客户完成签署
5. **文档管理**：已签署合同的下载和归档功能

## 相关文件

### 后端文件
- `OrderController.java` - 订单控制器，新增合同签署接口
- `IESignService.java` - e签宝服务接口（已存在）
- `ESignServiceImpl.java` - e签宝服务实现（已存在）

### 前端文件
- `order.js` - 订单API，新增合同签署接口调用
- `order-management/index.vue` - 订单管理主页面
- `ContractSignDialog.vue` - 合同签署对话框组件

### 依赖关系
- 依赖项目中已有的e签宝第三方集成
- 依赖订单管理的基础数据结构
- 依赖Element Plus UI组件库
