package org.nonamespace.word.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ConcurrentHashSet;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.sign.Md5Utils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.config.AliyunOssConfig;
import org.nonamespace.word.common.utils.OssService;
import org.nonamespace.word.common.utils.SentenceSplitter;
import org.nonamespace.word.common.utils.WordEnrichThreadPoolUtil;
import org.nonamespace.word.common.utils.io.CustomMultipartFile;
import org.nonamespace.word.openai.model.WordInfo;
import org.nonamespace.word.openai.model.enums.ModelEnum;
import org.nonamespace.word.openai.service.*;
import org.nonamespace.word.server.domain.HistoryWord;
import org.nonamespace.word.server.domain.Word;
import org.nonamespace.word.server.domain.WordEnrichErrors;
import org.nonamespace.word.server.domain.WordFetchClawSentenceAudio;
import org.nonamespace.word.server.dto.WordEditDto;
import org.nonamespace.word.server.dto.WordPageDto;
import org.nonamespace.word.server.mapper.HistoryWordMapper;
import org.nonamespace.word.server.mapper.WordFetchLogMapper;
import org.nonamespace.word.server.mapper.WordMapper;
import org.nonamespace.word.server.service.IWordEnrichErrorService;
import org.nonamespace.word.server.service.IWordFetchClawSentenceAudioService;
import org.nonamespace.word.server.service.IWordService;
import org.nonamespace.word.server.service.IYoudaoVoiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 单词: 核心词库，存储所有单词的基本信息及例句Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Slf4j
@Service
public class WordServiceImpl extends ServiceImpl<WordMapper, Word> implements IWordService {

    @Autowired
    private WordMapper wordMapper;
    @Autowired
    private HistoryWordMapper historyWordMapper;
    @Autowired
    private OssService ossService;
    @Autowired
    private ISiliconflowService siliconflowService;
    @Autowired
    private IYoudaoVoiceService youdaoVoiceService;
    @Autowired
    private IGrokService grokService;
    @Autowired
    private IVolcengineService volcengineService;
    @Autowired
    private WordFetchLogMapper wordFetchLogMapper;
    @Autowired
    private IClawCloudRunService clawCloudRunService;
    @Autowired
    private IWordFetchClawSentenceAudioService wordFetchClawSentenceAudioService;
    @Autowired
    private IWordEnrichErrorService wordEnrichErrorService;

    @Autowired
    RedisTemplate redisTemplate;
    @Autowired
    StringRedisTemplate stringRedisTemplate;


    @Resource
    private AliyunOssConfig ossConfig;
    @Value("${word.enrich.words-model}")
    private String enrichWordsModel;


    /**
     * 查询单词: 核心词库，存储所有单词的基本信息及例句
     *
     * @param id 单词: 核心词库，存储所有单词的基本信息及例句主键
     * @return 单词: 核心词库，存储所有单词的基本信息及例句
     */
    @Override
    public Word selectWordById(String id) {
        return wordMapper.selectById(id);
    }



    /**
     * 修改单词: 核心词库，存储所有单词的基本信息及例句
     * 
     * @param req 单词: 核心词库，存储所有单词的基本信息及例句
     * @param originWord 原单词信息
     * @return 结果
     */
    @Override
    public int updateWord(WordEditDto.Req req, Word originWord) {
        // 组装编辑信息
        Word word = new Word();
        word.setId(originWord.getId());
        BeanUtil.copyProperties(req, word, CopyOptions.create().ignoreNullValue());
        word.setVersion(originWord.getVersion() + 1L);
        word.setUpdateTime(DateUtil.date());
        word.setVideoUrl(req.getVideoFile());

        // 计算标记 - 单词测验标记
        word.setFlagPracticeWord(false);
        if(word.getMeanings() != null && !word.getMeanings().isEmpty()) {
            AtomicBoolean flag = new AtomicBoolean(true);
            word.getMeanings().forEach((k, v) -> {
                // 只要有一个为空，那就是false
                if(v.getPractices() == null  || v.getPractices() .isEmpty()) {
                    flag.set(false);
                }
            });
            word.setFlagPracticeWord(flag.get());
        }

        // 计算标记 - 句子翻译标记
        word.setFlagPracticeTranslate(false);
        if(word.getSentences() != null && !word.getSentences().isEmpty()) {
            AtomicBoolean flag = new AtomicBoolean(true);
            word.getSentences().forEach((k, v) -> {
                if(!(v != null && v.stream().anyMatch(s -> s.getPractices() != null && !s.getPractices().isEmpty() && s.getPractices().stream().noneMatch(p -> p.trim().isEmpty())))){
                    flag.set(false);
                }
            });
            word.setFlagPracticeTranslate(flag.get());
        }
        // 计算标记 - 句子排序标记
        word.setFlagPracticeOrder(false);
        if(word.getSentences() != null && !word.getSentences().isEmpty()) {
            AtomicBoolean flag = new AtomicBoolean(true);
            word.getSentences().forEach((k, v) -> {
                if(!(v != null && v.stream().anyMatch(s -> s.getStructurePartsEn() != null && !s.getStructurePartsEn().isEmpty() && s.getStructurePartsEn().stream().noneMatch(p -> p.trim().isEmpty())))){
                    flag.set(false);
                }
            });
            word.setFlagPracticeOrder(flag.get());
        }
        int flag = wordMapper.updateById(word);

        ThreadUtil.execute(() -> {
            // 保存历史记录
            HistoryWord history = HistoryWord.builder().wordId(originWord.getId())
                    .version(originWord.getVersion())
                    .wordObj(JSONUtil.toJsonStr(originWord))
                    .changeDescription(req.getChangeDescription())
                    .build();
            history.setId(IdUtil.getSnowflakeNextIdStr());
            history.setCreateTime(DateUtil.date());
            history.setUpdateTime(DateUtil.date());
            historyWordMapper.insert(history);
        });

        return flag;
    }


    /**
     * 分页查询
     * @param req
     * @return
     */
    @Override
    public List<WordPageDto.Resp> page(WordPageDto.Req req) {
        return wordMapper.selectPageByParam(req);
    }

    /**
     * 单词补全
     *  - 如果有传单词列表，则这些单词需要全部补全。
     *      - 如果单词存在库里，则更新
     *      - 如果单词不在库里，则忽略。 这里可以先把单词初始化入库后，在做更新。从而保留更新记录
     *  - 如果没有传单词列表，则获取单词。获取规则：
     *      word，phonetic_uk，phonetic_us，meanings，sentences，syllable  这些任何一个缺的都要补全
     * @param words
     * @return
     */
    @Deprecated
    @Override
    public void enrich(Set<String> words, boolean ifNullEnrichOther) {
        if(CollUtil.isEmpty(words) && !ifNullEnrichOther) {
            return ;
        }

        LambdaQueryWrapper<Word> queryWrapper = new LambdaQueryWrapper<Word>().eq(Word::getDeleted, false);
        List<Word> wordList = List.of();
        // 优先补齐传进来的单词
        if(CollUtil.isNotEmpty(words)){
            queryWrapper.in(Word::getWord, words);
            wordList = this.list(queryWrapper);
        } else if(ifNullEnrichOther) {
            wordList = wordMapper.listUnRichWords();
        }

        if(CollUtil.isEmpty(wordList)) {
            log.warn("[单词补全] 没有需要补全的单词.");
            return ;
        }
        CountDownLatch countDownLatch = new CountDownLatch(wordList.size());
        for (Word word : wordList) {
            WordEnrichThreadPoolUtil.executeTask(() -> {
                long start0 = System.currentTimeMillis();
                try {
                    log.info("[单词补全] 正在补全单词...{}", word.getWord());
                    // redis 判断该单词是否补全失败过
                    String redisStatus = stringRedisTemplate.opsForValue().get("word:lock:enrich:" + word.getWord());
                    if(redisStatus != null) {
                        log.warn("[单词补全] {} 补全失败过，直接跳过。 失败原因：{}", word.getWord(), redisStatus);
                        return ;
                    }
                    WordInfo wordInfo = BeanUtil.copyProperties(word, WordInfo.class);
                    WordInfo enrichWordInfo;
                    if (StrUtil.isNotBlank(enrichWordsModel) && enrichWordsModel.equalsIgnoreCase("siliconflow")) {
                        log.info("[单词补全] 使用硅基流动");
                        // 使用硅基流动
                        List<WordInfo> enrichWordInfos = siliconflowService.enrich(List.of(wordInfo));
                        enrichWordInfo = CollUtil.isNotEmpty(enrichWordInfos) ? enrichWordInfos.getFirst() : null;
                    } else if (StrUtil.isNotBlank(enrichWordsModel) && enrichWordsModel.equalsIgnoreCase("nebuis")) {
                        log.info("[单词补全] 使用nebuis api");
                        // 使用grok api
                        List<WordInfo> enrichWordInfos = nebiusService.enrich(List.of(wordInfo));
                        enrichWordInfo = CollUtil.isNotEmpty(enrichWordInfos) ? enrichWordInfos.getFirst() : null;
                    } else {
                        log.info("[单词补全] 使用grok api");
                        // 使用grok api
                        List<WordInfo> enrichWordInfos = grokService.enrich(List.of(wordInfo));
                        enrichWordInfo = CollUtil.isNotEmpty(enrichWordInfos) ? enrichWordInfos.getFirst() : null;
                    }

                    // 补全数据
                    if (enrichWordInfo == null) {
                        log.info("补全数据为空。单词={}", word.getWord());
                        stringRedisTemplate.opsForValue().set("word:lock:enrich:" + word.getWord(), "补全数据为空", 30, TimeUnit.MINUTES);
                        return;
                    }
                    // 组装WordEditDto.Req
                    WordEditDto.Req req = BeanUtil.copyProperties(enrichWordInfo, WordEditDto.Req.class);
                    req.setPhoneticUk(enrichWordInfo.getPhoneticUk());
                    req.setPhoneticUs(enrichWordInfo.getPhoneticUs());
                    Map<String, List<Word.Sentences>> sentencesTypeMap = JSONUtil.toBean(JSONUtil.toJsonStr(enrichWordInfo.getSentences()), new TypeReference<>() {
                    }, false);
                    Map<String, Word.Meanings> meaningsMap = JSONUtil.toBean(JSONUtil.toJsonStr(enrichWordInfo.getMeanings()), new TypeReference<>() {
                    }, false);

                    if(req.getMeanings() == null || req.getMeanings().isEmpty()) {
                        // 为空的话，整个写进去
                        req.setMeanings(meaningsMap);
                    } else {
                        // 补充meanings
                        req.getMeanings().forEach((k, v) -> {
                            Word.Meanings meanings = meaningsMap.get(k);
                            if (meanings != null) {
                                v.setPos(meanings.getPos());
                                v.setPractices(meanings.getPractices());
                            }
                        });
                    }

                    if(req.getSentences() == null || req.getSentences().isEmpty()) {
                        // 为空的话，整个写进去
                        req.setSentences(sentencesTypeMap);
                    } else {
                        // 补充sentences
                        req.getSentences().forEach((k, v) -> {
                            List<Word.Sentences> sentences = sentencesTypeMap.get(k);
                            if (CollUtil.isNotEmpty(sentences)) {
                                v.clear();
                                sentences.forEach(sen -> sen.setStructurePartsEn(SentenceSplitter.insertPipesCount(sen.getSentenceEn(), 5, 3, "|")));
                                v.addAll(sentences);
                            }
                        });
                    }
                    req.setId(word.getId());
                    this.updateWord(req, word);
                    log.info("[单词补全] {} 补全完成", word.getWord());
                } catch (Exception ignored) {
                    log.error("[单词补全] {} 补全单词出现异常:", word.getWord(), ignored);
                    stringRedisTemplate.opsForValue().set("word:lock:enrich:" + word.getWord(), "补全单词出现异常:" + ignored.getMessage(), 30, TimeUnit.MINUTES);
                    long end0 = System.currentTimeMillis();
                    // 写入数据库
                    wordEnrichErrorService.save(WordEnrichErrors.builder().word(word.getWord()).response(ignored.getMessage()).createTime(DateUtil.date()).millTimes(end0 - start0).build());
                } finally {
                    countDownLatch.countDown();
                }
            });
        }

        try {
            countDownLatch.await();
            log.info("[单词补全] 单词补全任务已完成，共补全{}条", wordList.size());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    public void fetchVoices(Set<String> words, int batchSize, ModelEnum modelEnum) {
        try {
            List<Word> wordList = this.lambdaQuery()
                    .in(CollUtil.isNotEmpty(words), Word::getWord, words)
                    // 如果有传单词列表，则单词列表重新更新
                    // 如果没有传单词列表，则只获取音标文件为空的单词刷新
                    .and(CollUtil.isEmpty(words), qw ->
                            qw.isNull(Word::getAudioUsUrl)
                                    .or()
                                    .isNull(Word::getAudioUkUrl)
                    )
                    .list();
            if (CollUtil.isEmpty(wordList)) {
                return;
            }
            List<List<Word>> partitioned = ListUtil.partition(wordList, batchSize);
            CountDownLatch countDownLatch = new CountDownLatch(partitioned.size());
            for(List<Word> list : partitioned) {
                WordEnrichThreadPoolUtil.executeTask(() -> {
                    try {
                        list.forEach(word -> {
                            WordEditDto.Req req = new WordEditDto.Req();
                            if(modelEnum.equals(ModelEnum.CLAWCLOUD)) {
                                byte[] usByte = clawCloudRunService.enrich(word.getWord(), 0);
                                byte[] ukByte = clawCloudRunService.enrich(word.getWord(), 1);
                                log.info("[{}] get us byte[] length={}", word.getWord(), usByte != null ? usByte.length : 0);
                                log.info("[{}] get uk byte[] length={}", word.getWord(), ukByte != null ? ukByte.length : 0);
                                if (usByte != null && usByte.length > 0) {
                                    req.setAudioUsUrl(this.uploadOss(word.getId(), usByte, "claw_audio_us.mp3"));
                                }
                                if (ukByte != null && ukByte.length > 0) {
                                    req.setAudioUkUrl(this.uploadOss(word.getId(), ukByte, "claw_audio_uk.mp3"));
                                }
                            }
                            this.updateWord(req, word);
                        });
                    } finally {
                        countDownLatch.countDown();
                    }
                });
            }
            countDownLatch.await();
        } catch (Exception ignored) {
            // ignored
        }
    }


//    @Override
//    public void fetchVoices(Set<String> words, int batchSize, ModelEnum modelEnum) {
//        try {
//            List<Word> wordList = this.lambdaQuery()
//                    .in(CollUtil.isNotEmpty(words), Word::getWord, words)
//                    // 如果有传单词列表，则单词列表重新更新
//                    // 如果没有传单词列表，则只获取音标文件为空的单词刷新
//                    .and(CollUtil.isEmpty(words), qw ->
//                            qw.isNull(Word::getAudioUsUrl)
//                                    .or()
//                                    .isNull(Word::getAudioUkUrl)
//                    )
//                    .list();
//            if (CollUtil.isEmpty(wordList)) {
//                return;
//            }
//            List<List<Word>> partitioned = ListUtil.partition(wordList, batchSize);
//            CountDownLatch countDownLatch = new CountDownLatch(partitioned.size());
//            for(List<Word> list : partitioned) {
//                WordEnrichThreadPoolUtil.executeTask(() -> {
//                    try {
//                        list.forEach(word -> {
//                            WordEditDto.Req req = new WordEditDto.Req();
//                            // 如果单词已经抓过了，就不需要重新抓取了
//                            List<WordFetchLog> wordFetchLogs = wordFetchLogMapper.selectList(new QueryWrapper<WordFetchLog>().lambda().eq(WordFetchLog::getWord, word.getWord()));
//                            List<WordFetchLog> fetchLogs = wordFetchLogs.stream().filter(w -> w.getYoudaoStatus() != null && w.getYoudaoStatus().equalsIgnoreCase("200")).toList();
//                            if(CollUtil.isNotEmpty(fetchLogs)){
//                                log.warn("[音标抓取] 单词[{}]，已存在抓取记录", word.getWord());
//                                WordFetchLog wordFetchLog = fetchLogs.getFirst();
//                                req.setAudioUsUrl(StrUtil.isNotBlank(wordFetchLog.getYoudaoAudioUs()) ? wordFetchLog.getYoudaoAudioUs() : wordFetchLog.getVolAudioUs());
//                                req.setAudioUkUrl(StrUtil.isNotBlank(wordFetchLog.getYoudaoAudioUk()) ? wordFetchLog.getYoudaoAudioUk() : wordFetchLog.getVolAudioUk());
//                                this.updateWord(req, word);
//                                if(StrUtil.isNotBlank(req.getAudioUsUrl()) && StrUtil.isNotBlank(req.getAudioUkUrl())) {
//                                    return ;
//                                }
//                            }
//
//                            WordFetchLog wordFetchLog = new WordFetchLog();
//                            wordFetchLog.setWord(word.getWord());
//                            if(modelEnum.equals(ModelEnum.YOUDAO)) {
//                                // 获取一下这个单词是否500，500的话 有道是没有这个单词的，没必要没出都获取。会限流
//                                if(CollUtil.isNotEmpty(wordFetchLogs)) {
//                                    // 如果包含了500的，就直接跳过
//                                    long count = wordFetchLogs.stream().filter(w -> w.getYoudaoStatus() != null && w.getYoudaoStatus().contains("500")).count();
//                                    if(count > 0) {
//                                        log.warn("[有道抓取] 单词[{}]抓取失败，已存在500的抓取记录", word.getWord());
//                                        return ;
//                                    }
//                                }
//
//                                // 有道抓取
//                                log.info("[有道抓取] 正在抓取单词[{}]...", word.getWord());
//                                byte[] audioUsBytes = youdaoVoiceService.downloadWordAudioByte(word.getWord(), 0);
//                                byte[] audioUkBytes = youdaoVoiceService.downloadWordAudioByte(word.getWord(), 1);
//                                if (audioUsBytes != null) {
//                                    wordFetchLog.setYoudaoStatus("200");
//                                    req.setAudioUsUrl(this.uploadOss(word.getId(), audioUsBytes, "yd_audio_us.mp3"));
//                                    wordFetchLog.setYoudaoAudioUs(req.getAudioUsUrl());
//                                }
//                                if (audioUkBytes != null) {
//                                    wordFetchLog.setYoudaoStatus("200");
//                                    req.setAudioUkUrl(this.uploadOss(word.getId(), audioUkBytes, "yd_audio_uk.mp3"));
//                                    wordFetchLog.setYoudaoAudioUk(req.getAudioUkUrl());
//                                }
//                                this.updateWord(req, word);
//                                wordFetchLogMapper.update(wordFetchLog, new QueryWrapper<WordFetchLog>().lambda().eq(WordFetchLog::getWord, word.getWord()));
//                            }
//                            if(modelEnum.equals(ModelEnum.VOLCENGINE)) {
//                                // 火山模型合成
//                                VolcengineTtsResponse usResp = null, ukResp = null;
//                                if(StrUtil.isBlankIfStr(word.getAudioUsUrl())){
//                                    wordFetchLog.setYoudaoStatus("200");
//                                    usResp = volcengineService.enrich(word.getWord(), 0);
//                                }
//                                if(StrUtil.isBlankIfStr(word.getAudioUkUrl())){
//                                    wordFetchLog.setYoudaoStatus("200");
//                                    ukResp = volcengineService.enrich(word.getWord(), 1);
//                                }
//                                if (usResp != null && StrUtil.isNotBlank(usResp.getData())) {
//                                    byte[] audioUsBytes = Base64.getMimeDecoder().decode(usResp.getData());
//                                    req.setAudioUsUrl(this.uploadOss(word.getId(), audioUsBytes, "vol_audio_us.mp3"));
//                                    wordFetchLog.setVolAudioUs(req.getAudioUsUrl());
//                                }
//                                if (ukResp != null && StrUtil.isNotBlank(ukResp.getData())) {
//                                    byte[] audioUkBytes = Base64.getMimeDecoder().decode(ukResp.getData());
//                                    req.setAudioUkUrl(this.uploadOss(word.getId(), audioUkBytes, "vol_audio_uk.mp3"));
//                                    wordFetchLog.setVolAudioUk(req.getAudioUkUrl());
//                                }
//                            }
//                            this.updateWord(req, word);
//                            wordFetchLogMapper.update(wordFetchLog, new QueryWrapper<WordFetchLog>().lambda().eq(WordFetchLog::getWord, word.getWord()));
//                        });
//                    } finally {
//                        countDownLatch.countDown();
//                    }
//                });
//            }
//            countDownLatch.await();
//        } catch (Exception ignored) {
//            // ignored
//        }
//    }



    @Override
    public void enrichSentencesAudio(Set<String> words, boolean ifNullEnrichOther) {
        if(CollUtil.isEmpty(words) && !ifNullEnrichOther) {
            return ;
        }

        LambdaQueryWrapper<Word> queryWrapper = new LambdaQueryWrapper<Word>().eq(Word::getDeleted, false);
        List<Word> wordList = List.of();
        // 优先补齐传进来的单词
        if(CollUtil.isNotEmpty(words)){
            queryWrapper.in(Word::getWord, words);
            wordList = this.list(queryWrapper);
        } else if(ifNullEnrichOther) {
            wordList = wordMapper.listUnRichSentencesAudio();
        }

        if(CollUtil.isEmpty(wordList)) {
            log.warn("[例句补全] 没有需要补全的单词.");
            return ;
        }
        CountDownLatch countDownLatch = new CountDownLatch(wordList.size());
        for (Word word : wordList) {
            WordEnrichThreadPoolUtil.executeTask(() -> {
                WordEditDto.Req req = new WordEditDto.Req();
                req.setId(word.getId());
                try {
                    log.info("[例句补全] 正在补全单词[{}]...", word.getWord());
                    List<WordFetchClawSentenceAudio> insertAudioList = new CopyOnWriteArrayList<>();
//                    WordEditDto.Req req = BeanUtil.copyProperties(word, WordEditDto.Req.class, "sentences");
                    Map<String, List<Word.Sentences>> sentencesMap = new HashMap<>();
                    word.getSentences().forEach((k, v) -> {
                        sentencesMap.put(k, BeanUtil.copyToList(v, Word.Sentences.class));
                    });
                    // 获取单词已有的音频数据
                    List<WordFetchClawSentenceAudio> sentenceAudios = wordFetchClawSentenceAudioService.list(new QueryWrapper<WordFetchClawSentenceAudio>().lambda().eq(WordFetchClawSentenceAudio::getWord, word.getWord()));
                    Map<String, WordFetchClawSentenceAudio> alreadySentenceAudioMap = new HashMap<>();
                    if(CollUtil.isNotEmpty(sentenceAudios)){
                        alreadySentenceAudioMap = sentenceAudios.stream().collect(Collectors.toMap(k -> k.getSentenceType() + "_" + k.getSentenceEn(), v -> v,
                                (oldValue,newValue) -> oldValue));
                    }
                    req.setSentences(sentencesMap);
                    Map<String, WordFetchClawSentenceAudio> finalAlreadySentenceAudioMap = alreadySentenceAudioMap;
                    req.getSentences().forEach((k, v) -> {
                        v.forEach(sentence -> {
                            if(StrUtil.isBlankIfStr(sentence.getSentenceEn())){
                                return ;
                            }


                            String key = k + "_" + sentence.getSentenceEn();
                            WordFetchClawSentenceAudio sentenceAudio = finalAlreadySentenceAudioMap.get(key);
                            if(StrUtil.isBlankIfStr(sentence.getAudioUsUrl())) {
                                if(sentenceAudio != null && StrUtil.isNotBlank(sentenceAudio.getAudioUsUrl())) {
                                    sentence.setAudioUsUrl(sentenceAudio.getAudioUsUrl());
                                } else {
                                    byte[] usByte = clawCloudRunService.enrich(sentence.getSentenceEn(), 0);
                                    log.info("[{}] get sentence us byte[] length={}", sentence.getSentenceEn(), usByte != null ? usByte.length : 0);
                                    sentence.setAudioUsUrl(this.uploadOss(word.getId(), usByte, "sentence_us_claw_" + IdUtil.getSnowflakeNextId() + ".mp3"));

                                    insertAudioList.add(new WordFetchClawSentenceAudio(word.getId(), word.getWord(), k, sentence.getSentenceEn(), sentence.getAudioUkUrl(), sentence.getAudioUsUrl()));
                                }
                            }
                            if(StrUtil.isBlankIfStr(sentence.getAudioUkUrl())) {
                                if(sentenceAudio != null && StrUtil.isNotBlank(sentenceAudio.getAudioUkUrl())) {
                                    sentence.setAudioUkUrl(sentenceAudio.getAudioUkUrl());
                                } else {
                                    byte[] ukByte = clawCloudRunService.enrich(sentence.getSentenceEn(), 1);
                                    log.info("[{}] get sentence uk byte[] length={}", sentence.getSentenceEn(), ukByte != null ? ukByte.length : 0);
                                    sentence.setAudioUkUrl(this.uploadOss(word.getId(), ukByte, "sentence_uk_claw_" + IdUtil.getSnowflakeNextId() + ".mp3"));

                                    insertAudioList.add(new WordFetchClawSentenceAudio(word.getId(), word.getWord(), k, sentence.getSentenceEn(), sentence.getAudioUkUrl(), sentence.getAudioUsUrl()));
                                }
                            }


//                            // 美式 没有就补充美式
//                            if(StrUtil.isBlankIfStr(sentence.getAudioUsUrl())) {
//                                VolcengineTtsResponse response = volcengineService.enrich(sentence.getSentenceEn(), 0);
//                                if(StrUtil.isNotEmpty(response.getData())){
//                                    byte[] bytes = Base64.getMimeDecoder().decode(response.getData());
//                                    sentence.setAudioUsUrl(this.uploadOss(word.getId(), bytes, "sentence_us_" + IdUtil.getSnowflakeNextId() + ".mp3"));
//                                }
//                            }
//
//                            // 英式 没有就补英式
//                            if(StrUtil.isBlankIfStr(sentence.getAudioUkUrl())){
//                                VolcengineTtsResponse response  = volcengineService.enrich(sentence.getSentenceEn(), 1);
//                                if(StrUtil.isNotEmpty(response.getData())){
//                                    byte[] bytes = Base64.getMimeDecoder().decode(response.getData());
//                                    sentence.setAudioUkUrl(this.uploadOss(word.getId(), bytes, "sentence_uk_" + IdUtil.getSnowflakeNextId() + ".mp3"));
//                                }
//                            }
                        });
                    });
                    wordFetchClawSentenceAudioService.saveBatch(insertAudioList);
                    this.updateWord(req, word);
                    log.info("[例句补全] 单词[{}]补全完成...", word.getWord());
                } catch (Exception ignored) {
                    log.error("[例句补全] 补全单词[{}]出现异常:", word.getWord(), ignored);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }

        try {
            countDownLatch.await();
            log.info("[例句补全] 单词补全任务已完成，共补全{}条", wordList.size());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Map<String, String> uploadOss(String wordId, MultipartFile[] files) {
        // 先获取单词
        Word word = this.getById(wordId);
        if(word == null){
            throw new NoSuchElementException("该单词不存在，或已被删除。");
        }
        // 开始上传
        Map<String, String> ossUrlMap = new HashMap<>();
        for (MultipartFile file : files) {
            String objectName = "word/" + Md5Utils.hash(word.getWord()) + "/" + file.getOriginalFilename();
            String ossUrl = ossService.uploadFile(file, objectName);
            ossUrlMap.put(file.getOriginalFilename(), ossUrl);
        }
        return ossUrlMap;
    }

    @Override
    public String uploadOss(String wordId, MultipartFile file) {
        // 先获取单词
        Word word = this.getById(wordId);
        if(word == null){
            throw new NoSuchElementException("该单词不存在，或已被删除。");
        }
        // 开始上传
        String objectName = "word/" + Md5Utils.hash(word.getWord()) + "/" + file.getOriginalFilename();
        return ossService.uploadFile(file, objectName);
    }

    @Override
    public String uploadOss(String wordId, byte[] bytes, String fileName) {
        CustomMultipartFile file = new CustomMultipartFile(bytes, fileName);
        return this.uploadOss(wordId, file);
    }

    static Set<String> meaningsSet = new ConcurrentHashSet<>();

    @Override
    public void generateWordMeanings(Set<String> wordSet){

        // 从redis中获取生成失败的单词
        Set<String> redisMeaningsSet = redisTemplate.opsForSet().members("word:meanings:generate:fail");

        // 获取所有单词
        List<Word> words;
        if(CollUtil.isNotEmpty(wordSet)){
            words = this.list(new LambdaQueryWrapper<Word>().eq(Word::getDeleted, false).in(Word::getWord, wordSet));
        } else {
//            words = this.list(new LambdaQueryWrapper<Word>().select(Word::getId, Word::getWord)
//                    .eq(Word::getDeleted, false)
//                    .notIn(!meaningsSet.isEmpty(), Word::getWord, meaningsSet)
//                    .in(redisMeaningsSet != null && !redisMeaningsSet.isEmpty(), Word::getWord, redisMeaningsSet)
//                    .orderByAsc(Word::getWord)
//            );
            words = wordMapper.listUnRichWords();
            if(CollUtil.isNotEmpty(words)) {
                words = words.stream().filter(word -> {
                    assert redisMeaningsSet != null;
                    return !redisMeaningsSet.contains(word.getWord());
                }).collect(Collectors.toList());
            }
        }
//        words = words.subList(0, 10);
        if(CollUtil.isEmpty(words)) {
            log.warn("[单词释义生成] 没有需要生成释义的单词.");
            return ;
        }

        CountDownLatch countDownLatch = new CountDownLatch(words.size());
        for (Word x : words) {
            WordEnrichThreadPoolUtil.executeTask(() -> {
                try {
                    if(meaningsSet.contains(x.getWord())) {
                        log.info("[单词释义生成] 单词[{}]的释义已存在，跳过生成", x.getWord());
                        countDownLatch.countDown();
                        return ;
                    }
                    meaningsSet.add(x.getWord());
//                    if(StrUtil.isBlankIfStr(x.getMeanings())) {
                        log.info("[单词释义生成] 正在生成单词[{}]的释义...", x.getWord());
//                        List<WordInfo> wordInfos = siliconflowService.generateWordMeanings(List.of(x.getWord()));
                        List<WordInfo> wordInfos = grokService.generateWordMeanings(List.of(x.getWord()));
                        if(CollUtil.isNotEmpty(wordInfos)) {
                            WordInfo wordInfo = wordInfos.getFirst();
                            if(!wordInfo.getMeanings().containsKey("通用")){
                                log.warn("[单词释义生成] 单词[{}]的释义没有通用释义，可能是生成失败或数据不完整", x.getWord());
                                throw new RuntimeException();
                            }
                            Word word = wordMapper.selectById(x.getId());
                            Word.Meanings m1 = word.getMeanings().get("通用");
                            if(m1 == null) {
                                m1 = new Word.Meanings();
                                word.getMeanings().put("通用", m1);
                            }
                            m1.setPos(new ArrayList<>());
                            WordInfo.Meanings m2 = wordInfo.getMeanings().get("通用");
                            m1.getPos().addAll(m2.getPos().stream().map(m-> new Word.Meanings.Pos(m.getPos(), m.getDef())).toList());
                            m1.setPractices(m2.getPractices());

                            // 更新例句
                            List<Word.Sentences> s1 = word.getSentences().computeIfAbsent("通用", k -> new ArrayList<>());
                            s1.clear();
                            List<WordInfo.Sentence> s2 = wordInfo.getSentences().get("通用");
                            s2.forEach(s->{
                                Word.Sentences sentence = new Word.Sentences();
                                sentence.setSentenceEn(s.getSentenceEn());
                                sentence.setSentenceCn(s.getSentenceCn());
                                sentence.setSyllables(s.getSyllables());
                                sentence.setStructurePartsEn(s.getStructurePartsEn());
                                sentence.setPractices(s.getPractices());
                                sentence.setStage(s.getStage());
                                s1.add(sentence);
                            });

                            wordMapper.updateById(word);

                        }else {
                            log.warn("[单词释义生成] 单词[{}]的释义没有通用释义，可能是生成失败或数据不完整", x.getWord());
                            throw new RuntimeException();
                        }
//                    }
                } catch (Exception e) {
                    log.error("[单词释义生成] 生成单词[{}]释义失败:", x.getWord(), e);
                    redisTemplate.opsForSet().add("word:meanings:generate:fail", x.getWord());
                } finally {
                    meaningsSet.remove(x.getWord());
                    countDownLatch.countDown();
                }
            });
        }

        try {
            countDownLatch.await();
            log.info("[单词释义生成] 单词释义生成任务已完成，共生成{}条", meaningsSet.size());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    public List<Word> selectRandomList(int count) {
        return this.baseMapper.selectRandomList(count);
    }


    @Autowired
    private INebiusService nebiusService;

    @Override
    public void enrichSentencesPractices(Set<String> words) {

        LambdaQueryWrapper<Word> queryWrapper = new LambdaQueryWrapper<Word>().eq(Word::getDeleted, false);
        List<Word> wordList;
        // 优先补齐传进来的单词
        if(CollUtil.isNotEmpty(words)){
            queryWrapper.in(Word::getWord, words);
            wordList = this.list(queryWrapper);
        } else {
            wordList = wordMapper.listUnRichWords();
        }

        if(CollUtil.isEmpty(wordList)) {
            log.warn("[例句混淆项补全] 没有需要补全的单词.");
            return ;
        }
        List<List<Word>> partitioned = ListUtil.partition(wordList, 1);
        CountDownLatch countDownLatch = new CountDownLatch(partitioned.size());
        for (int i = 0; i < partitioned.size(); i++) {
            List<Word> list = partitioned.get(i);
            int finalI = i;
            WordEnrichThreadPoolUtil.executeTask(() -> {
                try {
                    log.info("[例句混淆项补全] 正在补全第{}页...", finalI);
                    List<WordInfo> wordInfos = BeanUtil.copyToList(list, WordInfo.class);
                    List<WordInfo> enrichWordInfos = nebiusService.enrich(wordInfos);

                    // 补全数据
                    if(CollUtil.isEmpty(enrichWordInfos)){
                        return ;
                    }
                    // 将原有单词列表用word分组
                    Map<String, Word> wordMap = list.stream().collect(Collectors.toMap(Word::getWord, v -> v));
                    enrichWordInfos.forEach(enrich -> {
                        Word word = wordMap.get(enrich.getWord());
                        if(word == null) {
                            return ;
                        }
                        Map<String, List<Word.Sentences>> sentencesTypeMap = JSONUtil.toBean(JSONUtil.toJsonStr(enrich.getSentences()), new TypeReference<>() {
                        }, false);

                        // 补充sentences
                        Map<String, List<Word.Sentences>> wordSentences = word.getSentences();
                        wordSentences.forEach((k, v) -> {
                            List<Word.Sentences> sentences = sentencesTypeMap.get(k);
                            if(CollUtil.isNotEmpty(sentences)) {
                                LinkedHashMap<String, List<Word.Sentences>> collected = sentences.stream().collect(Collectors.groupingBy(Word.Sentences::getSentenceEn, LinkedHashMap::new, Collectors.toList()));
                                v.forEach(sen -> {
                                    Word.Sentences firstSentences = collected.get(sen.getSentenceEn()).getFirst();
                                    sen.setPractices(firstSentences.getPractices());
                                });
                            }
                        });

                        this.lambdaUpdate()
                                .set(Word::getSentences, wordSentences, "typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler")
                                .set(Word::getUpdateTime, DateUtil.date())
                                .eq(Word::getId, word.getId())
                                .update();
//                        word.setUpdateTime(DateUtil.date());
//                        this.updateById(word);
                        log.info("[例句混淆项补全] 单词 - {} - 补全完成", word.getWord());
                    });
                } catch (Exception ignored) {
                    log.error("[例句混淆项补全] 补全单词出现异常:", ignored);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }

        try {
            countDownLatch.await();
            log.info("[例句混淆项补全] 单词补全任务已完成，共补全{}条", wordList.size());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }


    }

    @Override
    public void enrichSentencesPeriod(Set<String> words) {
        LambdaQueryWrapper<Word> queryWrapper = new LambdaQueryWrapper<Word>().eq(Word::getDeleted, false);
        List<Word> wordList;
        // 优先补齐传进来的单词
        if(CollUtil.isNotEmpty(words)){
            queryWrapper.in(Word::getWord, words);
            wordList = this.list(queryWrapper);
        } else {
            wordList = wordMapper.selectList(new QueryWrapper<>());
        }

        if(CollUtil.isEmpty(wordList)) {
            log.warn("[例句句号补全] 没有需要补全的单词.");
            return ;
        }

        AtomicLong updateWordCnt = new AtomicLong(0);
        AtomicLong ignoreWordCnt = new AtomicLong(0);

        wordList.forEach(word -> {
            AtomicBoolean updateFlag = new AtomicBoolean(false);
            word.getSentences().forEach((k, v) -> {
                if(v == null) {
                    return ;
                }
                v.forEach(sentence -> {
                    if(!sentence.getSentenceCn().endsWith("。") && !sentence.getSentenceCn().endsWith("！") && !sentence.getSentenceCn().endsWith("？")){
                        sentence.setSentenceCn(sentence.getSentenceCn() + "。");
                        updateFlag.set(true);
                    }
                    List<String> practices = sentence.getPractices();
                    if(CollUtil.isNotEmpty(practices)){
                        for (int j = 0; j < practices.size(); j++) {
                            String s = practices.get(j);
                            if (!s.endsWith("。") && !s.endsWith("！") && !s.endsWith("？")) {
                                practices.set(j, s + "。");
                                updateFlag.set(true);
                            }
                        }
                    }
                });
            });

            if(updateFlag.get()) {
                updateWordCnt.getAndAdd(1);
                this.lambdaUpdate().set(Word::getSentences, word.getSentences(), "typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler")
                        .eq(Word::getId, word.getId()).update();
                log.info("[例句句号补全] 单词 - {} - 补全完成", word.getWord());
            } else {
                ignoreWordCnt.getAndAdd(1);
            }
        });

        System.out.println("补全完成，补全单词数量=" + updateWordCnt.get() + "， 无需补全单词数量={}" + ignoreWordCnt.get());

    }

    @Override
    public List<Word> listUnRichWords() {
        return this.baseMapper.listUnRichWords();
    }

    @Override
    public List<Word> listUnRichBasic() {
        return this.baseMapper.listUnRichBasic();
    }

    @Override
    public List<Word> listUnRichMeanings() {
        return this.baseMapper.listUnRichMeanings();
    }

    @Override
    public List<Word> listUnRichSentences() {
        return this.baseMapper.listUnRichSentences();
    }
}
