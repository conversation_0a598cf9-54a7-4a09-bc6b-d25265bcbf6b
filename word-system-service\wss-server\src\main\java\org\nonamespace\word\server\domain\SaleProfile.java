package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nonamespace.word.server.entity.DataEntity;

/**
 * 销售人员属性表
 * 用于存储销售人员的业务属性，避免依赖sys_user表
 * 
 * <AUTHOR>
 * @date 2025-06-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "sale_profile", autoResultMap = true)
public class SaleProfile extends DataEntity {

    /**
     * 销售人员ID (关联sys_user表的user_id)
     */
    @TableField("sales_id")
    private String salesId;

    /**
     * 销售人员姓名 (冗余字段，与sys_user.nick_name同步)
     */
    @TableField("sales_name")
    private String salesName;

    /**
     * 手机号码 (冗余字段，与sys_user.phonenumber同步)
     */
    @TableField("phone")
    private String phone;

    /**
     * 邮箱 (冗余字段，与sys_user.email同步)
     */
    @TableField("email")
    private String email;

    /**
     * 销售组ID
     */
    @TableField("sales_group_id")
    private String salesGroupId;

    /**
     * 销售组名称 (冗余字段)
     */
    @TableField("sales_group_name")
    private String salesGroupName;

    /**
     * 在销售组中的角色类型 (leader: 组长, member: 成员)
     */
//    @TableField("role_type")
//    private String roleType;

    /**
     * 状态 (active: 活跃, inactive: 停用)
     */
    @TableField("status")
    private String status;

    /**
     * 入职时间
     */
    @TableField("join_time")
    private java.util.Date joinTime;

    /**
     * 离职时间
     */
    @TableField("leave_time")
    private java.util.Date leaveTime;

    /**
     * 销售区域
     */
    @TableField("sales_area")
    private String salesArea;

    /**
     * 销售目标 (月度)
     */
    @TableField("monthly_target")
    private java.math.BigDecimal monthlyTarget;

    /**
     * 销售业绩 (当月)
     */
    @TableField("monthly_performance")
    private java.math.BigDecimal monthlyPerformance;

    /**
     * 客户数量
     */
    @TableField("customer_count")
    private Integer customerCount;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;
}
