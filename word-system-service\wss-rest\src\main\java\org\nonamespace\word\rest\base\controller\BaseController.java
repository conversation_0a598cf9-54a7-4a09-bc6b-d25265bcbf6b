package org.nonamespace.word.rest.base.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.page.TableDataInfo;

public class BaseController extends com.ruoyi.common.core.controller.BaseController {

    protected TableDataInfo getDataTable(Page<?> page) {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(200);
        rspData.setMsg("查询成功");
        rspData.setRows(page.getRecords());
        rspData.setTotal(page.getTotal());
        return rspData;
    }

    protected TableDataInfo pageError(int code, String msg) {
        TableDataInfo t = new TableDataInfo();
        t.setCode(code);
        t.setMsg(msg);
        return t;
    }
}
