package org.nonamespace.word.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.nonamespace.word.openai.model.enums.ModelEnum;
import org.nonamespace.word.server.domain.Word;
import org.nonamespace.word.server.dto.WordEditDto;
import org.nonamespace.word.server.dto.WordPageDto;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 单词: 核心词库，存储所有单词的基本信息及例句Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface IWordService extends IService<Word> {

    /**
     * 查询单词: 核心词库，存储所有单词的基本信息及例句
     * 
     * @param id 单词: 核心词库，存储所有单词的基本信息及例句主键
     * @return 单词: 核心词库，存储所有单词的基本信息及例句
     */
    public Word selectWordById(String id);



    /**
     * 修改单词: 核心词库，存储所有单词的基本信息及例句
     * 
     * @param req 单词: 核心词库，存储所有单词的基本信息及例句
     * @return 结果
     */
    public int updateWord(WordEditDto.Req req, Word originWord);


    /**
     * 分页查询
     * @param req
     * @return
     */
    public List<WordPageDto.Resp> page(WordPageDto.Req req);


    /**
     * 单词补全
     * @param words  需要补全的单词列表
     * @param ifNullEnrichOther 如果words为空，是否补齐其他缺失的单词
     * @return
     */
    @Deprecated
    public void enrich(Set<String> words, boolean ifNullEnrichOther);


    /**
     * 抓取某个word的音频文件
     */
    public void fetchVoices(Set<String> words, int batchSize, ModelEnum modelEnum);


    /**
     * 补全句子音频
     * @param words
     * @param ifNullEnrichOther
     */
    public void enrichSentencesAudio(Set<String> words, boolean ifNullEnrichOther);

    /**
     * 上传oss
     * @param wordId
     * @param files
     * @return
     */
    public Map<String, String> uploadOss(String wordId, MultipartFile[] files);

    public String uploadOss(String wordId, MultipartFile file);

    public String uploadOss(String wordId, byte[] bytes, String fileName);

    public void generateWordMeanings(Set<String> words);

    public List<Word> selectRandomList(int count);

    public void enrichSentencesPractices(Set<String> words);

    void enrichSentencesPeriod(Set<String> words);

    List<Word> listUnRichWords();

    List<Word> listUnRichBasic();

    List<Word> listUnRichMeanings();

    List<Word> listUnRichSentences();
}
