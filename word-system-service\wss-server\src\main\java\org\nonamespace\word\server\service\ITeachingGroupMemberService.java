package org.nonamespace.word.server.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.nonamespace.word.server.domain.TeachingGroupMember;
import org.nonamespace.word.server.dto.management.teachinggroup.TeacherDto;
import org.nonamespace.word.server.dto.management.teachinggroup.TeachingGroupDto;

import java.util.List;

/**
 * 教学组成员Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface ITeachingGroupMemberService extends IService<TeachingGroupMember> {

    /**
     * 分页查询教学组教师列表
     * 
     * @param req 查询条件
     * @return 教师列表
     */
    IPage<TeacherDto.BasicResp> selectGroupTeachersPage(TeachingGroupDto.GetGroupTeachersReq req);

    /**
     * 查询教学组所有教师
     * 
     * @param groupId 教学组ID
     * @return 教师列表
     */
    List<TeacherDto.BasicResp> selectGroupTeachers(String groupId);

    /**
     * 批量添加教师到教学组
     * 
     * @param groupId 教学组ID
     * @param teacherIds 教师ID列表
     * @return 是否成功
     */
    boolean addTeachersToGroup(String groupId, List<String> teacherIds);

    /**
     * 批量移除教师从教学组
     * 
     * @param groupId 教学组ID
     * @param teacherIds 教师ID列表
     * @return 是否成功
     */
    boolean removeTeachersFromGroup(String groupId, List<String> teacherIds);

    /**
     * 根据教学组ID删除所有成员
     * 
     * @param groupId 教学组ID
     * @return 是否成功
     */
    boolean removeAllMembersByGroupId(String groupId);

    /**
     * 检查教师是否已在其他教学组
     * 
     * @param teacherIds 教师ID列表
     * @return 已分配的教师ID列表
     */
    List<String> checkAssignedTeachers(List<String> teacherIds);

    /**
     * 获取教师的教学组信息
     * 
     * @param teacherId 教师ID
     * @return 教学组信息
     */
    TeachingGroupDto.Resp getTeacherGroup(String teacherId);
}
