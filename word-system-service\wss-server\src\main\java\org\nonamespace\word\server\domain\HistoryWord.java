package org.nonamespace.word.server.domain;

import com.ruoyi.common.annotation.Excel;
import lombok.*;
import org.nonamespace.word.server.entity.DataEntity;

/**
 * 单词历史: 存储单词的修改历史版本对象 history_word
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HistoryWord extends DataEntity {

    /** 关联的单词ID */
    private String wordId;

    /** 修改之前的版本号 */
    private Long version;

    /** 修改前内容 */
    @Excel(name = "修改前内容")
    private String wordObj;

    /** 修改描述 */
    @Excel(name = "修改描述")
    private String changeDescription;

}
