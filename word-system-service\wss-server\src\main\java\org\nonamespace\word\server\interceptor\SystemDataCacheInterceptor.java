package org.nonamespace.word.server.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.cache.SessionLevelCacheManager;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 系统数据缓存拦截器
 * 用于在请求结束时自动清理会话级别缓存
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@Component
public class SystemDataCacheInterceptor implements HandlerInterceptor {

    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
                              @NonNull Object handler, @Nullable Exception ex) throws Exception {
        try {
            // 清理会话级别缓存
            SessionLevelCacheManager.clearAllCaches();
        } catch (Exception e) {
            log.warn("清理会话级别缓存时发生异常", e);
        }
    }
}
