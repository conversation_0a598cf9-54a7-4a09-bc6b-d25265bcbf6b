package org.nonamespace.word.server.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.mapper.UserStudentExtMapper;
import org.nonamespace.word.server.domain.UserStudentExt;
import org.nonamespace.word.server.service.UserStudentExtService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserStudentExtServiceImpl extends ServiceImpl<UserStudentExtMapper, UserStudentExt> implements UserStudentExtService {

    @Override
    public Map<String, String> getSalesIdsByStudentIds(List<String> studentIds) {
        if (studentIds == null || studentIds.isEmpty()) {
            return new HashMap<>();
        }

        try {
            log.info("批量查询学生销售关系: studentIds={}", studentIds);

            // 批量查询学生扩展信息
            List<UserStudentExt> studentExts = this.lambdaQuery()
                    .in(UserStudentExt::getStudentId, studentIds)
                    .eq(UserStudentExt::getDeleted, false)
                    .list();

            // 转换为Map格式
            Map<String, String> result = studentExts.stream()
                    .filter(ext -> StrUtil.isNotEmpty(ext.getSalesId()))
                    .collect(Collectors.toMap(
                            UserStudentExt::getStudentId,
                            UserStudentExt::getSalesId,
                            (existing, replacement) -> existing // 如果有重复，保留第一个
                    ));

            log.info("批量查询学生销售关系完成: 查询{}个学生，找到{}个销售关系",
                    studentIds.size(), result.size());

            return result;

        } catch (Exception e) {
            log.error("批量查询学生销售关系失败: studentIds={}", studentIds, e);
            return new HashMap<>();
        }
    }
}
