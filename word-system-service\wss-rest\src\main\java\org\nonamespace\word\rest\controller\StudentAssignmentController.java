package org.nonamespace.word.rest.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.dto.management.student.SalesStudentDto;
import org.nonamespace.word.server.facade.ISalesStudentManagementFacade;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 学生分配管理Controller
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@RestController
@RequestMapping("/student-assignment")
@RequiredArgsConstructor
@Validated
public class StudentAssignmentController extends BaseController {

    private final ISalesStudentManagementFacade salesStudentManagementFacade;

    /**
     * 获取可分配学生列表
     */
    @PreAuthorize("@ss.hasPermi('student:assignment:list')")
    @GetMapping("/available-students")
    public AjaxResult getAvailableStudents(SalesStudentDto.GetListReq req) {
        try {
            IPage<SalesStudentDto.BasicResp> page = salesStudentManagementFacade.getSalesStudentPage(req);
            return AjaxResult.success(page);
        } catch (Exception e) {
            log.error("获取可分配学生列表失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 分配学生到销售
     */
    @PreAuthorize("@ss.hasPermi('student:assignment:assign')")
    @Log(title = "学生分配", businessType = BusinessType.UPDATE)
    @PostMapping("/assign")
    public AjaxResult assignStudents(@Valid @RequestBody SalesStudentDto.AssignReq req) {
        try {
            boolean success = salesStudentManagementFacade.assignStudentToSales(req);
            return success ? AjaxResult.success("分配成功") : AjaxResult.error("分配失败");
        } catch (Exception e) {
            log.error("分配学生失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 批量分配学生
     */
    @PreAuthorize("@ss.hasPermi('student:assignment:assign')")
    @Log(title = "学生分配", businessType = BusinessType.UPDATE)
    @PostMapping("/batch-assign")
    public AjaxResult batchAssignStudents(@Valid @RequestBody SalesStudentDto.BatchAssignReq req) {
        try {
            boolean success = salesStudentManagementFacade.batchAssignStudentsToSales(req);
            return success ? AjaxResult.success("批量分配成功") : AjaxResult.error("批量分配失败");
        } catch (Exception e) {
            log.error("批量分配学生失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 取消学生分配
     */
    @PreAuthorize("@ss.hasPermi('student:assignment:unassign')")
    @Log(title = "学生分配", businessType = BusinessType.UPDATE)
    @PostMapping("/unassign/{studentId}")
    public AjaxResult unassignStudent(@PathVariable String studentId) {
        try {
            SalesStudentDto.RemoveReq req = new SalesStudentDto.RemoveReq();
            req.setStudentId(studentId);
            req.setReason("管理员取消分配");
            
            boolean success = salesStudentManagementFacade.removeStudentFromSales(req);
            return success ? AjaxResult.success("取消分配成功") : AjaxResult.error("取消分配失败");
        } catch (Exception e) {
            log.error("取消学生分配失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 重新分配学生
     */
    @PreAuthorize("@ss.hasPermi('student:assignment:reassign')")
    @Log(title = "学生分配", businessType = BusinessType.UPDATE)
    @PostMapping("/reassign")
    public AjaxResult reassignStudent(@Valid @RequestBody SalesStudentDto.AssignReq req) {
        try {
            // 重新分配实际上就是分配到新的销售
            boolean success = salesStudentManagementFacade.assignStudentToSales(req);
            return success ? AjaxResult.success("重新分配成功") : AjaxResult.error("重新分配失败");
        } catch (Exception e) {
            log.error("重新分配学生失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 智能自动分配
     */
    @PreAuthorize("@ss.hasPermi('student:assignment:auto')")
    @Log(title = "学生分配", businessType = BusinessType.UPDATE)
    @PostMapping("/intelligent-assign")
    public AjaxResult intelligentAssignStudents(@Valid @RequestBody SalesStudentDto.IntelligentAssignReq req) {
        try {
            SalesStudentDto.IntelligentAssignResp result = salesStudentManagementFacade.intelligentAssignStudents(req);
            return AjaxResult.success("智能分配完成", result);
        } catch (Exception e) {
            log.error("智能分配学生失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 批量分配（兼容旧接口）
     */
    @PreAuthorize("@ss.hasPermi('student:assignment:auto')")
    @Log(title = "学生分配", businessType = BusinessType.UPDATE)
    @PostMapping("/auto-assign")
    public AjaxResult autoAssignStudents(@Valid @RequestBody SalesStudentDto.BatchAssignReq req) {
        try {
            // 自动分配使用批量分配的逻辑
            boolean success = salesStudentManagementFacade.batchAssignStudentsToSales(req);
            return success ? AjaxResult.success("自动分配成功") : AjaxResult.error("自动分配失败");
        } catch (Exception e) {
            log.error("自动分配学生失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取分配历史
     */
    @PreAuthorize("@ss.hasPermi('student:assignment:history')")
    @GetMapping("/history")
    public AjaxResult getAssignmentHistory(@RequestParam(required = false) String studentId) {
        try {
            if (studentId != null) {
                List<SalesStudentDto.AssignmentHistoryResp> history = 
                    salesStudentManagementFacade.getStudentAssignmentHistory(studentId);
                return AjaxResult.success(history);
            } else {
                // 返回所有分配历史的分页数据
                return AjaxResult.success("暂不支持查询所有历史记录");
            }
        } catch (Exception e) {
            log.error("获取分配历史失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取分配统计信息
     */
    @PreAuthorize("@ss.hasPermi('student:assignment:stats')")
    @GetMapping("/stats")
    public AjaxResult getAssignmentStats() {
        try {
            SalesStudentDto.StatsResp stats = salesStudentManagementFacade.getSalesStudentStats();
            return AjaxResult.success(stats);
        } catch (Exception e) {
            log.error("获取分配统计信息失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取销售组选项
     */
    @PreAuthorize("@ss.hasPermi('student:assignment:list')")
    @GetMapping("/sales-group-options")
    public AjaxResult getSalesGroupOptions() {
        try {
            List<SalesStudentDto.GroupOptionResp> options = salesStudentManagementFacade.getSalesGroupOptions();
            return AjaxResult.success(options);
        } catch (Exception e) {
            log.error("获取销售组选项失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取销售人员选项
     */
    @PreAuthorize("@ss.hasPermi('student:assignment:list')")
    @GetMapping("/sales-options")
    public AjaxResult getSalesOptions(@RequestParam(value = "groupId", required = false) String groupId) {
        try {
            List<SalesStudentDto.SalesOptionResp> options = salesStudentManagementFacade.getSalesOptions(groupId);
            return AjaxResult.success(options);
        } catch (Exception e) {
            log.error("获取销售人员选项失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取工作负载分布
     */
    @PreAuthorize("@ss.hasPermi('student:assignment:stats')")
    @GetMapping("/workload-distribution")
    public AjaxResult getWorkloadDistribution(@RequestParam(value = "groupId", required = false) String groupId) {
        try {
            // 这里可以基于销售人员的学生数量来计算工作负载分布
            // 暂时返回简单的统计信息
            SalesStudentDto.StatsResp stats = salesStudentManagementFacade.getSalesStudentStats();
            return AjaxResult.success(stats);
        } catch (Exception e) {
            log.error("获取工作负载分布失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }
}
