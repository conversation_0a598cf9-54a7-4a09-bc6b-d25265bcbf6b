<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.nonamespace.word</groupId>
        <artifactId>word-system-service</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>wss-openai</artifactId>


    <dependencies>
        <!-- Spring Boot Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- Spring AI -->
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-openai-spring-boot-starter</artifactId>
            <version>1.0.0-M6</version>
        </dependency>

        <!-- 项目依赖 -->
        <dependency>
            <groupId>org.nonamespace.word</groupId>
            <artifactId>wss-common</artifactId>
            <version>${project.version}</version>
        </dependency>

    </dependencies>
</project>
