package org.nonamespace.word.openai.model;


import cn.hutool.json.JSONUtil;
import lombok.*;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class XAICompletionResponse {

    private String id;
    private String object;
    private Long created;
    private String model;

    private List<Choice> choices;


    @Getter
    @Setter
    public static class Choice {
        private Long index;
        private Message message;
    }

    @Getter
    @Setter
    public static class Message {
        private String role;
        private String content;

        public List<WordInfo> getWordInfo() {
            return JSONUtil.toList(JSONUtil.toJsonStr(getContent()), WordInfo.class);
        }
    }

}
