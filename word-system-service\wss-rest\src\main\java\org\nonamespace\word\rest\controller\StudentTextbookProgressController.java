//package org.nonamespace.word.rest.controller;
//
//import java.util.List;
//
//import jakarta.servlet.http.HttpServletResponse;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.PutMapping;
//import org.springframework.web.bind.annotation.DeleteMapping;
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//import com.ruoyi.common.annotation.Log;
//import com.ruoyi.common.core.controller.BaseController;
//import com.ruoyi.common.core.domain.AjaxResult;
//import com.ruoyi.common.enums.BusinessType;
//import org.nonamespace.word.server.domain.StudentTextbookProgress;
//import org.nonamespace.word.server.service.IStudentTextbookProgressService;
//import com.ruoyi.common.utils.poi.ExcelUtil;
//
///**
// * 词定义 (统一教材与词): 定义各种词Controller
// *
// * <AUTHOR>
// * @date 2025-05-13
// */
//@RestController
//@RequestMapping("/word/progress")
//public class StudentTextbookProgressController extends BaseController
//{
//    @Autowired
//    private IStudentTextbookProgressService studentTextbookProgressService;
//
//
//    /**
//     * 导出词定义 (统一教材与词): 定义各种词列表
//     */
//    @PreAuthorize("@ss.hasPermi('word:progress:export')")
//    @Log(title = "词定义 (统一教材与词): 定义各种词", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, StudentTextbookProgress studentTextbookProgress)
//    {
//        List<StudentTextbookProgress> list = studentTextbookProgressService.selectStudentTextbookProgressList(studentTextbookProgress);
//        ExcelUtil<StudentTextbookProgress> util = new ExcelUtil<StudentTextbookProgress>(StudentTextbookProgress.class);
//        util.exportExcel(response, list, "词定义 (统一教材与词): 定义各种词数据");
//    }
//
//    /**
//     * 获取词定义 (统一教材与词): 定义各种词详细信息
//     */
//    @PreAuthorize("@ss.hasPermi('word:progress:query')")
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") String id)
//    {
//        return success(studentTextbookProgressService.selectStudentTextbookProgressById(id));
//    }
//
//    /**
//     * 新增词定义 (统一教材与词): 定义各种词
//     */
//    @PreAuthorize("@ss.hasPermi('word:progress:add')")
//    @Log(title = "词定义 (统一教材与词): 定义各种词", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody StudentTextbookProgress studentTextbookProgress)
//    {
//        return toAjax(studentTextbookProgressService.insertStudentTextbookProgress(studentTextbookProgress));
//    }
//
//    /**
//     * 修改词定义 (统一教材与词): 定义各种词
//     */
//    @PreAuthorize("@ss.hasPermi('word:progress:edit')")
//    @Log(title = "词定义 (统一教材与词): 定义各种词", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody StudentTextbookProgress studentTextbookProgress)
//    {
//        return toAjax(studentTextbookProgressService.updateStudentTextbookProgress(studentTextbookProgress));
//    }
//
//    /**
//     * 删除词定义 (统一教材与词): 定义各种词
//     */
//    @PreAuthorize("@ss.hasPermi('word:progress:remove')")
//    @Log(title = "词定义 (统一教材与词): 定义各种词", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable String[] ids)
//    {
//        return toAjax(studentTextbookProgressService.deleteStudentTextbookProgressByIds(ids));
//    }
//}
