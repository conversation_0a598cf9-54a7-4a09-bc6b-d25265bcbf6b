package org.nonamespace.word.server.mapper.order;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.nonamespace.word.server.domain.order.OrderRefundRecord;
import org.nonamespace.word.server.dto.order.RefundRecordDto;

import java.util.List;
import java.util.Map;

/**
 * 退款记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Mapper
public interface OrderRefundRecordMapper extends BaseMapper<OrderRefundRecord> {

    /**
     * 分页查询退款记录
     * 
     * @param page 分页参数
     * @param req 查询条件
     * @return 退款记录分页数据
     */
    IPage<RefundRecordDto.Resp> selectRefundRecordsByParam(Page<RefundRecordDto.Resp> page, @Param("req") RefundRecordDto.QueryReq req);

    /**
     * 查询退款记录详情
     * 
     * @param refundRecordId 退款记录ID
     * @return 退款记录详情
     */
    RefundRecordDto.DetailResp selectRefundRecordDetail(@Param("refundRecordId") String refundRecordId);

    /**
     * 查询退款统计数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 退款统计数据
     */
    @Select({
        "<script>",
        "SELECT ",
        "  DATE(create_time) as statistics_date,",
        "  COUNT(*) as total_refund_count,",
        "  SUM(refund_amount) as total_refund_amount,",
        "  SUM(CASE WHEN refund_type = 'partial' THEN 1 ELSE 0 END) as partial_refund_count,",
        "  SUM(CASE WHEN refund_type = 'partial' THEN refund_amount ELSE 0 END) as partial_refund_amount,",
        "  SUM(CASE WHEN refund_type = 'full' THEN 1 ELSE 0 END) as full_refund_count,",
        "  SUM(CASE WHEN refund_type = 'full' THEN refund_amount ELSE 0 END) as full_refund_amount,",
        "  SUM(CASE WHEN refund_status = 'success' THEN 1 ELSE 0 END) as success_refund_count,",
        "  SUM(CASE WHEN refund_status = 'failed' THEN 1 ELSE 0 END) as failed_refund_count,",
        "  AVG(refund_amount) as avg_refund_amount",
        "FROM order_refund_records ",
        "WHERE del_flag = '0'",
        "<if test='startDate != null'>",
        "  AND DATE(create_time) &gt;= #{startDate}",
        "</if>",
        "<if test='endDate != null'>",
        "  AND DATE(create_time) &lt;= #{endDate}",
        "</if>",
        "GROUP BY DATE(create_time)",
        "ORDER BY DATE(create_time) DESC",
        "</script>"
    })
    List<Map<String, Object>> selectRefundStatistics(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 查询按学科统计的退款数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 学科统计数据
     */
    @Select({
        "<script>",
        "SELECT ",
        "  subject,",
        "  COUNT(*) as refund_count,",
        "  SUM(refund_amount) as refund_amount",
        "FROM order_refund_records ",
        "WHERE del_flag = '0' AND subject IS NOT NULL",
        "<if test='startDate != null'>",
        "  AND DATE(create_time) &gt;= #{startDate}",
        "</if>",
        "<if test='endDate != null'>",
        "  AND DATE(create_time) &lt;= #{endDate}",
        "</if>",
        "GROUP BY subject",
        "ORDER BY refund_amount DESC",
        "</script>"
    })
    List<Map<String, Object>> selectRefundStatisticsBySubject(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 查询按操作人统计的退款数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 操作人统计数据
     */
    @Select({
        "<script>",
        "SELECT ",
        "  operator_name,",
        "  COUNT(*) as refund_count,",
        "  SUM(refund_amount) as refund_amount",
        "FROM order_refund_records ",
        "WHERE del_flag = '0' AND operator_name IS NOT NULL",
        "<if test='startDate != null'>",
        "  AND DATE(create_time) &gt;= #{startDate}",
        "</if>",
        "<if test='endDate != null'>",
        "  AND DATE(create_time) &lt;= #{endDate}",
        "</if>",
        "GROUP BY operator_name",
        "ORDER BY refund_amount DESC",
        "</script>"
    })
    List<Map<String, Object>> selectRefundStatisticsByOperator(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 查询订单的退款记录
     *
     * @param orderId 订单ID
     * @return 退款记录列表
     */
    @Select("SELECT * FROM order_refunds WHERE order_id = #{orderId} AND deleted = false ORDER BY create_time DESC")
    List<OrderRefundRecord> selectByOrderId(@Param("orderId") String orderId);
}
