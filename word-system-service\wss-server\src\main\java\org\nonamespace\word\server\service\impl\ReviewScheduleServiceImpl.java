package org.nonamespace.word.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.misc.WssContext;
import org.nonamespace.word.common.utils.OssService;
import org.nonamespace.word.server.domain.ReviewSchedule;
import org.nonamespace.word.server.domain.TextbookItem;
import org.nonamespace.word.server.domain.UserStudentExt;
import org.nonamespace.word.server.dto.ReviewScheduleQueryDto;
import org.nonamespace.word.server.dto.ReviewScheduleUploadDto;
import org.nonamespace.word.server.enums.ReviewScheduleStatusEnum;
import org.nonamespace.word.server.enums.ReviewScheduleTypeEnum;
import org.nonamespace.word.server.mapper.ReviewScheduleMapper;
import org.nonamespace.word.server.service.IReviewScheduleService;
import org.nonamespace.word.server.service.UserStudentExtService;
import org.nonamespace.word.server.util.SystemDataQueryUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 抗遗忘复习计划Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-20
 */
@Service
@Slf4j
public class ReviewScheduleServiceImpl extends ServiceImpl<ReviewScheduleMapper, ReviewSchedule> implements IReviewScheduleService
{
    @Autowired
    private ReviewScheduleMapper reviewScheduleMapper;

    @Autowired
    private OssService ossService;

    @Autowired
    private TextbookItemService textbookItemService;
    @Autowired
    private SystemDataQueryUtil systemDataQueryUtil;
    @Autowired
    private UserStudentExtService studentService;

    @Override
    public void generateReviewSchedules(String studentId, String courseId, Date date, List<String> textbookItemIds) {
        log.debug("生成抗遗忘复习计划, courseId: {}, 错误单词数: {}", courseId, textbookItemIds.size());

        if(CollUtil.isEmpty(textbookItemIds)) {
            return;
        }

        List<String> wordIds = textbookItemService.lambdaQuery().select(TextbookItem::getWordId).in(TextbookItem::getId, textbookItemIds).list().stream().map(TextbookItem::getWordId).distinct().collect(Collectors.toList());

        UserStudentExt student = studentService.lambdaQuery().eq(UserStudentExt::getStudentId, studentId).one();


        List<ReviewSchedule> allReviewSchedules = new ArrayList<>();

        for (ReviewScheduleTypeEnum reviewScheduleTypeEnum : ReviewScheduleTypeEnum.values()) {
            ReviewSchedule reviewSchedule = new ReviewSchedule();
            reviewSchedule.setStudentId(studentId);
            reviewSchedule.setCourseId(courseId);
            reviewSchedule.setReviewType(reviewScheduleTypeEnum.getType());
            reviewSchedule.setName(String.format("%s%s课程的%s抗遗忘复习",student.getName(), DateUtil.format(date, DatePattern.NORM_DATE_PATTERN), reviewScheduleTypeEnum.getType()));
            reviewSchedule.setScheduledTime(DateUtils.addDays(date, reviewScheduleTypeEnum.getDays()));
            reviewSchedule.setStatus(ReviewScheduleStatusEnum.WAIT_START.getValue());
            reviewSchedule.setTextbookItemIds(textbookItemIds);
            reviewSchedule.setWordIds(wordIds);
            reviewSchedule.setStatWordTotal((long) textbookItemIds.size());
            allReviewSchedules.add(reviewSchedule);
        }

        saveBatch(allReviewSchedules);
    }

    @Override
    public Page<ReviewScheduleQueryDto.Resp> queryPage(ReviewScheduleQueryDto.Req req) {
        log.debug("分页查询抗遗忘复习计划, 查询条件: {}", req);

        // 参数校验和默认值设置
        if (req.getPageNum() == null || req.getPageNum() < 1) {
            req.setPageNum(1);
        }
        if (req.getPageSize() == null || req.getPageSize() < 1) {
            req.setPageSize(10);
        }
        if (StrUtil.isBlank(req.getOrderBy())) {
            req.setOrderBy("scheduled_time");
        }
        if (StrUtil.isBlank(req.getOrderDirection())) {
            req.setOrderDirection("ASC");
        }

        if(!systemDataQueryUtil.isAdminOrHr() && !systemDataQueryUtil.isTeacherGroupManager() && systemDataQueryUtil.isTeacher()){
            req.setTeacherId(systemDataQueryUtil.getCurrentUserId());
        }

        // 创建分页对象
        Page<ReviewScheduleQueryDto.Resp> page = new Page<>(req.getPageNum(), req.getPageSize());

        // 执行分页查询
        Page<ReviewScheduleQueryDto.Resp> result = reviewScheduleMapper.selectPageWithStudent(page, req);

        log.debug("分页查询抗遗忘复习计划完成, 总记录数: {}, 当前页记录数: {}",
                result.getTotal(), result.getRecords().size());

        return result;
    }

    @Override
    public List<ReviewScheduleQueryDto.Resp> queryList(ReviewScheduleQueryDto.Req req) {
        log.debug("查询抗遗忘复习计划列表, 查询条件: {}", req);

        // 参数校验和默认值设置
        if (StrUtil.isBlank(req.getOrderBy())) {
            req.setOrderBy("scheduled_time");
        }
        if (StrUtil.isBlank(req.getOrderDirection())) {
            req.setOrderDirection("ASC");
        }

        // 执行查询
        List<ReviewScheduleQueryDto.Resp> result = reviewScheduleMapper.selectListWithStudent(req);

        log.debug("查询抗遗忘复习计划列表完成, 记录数: {}", result.size());

        return result;
    }

    @Override
    public ReviewScheduleUploadDto.UploadResp uploadReviewCompletion(ReviewScheduleUploadDto.UploadReq req) {
        log.debug("上传抗遗忘复习完成情况, reviewScheduleId: {}, 图片数量: {}",
                req.getReviewScheduleId(), req.getImages() != null ? req.getImages().length : 0);

        // 验证复习计划是否存在
        ReviewSchedule reviewSchedule = this.getById(req.getReviewScheduleId());
        if (reviewSchedule == null) {
            throw new IllegalArgumentException("复习计划不存在");
        }

        // 所有复习类型都支持上传图片，不再限制D14和D21
        // if (!"D14".equals(reviewSchedule.getReviewType()) && !"D21".equals(reviewSchedule.getReviewType())) {
        //     throw new IllegalArgumentException("只有D14和D21类型的复习支持上传图片");
        // }

        // 验证复习状态
        if (!"待开始".equals(reviewSchedule.getStatus()) && !"进行中".equals(reviewSchedule.getStatus())) {
            throw new IllegalArgumentException("当前复习状态不允许上传，状态: " + reviewSchedule.getStatus());
        }

        // 验证图片文件
        if (req.getImages() == null || req.getImages().length < 2) {
            throw new IllegalArgumentException("请至少上传两张图片");
        }

        if (req.getImages().length > 9) {
            throw new IllegalArgumentException("最多只能上传9张图片");
        }

        // 上传图片到OSS
        List<String> imageUrls = new ArrayList<>();
        try {
            for (MultipartFile image : req.getImages()) {
                // 验证文件类型
                String contentType = image.getContentType();
                if (contentType == null || !contentType.startsWith("image/")) {
                    throw new IllegalArgumentException("只能上传图片文件");
                }

                // 验证文件大小（10MB）
                if (image.getSize() > 10 * 1024 * 1024) {
                    throw new IllegalArgumentException("图片大小不能超过10MB");
                }

                // 构建OSS对象名称：review-upload/复习计划ID/时间戳_原文件名
                String fileName = image.getOriginalFilename();
                String objectName = "review-upload/" + req.getReviewScheduleId() + "/" +
                                  System.currentTimeMillis() + "_" + fileName;

                // 上传到OSS
                String imageUrl = ossService.uploadFile(image, objectName);
                imageUrls.add(imageUrl);
            }
        } catch (Exception e) {
            log.error("上传图片到OSS失败", e);
            throw new RuntimeException("上传图片失败: " + e.getMessage());
        }

        // 更新复习计划
        boolean updateResult = this.lambdaUpdate()
                .set(ReviewSchedule::getUploadedImages, imageUrls, "typeHandler=org.nonamespace.word.server.misc.mybatis.ListStringTypeHandler")
                .set(ReviewSchedule::getUploadedDescription, req.getDescription())
                .set(ReviewSchedule::getUploadedTime, WssContext.now())
                .set(ReviewSchedule::getStatus, "已完成")
                .set(ReviewSchedule::getActualEndTime, WssContext.now())
                .eq(ReviewSchedule::getId, req.getReviewScheduleId())
                .update();

        if (!updateResult) {
            throw new RuntimeException("上传失败，请重试");
        }

        // 构建响应
        ReviewScheduleUploadDto.UploadResp resp = new ReviewScheduleUploadDto.UploadResp();
        resp.setReviewScheduleId(req.getReviewScheduleId());
        resp.setStatus("已完成");
        resp.setUploadedTime(WssContext.now());
        resp.setMessage("上传成功");

        log.debug("抗遗忘复习完成情况上传成功, reviewScheduleId: {}, 图片数量: {}",
                req.getReviewScheduleId(), imageUrls.size());
        return resp;
    }

    @Override
    public ReviewScheduleUploadDto.ViewResp viewUploadedContent(ReviewScheduleUploadDto.ViewReq req) {
        log.debug("查看抗遗忘复习上传内容, reviewScheduleId: {}", req.getReviewScheduleId());

        // 查询复习计划详情（包含学生信息）
        ReviewScheduleQueryDto.Req queryReq = new ReviewScheduleQueryDto.Req();
        queryReq.setPageNum(1);
        queryReq.setPageSize(1);

        List<ReviewScheduleQueryDto.Resp> reviewList = reviewScheduleMapper.selectListWithStudent(queryReq);
        ReviewScheduleQueryDto.Resp review = reviewList.stream()
                .filter(r -> req.getReviewScheduleId().equals(r.getId()))
                .findFirst()
                .orElse(null);

        if (review == null) {
            throw new IllegalArgumentException("复习计划不存在");
        }

        // 构建响应
        ReviewScheduleUploadDto.ViewResp resp = new ReviewScheduleUploadDto.ViewResp();
        resp.setReviewScheduleId(review.getId());
        resp.setReviewName(review.getName());
        resp.setReviewType(review.getReviewType());
        resp.setStudentName(review.getStudentName());

        // 获取上传内容
        ReviewSchedule entity = this.getById(req.getReviewScheduleId());
        if (entity != null) {
            resp.setImageUrls(entity.getUploadedImages());
            resp.setDescription(entity.getUploadedDescription());
            resp.setUploadedTime(entity.getUploadedTime());
            resp.setHasUploaded(entity.getUploadedImages() != null && !entity.getUploadedImages().isEmpty());
        } else {
            resp.setHasUploaded(false);
        }

        log.debug("查看抗遗忘复习上传内容完成, reviewScheduleId: {}, hasUploaded: {}",
                req.getReviewScheduleId(), resp.getHasUploaded());
        return resp;
    }
}
