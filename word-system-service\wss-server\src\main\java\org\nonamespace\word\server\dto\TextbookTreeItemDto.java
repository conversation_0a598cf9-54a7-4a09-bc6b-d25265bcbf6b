package org.nonamespace.word.server.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 词定义 (统一教材与词): 定义各种词对象 textbook
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@Accessors(chain = true)
public class TextbookTreeItemDto
{
    private String nodeId;
    /** 节点ID */
    private String unitName;

    private String pid;

    private String parentNodeId;

    /** 类型 (学校教材,特色词表,学生词表) */
    private Integer nodeType;

    /** 标签，如["类型:学校教材", "年级:一年级", "版本:牛津版"] */
    private String textbookId;

    private String wordId;

    private String textbookName;
    private String word;
    private String textbookType;
    private List<String> wordTags;
    private Integer displayOrder;
    private Date createTime;
}
