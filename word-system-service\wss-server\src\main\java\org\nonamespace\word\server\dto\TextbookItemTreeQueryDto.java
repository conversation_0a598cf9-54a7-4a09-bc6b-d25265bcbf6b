package org.nonamespace.word.server.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 词定义 (统一教材与词): 定义各种词对象 textbook
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@Accessors(chain = true)
public class TextbookItemTreeQueryDto
{

    /** 节点ID */
    private String nodeId;

    private Integer nodeType;

    //单词、上课
    private String searchType;

    private String studentId;

    /** 类型 (学校教材,特色词表,学生词表) */
    private List<String> types;

    /** 标签，如["类型:学校教材", "年级:一年级", "版本:牛津版"] */
    private List<String> tags;
}
