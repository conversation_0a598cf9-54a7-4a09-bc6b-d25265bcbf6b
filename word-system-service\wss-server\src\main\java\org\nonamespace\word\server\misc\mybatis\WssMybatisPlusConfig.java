package org.nonamespace.word.server.misc.mybatis;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

@Configuration
public class WssMybatisPlusConfig {

    @Autowired
    MybatisPlusInterceptor interceptor;

    @PostConstruct
    public void init() {
        // 这里可以添加一些初始化逻辑，如果需要的话
        System.out.println("Mybatis Plus 配置已初始化");
//        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.POSTGRE_SQL));
    }
}