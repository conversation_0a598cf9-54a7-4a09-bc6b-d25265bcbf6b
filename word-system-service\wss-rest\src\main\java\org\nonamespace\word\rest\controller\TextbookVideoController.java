package org.nonamespace.word.rest.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.dto.TextbookVideoDto;
import org.nonamespace.word.server.service.ITextbookVideoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 教材单词视频管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@RestController
@RequestMapping("/textbook/video")
@RequiredArgsConstructor
@Slf4j
@Validated
public class TextbookVideoController extends BaseController {

    private final ITextbookVideoService textbookVideoService;

    /**
     * 上传单词视频
     */
    @PostMapping("/upload")
    public AjaxResult uploadVideo(@Valid TextbookVideoDto.UploadReq uploadReq) {
        try {
            TextbookVideoDto.VideoResp result = textbookVideoService.uploadVideo(uploadReq);
            return success(result);
        } catch (Exception e) {
            log.error("上传视频失败", e);
            return error("上传视频失败: " + e.getMessage());
        }
    }

    /**
     * 获取单词视频信息
     */
    @GetMapping("/info/{textbookItemId}")
    public AjaxResult getVideoInfo(@PathVariable String textbookItemId) {
        try {
            TextbookVideoDto.VideoResp result = textbookVideoService.getVideoInfo(textbookItemId);
            return success(result);
        } catch (Exception e) {
            log.error("获取视频信息失败", e);
            return error("获取视频信息失败: " + e.getMessage());
        }
    }

    /**
     * 查询视频列表
     */
    @GetMapping("/list")
    public AjaxResult getVideoList(TextbookVideoDto.QueryReq queryReq) {
        try {
            List<TextbookVideoDto.VideoResp> result = textbookVideoService.getVideoList(queryReq);
            return success(result);
        } catch (Exception e) {
            log.error("查询视频列表失败", e);
            return error("查询视频列表失败: " + e.getMessage());
        }
    }

    /**
     * 删除单词视频
     */
    @DeleteMapping("/delete")
    public AjaxResult deleteVideo(@Valid @RequestBody TextbookVideoDto.DeleteReq deleteReq) {
        try {
            boolean result = textbookVideoService.deleteVideo(deleteReq);
            if (result) {
                return success("视频删除成功");
            } else {
                return error("视频删除失败");
            }
        } catch (Exception e) {
            log.error("删除视频失败", e);
            return error("删除视频失败: " + e.getMessage());
        }
    }

    /**
     * 检查视频是否存在
     */
    @GetMapping("/exists/{textbookItemId}")
    public AjaxResult hasVideo(@PathVariable String textbookItemId) {
        try {
            boolean result = textbookVideoService.hasVideo(textbookItemId);
            return success(result);
        } catch (Exception e) {
            log.error("检查视频是否存在失败", e);
            return error("检查视频是否存在失败: " + e.getMessage());
        }
    }
}
