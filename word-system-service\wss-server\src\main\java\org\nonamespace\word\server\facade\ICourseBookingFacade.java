package org.nonamespace.word.server.facade;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.nonamespace.word.server.dto.management.booking.CourseBookingDto;

import java.util.List;

/**
 * 预约课申请Facade接口
 * 
 * <AUTHOR>
 * @date 2025-06-29
 */
public interface ICourseBookingFacade {

    /**
     * 分页查询预约课申请列表
     * 
     * @param req 查询请求
     * @return 预约课申请分页列表
     */
    IPage<CourseBookingDto.BasicResp> getCourseBookingPage(CourseBookingDto.GetListReq req);

    /**
     * 查询预约课申请详细信息
     * 
     * @param applicationId 申请ID
     * @return 预约课申请详细信息
     */
    CourseBookingDto.DetailResp getCourseBookingDetail(String applicationId);

    /**
     * 创建预约课申请
     *
     * @param req 创建请求
     * @return 申请ID
     */
    String createCourseBooking(CourseBookingDto.CreateReq req);

    /**
     * 创建预约课申请（支持试听课时间）
     *
     * @param req 创建请求（包含试听课时间）
     * @return 申请ID
     */
    String createCourseBookingWithTrialTime(CourseBookingDto.CreateReq req);

    /**
     * 更新预约课申请
     * 
     * @param req 更新请求
     * @return 是否成功
     */
    boolean updateCourseBooking(CourseBookingDto.UpdateReq req);

    /**
     * 删除预约课申请
     * 
     * @param applicationId 申请ID
     * @return 是否成功
     */
    boolean deleteCourseBooking(String applicationId);

    /**
     * 批量删除预约课申请
     * 
     * @param applicationIds 申请ID列表
     * @return 是否成功
     */
    boolean deleteCourseBookings(List<String> applicationIds);

    /**
     * 确认预约课申请
     * 
     * @param req 确认请求
     * @return 是否成功
     */
    boolean approveCourseBooking(CourseBookingDto.ApprovalReq req);

    /**
     * 拒绝预约课申请
     * 
     * @param req 拒绝请求
     * @return 是否成功
     */
    boolean rejectCourseBooking(CourseBookingDto.RejectionReq req);

    /**
     * 取消预约课申请
     * 
     * @param applicationId 申请ID
     * @return 是否成功
     */
    boolean cancelCourseBooking(String applicationId);

    /**
     * 获取预约课申请统计信息
     * 
     * @return 统计信息
     */
    CourseBookingDto.StatsResp getCourseBookingStats();

    /**
     * 查询可选教师列表
     * 
     * @param req 查询请求
     * @return 可选教师列表
     */
    List<CourseBookingDto.AvailableTeacherResp> getAvailableTeachers(CourseBookingDto.AvailableTeachersReq req);

    /**
     * 获取教学组选项
     * 
     * @return 教学组选项列表
     */
    List<CourseBookingDto.TeachingGroupOptionResp> getTeachingGroupOptions();

    /**
     * 获取待确认的申请数量
     * 
     * @param teachingGroupId 教学组ID（可选）
     * @return 待确认申请数量
     */
    Long getPendingApplicationCount(String teachingGroupId);

    /**
     * 作废预约课申请
     *
     * @param req 作废请求
     * @return 是否成功
     */
    boolean voidCourseBooking(CourseBookingDto.VoidReq req);

    /**
     * 发送微信通知
     *
     * @param applicationId 申请ID
     * @param notificationType 通知类型
     * @return 是否成功
     */
    boolean sendWechatNotification(String applicationId, String notificationType);
}
