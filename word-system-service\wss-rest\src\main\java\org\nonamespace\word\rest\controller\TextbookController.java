package org.nonamespace.word.rest.controller;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import jakarta.validation.Valid;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.domain.Textbook;
import org.nonamespace.word.server.domain.TextbookItem;
import org.nonamespace.word.server.dto.TextbookDto;
import org.nonamespace.word.server.dto.TextbookItemTreeQueryDto;
import org.nonamespace.word.server.dto.TextbookPageDto;
import org.nonamespace.word.server.enums.TextBookNodeEnum;
import org.nonamespace.word.server.facade.TextbookManagerService;
import org.nonamespace.word.server.service.ICourseService;
import org.nonamespace.word.server.service.ITextbookItemService;
import org.nonamespace.word.server.service.IWordService;
import org.nonamespace.word.server.service.impl.TextbookService;
import org.nonamespace.word.common.utils.OssService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 词定义 (统一教材与词): 定义各种词Controller
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Slf4j
@RestController
@RequestMapping("/word/textbook")
//@Anonymous
public class TextbookController extends BaseController {
    @Autowired
    private TextbookService textbookService;

    @Autowired
    private TextbookManagerService textbookManagerService;

    @Autowired
    private IWordService wordService;

    @Autowired
    private ICourseService courseService;

    @Autowired
    private ITextbookItemService textbookItemService;

    @Autowired
    private OssService ossService;


    /**
     * 获取词定义 (统一教材与词): 定义各种词详细信息
     */
    @PreAuthorize("@ss.hasPermi('word:textbook:query')")
    @GetMapping(value = "/tree")
    public AjaxResult treeSearch(TextbookItemTreeQueryDto searchDto) {
        return AjaxResult.success(textbookService.search(searchDto));
    }

    /**
     * 查询单词
     * @return
     */
    @PreAuthorize("@ss.hasPermi('word:textbook:query')")
    @GetMapping("/page")
    public AjaxResult page(TextbookPageDto.Req req) throws FileNotFoundException {
//        CourseInfoDto.Resp course = courseService.courseInfo("1928832820214677511");
//        courseService.generatePdf(course.getContent().getSections(), course.getId());
        return AjaxResult.success(textbookService.page(req));
    }

    @PreAuthorize("@ss.hasPermi('word:textbook:query')")
    @GetMapping("/list-all")
    public AjaxResult listAll() throws FileNotFoundException {
//        CourseInfoDto.Resp course = courseService.courseInfo("1928832820214677511");
//        courseService.generatePdf(course.getContent().getSections(), course.getId());
        return AjaxResult.success(textbookService.listAll());
    }


    /**
     * 新增词定义 (统一教材与词): 定义各种词
     */
    @PreAuthorize("@ss.hasPermi('word:textbook:add')")
    @Log(title = "创建或更新教材", businessType = BusinessType.INSERT)
    @PostMapping("/addOrUpdate")
    public AjaxResult addOrUpdate(TextbookDto textbookDto) {
        return AjaxResult.success(textbookService.insertOrUpdateTextbook(textbookDto));
    }


    /**
     * 删除词定义 (统一教材与词): 定义各种词
     */
    @PreAuthorize("@ss.hasPermi('word:textbook:remove')")
    @Log(title = "删除教材", businessType = BusinessType.DELETE)
	@PostMapping("/remove")
    public AjaxResult remove(@RequestBody List<String> ids) {
        textbookService.removeTextbooks(ids);
        return AjaxResult.success();
    }

//    @PreAuthorize("@ss.hasPermi('word:textbook:import')")
//    @GetMapping("/import")
//    public AjaxResult importTextbook() {
//        textbookManagerService.importTextbook();
//        return AjaxResult.success();
//    }

//
//    @PostMapping("/init-base-info")
//    public AjaxResult initTextbookBaseInfo(@RequestBody String jsonStr) {
//        textbookManagerService.initTextbookBaseInfo(jsonStr);
//        return AjaxResult.success();
//    }
//
//    @GetMapping("/fix-word-meanings")
//    public AjaxResult fixWordMeanings() {
//        textbookManagerService.fixWordMeanings();
//        return AjaxResult.success();
//    }

    /**
     * 下载教材讲义PDF
     * @param downloadReq 下载请求参数
     * @return PDF下载响应
     */
    @Log(title = "下载教材讲义", businessType = BusinessType.EXPORT)
    @PostMapping("/download-handout")
    public AjaxResult downloadHandout(@RequestBody @Valid DownloadReq downloadReq) {
        Textbook textbook = textbookService.getById(downloadReq.getTextbookId());
        if(textbook == null) {
            return AjaxResult.error("教材不存在");
        }

        LocalDate today = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M月d日");
        String date = today.format(formatter);
        String PDFName = StrUtil.format("【北大军哥名师团神奇英语】\n【{}】教材讲义", textbook.getName().trim());

        // 根据下载方式获取目标单词项ID列表
        List<String> targetTextbookItemIds = getTargetTextbookItemIds(downloadReq);

        // 使用现有的courseService.downloadPdf方法生成PDF
        ByteArrayOutputStream outputStream = courseService.downloadPdf(
                targetTextbookItemIds,
                true,
                null,
                PDFName
        );

        byte[] pdfBytes = outputStream.toByteArray();

        // 将字节数组转换为Base64字符串
        String base64Pdf = Base64.getEncoder().encodeToString(pdfBytes);

        // 构建响应数据
        Map<String, Object> responseData = new HashMap<>();
        responseData.put("body", base64Pdf);

        Map<String, String[]> responseHeaders = new HashMap<>();
        responseHeaders.put("Content-Type", new String[]{MediaType.APPLICATION_PDF_VALUE});
        responseHeaders.put("Content-Disposition", new String[]{"attachment; filename=\"" + PDFName.replace("\n", "") + "_" + date + ".pdf\""});
        responseHeaders.put("Content-Length", new String[]{String.valueOf(pdfBytes.length)});

        responseData.put("headers", responseHeaders);

        return AjaxResult.success(responseData);
    }

    /**
     * 下载教材音频ZIP
     * @param downloadReq 下载请求参数
     * @return 下载链接响应
     */
    @Log(title = "下载教材音频", businessType = BusinessType.EXPORT)
    @PostMapping("/download-audio")
    public AjaxResult downloadAudio(@RequestBody @Valid DownloadReq downloadReq) {
        Textbook textbook = textbookService.getById(downloadReq.getTextbookId());
        if(textbook == null) {
            return AjaxResult.error("教材不存在");
        }

        try {
            // 根据下载方式获取目标单词项ID列表
            List<String> targetTextbookItemIds = getTargetTextbookItemIds(downloadReq);

            // 生成音频ZIP文件并上传到OSS
            String downloadUrl = textbookManagerService.generateAudioZipAndUpload(targetTextbookItemIds, textbook);

            // 构建响应数据
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("downloadUrl", downloadUrl);

            return AjaxResult.success(responseData);
        } catch (Exception e) {
            log.error("生成音频ZIP失败: {}", e.getMessage(), e);
            return AjaxResult.error("生成音频文件失败: " + e.getMessage());
        }
    }

    /**
     * 根据下载请求获取目标单词项ID列表
     */
    private List<String> getTargetTextbookItemIds(DownloadReq downloadReq) {
        switch (downloadReq.getDownloadType()) {
            case "whole":
                // 整本下载：获取教材的所有单词项ID
                return getTextbookAllWordItemIds(downloadReq.getTextbookId());
            case "range":
                // 自选范围：根据起始和结束序号获取单词项ID
                if (downloadReq.getStartWordIndex() == null || downloadReq.getEndWordIndex() == null) {
                    throw new IllegalArgumentException("自选范围下载必须提供开始和结束单词序号");
                }
                if (downloadReq.getStartWordIndex() > downloadReq.getEndWordIndex()) {
                    throw new IllegalArgumentException("开始单词序号不能大于结束单词序号");
                }
                return getTextbookRangeWordItemIds(downloadReq.getTextbookId(),
                        downloadReq.getStartWordIndex(), downloadReq.getEndWordIndex());
            case "selected":
                // 选定单词：使用传入的单词项ID列表（兼容现有接口）
                if (downloadReq.getTextbookItemIds() == null || downloadReq.getTextbookItemIds().isEmpty()) {
                    throw new IllegalArgumentException("选定单词下载必须提供单词项ID列表");
                }
                return downloadReq.getTextbookItemIds();
            default:
                throw new IllegalArgumentException("不支持的下载方式: " + downloadReq.getDownloadType());
        }
    }

    /**
     * 获取教材的所有单词项ID
     */
    private List<String> getTextbookAllWordItemIds(String textbookId) {
        List<TextbookItem> textbookItems = textbookItemService.lambdaQuery()
                .select(TextbookItem::getId, TextbookItem::getDisplayOrder)
                .eq(TextbookItem::getTextbookId, textbookId)
                .eq(TextbookItem::getNodeType, TextBookNodeEnum.WORD.getValue())
                .isNotNull(TextbookItem::getWordId)
                .orderByAsc(TextbookItem::getDisplayOrder)
                .list();
        if(textbookItems.isEmpty()) {
            return new ArrayList<>();
        }

        // 检查最后一个单词项的显示顺序是否与列表大小一致，更新这本教材的数量
        if(textbookItems.getLast().getDisplayOrder()!= textbookItems.size()) {
            textbookItemService.updateDisplayOrder(textbookId);
        }

        return textbookItems
                .stream()
                .map(TextbookItem::getId)
                .toList();
    }

    /**
     * 获取教材指定范围的单词项ID
     */
    private List<String> getTextbookRangeWordItemIds(String textbookId, Integer startWordIndex, Integer endWordIndex) {
        // 获取教材的所有单词项，按显示顺序排序
        List<TextbookItem> textbookItems = textbookItemService.lambdaQuery()
                .select(TextbookItem::getId, TextbookItem::getDisplayOrder)
                .eq(TextbookItem::getTextbookId, textbookId)
                .eq(TextbookItem::getNodeType, TextBookNodeEnum.WORD.getValue())
                .isNotNull(TextbookItem::getWordId)
                .orderByAsc(TextbookItem::getDisplayOrder)
                .list();

        if (textbookItems.isEmpty()) {
            return new ArrayList<>();
        }

        // 验证范围是否有效
        int totalWords = textbookItems.size();
        if (startWordIndex > totalWords || endWordIndex > totalWords) {
            throw new IllegalArgumentException(String.format("单词序号超出范围，教材共有%d个单词", totalWords));
        }

        // 检查最后一个单词项的显示顺序是否与列表大小一致，更新这本教材的数量
        if(textbookItems.getLast().getDisplayOrder()!= textbookItems.size()) {
            textbookItemService.updateDisplayOrder(textbookId);
        }

        // 获取指定范围的单词项ID（注意：序号从1开始，但List索引从0开始）
        return textbookItems.subList(startWordIndex - 1, endWordIndex)
                .stream()
                .map(TextbookItem::getId)
                .toList();
    }

    /**
     * 下载请求DTO
     */
    @Data
    public static class DownloadReq {
        private String textbookId;
        private String downloadType;
        private Integer startWordIndex;
        private Integer endWordIndex;
        private List<String> textbookItemIds;
    }

}
