package org.nonamespace.word.server.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR> Created on 2025/05/13 19:07
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class DataEntity extends BaseEntity {

    /** 创建时间 */
    @TableField(
            fill = FieldFill.INSERT,
            value = "create_time"
    )
    private Date createTime;

    /** 更新时间 */
    @TableField(
            fill = FieldFill.INSERT_UPDATE,
            value = "update_time"
    )
    private Date updateTime;

    @TableField(
            fill = FieldFill.INSERT,
            value = "create_by"
    )
    private String createBy;

    @TableField(
            fill = FieldFill.INSERT_UPDATE,
            value = "update_by"
    )
    private String updateBy;


    /** 是否删除 */
    @TableField("deleted")
    @TableLogic
    @ApiModelProperty(value = "是否删除  0-非删除  1-删除")
    private Boolean deleted;


}
