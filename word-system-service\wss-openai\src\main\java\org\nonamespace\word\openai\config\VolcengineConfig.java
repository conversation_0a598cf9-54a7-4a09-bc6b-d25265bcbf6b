package org.nonamespace.word.openai.config;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


/**
 * 火山模型相关配置信息
 */
@Data
@Component
@ConfigurationProperties(prefix = "volcengine")
public class VolcengineConfig {

    private String baseUrl;
    private Speech speech;


    @Getter
    @Setter
    public static class Speech {
        private String appid;
        private String cluster;
        private String secretkey;
        private String accessToken;
        private VoiceType voiceType;

        @Getter
        @Setter
        public static class VoiceType {
            private String us;
            private String uk;
        }
    }

}
