package org.nonamespace.word.rest.controller.order;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.dto.order.RefundRecordDto;
import org.nonamespace.word.server.service.order.IOrderRefundRecordService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 退款记录管理Controller
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Slf4j
@RestController
@RequestMapping("/refund-records")
@RequiredArgsConstructor
@Validated
public class RefundRecordController extends BaseController {

    private final IOrderRefundRecordService refundRecordService;

    /**
     * 分页查询退款记录
     */
    @PreAuthorize("@ss.hasPermi('refund:records:query')")
    @GetMapping("/list")
    public TableDataInfo list(RefundRecordDto.QueryReq req) {
        log.info("分页查询退款记录: req={}", req);
        IPage<RefundRecordDto.Resp> page = refundRecordService.selectRefundRecordsByParam(req);
        TableDataInfo dataTable = getDataTable(page.getRecords());
        log.info("查询退款记录成功: total={}", page.getTotal());
        return dataTable;
    }

    /**
     * 查询退款记录详情
     */
    @PreAuthorize("@ss.hasPermi('refund:records:detail')")
    @GetMapping("/{refundRecordId}")
    public AjaxResult getDetail(@PathVariable String refundRecordId) {
        try {
            log.info("查询退款记录详情: refundRecordId={}", refundRecordId);
            
            RefundRecordDto.DetailResp detail = refundRecordService.getRefundRecordDetail(refundRecordId);
            
            if (detail == null) {
                return AjaxResult.error("退款记录不存在");
            }
            
            log.info("查询退款记录详情成功: refundRecordId={}", refundRecordId);
            return AjaxResult.success(detail);
            
        } catch (Exception e) {
            log.error("查询退款记录详情失败: refundRecordId={}", refundRecordId, e);
            return AjaxResult.error("查询退款记录详情失败: " + e.getMessage());
        }
    }

    /**
     * 审批退款记录
     */
    @PreAuthorize("@ss.hasPermi('refund:records:approve')")
    @Log(title = "退款记录审批", businessType = BusinessType.UPDATE)
    @PostMapping("/approve")
    public AjaxResult approve(@RequestBody @Validated RefundRecordDto.ApprovalReq req) {
        try {
            log.info("审批退款记录: req={}", req);
            
            String approverId = getUserId().toString();
            String approverName = getUsername();
            
            refundRecordService.approveRefundRecord(
                    req.getRefundRecordId(), 
                    req.getApprovalResult(), 
                    req.getApprovalRemark(),
                    approverId, 
                    approverName
            );
            
            log.info("审批退款记录成功: refundRecordId={}, result={}", 
                    req.getRefundRecordId(), req.getApprovalResult());
            
            return AjaxResult.success("审批成功");
            
        } catch (Exception e) {
            log.error("审批退款记录失败", e);
            return AjaxResult.error("审批失败: " + e.getMessage());
        }
    }

    /**
     * 查询退款统计数据
     */
    @PreAuthorize("@ss.hasPermi('refund:records:statistics')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics(@RequestParam(required = false) String startDate,
                                  @RequestParam(required = false) String endDate) {
        try {
            log.info("查询退款统计数据: startDate={}, endDate={}", startDate, endDate);
            
            RefundRecordDto.StatisticsResp statistics = refundRecordService.getRefundStatistics(startDate, endDate);
            
            log.info("查询退款统计数据成功");
            return AjaxResult.success(statistics);
            
        } catch (Exception e) {
            log.error("查询退款统计数据失败", e);
            return AjaxResult.error("查询统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 查询待审批的退款记录数量
     */
    @PreAuthorize("@ss.hasPermi('refund:records:query')")
    @GetMapping("/pending-count")
    public AjaxResult getPendingApprovalCount() {
        try {
            Integer count = refundRecordService.getPendingApprovalCount();
            return AjaxResult.success(count);
            
        } catch (Exception e) {
            log.error("查询待审批退款记录数量失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 导出退款记录
     */
    @PreAuthorize("@ss.hasPermi('refund:records:export')")
    @Log(title = "退款记录导出", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RefundRecordDto.QueryReq req) {
        try {
            log.info("开始导出退款记录: req={}", req);
            
            List<RefundRecordDto.Resp> list = refundRecordService.exportRefundRecords(req);
            
            ExcelUtil<RefundRecordDto.Resp> util = new ExcelUtil<>(RefundRecordDto.Resp.class);
            util.exportExcel(response, list, "退款记录数据");
            
            log.info("导出退款记录成功: count={}", list.size());
            
        } catch (Exception e) {
            log.error("导出退款记录失败", e);
        }
    }

    /**
     * 查询订单的退款记录
     */
    @PreAuthorize("@ss.hasPermi('refund:records:query')")
    @GetMapping("/order/{orderId}")
    public AjaxResult getRefundRecordsByOrderId(@PathVariable String orderId) {
        try {
            log.info("查询订单退款记录: orderId={}", orderId);
            
            List<org.nonamespace.word.server.domain.order.OrderRefundRecord> records = 
                    refundRecordService.getRefundRecordsByOrderId(orderId);
            
            log.info("查询订单退款记录成功: orderId={}, count={}", orderId, records.size());
            return AjaxResult.success(records);
            
        } catch (Exception e) {
            log.error("查询订单退款记录失败: orderId={}", orderId, e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }
}
