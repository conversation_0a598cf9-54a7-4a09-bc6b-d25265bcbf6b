package org.nonamespace.word.server.jobs;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * //TODO
 *
 * <AUTHOR>
 * @date 2025/6/7 11:24
 */
@Slf4j
@RequiredArgsConstructor
public class BaseJobTask {

    private final StringRedisTemplate stringRedisTemplate;

    public void executeJob(String uniqueJobKey, Consumer<Void> consumer, Integer expire, TimeUnit timeUnit) {
        String lockKey = "course:lock:remind:" + uniqueJobKey;
        try {
            // 10分钟过期
            String locked = stringRedisTemplate.opsForValue().get(lockKey);
            if (StrUtil.isBlankIfStr(locked)) {
                stringRedisTemplate.opsForValue().set(lockKey, "locked", expire, timeUnit);
                consumer.accept(null);
            } else {
                log.warn("[{}] - 上次任务还未结束，本次任务跳过", uniqueJobKey);
            }
        } catch (Exception e) {
            throw new RuntimeException("[{}] - 任务执行异常", e);
        } finally {
            stringRedisTemplate.delete(lockKey);
        }
    }

}
