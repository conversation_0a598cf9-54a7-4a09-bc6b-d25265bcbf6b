package org.nonamespace.word.server.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.misc.WssContext;
import org.nonamespace.word.server.domain.StudentCourseConsumption;
import org.nonamespace.word.server.mapper.StudentCourseConsumptionMapper;
import org.nonamespace.word.server.service.IStudentCourseConsumptionService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 学生课消记录Service实现类
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@Service
public class StudentCourseConsumptionServiceImpl extends ServiceImpl<StudentCourseConsumptionMapper, StudentCourseConsumption> 
        implements IStudentCourseConsumptionService {

    @Override
    public StudentCourseConsumption recordConsumption(String studentId, String subject, String specification,
                                                    BigDecimal consumedHours, Date consumptionTime,
                                                    String courseId, String courseHoursId, String teacherId, String remark) {
        // 调用带性质参数的方法，默认为正式课
        return recordConsumption(studentId, subject, specification, "正式课", consumedHours, consumptionTime,
                courseId, courseHoursId, teacherId, remark);
    }

    @Override
    public StudentCourseConsumption recordConsumption(String studentId, String subject, String specification, String nature,
                                                    BigDecimal consumedHours, Date consumptionTime,
                                                    String courseId, String courseHoursId, String teacherId, String remark) {
        StudentCourseConsumption record = new StudentCourseConsumption();
        record.setId(IdUtil.getSnowflakeNextIdStr());
        record.setStudentId(studentId);
        record.setSubject(subject);
        record.setSpecification(specification);
        record.setNature(nature); // 使用传入的课程性质
        record.setConsumedHours(consumedHours);
        record.setConsumptionTime(consumptionTime);
        record.setCourseId(courseId);
        record.setCourseHoursId(courseHoursId);
        record.setTeacherId(teacherId);
        record.setRemark(remark);
        record.setStatus("active");
        record.setCreateTime(WssContext.now());
        record.setUpdateTime(WssContext.now());
        record.setDeleted(false);

        save(record);
        log.info("记录课消成功: studentId={}, subject={}, specification={}, nature={}, hours={}, courseHoursId={}",
                studentId, subject, specification, nature, consumedHours, courseHoursId);

        return record;
    }
}
