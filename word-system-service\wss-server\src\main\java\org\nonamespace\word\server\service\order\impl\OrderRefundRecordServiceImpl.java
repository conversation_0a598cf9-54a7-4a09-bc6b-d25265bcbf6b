package org.nonamespace.word.server.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.constants.OrderConstants;
import org.nonamespace.word.server.domain.order.OrderRefundRecord;
import org.nonamespace.word.server.domain.order.Orders;
import org.nonamespace.word.server.domain.order.OrdersTrx;
import org.nonamespace.word.server.dto.order.RefundRecordDto;
import org.nonamespace.word.server.mapper.order.OrderRefundRecordMapper;
import org.nonamespace.word.server.service.base.BaseService;
import org.nonamespace.word.server.service.order.IOrderRefundRecordService;
import org.nonamespace.word.server.util.OrderTrxCodeUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 退款记录Service实现类
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderRefundRecordServiceImpl extends BaseService<OrderRefundRecordMapper, OrderRefundRecord> 
        implements IOrderRefundRecordService {

    private final OrderTrxCodeUtil orderTrxCodeUtil;

    @Override
    public OrderRefundRecord createRefundRecord(Orders orders, OrdersTrx originalTrx, OrdersTrx refundTrx,
                                              String refundType, Long refundAmount, String refundReason,
                                              String operatorId, String operatorName) {
        
        log.info("创建退款记录: orderId={}, refundType={}, refundAmount={}", 
                orders.getId(), refundType, refundAmount);
        
        // 构建退款记录
        OrderRefundRecord refundRecord = OrderRefundRecord.builder()
                .orderId(orders.getId())
                .refundNo(orderTrxCodeUtil.generalOrderCode(OrderConstants.TrxType.REFUND))
                .originalTrxId(originalTrx != null ? originalTrx.getId() : null)
                .refundTrxId(refundTrx != null ? refundTrx.getId() : null)
                .refundType(refundType)
                .refundAmount(refundAmount)
                .refundReason(refundReason)
                .refundStatus(OrderConstants.RefundStatus.PROCESSING)
                .refundMethod(OrderConstants.RefundMethod.ORIGINAL)
                .studentId(orders.getStudentId())
                .salerId(orders.getSalerId())
                .productId(orders.getProductId())
                .products(orders.getProducts())
                .orders(orders)
                .ordersTrxs(refundTrx)
                .build();

        // 设置学生信息（需要通过用户服务获取，这里先设置ID）
        refundRecord.setStudentId(orders.getStudentId());
        refundRecord.setSalerId(orders.getSalerId());
        // 保存退款记录
        this.save(refundRecord);
        
        log.info("退款记录创建成功: refundRecordId={}", refundRecord.getId());
        return refundRecord;
    }

    @Override
    public void updateRefundStatus(String refundRecordId, String refundStatus, String errorMessage) {
        log.info("更新退款记录状态: refundRecordId={}, refundStatus={}", refundRecordId, refundStatus);
        
        LambdaUpdateWrapper<OrderRefundRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrderRefundRecord::getId, refundRecordId)
                .set(OrderRefundRecord::getRefundStatus, refundStatus)
                .set(OrderRefundRecord::getUpdateTime, new Date())
                .set(OrderRefundRecord::getUpdateBy, SecurityUtils.getUserId().toString());
        
        if (OrderConstants.RefundStatus.SUCCESS.equals(refundStatus)) {
            updateWrapper.set(OrderRefundRecord::getRefundTime, new Date());
        }
        
        if (StrUtil.isNotBlank(errorMessage)) {
            updateWrapper.set(OrderRefundRecord::getErrorMessage, errorMessage);
        }
        
        this.update(updateWrapper);
    }

    @Override
    public void updatePlatformInfo(String refundRecordId, String platformRefundId, String platformResponse) {
        log.info("更新退款记录平台信息: refundRecordId={}, platformRefundId={}", refundRecordId, platformRefundId);
        
        LambdaUpdateWrapper<OrderRefundRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrderRefundRecord::getId, refundRecordId)
                .set(OrderRefundRecord::getPlatformRefundId, platformRefundId)
                .set(OrderRefundRecord::getPlatformResponse, platformResponse)
                .set(OrderRefundRecord::getUpdateTime, new Date())
                .set(OrderRefundRecord::getUpdateBy, SecurityUtils.getUserId().toString());
        
        this.update(updateWrapper);
    }

    @Override
    public IPage<RefundRecordDto.Resp> selectRefundRecordsByParam(RefundRecordDto.QueryReq req) {
        Page<RefundRecordDto.Resp> page = new Page<>(req.getPageNum(), req.getPageSize());
        return this.baseMapper.selectRefundRecordsByParam(page, req);
    }

    @Override
    public RefundRecordDto.DetailResp getRefundRecordDetail(String refundRecordId) {
        return this.baseMapper.selectRefundRecordDetail(refundRecordId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approveRefundRecord(String refundRecordId, String approvalResult, String approvalRemark,
                                  String approverId, String approverName) {
        log.info("审批退款记录: refundRecordId={}, approvalResult={}", refundRecordId, approvalResult);
        
        LambdaUpdateWrapper<OrderRefundRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrderRefundRecord::getId, refundRecordId)
                .set(OrderRefundRecord::getUpdateTime, new Date())
                .set(OrderRefundRecord::getUpdateBy, approverId);
        
        this.update(updateWrapper);
    }

    @Override
    public RefundRecordDto.StatisticsResp getRefundStatistics(String startDate, String endDate) {
        // 查询基础统计数据
        List<Map<String, Object>> basicStats = this.baseMapper.selectRefundStatistics(startDate, endDate);
        
        // 查询学科统计数据
        List<Map<String, Object>> subjectStats = this.baseMapper.selectRefundStatisticsBySubject(startDate, endDate);
        
        // 查询操作人统计数据
        List<Map<String, Object>> operatorStats = this.baseMapper.selectRefundStatisticsByOperator(startDate, endDate);
        
        // 构建响应对象
        RefundRecordDto.StatisticsResp resp = new RefundRecordDto.StatisticsResp();
        
        if (CollUtil.isNotEmpty(basicStats)) {
            Map<String, Object> stat = basicStats.get(0);
            resp.setStatisticsDate(stat.get("statistics_date").toString());
            resp.setTotalRefundCount(((Number) stat.get("total_refund_count")).intValue());
            resp.setTotalRefundAmountYuan(formatAmountToYuan((Number) stat.get("total_refund_amount")));
            resp.setPartialRefundCount(((Number) stat.get("partial_refund_count")).intValue());
            resp.setPartialRefundAmountYuan(formatAmountToYuan((Number) stat.get("partial_refund_amount")));
            resp.setFullRefundCount(((Number) stat.get("full_refund_count")).intValue());
            resp.setFullRefundAmountYuan(formatAmountToYuan((Number) stat.get("full_refund_amount")));
            resp.setSuccessRefundCount(((Number) stat.get("success_refund_count")).intValue());
            resp.setFailedRefundCount(((Number) stat.get("failed_refund_count")).intValue());
            resp.setAvgRefundAmountYuan(formatAmountToYuan((Number) stat.get("avg_refund_amount")));
        }
        
        // 设置学科统计
        List<RefundRecordDto.SubjectStatistics> subjectStatistics = subjectStats.stream()
                .map(stat -> {
                    RefundRecordDto.SubjectStatistics subjectStat = new RefundRecordDto.SubjectStatistics();
                    subjectStat.setSubject(stat.get("subject").toString());
                    subjectStat.setRefundCount(((Number) stat.get("refund_count")).intValue());
                    subjectStat.setRefundAmountYuan(formatAmountToYuan((Number) stat.get("refund_amount")));
                    return subjectStat;
                })
                .collect(Collectors.toList());
        resp.setSubjectStatistics(subjectStatistics);
        
        // 设置操作人统计
        List<RefundRecordDto.OperatorStatistics> operatorStatistics = operatorStats.stream()
                .map(stat -> {
                    RefundRecordDto.OperatorStatistics operatorStat = new RefundRecordDto.OperatorStatistics();
                    operatorStat.setOperatorName(stat.get("operator_name").toString());
                    operatorStat.setRefundCount(((Number) stat.get("refund_count")).intValue());
                    operatorStat.setRefundAmountYuan(formatAmountToYuan((Number) stat.get("refund_amount")));
                    return operatorStat;
                })
                .collect(Collectors.toList());
        resp.setOperatorStatistics(operatorStatistics);
        
        return resp;
    }

    @Override
    public List<OrderRefundRecord> getRefundRecordsByOrderId(String orderId) {
        return this.baseMapper.selectByOrderId(orderId);
    }

    @Override
    public Integer getPendingApprovalCount() {
        return 0;
    }

    @Override
    public List<RefundRecordDto.Resp> exportRefundRecords(RefundRecordDto.QueryReq req) {
        // 设置大的页面大小用于导出
        req.setPageSize(req.getMaxExportCount() != null ? req.getMaxExportCount() : 10000);
        req.setPageNum(1);
        
        IPage<RefundRecordDto.Resp> page = selectRefundRecordsByParam(req);
        return page.getRecords();
    }

    @Override
    public boolean existsRefundRecord(String orderId, String refundType) {
        LambdaQueryWrapper<OrderRefundRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderRefundRecord::getOrderId, orderId)
                .eq(OrderRefundRecord::getRefundType, refundType)
                .eq(OrderRefundRecord::getRefundStatus, OrderConstants.RefundStatus.SUCCESS);
        
        return this.count(queryWrapper) > 0;
    }

    @Override
    public Long getTotalRefundAmountByOrderId(String orderId) {
        LambdaQueryWrapper<OrderRefundRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderRefundRecord::getOrderId, orderId)
                .eq(OrderRefundRecord::getRefundStatus, OrderConstants.RefundStatus.SUCCESS);
        
        List<OrderRefundRecord> records = this.list(queryWrapper);
        return records.stream()
                .mapToLong(OrderRefundRecord::getRefundAmount)
                .sum();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateRefundStatus(List<String> refundRecordIds, String refundStatus) {
        if (CollUtil.isEmpty(refundRecordIds)) {
            return;
        }
        
        LambdaUpdateWrapper<OrderRefundRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(OrderRefundRecord::getId, refundRecordIds)
                .set(OrderRefundRecord::getRefundStatus, refundStatus)
                .set(OrderRefundRecord::getUpdateTime, new Date())
                .set(OrderRefundRecord::getUpdateBy, SecurityUtils.getUserId().toString());
        
        if (OrderConstants.RefundStatus.SUCCESS.equals(refundStatus)) {
            updateWrapper.set(OrderRefundRecord::getRefundTime, new Date());
        }
        
        this.update(updateWrapper);
    }

    /**
     * 格式化金额为元（保留2位小数）
     */
    private String formatAmountToYuan(Number amountFen) {
        if (amountFen == null) {
            return "0.00";
        }
        return String.format("%.2f", amountFen.longValue() / 100.0);
    }
}
