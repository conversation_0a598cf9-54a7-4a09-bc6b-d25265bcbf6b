package org.nonamespace.word.server.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR> <PERSON> on 2025/05/13 20:00
 */
@Data
@Accessors(chain = true)
public class BaseEntity  implements Serializable {
    @TableId(
            type = IdType.ASSIGN_ID
    )
    private String id;

    @Serial
    private static final long serialVersionUID = 1L;
}
