package org.nonamespace.word.server.misc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * 用于存储句子片段及其原始索引的记录类 (Java 14+)。
 *
 * @param content       片段内容
 * @param originalIndex 片段在原始句子中的0基索引
 */
record IndexedPart(String content, int originalIndex) {
}

/**
 * 句子排序题目生成器。
 */
public class SentenceOrderQuizGenerator {

    /**
     * 根据输入的句子生成排序题目。
     *
     * @param sentence 一个由'|'分割成三段的英语句子。
     * @return SentenceQuiz 对象，包含题目信息。
     * @throws IllegalArgumentException 如果句子不包含三段。
     */
    public static SentenceQuiz generateQuiz(String sentence) {
        String[] partsArray = StrUtil.splitToArray(sentence, '|');
        int partCnt = Math.min(3, partsArray.length);
//        if (partsArray == null || partsArray.partCnt != 3) {
//            return SentenceQuiz.ERROR; // 如果不是三段，返回错误题目
//        }
        List<String> originalParts = Arrays.asList(partsArray);

        // 1. 创建带原始索引的片段对象列表
        List<IndexedPart> indexedParts =
            IntStream.range(0, originalParts.size())
                     .mapToObj(i -> new IndexedPart(originalParts.get(i).trim(), i))
                     .toList();

        // 2. 打乱片段顺序以生成题目的显示部分 (这些片段将被标记为 1, 2, 3)
        List<IndexedPart> shuffledForQuestionDisplay = new ArrayList<>(indexedParts);
        Collections.shuffle(shuffledForQuestionDisplay, RandomUtil.getRandom());

        List<String> questionSegmentsToDisplay = shuffledForQuestionDisplay.stream()
                .map(IndexedPart::content)
                .collect(Collectors.toList());

        // 3. 确定正确答案的顺序
        // correctAnswerOrder 列表的索引代表原始片段的顺序 (0, 1, 2)
        // correctAnswerOrder 列表的值代表该原始片段在打乱后问题中的显示序号 (1, 2, 3)
        // 例如: correctAnswerOrder.get(0) 是原始第一片段在打乱后的显示序号
        List<Integer> correctAnswerOrder = new ArrayList<>(Collections.nCopies(partCnt, 0));
        for (int i = 0; i < shuffledForQuestionDisplay.size(); i++) {
            IndexedPart currentDisplayPart = shuffledForQuestionDisplay.get(i);
            // currentDisplayPart.originalIndex() 是这个片段在原始句子中的位置 (0, 1, or 2)
            // (i + 1) 是这个片段在当前题目中的显示序号 (1, 2, or 3)
            correctAnswerOrder.set(currentDisplayPart.originalIndex(), i + 1);
        }

        // 4. 生成所有可能的显示序号组合 (1, 2, 3 的排列)
        List<List<Integer>> allPermutations = permutations(IntStream.range(1, partCnt + 1).boxed().toList());

        // 5. 筛选出错误的选项（排除正确答案）
        List<List<Integer>> incorrectOptions = allPermutations.stream()
            .filter(perm -> !perm.equals(correctAnswerOrder))
            .collect(Collectors.toList());
        Collections.shuffle(incorrectOptions, RandomUtil.getRandom());

        // 6. 组合最终的四个选项顺序 (1个正确答案, 3个错误答案)
        List<List<Integer>> finalOptionOrders = Stream.concat(
            Stream.of(correctAnswerOrder),
            incorrectOptions.stream().limit(partCnt)
        ).collect(Collectors.toList());
        // 确保有4个选项，对于3个片段的情况，总有 3!-1 = 5个干扰项，所以足够。

        Collections.shuffle(finalOptionOrders, RandomUtil.getRandom()); // 打乱选项的顺序

        // 7. 确定正确答案的选项索引
        int correctIndexInOptions = -1;
        for (int i = 0; i < finalOptionOrders.size(); i++) {
            if (finalOptionOrders.get(i).equals(correctAnswerOrder)) {
                correctIndexInOptions = i;
                break;
            }
        }

        // 8. 格式化选项文本 (例如, "(1, 2, 3)")
        List<String> optionsAsTextFormatted = finalOptionOrders.stream()
            .map(order -> order.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(", ")))
            .collect(Collectors.toList());
        
        return new SentenceQuiz(questionSegmentsToDisplay, optionsAsTextFormatted, correctIndexInOptions, correctAnswerOrder);
    }

    private static List<List<Integer>> permutations(List<Integer> list) {
        if (list.size() == 1) return Collections.singletonList(list);
        List<List<Integer>> result = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            List<Integer> sublist = new ArrayList<>(list);
            Integer removed = sublist.remove(i);
            for (List<Integer> perm : permutations(sublist)) {
                List<Integer> newPerm = new ArrayList<>();
                newPerm.add(removed);
                newPerm.addAll(perm);
                result.add(newPerm);
            }
        }
        return result;
    }


    /**
     * 封装生成的句子排序题目信息。
     */
    @Data
    public static class SentenceQuiz {
        private final List<String> questionSegments; // 打乱后的句子片段 (显示为 1, 2, 3)
        private final List<String> optionsText;      // 格式化的选项文本，例如 ["(1, 2, 3)", "(2, 1, 3)", ...]
        private final Integer correctAnswerIndex;         // 正确答案的选项索引
        private final List<Integer> correctSequenceIndices; // 正确的片段序号组合, e.g., [3, 1, 2]

        public SentenceQuiz(List<String> questionSegments, List<String> optionsText, int correctAnswerIndex, List<Integer> correctSequenceIndices) {
            this.questionSegments = questionSegments;
            this.optionsText = optionsText;
            this.correctAnswerIndex = correctAnswerIndex;
            this.correctSequenceIndices = correctSequenceIndices;
        }

        public static SentenceQuiz ERROR = new SentenceQuiz(CollUtil.toList("题目错误", "题目错误", "题目错误", "题目错误"), CollUtil.toList("题目", "错误", ""), 0, CollUtil.toList(1, 2,3));
    }

}