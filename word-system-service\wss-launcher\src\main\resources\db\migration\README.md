# 数据库版本管理规范

## Flyway脚本命名规范

### 版本化脚本（Versioned Migrations）
格式：`V{版本号}__{描述}.sql`

#### 版本号规则
- 主版本.次版本.修订版本（如：V1.0.1）
- 主版本：重大架构变更
- 次版本：功能性变更
- 修订版本：bug修复或小调整

#### 示例
```
V1.0.0__Baseline.sql                    # 基线版本
V1.0.1__Add_user_profile_table.sql      # 添加用户资料表
V1.0.2__Update_course_status_enum.sql   # 更新课程状态枚举
V1.1.0__Add_notification_system.sql     # 添加通知系统
V2.0.0__Refactor_user_system.sql        # 重构用户系统
```

### 可重复脚本（Repeatable Migrations）
格式：`R__{描述}.sql`

用于视图、存储过程、函数等可重复执行的脚本

#### 示例
```
R__Create_user_statistics_view.sql      # 创建用户统计视图
R__Update_course_summary_procedure.sql  # 更新课程汇总存储过程
```

## 脚本编写规范

### 1. 文件头注释
```sql
-- 脚本描述：添加用户资料表
-- 版本：V1.0.1
-- 作者：开发者姓名
-- 创建时间：2025-07-06
-- 说明：详细的变更说明
```

### 2. 事务处理
- 每个脚本应该是原子性的
- 复杂变更使用事务包装
- 提供回滚说明

### 3. 兼容性考虑
- 向后兼容
- 数据迁移安全
- 索引优化

### 4. 测试要求
- 在开发环境充分测试
- 提供测试数据验证
- 性能影响评估

## 开发流程

### 1. 创建新脚本
1. 确定版本号（避免冲突）
2. 创建脚本文件
3. 编写变更SQL
4. 本地测试验证

### 2. 代码审查
1. 脚本语法检查
2. 业务逻辑验证
3. 性能影响评估
4. 安全性检查

### 3. 部署流程
1. 开发环境验证
2. 测试环境部署
3. 预生产验证
4. 生产环境部署

## 注意事项

### ⚠️ 重要提醒
1. **禁止修改已部署的脚本**
2. **生产环境部署前必须备份**
3. **重大变更需要回滚预案**
4. **版本号分配需要团队协调**

### 🔧 最佳实践
1. 脚本保持简单明确
2. 一个脚本一个功能
3. 充分的注释说明
4. 测试数据准备

### 📊 监控检查
1. 执行时间监控
2. 错误日志检查
3. 数据一致性验证
4. 性能指标对比
