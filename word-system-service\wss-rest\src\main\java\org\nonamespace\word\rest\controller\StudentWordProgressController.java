package org.nonamespace.word.rest.controller;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import org.nonamespace.word.server.domain.StudentWordProgress;
import org.nonamespace.word.server.service.IStudentWordProgressService;
import com.ruoyi.common.utils.poi.ExcelUtil;

/**
 * 学生单词学习进度Controller
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@RestController
@RequestMapping("/student/progress")
public class StudentWordProgressController extends BaseController
{
    @Autowired
    private IStudentWordProgressService studentWordProgressService;


    /**
     * 获取学生的单词最新学习进度
     * @param studentId
     * @return
     */
    @GetMapping(value = "/word/{studentId}")
    public AjaxResult getProgressByStudentId(@PathVariable("studentId") String studentId,@RequestParam(value = "textbookId",required = false) String textbookId) {
        return success(studentWordProgressService.getProgressByStudentId(studentId,textbookId));
    }
}
