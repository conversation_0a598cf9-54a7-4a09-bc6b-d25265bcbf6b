package org.nonamespace.word.server.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.domain.TeacherTimeSlot;
import org.nonamespace.word.server.dto.management.teachinggroup.TeacherDto;
import org.nonamespace.word.server.exception.ServiceException;
import org.nonamespace.word.server.mapper.TeacherTimeSlotMapper;
import org.nonamespace.word.server.service.ITeacherTimeSlotService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 教师时间表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Slf4j
@Service
public class TeacherTimeSlotServiceImpl extends ServiceImpl<TeacherTimeSlotMapper, TeacherTimeSlot> implements ITeacherTimeSlotService {

    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");

    @Override
    public List<TeacherDto.TimeSlotResp> selectByTeacherId(String teacherId) {
        if (teacherId == null) {
            throw ServiceException.paramError("教师ID不能为空");
        }

        try {
            List<TeacherTimeSlot> timeSlots = lambdaQuery()
                    .eq(TeacherTimeSlot::getTeacherId, teacherId)
                    .eq(TeacherTimeSlot::getDeleted, false)
                    .orderByAsc(TeacherTimeSlot::getWeekday, TeacherTimeSlot::getStartTime)
                    .list();

            return convertToTimeSlotResp(timeSlots);
        } catch (Exception e) {
            log.error("查询教师时间表失败: teacherId={}", teacherId, e);
            throw ServiceException.systemError("查询教师时间表失败", e);
        }
    }

    @Override
    public int updateBatchByTeacherId(String teacherId, List<TeacherDto.TimeSlotResp> timeSlots) {
        // 先查询原有记录数量
        long originalCount = lambdaQuery()
                .eq(TeacherTimeSlot::getTeacherId, teacherId)
                .eq(TeacherTimeSlot::getDeleted, false)
                .count();

        // 逻辑删除原有记录
        boolean deleteSuccess = lambdaUpdate()
                .set(TeacherTimeSlot::getDeleted, true)
                .set(TeacherTimeSlot::getUpdateTime, new Date())
                .eq(TeacherTimeSlot::getTeacherId, teacherId)
                .eq(TeacherTimeSlot::getDeleted, false)
                .update();

        int totalAffected = deleteSuccess ? (int) originalCount : 0;

        // 如果有新的时间段，则插入
        if (!CollectionUtils.isEmpty(timeSlots)) {
            List<TeacherTimeSlot> entities = convertToEntity(teacherId, timeSlots);
            boolean insertSuccess = saveBatch(entities);
            if (insertSuccess) {
                totalAffected += entities.size();
            }
        }

        return totalAffected;
    }

    @Override
    public int deleteByTeacherId(String teacherId) {
        return lambdaUpdate()
                .set(TeacherTimeSlot::getDeleted, true)
                .set(TeacherTimeSlot::getUpdateTime, new Date())
                .eq(TeacherTimeSlot::getTeacherId, teacherId)
                .update() ? 1 : 0;
    }

    @Override
    public int insertBatch(List<TeacherTimeSlot> timeSlots) {
        if (CollectionUtils.isEmpty(timeSlots)) {
            return 0;
        }
        return saveBatch(timeSlots) ? timeSlots.size() : 0;
    }

    @Override
    public List<TeacherDto.TimeSlotResp> selectByTeacherIds(List<String> teacherIds) {
        if (CollectionUtils.isEmpty(teacherIds)) {
            return new ArrayList<>();
        }

        List<TeacherTimeSlot> timeSlots = lambdaQuery()
                .in(TeacherTimeSlot::getTeacherId, teacherIds)
                .eq(TeacherTimeSlot::getDeleted, false)
                .orderByAsc(TeacherTimeSlot::getTeacherId, TeacherTimeSlot::getWeekday, TeacherTimeSlot::getStartTime)
                .list();

        return convertToTimeSlotResp(timeSlots);
    }

    @Override
    public boolean updateTeacherTimeSlots(TeacherDto.UpdateTimeSlotsReq req) {
        String teacherId = req.getTeacherId();

        // 先删除原有的时间表
        deleteByTeacherId(teacherId);
        
        // 如果有新的时间表，则插入
        if (!CollectionUtils.isEmpty(req.getTimeSlots())) {
            List<TeacherTimeSlot> timeSlots = convertToEntity(teacherId, req.getTimeSlots());
            insertBatch(timeSlots);
        }
        
        return true;
    }

    @Override
    public List<TeacherDto.AvailableTimeSlotsDto> getAvailableTimeSlots(String teacherId) {
        // 查询教师的可用时间段（状态为available）
        LambdaQueryWrapper<TeacherTimeSlot> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeacherTimeSlot::getTeacherId, teacherId)
                   .eq(TeacherTimeSlot::getStatus, "available")
                   .orderByAsc(TeacherTimeSlot::getWeekday)
                   .orderByAsc(TeacherTimeSlot::getStartTime);
        
        List<TeacherTimeSlot> timeSlots = list(queryWrapper);
        
        // 按星期几分组
        Map<Integer, List<TeacherTimeSlot>> groupedByWeekday = timeSlots.stream()
                .collect(Collectors.groupingBy(TeacherTimeSlot::getWeekday));
        
        List<TeacherDto.AvailableTimeSlotsDto> result = new ArrayList<>();
        
        for (Map.Entry<Integer, List<TeacherTimeSlot>> entry : groupedByWeekday.entrySet()) {
            TeacherDto.AvailableTimeSlotsDto dto = new TeacherDto.AvailableTimeSlotsDto();
            dto.setWeekday(entry.getKey());
            
            List<TeacherDto.AvailableTimeSlotsDto.TimeSlot> timeSlotList = entry.getValue().stream()
                    .map(slot -> {
                        TeacherDto.AvailableTimeSlotsDto.TimeSlot timeSlot = new TeacherDto.AvailableTimeSlotsDto.TimeSlot();
                        timeSlot.setStartTime(slot.getStartTime().format(TIME_FORMATTER));
                        timeSlot.setEndTime(slot.getEndTime().format(TIME_FORMATTER));
                        return timeSlot;
                    })
                    .collect(Collectors.toList());
            
            dto.setTimeSlots(timeSlotList);
            result.add(dto);
        }
        
        // 按星期几排序
        result.sort((a, b) -> a.getWeekday().compareTo(b.getWeekday()));
        
        return result;
    }

    /**
     * 将实体类转换为DTO
     */
    private List<TeacherDto.TimeSlotResp> convertToTimeSlotResp(List<TeacherTimeSlot> timeSlots) {
        return timeSlots.stream().map(slot -> {
            TeacherDto.TimeSlotResp resp = new TeacherDto.TimeSlotResp();
            resp.setId(slot.getId());
            resp.setTeacherId(slot.getTeacherId());
            resp.setWeekday(slot.getWeekday());
            resp.setStartTime(slot.getStartTime() != null ? slot.getStartTime().format(TIME_FORMATTER) : null);
            resp.setEndTime(slot.getEndTime() != null ? slot.getEndTime().format(TIME_FORMATTER) : null);
            resp.setStatus(slot.getStatus());
            resp.setRemark(slot.getRemark());
            return resp;
        }).collect(Collectors.toList());
    }

    /**
     * 将DTO转换为实体类
     */
    private List<TeacherTimeSlot> convertToEntity(String teacherId, List<TeacherDto.TimeSlotResp> timeSlotDtos) {
        Date now = new Date();
        return timeSlotDtos.stream().map(dto -> {
            TeacherTimeSlot entity = new TeacherTimeSlot();
            entity.setId(IdUtil.getSnowflakeNextIdStr());
            entity.setTeacherId(teacherId);
            entity.setWeekday(dto.getWeekday());

            // 安全的时间解析和验证
            try {
                LocalTime startTime = null;
                LocalTime endTime = null;

                if (dto.getStartTime() != null && !dto.getStartTime().trim().isEmpty()) {
                    startTime = LocalTime.parse(dto.getStartTime().trim(), TIME_FORMATTER);
                    entity.setStartTime(startTime);
                } else {
                    throw ServiceException.paramError("开始时间不能为空");
                }

                if (dto.getEndTime() != null && !dto.getEndTime().trim().isEmpty()) {
                    endTime = LocalTime.parse(dto.getEndTime().trim(), TIME_FORMATTER);
                    entity.setEndTime(endTime);
                } else {
                    throw ServiceException.paramError("结束时间不能为空");
                }

                // 验证时间段有效性
                if (startTime != null && endTime != null) {
                    if (!startTime.isBefore(endTime)) {
                        throw ServiceException.paramError(
                            String.format("开始时间(%s)必须小于结束时间(%s)",
                                dto.getStartTime(), dto.getEndTime()));
                    }

                    // 检查时间段长度（至少15分钟）
                    long minutes = java.time.Duration.between(startTime, endTime).toMinutes();
                    if (minutes < 5) {
                        throw ServiceException.paramError(
                            String.format("时间段长度至少需要5分钟，当前为%d分钟", minutes));
                    }
                }

            } catch (DateTimeParseException e) {
                log.error("时间格式解析失败: startTime={}, endTime={}", dto.getStartTime(), dto.getEndTime(), e);
                throw ServiceException.paramError("时间格式不正确，请使用HH:mm格式: " + e.getMessage());
            }

            entity.setStatus(dto.getStatus() != null ? dto.getStatus() : "available");
            entity.setRemark(dto.getRemark());
            entity.setCreateTime(now);
            entity.setUpdateTime(now);
            entity.setDeleted(false);
            return entity;
        }).collect(Collectors.toList());
    }
}
