-- =====================================================
-- 订单导出权限配置SQL
-- 版本: V1.2.0_001
-- 描述: 为订单管理和交易流水添加导出权限
-- 作者: WSS-AGENT
-- 日期: 2025-08-07
-- =====================================================

-- =====================================================
-- 1. 订单导出权限按钮 (menu_type = 'F')
-- =====================================================

-- 1.1 普通用户订单导出权限
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (4001, '订单导出', 2030, 10, '', '', '', 1, 0, 'F', '0', '0', 'order:export', '#', 'admin', NOW(), '普通用户订单数据导出权限');

-- 1.2 管理员订单导出权限
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (4002, '管理员订单导出', 2031, 10, '', '', '', 1, 0, 'F', '0', '0', 'order:manager:export', '#', 'admin', NOW(), '管理员订单数据导出权限');

-- 1.3 交易流水导出权限
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (4003, '交易流水导出', 2030, 11, '', '', '', 1, 0, 'F', '0', '0', 'order:trx:export', '#', 'admin', NOW(), '交易流水数据导出权限');

-- 1.4 管理员交易流水导出权限
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (4004, '管理员交易流水导出', 2031, 11, '', '', '', 1, 0, 'F', '0', '0', 'order:manager:trx:export', '#', 'admin', NOW(), '管理员交易流水数据导出权限');

-- =====================================================
-- 2. 角色权限关联配置 (sys_role_menu)
-- =====================================================

-- 2.1 为超级管理员角色分配所有导出权限
INSERT INTO words.sys_role_menu (role_id, menu_id) VALUES
(1, 4001), -- 超级管理员 - 订单导出
(1, 4002), -- 超级管理员 - 管理员订单导出
(1, 4003), -- 超级管理员 - 交易流水导出
(1, 4004); -- 超级管理员 - 管理员交易流水导出

-- 2.2 为人力角色分配所有导出权限
INSERT INTO words.sys_role_menu (role_id, menu_id) VALUES
(2, 4001), -- 人力 - 订单导出
(2, 4002), -- 人力 - 管理员订单导出
(2, 4003), -- 人力 - 交易流水导出
(2, 4004); -- 人力 - 管理员交易流水导出

-- 2.3 为销售总监角色分配管理员导出权限
INSERT INTO words.sys_role_menu (role_id, menu_id) VALUES
(9, 4002), -- 销售总监 - 管理员订单导出
(9, 4004); -- 销售总监 - 管理员交易流水导出

-- 2.4 为销售组长角色分配普通导出权限
INSERT INTO words.sys_role_menu (role_id, menu_id) VALUES
(10, 4001), -- 销售组长 - 订单导出
(10, 4003); -- 销售组长 - 交易流水导出

-- 2.5 为销售角色分配普通导出权限
INSERT INTO words.sys_role_menu (role_id, menu_id) VALUES
(8, 4001), -- 销售 - 订单导出
(8, 4003); -- 销售 - 交易流水导出

-- 2.6 为教学组长角色分配普通导出权限
INSERT INTO words.sys_role_menu (role_id, menu_id) VALUES
(11, 4001), -- 教学组长 - 订单导出
(11, 4003); -- 教学组长 - 交易流水导出

-- =====================================================
-- 3. 权限说明
-- =====================================================

/*
导出权限说明：
1. order:export - 普通用户订单导出权限
   - 可以导出自己权限范围内的订单数据
   - 受数据权限控制，只能导出有权限查看的订单

2. order:manager:export - 管理员订单导出权限
   - 可以导出所有订单数据
   - 不受数据权限限制

3. order:trx:export - 交易流水导出权限
   - 可以导出自己权限范围内的交易流水
   - 受数据权限控制

4. order:manager:trx:export - 管理员交易流水导出权限
   - 可以导出所有交易流水数据
   - 不受数据权限限制

角色权限分配：
- 超级管理员、人力：拥有所有导出权限
- 销售总监：拥有管理员级别的导出权限
- 销售组长、销售、教学组长：拥有普通导出权限

数据权限控制：
- 普通导出权限会根据用户的数据权限范围进行过滤
- 管理员导出权限可以导出全部数据
- 销售人员只能导出自己负责的学生相关订单
- 销售组长可以导出本组销售人员负责的学生相关订单
*/

-- =====================================================
-- 4. 更新序列和完成提示
-- =====================================================

-- 更新菜单ID序列（如果使用序列）
SELECT setval('words.sys_menu_menu_id_seq', 4100);

-- 执行完成
SELECT '订单导出权限配置SQL执行完成！' as result;
