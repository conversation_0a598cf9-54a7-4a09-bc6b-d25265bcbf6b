package org.nonamespace.word.server.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.concurrent.ConcurrentMapCache;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Collection;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 会话级别缓存管理器
 * 使用ThreadLocal实现请求级别的缓存隔离
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
public class SessionLevelCacheManager implements CacheManager {

    /**
     * 线程本地缓存存储
     * Key: 缓存名称, Value: 缓存实例
     */
    private static final ThreadLocal<ConcurrentMap<String, Cache>> CACHE_HOLDER = 
            ThreadLocal.withInitial(ConcurrentHashMap::new);

    /**
     * 预定义的缓存名称
     */
    private final Collection<String> cacheNames;

    public SessionLevelCacheManager(Collection<String> cacheNames) {
        this.cacheNames = cacheNames;
    }

    @Override
    @Nullable
    public Cache getCache(@NonNull String name) {
        ConcurrentMap<String, Cache> caches = CACHE_HOLDER.get();
        
        // 如果缓存已存在，直接返回
        Cache cache = caches.get(name);
        if (cache != null) {
            return cache;
        }

        // 创建新的缓存实例
        cache = new SessionLevelCache(name);
        Cache existingCache = caches.putIfAbsent(name, cache);
        
        if (existingCache != null) {
            cache = existingCache;
        }
        
        log.debug("创建会话级别缓存: {}", name);
        return cache;
    }

    @Override
    @NonNull
    public Collection<String> getCacheNames() {
        return cacheNames;
    }

    /**
     * 清除当前线程的所有缓存
     */
    public static void clearAllCaches() {
        ConcurrentMap<String, Cache> caches = CACHE_HOLDER.get();
        if (caches != null && !caches.isEmpty()) {
            caches.values().forEach(Cache::clear);
            caches.clear();
            log.debug("已清除当前线程的所有会话级别缓存");
        }
        CACHE_HOLDER.remove();
    }

    /**
     * 会话级别缓存实现
     */
    private static class SessionLevelCache extends ConcurrentMapCache {
        
        public SessionLevelCache(String name) {
            super(name);
        }

        @Override
        public String toString() {
            return "SessionLevelCache{name='" + getName() + "'}";
        }
    }
}
