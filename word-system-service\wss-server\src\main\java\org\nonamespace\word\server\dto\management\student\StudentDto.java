package org.nonamespace.word.server.dto.management.student;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 学生相关DTO
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
public class StudentDto {

    /**
     * 学生基础信息响应
     */
    @Data
    public static class BasicResp {
        private String id;
        private String name;
        private String phone;
        private String gender;
        private String grade;
        private String school;
        private String className;
        private String parentName;
        private String parentPhone;
        private String teacherId;
        private String teacherName;
        private BigDecimal totalHours;
        private BigDecimal consumedHours;
        private BigDecimal remainingHours;
        private String status;
        private Date createTime;
        private Date updateTime;

        // 销售相关字段
        private String salesId;
        private String salesName;
        private String salesPhone;
        private String salesGroupId;
        private String salesGroupName;
        private Date assignTime;
    }

    /**
     * 学生详细信息响应
     */
    @Data
    public static class DetailResp {
        private String id;
        private String name;
        private String phone;
        private String gender;
        private String grade;
        private String school;
        private String className;
        private String parentName;
        private String parentPhone;
        private String teacherId;
        private String teacherName;
        private BigDecimal totalHours;
        private BigDecimal consumedHours;
        private BigDecimal remainingHours;
        private String learningGoals;
        private String remarks;
        private String status;
        private Date createTime;
        private Date updateTime;

        // 销售相关字段
        private String salesId;
        private String salesName;
        private String salesPhone;
        private String salesGroupId;
        private String salesGroupName;
        private Date assignTime;
    }

    /**
     * 创建学生请求
     */
    @Data
    public static class CreateReq {
        @NotBlank(message = "学生姓名不能为空")
        private String name;

        @NotBlank(message = "手机号码不能为空")
        private String phone;

        @NotBlank(message = "性别不能为空")
        private String gender;

        private String grade;
        private String school;
        private String className;
        private String parentName;
        private String parentPhone;
        private String teacherId;
        private String learningGoals;
        private String remarks;
        private String status;
    }

    /**
     * 更新学生请求
     */
    @Data
    public static class UpdateReq {
        @NotBlank(message = "学生ID不能为空")
        private String id;

        @NotBlank(message = "学生姓名不能为空")
        private String name;

        @NotBlank(message = "手机号码不能为空")
        private String phone;

        @NotBlank(message = "性别不能为空")
        private String gender;

        private String grade;
        private String school;
        private String className;
        private String parentName;
        private String parentPhone;
        private String teacherId;
        private BigDecimal totalHours;
        private BigDecimal consumedHours;
        private String learningGoals;
        private String remarks;
        private String status;
    }

    /**
     * 学生列表查询请求
     */
    @Data
    public static class GetListReq {
        private String keyword;
        private String name;
        private String phone;
        private String grade;
        private String school;
        private String teacherId;
        private String status;
        private Integer pageNum = 1;
        private Integer pageSize = 20;


        private Date createTimeStart;
        private Date createTimeEnd;

        // 销售相关查询条件
        private String salesId;
        private String salesGroupId;
        private List<String> salesIds; // 用于销售组长查询本组销售的学生
        private Date assignTimeStart;
        private Date assignTimeEnd;
    }

    /**
     * 学生课表响应
     */
    @Data
    public static class ScheduleResp {
        private String id;
        private String studentId;
        private String teacherId;
        private String teacherName;
        private String subject;
        private String courseType;
        private Date startTime;
        private Date endTime;
        private String status;
        private String remarks;
    }

    /**
     * 学生统计信息响应
     */
    @Data
    public static class StatsResp {
        private Integer totalStudents;
        private Integer activeStudents;
        private Integer inactiveStudents;
        private Integer graduatedStudents;
        private Integer newStudentsThisMonth;
    }

    /**
     * 可分配学生响应（用于教师分配学生）
     */
    @Data
    public static class AvailableResp {
        private String id;
        private String name;
        private String phone;
        private String grade;
        private String school;
        private BigDecimal remainingHours;
        private String status;
    }

    /**
     * 学生课程统计响应
     */
    @Data
    public static class CourseStatsResp {
        private String studentId;
        private String studentName;
        private Integer totalCourses;
        private Integer completedCourses;
        private Integer scheduledCourses;
        private Integer cancelledCourses;
        private BigDecimal totalHours;
        private BigDecimal consumedHours;
        private BigDecimal remainingHours;
    }

    /**
     * 学生最近课程响应
     */
    @Data
    public static class RecentCourseResp {
        private String id;
        private String teacherName;
        private String subject;
        private Date startTime;
        private Date endTime;
        private String status;
        private String remarks;
        private Date createTime;
    }
}
