package org.nonamespace.word.server.dto;


import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Data
public class CourseGetReviewDto {


    @Getter
    @Setter
    public static class Resp {

        private List<Review> reviews;

        @Getter
        @Setter
        public static class Review {
            private String scheduledTime;
            private String studentId;
            private List<DetailContent> detailContents;
        }

        @Getter
        @Setter
        public static class DetailContent {
            private String id;
            private String courseId;
            private String reviewType;
            private String name;
            private Date actualTime;
            private String status;
            private long wordCount;

        }

    }
}
