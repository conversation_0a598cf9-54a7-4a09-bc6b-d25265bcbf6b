-- =====================================================
-- 退款记录表结构调整
-- 版本: V1.2.1_001
-- 描述: 将order_refund_records表调整为order_refunds表结构
-- 作者: WSS-AGENT
-- 日期: 2025-01-27
-- =====================================================

-- 1. 备份现有数据（如果有的话）
CREATE TABLE IF NOT EXISTS words.order_refund_records_backup AS 
SELECT * FROM words.order_refund_records WHERE 1=1;

-- 2. 创建新的order_refunds表
CREATE TABLE IF NOT EXISTS words.order_refunds (
    id varchar(64) NOT NULL DEFAULT nextval('"words".order_refund_records_id_seq'::regclass),
    order_id varchar(64) NOT NULL,
    refund_no varchar(100) NOT NULL,
    original_trx_id varchar(64) NOT NULL,
    refund_trx_id varchar(64) NOT NULL,
    refund_type varchar(20) NOT NULL,
    refund_amount int8 NOT NULL,
    refund_reason varchar(500) NOT NULL,
    refund_status varchar(20) NOT NULL DEFAULT 'processing'::character varying,
    student_id varchar(64),
    saler_id varchar(64),
    product_id varchar(64),
    products varchar(200),
    orders varchar(50),
    orders_trxs varchar(50),
    refund_method varchar(50),
    platform_refund_id varchar(100),
    platform_response text,
    refund_time timestamp(6),
    error_message text,
    remark text,
    create_time timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by varchar(64) NOT NULL,
    update_by varchar(64) NOT NULL,
    deleted bool NOT NULL DEFAULT false,
    PRIMARY KEY (id)
);

-- 3. 设置表所有者
ALTER TABLE words.order_refunds OWNER TO main;

-- 4. 创建更新时间触发器
CREATE TRIGGER update_order_refunds_timestamp 
    BEFORE UPDATE ON words.order_refunds
    FOR EACH ROW
    EXECUTE PROCEDURE words.update_timestamp();

-- 5. 添加表和字段注释
COMMENT ON TABLE words.order_refunds IS '退款记录表';
COMMENT ON COLUMN words.order_refunds.id IS '退款记录ID';
COMMENT ON COLUMN words.order_refunds.order_id IS '订单ID';
COMMENT ON COLUMN words.order_refunds.refund_no IS '退款订单号';
COMMENT ON COLUMN words.order_refunds.original_trx_id IS '原交易流水ID';
COMMENT ON COLUMN words.order_refunds.refund_trx_id IS '退款交易流水ID';
COMMENT ON COLUMN words.order_refunds.refund_type IS '退款类型：partial-部分退款，full-全额退款';
COMMENT ON COLUMN words.order_refunds.refund_amount IS '退款金额（分）';
COMMENT ON COLUMN words.order_refunds.refund_reason IS '退款原因';
COMMENT ON COLUMN words.order_refunds.refund_status IS '退款状态：processing-处理中，success-成功，failed-失败';
COMMENT ON COLUMN words.order_refunds.student_id IS '学生ID';
COMMENT ON COLUMN words.order_refunds.saler_id IS '销售员ID';
COMMENT ON COLUMN words.order_refunds.product_id IS '产品ID';
COMMENT ON COLUMN words.order_refunds.products IS '原产品信息';
COMMENT ON COLUMN words.order_refunds.orders IS '原订单信息';
COMMENT ON COLUMN words.order_refunds.orders_trxs IS '原交易流水信息';
COMMENT ON COLUMN words.order_refunds.refund_method IS '退款方式：original-原路退回，manual-手动退款';
COMMENT ON COLUMN words.order_refunds.platform_refund_id IS '支付平台退款ID';
COMMENT ON COLUMN words.order_refunds.platform_response IS '支付平台响应信息';
COMMENT ON COLUMN words.order_refunds.refund_time IS '退款完成时间';
COMMENT ON COLUMN words.order_refunds.error_message IS '错误信息（退款失败时）';
COMMENT ON COLUMN words.order_refunds.remark IS '备注';
COMMENT ON COLUMN words.order_refunds.create_time IS '创建时间';
COMMENT ON COLUMN words.order_refunds.update_time IS '更新时间';
COMMENT ON COLUMN words.order_refunds.create_by IS '创建人';
COMMENT ON COLUMN words.order_refunds.update_by IS '更新人';
COMMENT ON COLUMN words.order_refunds.deleted IS '删除标志';

-- 6. 迁移现有数据（如果order_refund_records表存在且有数据）
INSERT INTO words.order_refunds (
    id, order_id, refund_no, original_trx_id, refund_trx_id, 
    refund_type, refund_amount, refund_reason, refund_status,
    student_id, saler_id, product_id, 
    products, orders, orders_trxs,
    refund_method, platform_refund_id, platform_response,
    refund_time, error_message, remark,
    create_time, update_time, create_by, update_by, deleted
)
SELECT 
    id, order_id, 
    COALESCE(order_no, 'REF' || id) as refund_no, -- 使用order_no作为refund_no，如果为空则生成
    original_trx_id, refund_trx_id,
    refund_type, refund_amount, refund_reason, refund_status,
    student_id, saler_id, product_id,
    COALESCE(product_name, '') as products, -- 产品信息
    COALESCE(order_no, '') as orders, -- 订单信息
    '' as orders_trxs, -- 交易流水信息（新字段，暂时为空）
    refund_method, platform_refund_id, platform_response,
    refund_time, error_message, remark,
    create_time, update_time, create_by, update_by,
    CASE 
        WHEN del_flag = '0' THEN false 
        ELSE true 
    END as deleted
FROM words.order_refund_records
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'words' AND table_name = 'order_refund_records');

-- 7. 创建索引
CREATE INDEX IF NOT EXISTS idx_order_refunds_order_id ON words.order_refunds(order_id);
CREATE INDEX IF NOT EXISTS idx_order_refunds_refund_no ON words.order_refunds(refund_no);
CREATE INDEX IF NOT EXISTS idx_order_refunds_status ON words.order_refunds(refund_status);
CREATE INDEX IF NOT EXISTS idx_order_refunds_create_time ON words.order_refunds(create_time);
CREATE INDEX IF NOT EXISTS idx_order_refunds_deleted ON words.order_refunds(deleted);

-- 8. 更新统计视图（如果存在）
DROP VIEW IF EXISTS words.v_refund_statistics;
CREATE OR REPLACE VIEW words.v_refund_statistics AS
SELECT
    DATE_TRUNC('day', create_time) as refund_date,
    refund_type,
    refund_status,
    COUNT(*) as refund_count,
    SUM(refund_amount) as total_refund_amount,
    AVG(refund_amount) as avg_refund_amount
FROM words.order_refunds
WHERE deleted = false
GROUP BY DATE_TRUNC('day', create_time), refund_type, refund_status;

-- 9. 删除旧表（谨慎操作，建议在确认新表正常工作后再执行）
-- DROP TABLE IF EXISTS words.order_refund_records;

-- 10. 重命名序列（如果需要）
-- ALTER SEQUENCE words.order_refund_records_id_seq RENAME TO order_refunds_id_seq;
