package org.nonamespace.word.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.entity.SysUser;
import org.nonamespace.word.server.domain.ViewUser;
import org.springframework.cache.annotation.Cacheable;

import java.util.List;

public interface IUserService extends IService<SysUser> {

    Long nextUserId();

    boolean resetPassword(Long userId);

    String getDefaultPassword();

    @Cacheable(value = "systemData:permissions", key = "'allUser'", cacheManager = "sessionCacheManager")
    List<ViewUser> viewUsers();

    /**
     * 获取用户显示名称
     *
     * @param user 用户信息
     * @return 用户显示名称
     */
    String getUserDisplayName(SysUser user);

    /**
     * 手机号是否已被使用
     * @param phoneNumber
     * @return
     */
    public boolean isPhoneNumberExists(String phoneNumber);

    /**
     * 检查手机号是否被其他用户使用（排除指定用户）
     * @param phoneNumber 手机号
     * @param excludeUserId 要排除的用户ID
     * @return 是否被其他用户使用
     */
    public boolean isPhoneNumberUsedByOtherUser(String phoneNumber, Long excludeUserId);
}
