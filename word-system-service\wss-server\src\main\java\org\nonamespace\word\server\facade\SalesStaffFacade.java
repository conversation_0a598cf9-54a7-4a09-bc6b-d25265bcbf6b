package org.nonamespace.word.server.facade;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.nonamespace.word.server.dto.sales.SalesStaffDto;

import java.util.List;

/**
 * 销售人员管理业务门面接口
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
public interface SalesStaffFacade {

    /**
     * 分页查询销售人员列表
     * 
     * @param req 查询请求
     * @return 分页结果
     */
    IPage<SalesStaffDto.Resp> getSalesStaffPage(SalesStaffDto.GetListReq req);

    /**
     * 根据ID查询销售人员详情
     * 
     * @param id 销售人员ID
     * @return 销售人员详情
     */
    SalesStaffDto.Resp getSalesStaffById(String id);

    /**
     * 创建销售人员
     * 
     * @param req 创建请求
     * @return 销售人员ID
     */
    String createSalesStaff(SalesStaffDto.CreateReq req);

    /**
     * 更新销售人员
     * 
     * @param req 更新请求
     * @return 是否成功
     */
    boolean updateSalesStaff(SalesStaffDto.UpdateReq req);

    /**
     * 删除销售人员
     * 
     * @param ids 销售人员ID数组
     * @return 是否成功
     */
    boolean deleteSalesStaff(String[] ids);

    /**
     * 获取可用销售人员列表
     * 
     * @param req 查询请求
     * @return 可用销售人员列表
     */
    List<SalesStaffDto.AvailableResp> getAvailableSalesStaff(SalesStaffDto.GetAvailableReq req);

    /**
     * 获取销售人员统计信息
     * 
     * @return 统计信息
     */
    SalesStaffDto.StatsResp getSalesStaffStats();

    /**
     * 重置销售人员密码
     * 
     * @param id 销售人员ID
     * @param newPassword 新密码
     * @return 是否成功
     */
    boolean resetSalesStaffPassword(String id, String newPassword);

    /**
     * 启用/停用销售人员
     * 
     * @param id 销售人员ID
     * @param status 状态
     * @return 是否成功
     */
    boolean changeSalesStaffStatus(String id, String status);

    /**
     * 分配销售到销售组
     * 
     * @param req 分配请求
     * @return 是否成功
     */
    boolean assignSalesToGroup(SalesStaffDto.AssignReq req);

    /**
     * 批量分配销售到销售组
     * 
     * @param req 批量分配请求
     * @return 是否成功
     */
    boolean batchAssignSalesToGroup(SalesStaffDto.BatchAssignReq req);

    /**
     * 从销售组移除销售
     * 
     * @param salesId 销售人员ID
     * @param groupId 销售组ID
     * @return 是否成功
     */
    boolean removeSalesFromGroup(String salesId, String groupId);
}
