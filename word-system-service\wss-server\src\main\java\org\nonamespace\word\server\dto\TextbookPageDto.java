package org.nonamespace.word.server.dto;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.annotation.Excel;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.nonamespace.word.server.domain.Word;

import java.util.List;

@Data
@Builder
public class TextbookPageDto {

    /**
     * 请求实体类
     */
    @Getter
    @Setter
    public static class Req {
        /* 词表名称-模糊 */
        private String searchName;
        /* 教材类型 */
        private List<String> textBookTypeList;
        private int pageSize = 1;
        private int pageNum = 10;
        /* 教材名称 */
//        private QryCondition textBookName;
    }


    /**
     * 响应实体类
     */
    @Getter
    @Setter
    public static class Resp {
        /** 词表ID */
        private String id;

        /** 词表名称 (例如: "高频315词表", "牛津版三年级上册U1") */
        private String name;

        /** 描述 */
        private String description;

        /**
         * 封面
         */
        private String cover;

        /** 类型 (学校教材,特色词表,学生词表) */
        private String typeName;

        private String type;

        private String createBy;

        /** 词表结构 */
        private String wordList;

        /** 标签，如["类型:学校教材", "年级:一年级", "版本:牛津版"] */
        private String[] tags;

        /** 单词数 */
        private Long statWordCnt;

        /** 单元数 */
        private Long statUnitCnt;

        private String publisher;
        private Integer grade;
        private Integer semester;
        private String required;

        /**
         * 阶段
         */
        private String stage;
    }

}
