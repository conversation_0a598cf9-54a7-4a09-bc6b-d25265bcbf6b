package org.nonamespace.word.rest.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.dto.management.teachermatch.TeacherMatchDto;
import org.nonamespace.word.server.dto.management.teachinggroup.TeacherDto;
import org.nonamespace.word.server.service.ITeacherMatchService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/user/teacher")
@RequiredArgsConstructor
public class TeacherUserController extends BaseController {

    private final ITeacherMatchService teacherMatchService;

    @GetMapping("/available-time-slots")
    public AjaxResult availableTimeSlots(@RequestParam(value = "id", required = false) String teacherId) {
        try {
            TeacherMatchDto.TeacherWeeklySchedule result = teacherMatchService.getTeacherWeeklySchedule(teacherId, DateUtil.today());

            result.getDailySchedules().forEach(x-> x.getTimeSlots().removeIf(t-> !Objects.equals(t.getStatus(), "available")));

            List<TeacherDto.AvailableTimeSlotsDto> timeSlots = new ArrayList<>();

            // 将 DailySchedule 转换为 AvailableTimeSlotsDto
            if (result.getDailySchedules() != null) {
                // 按星期几分组时间段
                for (int weekday = 1; weekday <= 7; weekday++) {
                    final int currentWeekday = weekday;

                    // 收集当前星期几的所有可用时间段
                    List<TeacherDto.AvailableTimeSlotsDto.TimeSlot> dayTimeSlots = new ArrayList<>();

                    result.getDailySchedules().stream()
                        .filter(daily -> Objects.equals(daily.getWeekday(), currentWeekday))
                        .forEach(daily -> {
                            if (daily.getTimeSlots() != null) {
                                daily.getTimeSlots().forEach(slot -> {
                                    // 只添加可用的时间段（已经在前面过滤了status为available的）
                                    TeacherDto.AvailableTimeSlotsDto.TimeSlot timeSlot =
                                        new TeacherDto.AvailableTimeSlotsDto.TimeSlot();
                                    // 处理时间格式，去掉秒数部分
                                    timeSlot.setStartTime(formatTime(slot.getStartTime()));
                                    timeSlot.setEndTime(formatTime(slot.getEndTime()));
                                    dayTimeSlots.add(timeSlot);
                                });
                            }
                        });

                    // 如果当前星期几有可用时间段，则添加到结果中
                    if (!dayTimeSlots.isEmpty()) {
                        TeacherDto.AvailableTimeSlotsDto availableTimeSlots =
                            new TeacherDto.AvailableTimeSlotsDto();
                        availableTimeSlots.setWeekday(currentWeekday);
                        availableTimeSlots.setTimeSlots(dayTimeSlots);
                        timeSlots.add(availableTimeSlots);
                    }
                }
            }


            JSONObject json = new JSONObject();
            json.putOpt("availableTimeSlots", timeSlots);



            return success(json);
        } catch (Exception e) {
            log.error("获取教师时间表失败", e);
            return error("获取教师时间表失败: " + e.getMessage());
        }
    }

    /**
     * 格式化时间，去掉秒数部分
     * 将 "09:35:00" 格式转换为 "09:35"
     *
     * @param time 原始时间字符串
     * @return 格式化后的时间字符串
     */
    private String formatTime(String time) {
        if (time == null || time.isEmpty()) {
            return time;
        }

        // 如果时间包含秒数部分，则去掉
        if (time.length() > 5 && time.charAt(5) == ':') {
            return time.substring(0, 5);
        }

        return time;
    }
}
