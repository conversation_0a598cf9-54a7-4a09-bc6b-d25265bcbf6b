package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nonamespace.word.server.entity.DataEntity;

import java.util.List;

/**
 * 产品信息表（课时包）
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@TableName(value = "product", autoResultMap = true)
public class Product extends DataEntity {

    /**
     * 产品名称
     */
    @TableField("name")
    private String name;

    /**
     * 产品类型 (课程包、单次课程、教材等)
     */
    private String type;

    /**
     * 产品描述
     */
    @TableField("description")
    private String description;

    /**
     * 学科 (英语、数学、语文、物理等)
     */
    @TableField("subject")
    private String subject;

    /**
     * 课型 (基础课、提升课、专项课等)
     */
    @TableField("course_type")
    private String courseType;

    /**
     * 适用年级（多选，JSON数组）
     */
    @TableField(value = "applicable_grades", typeHandler = JacksonTypeHandler.class)
    private List<String> applicableGrades;

    /**
     * 单价（分）
     */
    @TableField("unit_price")
    private Long unitPrice;

    /**
     * 数量
     */
    @TableField("quantity")
    private Integer quantity;

    /**
     * 是否有赠送课时
     */
    @TableField("has_bonus_hours")
    private Boolean hasBonusHours;

    /**
     * 赠送课时数量
     */
    @TableField("bonus_hours_quantity")
    private Integer bonusHoursQuantity;

    /**
     * 是否包含教材费
     */
    @TableField("has_material_fee")
    private Boolean hasMaterialFee;

    /**
     * 教材费（分）
     */
    @TableField("material_fee")
    private Long materialFee;

    /**
     * 原价（分）= 单价 * 数量 + 教材费
     */
    @TableField("original_price")
    private Long originalPrice;

    /**
     * 售价（分）
     */
    @TableField("selling_price")
    private Long sellingPrice;

    /**
     * 产品状态 (上架、下架)
     */
    @TableField("status")
    private String status;

    /**
     * 产品封面图片URL
     */
    @TableField("cover_image")
    private String coverImage;

    /**
     * 产品详情图片URLs (JSON数组)
     */
    @TableField("detail_images")
    private String detailImages;

    /**
     * 排序权重
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 库存数量
     */
    @TableField("stock")
    private Integer stock;

    /**
     * 是否限制库存
     */
    @TableField("stock_limited")
    private Boolean stockLimited;

    /**
     * 销售数量
     */
    @TableField("sales_count")
    private Integer salesCount;

    /**
     * 产品标签 (JSON数组)
     */
    @TableField("tags")
    private String tags;

    /**
     * 产品详情内容
     */
    @TableField("content")
    private String content;
}
