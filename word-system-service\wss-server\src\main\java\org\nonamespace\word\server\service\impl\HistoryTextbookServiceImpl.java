package org.nonamespace.word.server.service.impl;

import java.util.List;

import org.nonamespace.word.server.service.base.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.nonamespace.word.server.mapper.HistoryTextbookMapper;
import org.nonamespace.word.server.domain.HistoryTextbook;
import org.nonamespace.word.server.service.IHistoryTextbookService;

/**
 * 词历史: 存储词的修改历史版本Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Service
public class HistoryTextbookServiceImpl extends BaseService<HistoryTextbookMapper,HistoryTextbook> implements IHistoryTextbookService
{
    @Autowired
    private HistoryTextbookMapper historyTextbookMapper;

    /**
     * 查询词历史: 存储词的修改历史版本
     * 
     * @param id 词历史: 存储词的修改历史版本主键
     * @return 词历史: 存储词的修改历史版本
     */
    @Override
    public HistoryTextbook selectHistoryTextbookById(String id)
    {
        return historyTextbookMapper.selectHistoryTextbookById(id);
    }

    /**
     * 查询词历史: 存储词的修改历史版本列表
     * 
     * @param historyTextbook 词历史: 存储词的修改历史版本
     * @return 词历史: 存储词的修改历史版本
     */
    @Override
    public List<HistoryTextbook> selectHistoryTextbookList(HistoryTextbook historyTextbook)
    {
        return historyTextbookMapper.selectHistoryTextbookList(historyTextbook);
    }

    /**
     * 新增词历史: 存储词的修改历史版本
     * 
     * @param historyTextbook 词历史: 存储词的修改历史版本
     * @return 结果
     */
    @Override
    public int insertHistoryTextbook(HistoryTextbook historyTextbook)
    {
        return historyTextbookMapper.insertHistoryTextbook(historyTextbook);
    }

    /**
     * 修改词历史: 存储词的修改历史版本
     * 
     * @param historyTextbook 词历史: 存储词的修改历史版本
     * @return 结果
     */
    @Override
    public int updateHistoryTextbook(HistoryTextbook historyTextbook)
    {
        return historyTextbookMapper.updateHistoryTextbook(historyTextbook);
    }

    /**
     * 批量删除词历史: 存储词的修改历史版本
     * 
     * @param ids 需要删除的词历史: 存储词的修改历史版本主键
     * @return 结果
     */
    @Override
    public int deleteHistoryTextbookByIds(String[] ids)
    {
        return historyTextbookMapper.deleteHistoryTextbookByIds(ids);
    }

    /**
     * 删除词历史: 存储词的修改历史版本信息
     * 
     * @param id 词历史: 存储词的修改历史版本主键
     * @return 结果
     */
    @Override
    public int deleteHistoryTextbookById(String id)
    {
        return historyTextbookMapper.deleteHistoryTextbookById(id);
    }
}
