package org.nonamespace.word.server.domain.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.*;
import org.nonamespace.word.server.domain.Product;
import org.nonamespace.word.server.entity.DataEntity;

import java.util.Date;

/**
 * 退款记录实体类
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("order_refunds")
public class OrderRefundRecord extends DataEntity {


    /**
     * 订单ID
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 退款订单号
     */
    @TableField("refund_no")
    private String refundNo;

    /**
     * 原交易流水ID
     */
    @TableField("original_trx_id")
    private String originalTrxId;

    /**
     * 退款交易流水ID
     */
    @TableField("refund_trx_id")
    private String refundTrxId;

    /**
     * 退款类型：partial-部分退款，full-全额退款
     */
    @TableField("refund_type")
    private String refundType;

    /**
     * 退款金额（分）
     */
    @TableField("refund_amount")
    private Long refundAmount;

    /**
     * 退款原因
     */
    @TableField("refund_reason")
    private String refundReason;

    /**
     * 退款状态：processing-处理中，success-成功，failed-失败
     */
    @TableField("refund_status")
    private String refundStatus;

    /**
     * 学生ID
     */
    @TableField("student_id")
    private String studentId;

    /**
     * 销售员ID
     */
    @TableField("saler_id")
    private String salerId;

    /**
     * 产品ID
     */
    @TableField("product_id")
    private String productId;

    /**
     * 原产品信息
     */
    @TableField(value = "products", typeHandler = JacksonTypeHandler.class)
    private Product products;

    /**
     * 原订单信息
     */
    @TableField(value = "orders", typeHandler = JacksonTypeHandler.class)
    private Orders orders;

    /**
     * 原交易流水信息
     */
    @TableField(value = "orders_trxs", typeHandler = JacksonTypeHandler.class)
    private OrdersTrx ordersTrxs;

    /**
     * 退款方式：original-原路退回，manual-手动退款
     */
    @TableField("refund_method")
    private String refundMethod;

    /**
     * 支付平台退款ID
     */
    @TableField("platform_refund_id")
    private String platformRefundId;

    /**
     * 支付平台响应信息
     */
    @TableField("platform_response")
    private String platformResponse;

    /**
     * 退款完成时间
     */
    @TableField("refund_time")
    private Date refundTime;

    /**
     * 错误信息（退款失败时）
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}
