package org.nonamespace.word.server.domain.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.nonamespace.word.server.entity.DataEntity;

import java.util.Date;

/**
 * 退款记录实体类
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("order_refund_records")
public class OrderRefundRecord extends DataEntity {


    /**
     * 订单ID
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 原交易流水ID
     */
    @TableField("original_trx_id")
    private String originalTrxId;

    /**
     * 退款交易流水ID
     */
    @TableField("refund_trx_id")
    private String refundTrxId;

    /**
     * 退款类型：partial-部分退款，full-全额退款
     */
    @TableField("refund_type")
    private String refundType;

    /**
     * 退款金额（分）
     */
    @TableField("refund_amount")
    private Long refundAmount;

    /**
     * 退款原因
     */
    @TableField("refund_reason")
    private String refundReason;

    /**
     * 退款状态：processing-处理中，success-成功，failed-失败
     */
    @TableField("refund_status")
    private String refundStatus;

    /**
     * 学生ID
     */
    @TableField("student_id")
    private String studentId;

    /**
     * 学生姓名
     */
    @TableField("student_name")
    private String studentName;

    /**
     * 学生手机号
     */
    @TableField("student_phone")
    private String studentPhone;

    /**
     * 销售员ID
     */
    @TableField("saler_id")
    private String salerId;

    /**
     * 销售员姓名
     */
    @TableField("saler_name")
    private String salerName;

    /**
     * 产品ID
     */
    @TableField("product_id")
    private String productId;

    /**
     * 产品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 学科
     */
    @TableField("subject")
    private String subject;

    /**
     * 课型
     */
    @TableField("course_type")
    private String courseType;

    /**
     * 退款方式：original-原路退回，manual-手动退款
     */
    @TableField("refund_method")
    private String refundMethod;

    /**
     * 支付平台退款ID
     */
    @TableField("platform_refund_id")
    private String platformRefundId;

    /**
     * 支付平台响应信息
     */
    @TableField("platform_response")
    private String platformResponse;

    /**
     * 操作人ID
     */
    @TableField("operator_id")
    private String operatorId;

    /**
     * 操作人姓名
     */
    @TableField("operator_name")
    private String operatorName;

    /**
     * 操作人角色
     */
    @TableField("operator_role")
    private String operatorRole;

    /**
     * 审批状态：pending-待审批，approved-已审批，rejected-已拒绝，auto_approved-自动审批
     */
    @TableField("approval_status")
    private String approvalStatus;

    /**
     * 审批人ID
     */
    @TableField("approver_id")
    private String approverId;

    /**
     * 审批人姓名
     */
    @TableField("approver_name")
    private String approverName;

    /**
     * 审批时间
     */
    @TableField("approval_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date approvalTime;

    /**
     * 审批备注
     */
    @TableField("approval_remark")
    private String approvalRemark;

    /**
     * 退款完成时间
     */
    @TableField("refund_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date refundTime;

    /**
     * 错误信息（退款失败时）
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 退款类型常量
     */
    public static class RefundType {
        public static final String PARTIAL = "partial";
        public static final String FULL = "full";
    }

    /**
     * 退款状态常量
     */
    public static class RefundStatus {
        public static final String PROCESSING = "processing";
        public static final String SUCCESS = "success";
        public static final String FAILED = "failed";
    }

    /**
     * 审批状态常量
     */
    public static class ApprovalStatus {
        public static final String PENDING = "pending";
        public static final String APPROVED = "approved";
        public static final String REJECTED = "rejected";
        public static final String AUTO_APPROVED = "auto_approved";
    }

    /**
     * 退款方式常量
     */
    public static class RefundMethod {
        public static final String ORIGINAL = "original";
        public static final String MANUAL = "manual";
    }
}
