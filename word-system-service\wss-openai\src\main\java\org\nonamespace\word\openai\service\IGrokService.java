package org.nonamespace.word.openai.service;

import org.nonamespace.word.openai.model.WordInfo;

import java.util.List;

/**
 * grok api
 *
 * <AUTHOR>
 * @date 2025/5/20 9:25
 */
public interface IGrokService {

    List<WordInfo> generateWordMeanings(List<String> words);

    List<WordInfo> enrich(List<WordInfo> words);
    List<WordInfo> enrichBasic(List<WordInfo> words);
    List<WordInfo> enrichMeanings(List<WordInfo> words);
    List<WordInfo> enrichSentences(List<WordInfo> words);
}
