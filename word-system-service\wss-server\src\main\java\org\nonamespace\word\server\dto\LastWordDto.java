package org.nonamespace.word.server.dto;

import lombok.Data;
import lombok.experimental.Accessors;
import org.nonamespace.word.server.vo.TextbookTreeVo;

import java.util.List;

/**
 * <AUTHOR> Created on 2025/05/27 20:03
 */
@Data
@Accessors(chain = true)
public class LastWordDto {
    private String textbookId;
    private String unitItemId;
    private String wordItemId;
    private String word;

    private List<TextbookTreeVo> textbookTreeVos;
}
