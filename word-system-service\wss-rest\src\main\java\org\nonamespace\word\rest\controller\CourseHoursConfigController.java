package org.nonamespace.word.rest.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.util.CourseHoursConfigUtil;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 课消配置管理控制器
 *
 * 用于管理统一的课消功能开关（包含课时余额检查和课消）
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Slf4j
@RestController
@RequestMapping("/api/course-hours-config")
@RequiredArgsConstructor
public class CourseHoursConfigController {

    private final CourseHoursConfigUtil courseHoursConfigUtil;
    private final StringRedisTemplate stringRedisTemplate;

    /**
     * 获取当前配置状态
     */
    @GetMapping("/status")
    public AjaxResult getStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            boolean enabled = courseHoursConfigUtil.isCourseHoursEnabled();
            status.put("courseHoursEnabled", enabled);
            // 兼容旧的字段名
            status.put("hoursBalanceCheckEnabled", enabled);
            status.put("courseHoursConsumptionEnabled", enabled);

            log.info("获取课消配置状态: {}", status);
            return AjaxResult.success("获取配置状态成功", status);
        } catch (Exception e) {
            log.error("获取课消配置状态失败", e);
            return AjaxResult.error("获取配置状态失败: " + e.getMessage());
        }
    }

    /**
     * 设置课消功能开关
     */
    @PostMapping("/enabled/{enabled}")
    public AjaxResult setCourseHoursEnabled(@PathVariable boolean enabled) {
        try {
            stringRedisTemplate.opsForValue().set("course:hours:enabled", String.valueOf(enabled));

            log.info("设置课消功能开关: {}", enabled);
            return AjaxResult.success("设置课消功能开关成功");
        } catch (Exception e) {
            log.error("设置课消功能开关失败: enabled={}", enabled, e);
            return AjaxResult.error("设置开关失败: " + e.getMessage());
        }
    }

    /**
     * 重置为默认值（关闭）
     */
    @PostMapping("/reset")
    public AjaxResult reset() {
        try {
            stringRedisTemplate.opsForValue().set("course:hours:enabled", "false");

            log.info("重置课消配置为默认值（关闭）");
            return AjaxResult.success("重置配置成功");
        } catch (Exception e) {
            log.error("重置课消配置失败", e);
            return AjaxResult.error("重置配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取Redis键值信息（调试用）
     */
    @GetMapping("/debug/redis-keys")
    public AjaxResult getRedisKeys() {
        try {
            Map<String, String> keys = new HashMap<>();
            keys.put("course:hours:enabled",
                stringRedisTemplate.opsForValue().get("course:hours:enabled"));

            return AjaxResult.success("获取Redis键值成功", keys);
        } catch (Exception e) {
            log.error("获取Redis键值失败", e);
            return AjaxResult.error("获取Redis键值失败: " + e.getMessage());
        }
    }
}
