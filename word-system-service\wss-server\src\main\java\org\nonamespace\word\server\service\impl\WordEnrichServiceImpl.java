package org.nonamespace.word.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.utils.SentenceSplitter;
import org.nonamespace.word.common.utils.WordEnrichThreadPoolUtil;
import org.nonamespace.word.openai.model.WordInfo;
import org.nonamespace.word.openai.model.enums.ModelEnum;
import org.nonamespace.word.openai.service.IGrokService;
import org.nonamespace.word.openai.service.INebiusService;
import org.nonamespace.word.openai.service.ISiliconflowService;
import org.nonamespace.word.server.domain.Word;
import org.nonamespace.word.server.domain.WordEnrichErrors;
import org.nonamespace.word.server.dto.WordEditDto;
import org.nonamespace.word.server.service.IWordEnrichErrorService;
import org.nonamespace.word.server.service.IWordEnrichService;
import org.nonamespace.word.server.service.IWordService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Supplier;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
public class WordEnrichServiceImpl implements IWordEnrichService {

    private final IWordService wordService;
    private final StringRedisTemplate stringRedisTemplate;
    private final ISiliconflowService siliconflowService;
    private final INebiusService nebiusService;
    private final IGrokService grokService;

    private final IWordEnrichErrorService wordEnrichErrorService;

    @Value("${word.enrich.words-model}")
    private String enrichWordsModel;

    private static final String UNRICH_BASIC_REDIS_KEY_PREFIX = "word:lock:enrich:basic:";
    private static final String UNRICH_MEANINGS_REDIS_KEY_PREFIX = "word:lock:enrich:meanings:";
    private static final String UNRICH_SENTENCES_REDIS_KEY_PREFIX = "word:lock:enrich:sentences:";


    /**
     * 只用于补全音节，音标
     * @param words
     * @param ifNullEnrichOther
     */
    @Override
    public void enrichBasic(Set<String> words, boolean ifNullEnrichOther) {

        List<Word> wordList = queryUnRichWords(words, ifNullEnrichOther, wordService::listUnRichBasic);

        if(CollUtil.isEmpty(wordList)) {
            log.warn("[单词 音节 补全] 没有需要补全的单词.");
            return ;
        }
        CountDownLatch countDownLatch = new CountDownLatch(wordList.size());
        for (Word word : wordList) {
            WordEnrichThreadPoolUtil.executeTask(() -> {
                long start0 = System.currentTimeMillis();
                try {
                    log.info("[单词 音节 补全] 正在补全单词...{}", word.getWord());
                    // redis 判断该单词是否补全失败过
                    String redisStatus = stringRedisTemplate.opsForValue().get(UNRICH_BASIC_REDIS_KEY_PREFIX + word.getWord());
                    if(redisStatus != null) {
                        log.warn("[单词 音节 补全] {} 补全失败过，直接跳过。 失败原因：{}", word.getWord(), redisStatus);
                        return ;
                    }
                    WordInfo wordInfo = new WordInfo();
                    wordInfo.setWord(word.getWord());
                    wordInfo.setSyllables(word.getSyllables());
                    wordInfo.setPhoneticUk(word.getPhoneticUk());
                    wordInfo.setPhoneticUs(word.getPhoneticUs());

                    WordInfo enrichWordInfo;
                    if (StrUtil.isNotBlank(enrichWordsModel) && enrichWordsModel.equalsIgnoreCase(ModelEnum.SILICONFLOW.name())) {
                        log.info("[单词 音节 补全] 使用硅基流动");
                        // 使用硅基流动
                        List<WordInfo> enrichWordInfos = siliconflowService.enrichBasic(List.of(wordInfo));
                        enrichWordInfo = CollUtil.isNotEmpty(enrichWordInfos) ? enrichWordInfos.getFirst() : null;
                    } else if (StrUtil.isNotBlank(enrichWordsModel) && enrichWordsModel.equalsIgnoreCase(ModelEnum.NEBUIS.name())) {
                        log.info("[单词 音节 补全] 使用nebuis api");
                        // 使用grok api
                        List<WordInfo> enrichWordInfos = nebiusService.enrichBasic(List.of(wordInfo));
                        enrichWordInfo = CollUtil.isNotEmpty(enrichWordInfos) ? enrichWordInfos.getFirst() : null;
                    } else {
                        log.info("[单词 音节 补全] 使用grok api");
                        // 使用grok api
                        List<WordInfo> enrichWordInfos = grokService.enrichBasic(List.of(wordInfo));
                        enrichWordInfo = CollUtil.isNotEmpty(enrichWordInfos) ? enrichWordInfos.getFirst() : null;
                    }

                    // 补全数据
                    if (enrichWordInfo == null) {
                        log.info("[单词 音节 补全] 补全数据为空。单词={}", word.getWord());
                        stringRedisTemplate.opsForValue().set(UNRICH_BASIC_REDIS_KEY_PREFIX + word.getWord(), "补全数据为空", 5, TimeUnit.SECONDS);
                        return;
                    }
                    // 组装WordEditDto.Req
                    WordEditDto.Req req = new WordEditDto.Req();
                    req.setSyllables(enrichWordInfo.getSyllables());
                    req.setPhoneticUk(enrichWordInfo.getPhoneticUk());
                    req.setPhoneticUs(enrichWordInfo.getPhoneticUs());
                    req.setId(word.getId());
                    wordService.updateWord(req, word);
                    log.info("[单词 音节 补全] {} 补全完成", word.getWord());
                } catch (Exception e) {
                    log.error("[单词 音节 补全] {} 补全单词出现异常:", word.getWord(), e);
                    stringRedisTemplate.opsForValue().set(UNRICH_BASIC_REDIS_KEY_PREFIX + word.getWord(), "补全单词出现异常:" + e.getMessage(), 5, TimeUnit.SECONDS);

                    long end0 = System.currentTimeMillis();
                    // 写入数据库
                    wordEnrichErrorService.save(WordEnrichErrors.builder()
                            .word(word.getWord()).enrichStage("[enrichBasic] 单词音节补全")
                            .response(e.getMessage())
                            .createTime(DateUtil.date()).millTimes(end0 - start0).build());
                } finally {
                    countDownLatch.countDown();
                }
            });
        }

        try {
            countDownLatch.await();
            log.info("[单词 音节 补全] 单词补全任务已完成，共补全{}条", wordList.size());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    public void enrichMeanings(Set<String> words, boolean ifNullEnrichOther) {

        List<Word> wordList = queryUnRichWords(words, ifNullEnrichOther, wordService::listUnRichMeanings);

        if(CollUtil.isEmpty(wordList)) {
            log.warn("[单词 释义 补全] 没有需要补全的单词.");
            return ;
        }
        CountDownLatch countDownLatch = new CountDownLatch(wordList.size());
        for (Word word : wordList) {
            WordEnrichThreadPoolUtil.executeTask(() -> {
                long start0 = System.currentTimeMillis();
                WordEditDto.Req req = new WordEditDto.Req();
                req.setId(word.getId());
                req.setMeanings(word.getMeanings());
                try {
                    log.info("[单词 释义 补全] 正在补全单词...{}", word.getWord());
                    // redis 判断该单词是否补全失败过
                    String redisStatus = stringRedisTemplate.opsForValue().get(UNRICH_MEANINGS_REDIS_KEY_PREFIX + word.getWord());
                    if(redisStatus != null) {
                        log.warn("[单词 释义 补全] {} 补全失败过，直接跳过。 失败原因：{}", word.getWord(), redisStatus);
                        return ;
                    }
                    WordInfo wordInfo = new WordInfo();
                    wordInfo.setWord(word.getWord());
                    // 如果为空，全套服务
                    if(req.getMeanings() == null || req.getMeanings().isEmpty()) {
                        Map<String, Word.Meanings> meaningsMap = new HashMap<>();
                        meaningsMap.put("特色", new Word.Meanings());
                        meaningsMap.put("通用", new Word.Meanings());
                        req.setMeanings(meaningsMap);
                    }


                    // 将单词释义内容，按照特色/通用补全，一个一个补
                    req.getMeanings().forEach((k, v) -> {
                        if(!checkWordMeaningsIsUnRich(v)) {  // 不需要补全
                            return ;
                        }
                        Map<String, WordInfo.Meanings> meanings = new HashMap<>();
                        meanings.put(k, BeanUtil.copyProperties(v, WordInfo.Meanings.class));
                        wordInfo.setMeanings(meanings);
                        WordInfo enrichWordInfo = null;
                        if (StrUtil.isNotBlank(enrichWordsModel) && enrichWordsModel.equalsIgnoreCase("siliconflow")) {
                            log.info("[单词 释义 补全] 使用硅基流动");
                            // 使用硅基流动
                            List<WordInfo> enrichWordInfos = siliconflowService.enrichMeanings(List.of(wordInfo));
                            enrichWordInfo = CollUtil.isNotEmpty(enrichWordInfos) ? enrichWordInfos.getFirst() : null;
                        } else if (StrUtil.isNotBlank(enrichWordsModel) && enrichWordsModel.equalsIgnoreCase("nebuis")) {
                            log.info("[单词 释义 补全] 使用nebuis api");
                            // 使用grok api
                            List<WordInfo> enrichWordInfos = nebiusService.enrichMeanings(List.of(wordInfo));
                            enrichWordInfo = CollUtil.isNotEmpty(enrichWordInfos) ? enrichWordInfos.getFirst() : null;
                        } else {
                            log.info("[单词 释义 补全] 使用grok api");
                            // 使用grok api
                            List<WordInfo> enrichWordInfos = grokService.enrichMeanings(List.of(wordInfo));
                            enrichWordInfo = CollUtil.isNotEmpty(enrichWordInfos) ? enrichWordInfos.getFirst() : null;
                        }

                        // 补全数据
                        if (enrichWordInfo == null) {
                            log.info("[单词 释义 补全] 补全数据为空。单词={}", word.getWord());
                            stringRedisTemplate.opsForValue().set(UNRICH_MEANINGS_REDIS_KEY_PREFIX + word.getWord(), "补全数据为空", 5, TimeUnit.SECONDS);
                            return;
                        }
                        Collection<WordInfo.Meanings> values = enrichWordInfo.getMeanings().values();
                        if(CollUtil.isNotEmpty(values) && !values.isEmpty()) {
                            WordInfo.Meanings meanings1 = values.stream().findFirst().get();
                            v.setPos(BeanUtil.copyToList(meanings1.getPos(), Word.Meanings.Pos.class));
                            v.setPractices(meanings1.getPractices());
                        }
                    });

                    // 组装WordEditDto.Req
//                    WordEditDto.Req req = BeanUtil.copyProperties(word, WordEditDto.Req.class);
//                    req.setId(word.getId());
                    req.setSentences(null);  // 保险起见。这里将例句置空，否则会被更新
                    wordService.updateWord(req, word);
                    log.info("[单词 释义 补全] {} 补全完成", word.getWord());
                } catch (Exception ignored) {
                    log.error("[单词 释义 补全] {} 补全单词出现异常:", word.getWord(), ignored);
                    stringRedisTemplate.opsForValue().set(UNRICH_MEANINGS_REDIS_KEY_PREFIX + word.getWord(), "补全单词出现异常:" + ignored.getMessage(), 5, TimeUnit.SECONDS);

                    long end0 = System.currentTimeMillis();
                    // 写入数据库
                    wordEnrichErrorService.save(WordEnrichErrors.builder()
                            .word(word.getWord()).enrichStage("[enrichMeanings] 单词释义补全")
                            .response(ignored.getMessage()).createTime(DateUtil.date()).millTimes(end0 - start0)
                            .build());
                } finally {
                    countDownLatch.countDown();
                }
            });
        }

        try {
            countDownLatch.await();
            log.info("[单词 释义 补全] 单词补全任务已完成，共补全{}条", wordList.size());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    public void enrichSentences(Set<String> words, boolean ifNullEnrichOther) {

        List<Word> wordList = queryUnRichWords(words, ifNullEnrichOther, wordService::listUnRichSentences);
        if(CollUtil.isEmpty(wordList)) {
            log.warn("[单词 例句 补全] 没有需要补全的单词.");
            return ;
        }
        CountDownLatch countDownLatch = new CountDownLatch(wordList.size());
        for (Word word : wordList) {
            WordEnrichThreadPoolUtil.executeTask(() -> {
                long start0 = System.currentTimeMillis();
                WordEditDto.Req req = new WordEditDto.Req();
                req.setId(word.getId());
                req.setSentences(word.getSentences());
                try {
                    log.info("[单词 例句 补全] 正在补全单词...{}", word.getWord());
                    // redis 判断该单词是否补全失败过
                    String redisStatus = stringRedisTemplate.opsForValue().get(UNRICH_SENTENCES_REDIS_KEY_PREFIX + word.getWord());
                    if(redisStatus != null) {
                        log.warn("[单词 例句 补全] {} 补全失败过，直接跳过。 失败原因：{}", word.getWord(), redisStatus);
                        return ;
                    }

                    // 如果为空，全套服务
                    if(req.getSentences() == null || req.getSentences().isEmpty()) {
                        Map<String, List<Word.Sentences>> sentencesMap = new HashMap<>();
                        List<Word.Sentences> l = new ArrayList<>();
                        // 为空的话，把小初高的stage都补上
                        l.add(new Word.Sentences("小学"));
                        l.add(new Word.Sentences("初中"));
                        l.add(new Word.Sentences("高中"));
                        sentencesMap.put("特色", l);
                        sentencesMap.put("通用", l);
                        req.setSentences(sentencesMap);
                    }

                    WordInfo wordInfo = new WordInfo();
                    wordInfo.setWord(word.getWord());
                    req.getSentences().forEach((k, v) -> {
                        if(CollUtil.isEmpty(v)) {
                            // 为空的话，把小初高的stage都补上
                            v.add(new Word.Sentences("小学"));
                            v.add(new Word.Sentences("初中"));
                            v.add(new Word.Sentences("高中"));
                        }

                        // 缺哪个补哪个，先根据stage分组下
                        Map<String, List<Word.Sentences>> collected = v.stream().collect(Collectors.groupingBy(Word.Sentences::getStage));
                        if(CollUtil.isEmpty(collected.get("小学"))) {
                            v.add(new Word.Sentences("小学"));
                        }
                        if(CollUtil.isEmpty(collected.get("初中"))) {
                            v.add(new Word.Sentences("初中"));
                        }
                        if(CollUtil.isEmpty(collected.get("高中"))) {
                            v.add(new Word.Sentences("高中"));
                        }

                        // 分别根据stage获取数据
                        v.forEach(sentence -> {
                            if(!checkWordSentencesIsUnRich(sentence)) { // 不需要补全该例句
                                return ;
                            }
                            Map<String, List<WordInfo.Sentence>> senMap = new HashMap<>();
                            senMap.put(k, List.of(BeanUtil.copyProperties(sentence, WordInfo.Sentence.class)));
                            wordInfo.setSentences(senMap);
                            WordInfo enrichWordInfo;
                            if (StrUtil.isNotBlank(enrichWordsModel) && enrichWordsModel.equalsIgnoreCase(ModelEnum.SILICONFLOW.name())) {
                                log.info("[单词 例句 补全] 使用硅基流动");
                                // 使用硅基流动
                                List<WordInfo> enrichWordInfos = siliconflowService.enrichSentences(List.of(wordInfo));
                                enrichWordInfo = CollUtil.isNotEmpty(enrichWordInfos) ? enrichWordInfos.getFirst() : null;
                            } else if (StrUtil.isNotBlank(enrichWordsModel) && enrichWordsModel.equalsIgnoreCase(ModelEnum.NEBUIS.name())) {
                                log.info("[单词 例句 补全] 使用nebuis api");
                                // 使用grok api
                                List<WordInfo> enrichWordInfos = nebiusService.enrichSentences(List.of(wordInfo));
                                enrichWordInfo = CollUtil.isNotEmpty(enrichWordInfos) ? enrichWordInfos.getFirst() : null;
                            } else {
                                log.info("[单词 例句 补全] 使用grok api");
                                // 使用grok api
                                List<WordInfo> enrichWordInfos = grokService.enrichSentences(List.of(wordInfo));
                                enrichWordInfo = CollUtil.isNotEmpty(enrichWordInfos) ? enrichWordInfos.getFirst() : null;
                            }

                            // 补全数据
                            if (enrichWordInfo == null) {
                                log.info("[单词 例句 补全] 补全数据为空。单词={}", word.getWord());
                                stringRedisTemplate.opsForValue().set(UNRICH_SENTENCES_REDIS_KEY_PREFIX + word.getWord(), "补全数据为空", 5, TimeUnit.SECONDS);
                                return;
                            }

                            Collection<List<WordInfo.Sentence>> values = enrichWordInfo.getSentences().values();
                            if(CollUtil.isNotEmpty(values) && !values.isEmpty()) {
                                List<WordInfo.Sentence> enrichSentence = values.stream().findFirst().get();
                                if(StrUtil.isBlankIfStr(sentence.getSentenceEn())) {
                                    sentence.setSentenceEn(enrichSentence.getFirst().getSentenceEn());
                                }
                                if(StrUtil.isBlankIfStr(sentence.getSentenceCn())) {
                                    sentence.setSentenceCn(enrichSentence.getFirst().getSentenceCn());
                                }
                                if(StrUtil.isBlankIfStr(sentence.getSyllables())) {
                                    sentence.setSyllables(enrichSentence.getFirst().getSyllables());
                                }
                                if(CollUtil.isEmpty(sentence.getPractices())) {
                                    sentence.setPractices(enrichSentence.getFirst().getPractices());
                                }
                                if(CollUtil.isEmpty(sentence.getStructurePartsEn())) {
                                    sentence.setStructurePartsEn(SentenceSplitter.insertPipesCount(sentence.getSentenceEn(), 5, 3, "|"));
                                }
                            }
                        });
                    });
                    // 组装WordEditDto.Req
//                    WordEditDto.Req req = BeanUtil.copyProperties(word, WordEditDto.Req.class);
                    req.setId(word.getId());
                    req.setMeanings(null);  // 保险起见。这里将释义置空，否则会被更新
                    wordService.updateWord(req, word);
                    log.info("[单词 例句 补全] {} 补全完成", word.getWord());
                } catch (Exception ignored) {
                    log.error("[单词 例句 补全] {} 补全单词出现异常:", word.getWord(), ignored);
                    stringRedisTemplate.opsForValue().set(UNRICH_SENTENCES_REDIS_KEY_PREFIX + word.getWord(), "补全单词出现异常:" + ignored.getMessage(), 5, TimeUnit.SECONDS);
                    long end0 = System.currentTimeMillis();
                    // 写入数据库
                    wordEnrichErrorService.save(WordEnrichErrors.builder().word(word.getWord())
                            .enrichStage("[enrichSentences] 单词例句补全").response(ignored.getMessage()).createTime(DateUtil.date()).millTimes(end0 - start0).build());
                } finally {
                    countDownLatch.countDown();
                }
            });
        }

        try {
            countDownLatch.await();
            log.info("[单词 例句 补全] 单词补全任务已完成，共补全{}条", wordList.size());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取需要补全单词
     * @param words
     * @param ifNullEnrichOther
     * @return
     */
    private List<Word> queryUnRichWords(Set<String> words, boolean ifNullEnrichOther, Supplier<List<Word>> supplier) {

        if(CollUtil.isEmpty(words) && !ifNullEnrichOther) {
            return null;
        }
        LambdaQueryWrapper<Word> queryWrapper = new LambdaQueryWrapper<Word>().eq(Word::getDeleted, false);
        List<Word> wordList = List.of();
        // 优先补齐传进来的单词
        if(CollUtil.isNotEmpty(words)){
            queryWrapper.in(Word::getWord, words);
            wordList = wordService.list(queryWrapper);
        } else if(ifNullEnrichOther) {
            wordList = supplier.get();
        }

        return wordList;
    }


    /**
     * 判断当前meaning是否需要补全
     * @param meaning
     * @return
     */
    private boolean checkWordMeaningsIsUnRich(Word.Meanings meaning) {
        if(meaning == null || CollUtil.isEmpty(meaning.getPractices()) || CollUtil.isEmpty(meaning.getPos())) {
            return true;
        }

        AtomicBoolean isUnRich = new AtomicBoolean(false);
        for(Word.Meanings.Pos pos : meaning.getPos()) {
            if(StrUtil.isBlankIfStr(pos.getDef()) || StrUtil.isBlankIfStr(pos.getPos())) {
                isUnRich.set(true);
                break;
            }
        }

        return isUnRich.get();
    }

    private boolean checkWordSentencesIsUnRich(Word.Sentences sentences) {
        return sentences == null || StrUtil.isBlankIfStr(sentences.getSyllables())
                || StrUtil.isBlankIfStr(sentences.getSentenceEn()) || StrUtil.isBlankIfStr(sentences.getSentenceCn())
                || CollUtil.isEmpty(sentences.getPractices())
                || sentences.getPractices().size() < 3;
    }
}
