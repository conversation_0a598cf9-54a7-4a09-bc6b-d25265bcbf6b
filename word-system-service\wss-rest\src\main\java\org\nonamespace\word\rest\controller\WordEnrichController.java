package org.nonamespace.word.rest.controller;


import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.AjaxResult;
import lombok.RequiredArgsConstructor;
import org.nonamespace.word.common.utils.SentenceSplitter;
import org.nonamespace.word.common.utils.WordEnrichThreadPoolUtil;
import org.nonamespace.word.openai.model.enums.ModelEnum;
import org.nonamespace.word.server.domain.Word;
import org.nonamespace.word.server.service.IWordEnrichService;
import org.nonamespace.word.server.service.IWordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CountDownLatch;

/**
 * 用于单词补全
 */
@RestController
@RequestMapping("/word/enrich")
@Anonymous
@RequiredArgsConstructor
public class WordEnrichController {

    private final IWordService wordService;
    private final IWordEnrichService wordEnrichService;


    /**
     * 单词补全
     * @param words
     * @return
     */
    @PostMapping("/enrich")
    public AjaxResult enrich(@RequestBody Set<String> words){
        wordService.enrich(words, true);
        return AjaxResult.success();
    }


    @PostMapping("/enrichAll")
    public AjaxResult enrichAll(@RequestBody Set<String> words){
        for (int i = 0; i < 5; i++) {
            wordEnrichService.enrichBasic(Set.of(), true);
            wordEnrichService.enrichMeanings(Set.of(), true);
            wordEnrichService.enrichSentences(Set.of(), true);
            wordService.enrichSentencesAudio(Set.of(), true);
            wordService.fetchVoices(Set.of(), 6, ModelEnum.CLAWCLOUD);
        }
        return AjaxResult.success();
    }


    @PostMapping("/enrichBasic")
    public AjaxResult enrichBasic(@RequestBody Set<String> words){
        wordEnrichService.enrichBasic(words, true);
        return AjaxResult.success();
    }

    @PostMapping("/enrichMeanings")
    public AjaxResult enrichMeanings(@RequestBody Set<String> words){
        wordEnrichService.enrichMeanings(words, true);
        return AjaxResult.success();
    }

    @PostMapping("/enrichSentences")
    public AjaxResult enrichSentences(@RequestBody Set<String> words){
        wordEnrichService.enrichSentences(words, true);
        return AjaxResult.success();
    }


    /**
     * 抓取单词发音音频文件
     * @return
     */
    @PostMapping("fetchVoices")
    public AjaxResult fetchVoices(@RequestBody Set<String> words) {
//        wordService.fetchVoices(words, 1, ModelEnum.YOUDAO);
//        wordService.fetchVoices(words, 1, ModelEnum.VOLCENGINE);
        wordService.fetchVoices(words, 1, ModelEnum.CLAWCLOUD);
        return AjaxResult.success();
    }


    /**
     * 补全句子音频
     * @param words
     * @return
     */
    @PostMapping("enrichSentencesAudio")
    public AjaxResult enrichSentencesAudio(@RequestBody Set<String> words) {
        wordService.enrichSentencesAudio(words, true);
        return AjaxResult.success();
    }

    /**
     * 生成单词释义
     * @return
     */
    @PostMapping("generateWordMeanings")
    public AjaxResult generateWordMeanings(@RequestBody Set<String> words) {
        // 先连续循环3-4此，然后刷例句音频
        wordService.generateWordMeanings(words);
//        wordService.fetchVoices(words, 1, ModelEnum.CLAWCLOUD);
        return AjaxResult.success();
    }


    /**
     * 用于批量刷新例句的分割选项
     */
    @GetMapping("enrichErrorStructurePartsEn")
    public void enrichErrorStructurePartsEn() {
        List<Word> listed = wordService.list();
        List<String> wordIds = new ArrayList<>();
        CountDownLatch countDownLatch = new CountDownLatch(listed.size());
        for (int i = 0; i < listed.size(); i++) {
            int finalI = i;
            WordEnrichThreadPoolUtil.executeTask(() -> {
                try {
                    Word word = listed.get(finalI);
                    word.getSentences().forEach((key, sentence) -> {
                        sentence.forEach(sen -> sen.setStructurePartsEn(SentenceSplitter.insertPipesCount(sen.getSentenceEn(), 5, 3, "|")));
                    });
                    word.setUpdateTime(DateUtil.date());
                    wordService.updateById(word);
                } finally {
                    countDownLatch.countDown();
                }
            });
//            List<Word.Sentences> tese = word.getSentences().get("特殊");
//            if(CollUtil.isNotEmpty(tese)){
//                for (int j = 0; j < tese.size(); j++) {
////                    List<String> structurePartsEn = tese.get(j).getStructurePartsEn();
////                    // 判断structurePartsEn里面的值的长度是否一致
////                    int firstLength = structurePartsEn.get(0).replaceAll(" ", "").length();
////                    for (int k = 1; k < structurePartsEn.size(); k++) {
////                        if (structurePartsEn.get(k).replaceAll(" ", "").length() != firstLength) {
////                            wordIds.add(word.getId());
////                            break;
////                        }
////                    }
//
//                    String newEn = tese.get(j).getSentenceEn().replaceAll("[\\s|]", "");
//                    List<String> structurePartsEn = tese.get(j).getStructurePartsEn();
//                    for (int k = 1; k < structurePartsEn.size(); k++) {
//                        if (structurePartsEn.get(k).replaceAll("[\\s|]", "").length() != newEn.length()) {
//                            wordIds.add(word.getId());
//                            break;
//                        }
//                    }
//                }
//            }
        }

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        System.out.println("更新完成");

//        System.out.println("================= 错误的单词数量：" + wordIds.stream().distinct().count());
//        System.out.println("================= 错误的单词：" + wordIds.stream().distinct().toList().toString());


//        wordIds.forEach(id-> {
//            Word word = wordService.getById("1925028515384705028");
//            word.getSentences().forEach((k, sentences) -> {
//                sentences.forEach(sen -> sen.setStructurePartsEn(SentenceInsertPipesUtils.insertPipesCount(sen.getSentenceEn(), 3, 2, '|')));
//            });
//            wordService.updateById(word);
//            System.out.println("---更新成功："+ id);
//        });
    }


    /**
     * 补全例句中的混淆项
     */
    @PostMapping("enrichSentencesPractices")
    public AjaxResult enrichSentencesPractices(@RequestBody Set<String> words) {
        wordService.enrichSentencesPractices(words);
        return AjaxResult.success();
    }


    /**
     * 补全例句句子添加句号
     * @param words
     * @return
     */
    @PostMapping("enrichSentencesPeriod")
    public AjaxResult enrichSentencesPeriod(@RequestBody Set<String> words) {
        wordService.enrichSentencesPeriod(words);
        return AjaxResult.success();
    }

}
