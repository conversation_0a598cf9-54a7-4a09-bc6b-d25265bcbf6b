package org.nonamespace.word.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.nonamespace.word.server.domain.TeachingGroup;
import org.nonamespace.word.server.dto.management.teachinggroup.TeachingGroupDto;

import java.util.List;

/**
 * 教学组Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Mapper
public interface TeachingGroupMapper extends BaseMapper<TeachingGroup> {

    /**
     * 分页查询教学组列表（包含组长和教务姓名）
     * 
     * @param page 分页参数
     * @param req 查询条件
     * @return 教学组列表
     */
    IPage<TeachingGroupDto.Resp> selectTeachingGroupPage(Page<TeachingGroupDto.Resp> page, @Param("req") TeachingGroupDto.GetListReq req);

    /**
     * 根据ID查询教学组详情（包含组长和教务姓名）
     * 
     * @param id 教学组ID
     * @return 教学组详情
     */
    TeachingGroupDto.Resp selectTeachingGroupById(@Param("id") String id);

    /**
     * 获取教学组统计信息
     * 
     * @return 统计信息
     */
    TeachingGroupDto.StatsResp selectTeachingGroupStats();

    /**
     * 更新教学组成员数量
     * 
     * @param groupId 教学组ID
     */
    void updateMemberCount(@Param("groupId") String groupId);

    /**
     * 批量删除教学组
     * 
     * @param ids 教学组ID列表
     * @return 影响行数
     */
    int deleteBatchByIds(@Param("ids") List<String> ids);
}
