package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nonamespace.word.server.entity.DataEntity;
import org.nonamespace.word.server.misc.mybatis.ListStringTypeHandler;

import java.util.Date;
import java.util.List;

/**
 * 抗遗忘复习计划对象 review_schedule
 * 
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "review_schedule", autoResultMap = true)
public class ReviewSchedule extends DataEntity {


    /** 学生ID */
    @Excel(name = "学生ID")
    private String studentId;

    /** 关联的原始学习课程ID (用于追溯) */
    @Excel(name = "关联的原始学习课程ID (用于追溯)")
    private String courseId;

    /** 复习类型 (如 D2, D4, D7, D14, D21) */
    @Excel(name = "复习类型 (如 D2, D4, D7, D14, D21)")
    private String reviewType;

    /** 复习任务名称（5月1日课程的D2抗遗忘复习） */
    @Excel(name = "复习任务名称", readConverterExp = "5=月1日课程的D2抗遗忘复习")
    private String name;

    /** 计划复习时间 */
    @Excel(name = "计划复习时间")
    private Date scheduledTime;

    /** 实际开始复习时间 */
    @Excel(name = "实际开始复习时间")
    private Date actualStartTime;

    /** 实际完成复习时间 */
    @Excel(name = "实际完成复习时间")
    private Date actualEndTime;

    /** 复习状态 (待开始, 进行中, 已完成, 已跳过) */
    @Excel(name = "复习状态 (待开始, 进行中, 已完成, 已跳过)")
    private String status;

    /** 本次复习的单词ID列表 */
    @Excel(name = "本次复习的单词ID列表")
    @TableField(typeHandler = ListStringTypeHandler.class)
    private List<String> wordIds;

    /** 本次复习的词表项ID列表 */
    @Excel(name = "本次复习的词表项ID列表")
    @TableField(typeHandler = ListStringTypeHandler.class)
    private List<String> textbookItemIds;


    /** 版本号 */
    @Excel(name = "版本号")
    private Long version;


    /** 复习内容 (JSONB, {type: "D2", questions: [{wordId: "", word: "", type: "", sentence: "", ordering: ["", "", ""], options: ["", "", "", ""], answer: "", studentAnswer: ""}]};) */
    @Excel(name = "复习内容")
    private String content;

    /** 复习单词总数 */
    @Excel(name = "复习单词总数")
    private Long statWordTotal;

    /** 复习正确单词数 */
    @Excel(name = "复习正确单词数")
    private Long statWordCorrect;

    /** 复习错误单词数 */
    @Excel(name = "复习错误单词数")
    private Long statWordIncorrect;

    /** 复习总题数 */
    @Excel(name = "复习总题数")
    private Long statStepTotal;

    /** 复习正确题数 */
    @Excel(name = "复习正确题数")
    private Long statStepCorrect;

    /** 复习错误题数 */
    @Excel(name = "复习错误题数")
    private Long statStepIncorrect;

    /** 完成复习的课程ID */
    @Excel(name = "完成复习的课程ID")
    private String reviewCourseId;

    /** 是否自己完成复习 */
    @Excel(name = "是否自己完成复习")
    private Boolean reviewByOneself;

    /** 完成复习的人员 */
    @Excel(name = "完成复习的人员")
    private String reviewByUserId;

    /** 上传的图片URL列表 (用于D14、D21类型的复习) */
    @Excel(name = "上传的图片URL列表")
    @TableField(typeHandler = ListStringTypeHandler.class)
    private List<String> uploadedImages;

    /** 上传的说明文字 (用于D14、D21类型的复习) */
    @Excel(name = "上传的说明文字")
    private String uploadedDescription;

    /** 上传时间 */
    @Excel(name = "上传时间")
    private Date uploadedTime;

}
