package org.nonamespace.word.server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.nonamespace.word.server.domain.HistoryWord;
import org.nonamespace.word.server.mapper.HistoryWordMapper;
import org.nonamespace.word.server.service.IHistoryWordService;
import org.springframework.stereotype.Service;

/**
 * 单词历史: 存储单词的修改历史版本Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Service
public class HistoryWordServiceImpl extends ServiceImpl<HistoryWordMapper, HistoryWord> implements IHistoryWordService {

}
