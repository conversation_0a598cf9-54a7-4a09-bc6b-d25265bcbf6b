package org.nonamespace.word.server.dto;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.nonamespace.word.server.domain.Word;
import org.nonamespace.word.server.misc.mybatis.ListStringTypeHandler;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class WordPageDto {

    /**
     * 请求实体类
     */
    @Getter
    @Setter
    public static class Req {
        /* 词表Id */
        private String textbookId;
        /* 单元Id */
        private String unitId;
        /* 教材类型 */
        private String textBookType;
        /* 教材名称 */
        private QryCondition textBookName;
        /* 单词 */
        private QryCondition word;

        private Boolean qryTextbook = Boolean.FALSE;

    }


    /**
     * 响应实体类
     */
    @Getter
    @Setter
    public static class Resp {
        private String wordId;
        private String textbookId;
        private String textbookType;
        private String textbookName;
        private String word;

        /** 音节 */
        private String syllables;

        /** 英式音标 */
        private String phoneticUk;

        /** 美式音标 */
        private String phoneticUs;

        /** 英式发音音频文件URL */
        private String audioUkUrl;

        /** 美式发音音频文件URL */
        private String audioUsUrl;

        /** 词义(可包含多个词性及对应中文解释,如{pos: [{"pos": "n.", "def": "猫"}, {"pos": "v.", "def": "抓"}], practices:["", ""]) */
        @JsonIgnoreProperties
        private transient String meaningsStr;
        private Map<String, Word.Meanings> meanings;

        /** 例句 ([{"sentence_en": "...", "sentence_cn": "...", "audio_uk_url": "...", "audio_us_url": "...", "structure_parts_en": [["...", "..."]]}]) */
        private Map<String, List<Word.Sentences>> sentences;
        @JsonIgnoreProperties
        private transient String sentencesStr;

        /** 标签 (数组类型, 例如: "高频"、"名词"、"动词") */

        @TableField(typeHandler = ListStringTypeHandler.class)
        private List<String> tags;

        /** 难度等级 (1-5) */
        private Long difficulty;

        /** 标准讲解视频URL (通用) */
        private Map<String, String> videoUrl;
        @JsonIgnoreProperties
        private transient String videoUrlStr;

        /** 单词测验标记 */
        private Boolean flagPracticeWord;

        /** 句子翻译标记 */
        private Boolean flagPracticeTranslate;

        /** 句子排序标记 */
        private Boolean flagPracticeOrder;

        public Map<String, Word.Meanings> getMeanings() {
            if(StrUtil.isBlankIfStr(meaningsStr)){
                return null;
            }
            return JSONUtil.toBean(meaningsStr, JSONConfig.create().setIgnoreNullValue(true), Map.class);
        }

        public Map<String, List<Word.Sentences>> getSentences() {
            if(StrUtil.isBlankIfStr(sentencesStr)){
                return null;
            }
            return JSONUtil.toBean(sentencesStr, JSONConfig.create().setIgnoreNullValue(true), Map.class);
        }

        public Map<String, String> getVideoUrl() {
            if(StrUtil.isBlankIfStr(videoUrlStr)){
                return null;
            }
            return JSONUtil.toBean(videoUrlStr, JSONConfig.create().setIgnoreNullValue(true), Map.class);
        }
    }

}
