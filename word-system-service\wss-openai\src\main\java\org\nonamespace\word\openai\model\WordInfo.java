package org.nonamespace.word.openai.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 单词信息实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WordInfo {

    /**
     * 单词
     */
    private String word;

    /**
     * 英式音标
     */
    private String phoneticUk;

    /**
     * 美式音标
     */
    private String phoneticUs;

    /**
     * 音节
     */
    private String syllables;

    /**
     * 单词含义
     */
    private Map<String, Meanings> meanings;

    /**
     * 例句
     */
    private Map<String, List<Sentence>> sentences;

    /**
     * 单词含义
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Meanings {
        /**
         * 词性和释义
         */
        private List<PosDefinition> pos;

        /**
         * 混淆释义练习
         */
        private List<String> practices;
    }

    /**
     * 词性和释义
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class PosDefinition {
        /**
         * 词性
         */
        private String pos;

        /**
         * 释义
         */
        private String def;
    }

    /**
     * 例句
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Sentence {
        private String syllables;
        /**
         * 英文例句
         */
        private String sentenceEn;

        /**
         * 中文翻译
         */
        private String sentenceCn;

        /**
         * 适用阶段
         */
        private String stage;

        /**
         * 混淆翻译练习
         */
        private List<String> practices;

        /**
         * 句子结构拆分
         */
        private List<String> structurePartsEn;
        private String audioUkUrl;
        private String audioUsUrl;
    }
}
