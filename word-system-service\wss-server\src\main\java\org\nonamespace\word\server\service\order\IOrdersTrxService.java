package org.nonamespace.word.server.service.order;

import com.baomidou.mybatisplus.extension.service.IService;
import org.nonamespace.word.server.domain.order.OrdersTrx;

import java.util.List;

/**
 * 订单交易记录表Service接口
 * 
 * <AUTHOR>
 */
public interface IOrdersTrxService extends IService<OrdersTrx> {

    /**
     * 根据订单ID查询交易记录
     * 
     * @param orderId 订单ID
     * @return 交易记录列表
     */
    List<OrdersTrx> getByOrderId(String orderId);

    /**
     * 根据订单号查询交易记录
     * 
     * @param orderNo 订单号
     * @return 交易记录列表
     */
    List<OrdersTrx> getByOrderNo(String orderNo);

    /**
     * 根据商户交易流水号查询交易记录
     * 
     * @param cusTrxSeq 商户交易流水号
     * @return 交易记录
     */
    OrdersTrx getByCusTrxSeq(String cusTrxSeq);

    /**
     * 根据交易类型查询交易记录
     * 
     * @param trxType 交易类型
     * @return 交易记录列表
     */
    List<OrdersTrx> getByTrxType(String trxType);

    /**
     * 根据交易状态查询交易记录
     * 
     * @param trxStatus 交易状态
     * @return 交易记录列表
     */
    List<OrdersTrx> getByTrxStatus(String trxStatus);

    /**
     * 根据支付类型查询交易记录
     * 
     * @param payType 支付类型
     * @return 交易记录列表
     */
    List<OrdersTrx> getByPayType(String payType);

}