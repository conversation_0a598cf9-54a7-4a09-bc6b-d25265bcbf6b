package org.nonamespace.word.rest.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import lombok.Cleanup;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.dto.curriculum.*;
import org.nonamespace.word.server.facade.CurriculumFacade;
import org.nonamespace.word.server.service.ICourseService;
import org.nonamespace.word.server.service.ITeacherManagementService;
import org.nonamespace.word.server.service.IUserService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;

@RestController
@RequestMapping("/curriculum")
@RequiredArgsConstructor
//@Anonymous
@Slf4j
public class CurriculumController extends BaseController {
    private final ICourseService courseService;

    private final IUserService userService;

    private final CurriculumFacade curriculumFacade;

    private final ITeacherManagementService teacherManagementService;

    /**
     * 获取课程（课表）
     */
    @GetMapping("/schedule")
    public AjaxResult getSchedule(CurriculemGetScheduleDto.Req req) {
        return success(curriculumFacade.getSchedule(req));
    }


    /**
     * 创建排课
     */
    @PostMapping("/schedule")
    @Log(title = "排课", businessType = BusinessType.INSERT)
    public AjaxResult createSchedule(@RequestBody CurriculumCreateScheduleDto.Req req) {
        curriculumFacade.createSchedule(req);
        return success();
    }

    /**
     * 停课
     */
    @PostMapping("/course/{courseId}/cancel")
    @Log(title = "停课", businessType = BusinessType.UPDATE)
    public AjaxResult cancelCourse(@PathVariable("courseId") String courseId, CurriculeCancelScheduleDto.Req req) {
        req.setCourseId(courseId);
        curriculumFacade.cancelCourse(req);

        return success();
    }

    /**
     * 消课
     */
    @PostMapping("/course/consume")
    @Log(title = "手动消课", businessType = BusinessType.UPDATE)
    public AjaxResult consumeCourse(@RequestBody CurriculumConsumeCourseDto.Req req) {
        curriculumFacade.consumeCourse(req);
        return success();
    }

    @GetMapping("/students")
    public AjaxResult students(CurriculumUsersDto.Req req) {
        return success(teacherManagementService.students(req));
    }

    @GetMapping("/teachers")
    public AjaxResult teachers(CurriculumUsersDto.Req req) {
        return success(teacherManagementService.teachers(req));
    }

    /**
     * 导入课表
     */
    @PostMapping("/schedule/import")
    public AjaxResult importSchedule(@RequestParam("file") MultipartFile file) {
        try {
            @Cleanup
            InputStream inputStream = file.getInputStream();
            curriculumFacade.importCurriculum(inputStream);
            return success();
        } catch (Exception e) {
            log.error("导入课表失败", e);
            return error("导入课表失败: " + e.getMessage());
        }
    }

    @PostMapping("/course/reschedule")
    @Log(title = "调课", businessType = BusinessType.UPDATE)
    public AjaxResult reschedule(@RequestBody CurriculumCourseRescheduleDto.Req req) {
        curriculumFacade.reschedule(req);
        return success();
    }
}
