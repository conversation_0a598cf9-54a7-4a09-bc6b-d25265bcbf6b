package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.JdbcType;
import org.nonamespace.word.server.entity.DataEntity;
import org.nonamespace.word.server.misc.mybatis.ListStringTypeHandler;

import java.time.LocalDate;
import java.util.List;

/**
 * 教师档案信息实体类
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "teacher_profile", autoResultMap = true)
public class TeacherProfile extends DataEntity {

    /**
     * 教师ID (关联sys_user表)
     */
    @TableField("teacher_id")
    private String teacherId;

    /**
     * 用户昵称 (冗余字段，与sys_user.nick_name同步)
     */
    @TableField("nick_name")
    private String nickName;

    /**
     * 手机号码 (冗余字段，与sys_user.phonenumber同步)
     */
    @TableField("phonenumber")
    private String phonenumber;

    /**
     * 真实姓名
     */
    @TableField("real_name")
    private String realName;

    /**
     * 性别 (0: 男, 1: 女, 2: 未知)
     */
    @TableField("gender")
    private String gender;

    /**
     * 年龄
     */
    @TableField("age")
    private Integer age;

    /**
     * 头像URL
     */
    @TableField("avatar")
    private String avatar;

    /**
     * 目前所在地
     */
    @TableField("current_location")
    private String currentLocation;

    /**
     * 兼职/全职 (full_time: 全职, intended_full_time: 意向全职, part_time: 兼职)
     */
    @TableField("employment_type")
    private String employmentType;

    /**
     * 目前状态 (上班族、学生、居家办公)
     */
    @TableField("current_status")
    private String currentStatus;

    /**
     * 最高学历
     */
    @TableField("education")
    private String education;

    /**
     * 毕业院校
     */
    @TableField("graduate_school")
    private String graduateSchool;

    /**
     * 毕业专业
     */
    @TableField("major")
    private String major;

    /**
     * 大学属性 (985, 211, 重点, 普通)
     */
    @TableField("university_type")
    private String universityType;

    /**
     * 是否师范类
     */
    @TableField("is_normal_university")
    private Boolean isNormalUniversity;

    /**
     * 是否留学
     */
    @TableField("study_abroad")
    private Boolean studyAbroad;

    /**
     * 留学国家
     */
    @TableField("study_abroad_country")
    private String studyAbroadCountry;

    /**
     * 教资级别
     */
    @TableField("teaching_certificate_level")
    private String teachingCertificateLevel;

    /**
     * 教授学科 (VARCHAR数组)
     */
    @TableField(value = "subjects", typeHandler = ListStringTypeHandler.class, jdbcType = JdbcType.ARRAY)
    private List<String> subjects;

    /**
     * 已通过培训科目 (VARCHAR数组)
     */
    @TableField(value = "training_subjects", typeHandler = ListStringTypeHandler.class, jdbcType = JdbcType.ARRAY)
    private List<String> trainingSubjects;

    /**
     * 英语资质
     */
    @TableField("english_qualification")
    private String englishQualification;

    /**
     * 普通话资质
     */
    @TableField("mandarin_qualification")
    private String mandarinQualification;

    /**
     * 沟通能力 (优秀, 良好, 一般, 薄弱)
     */
    @TableField("communication_ability")
    private String communicationAbility;

    /**
     * 教学经历
     */
    @TableField("teaching_experience")
    private String teachingExperience;

    /**
     * 教过课程 (VARCHAR数组)
     */
    @TableField(value = "taught_courses", typeHandler = ListStringTypeHandler.class, jdbcType = JdbcType.ARRAY)
    private List<String> taughtCourses;

    /**
     * 获奖奖项
     */
    @TableField("awards")
    private String awards;

    /**
     * 教龄
     */
    @TableField("teaching_years")
    private Integer teachingYears;

    /**
     * 上课风格 (VARCHAR数组)
     */
    @TableField(value = "teaching_style", typeHandler = ListStringTypeHandler.class, jdbcType = JdbcType.ARRAY)
    private List<String> teachingStyle;

    /**
     * 英语发音 (良好, 一般, 正常, 优秀（母语水平）)
     */
    @TableField("english_pronunciation")
    private String englishPronunciation;

    /**
     * 适合学生年级 (VARCHAR数组)
     */
    @TableField(value = "suitable_grades", typeHandler = ListStringTypeHandler.class, jdbcType = JdbcType.ARRAY)
    private List<String> suitableGrades;

    /**
     * 适合学生学习程度 (VARCHAR数组)
     */
    @TableField(value = "suitable_levels", typeHandler = ListStringTypeHandler.class, jdbcType = JdbcType.ARRAY)
    private List<String> suitableLevels;

    /**
     * 适合学生性格 (外向活泼, 内向腼腆, 都适合)
     */
    @TableField("suitable_personality")
    private String suitablePersonality;

    /**
     * 其他信息
     */
    @TableField("other")
    private String other;

    /**
     * 简介
     */
    @TableField("introduction")
    private String introduction;

    /**
     * 状态 (active: 活跃, inactive: 停用)
     */
    @TableField("status")
    private String status;

    /**
     * 暑期课上课时间 (full: 全满档, golden: 黄金档, other: 其他档)
     */
    @TableField("summer_schedule_type")
    private String summerScheduleType;

    /**
     * 正式入职时间
     */
    @TableField("formal_entry_date")
    private LocalDate formalEntryDate;

    /**
     * 资质证书文件URL数组
     */
    @TableField(value = "qualification_certificates", typeHandler = ListStringTypeHandler.class, jdbcType = JdbcType.ARRAY)
    private List<String> qualificationCertificates;

    /**
     * 示范上课视频URL数组
     */
    @TableField(value = "demo_videos", typeHandler = ListStringTypeHandler.class, jdbcType = JdbcType.ARRAY)
    private List<String> demoVideos;
}
