package org.nonamespace.word.server.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

// 建议创建一个通用的Mapper来存放这类公共方法
@Mapper
public interface SeqIdMapper {

    /**
     * 获取指定序列的下一个值
     * @param sequenceName 序列名称
     * @return 序列的下一个值
     */
    @Select("SELECT nextval(#{sequenceName})")
    Long getNextVal(String sequenceName);

    /**
     * 获取下一个用户ID
     * @return 下一个用户ID
     */
    @Select("SELECT nextval('sys_user_user_id_seq')")
    Long getNextUserId();

    /**
     * 获取下一个部门ID
     * @return 下一个部门ID
     */
    @Select("SELECT nextval('sys_dept_dept_id_seq')")
    Long getNextDeptId();
}