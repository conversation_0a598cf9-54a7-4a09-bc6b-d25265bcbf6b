//package org.nonamespace.word.rest.controller;
//
//
//import cn.hutool.core.collection.CollUtil;
//import com.ruoyi.common.annotation.Anonymous;
//import org.nonamespace.word.common.utils.SentenceInsertPipesUtils;
//import org.nonamespace.word.server.domain.Word;
//import org.nonamespace.word.server.service.IWordService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.ArrayList;
//import java.util.Collections;
//import java.util.List;
//
//@RequestMapping("/test/word")
//@RestController
//@Anonymous
//public class TestWordController {
//
//    @Autowired
//    private IWordService wordService;
//
//    @GetMapping("enrichErrorStructurePartsEn")
//    public void enrichErrorStructurePartsEn() {
//        List<Word> listed = wordService.list();
//        List<String> wordIds = new ArrayList<>();
//        for (int i = 1; i < listed.size(); i++) {
//            Word word = listed.get(i);
//            List<Word.Sentences> tese = word.getSentences().get("通用");
//            if(CollUtil.isNotEmpty(tese)){
//                for (int j = 0; j < tese.size(); j++) {
////                    List<String> structurePartsEn = tese.get(j).getStructurePartsEn();
////                    // 判断structurePartsEn里面的值的长度是否一致
////                    int firstLength = structurePartsEn.get(0).replaceAll(" ", "").length();
////                    for (int k = 1; k < structurePartsEn.size(); k++) {
////                        if (structurePartsEn.get(k).replaceAll(" ", "").length() != firstLength) {
////                            wordIds.add(word.getId());
////                            break;
////                        }
////                    }
//
//                    String newEn = tese.get(j).getSentenceEn().replaceAll("[\\s|]", "");
//                    List<String> structurePartsEn = tese.get(j).getStructurePartsEn();
//                    for (int k = 1; k < structurePartsEn.size(); k++) {
//                        if (structurePartsEn.get(k).replaceAll("[\\s|]", "").length() != newEn.length()) {
//                            wordIds.add(word.getId());
//                            break;
//                        }
//                    }
//                }
//            }
//        }
//
//        System.out.println("================= 错误的单词数量：" + wordIds.stream().distinct().count());
//        System.out.println("================= 错误的单词：" + wordIds.stream().distinct().toList().toString());
//
//
////        wordIds.forEach(id-> {
////            Word word = wordService.getById(id);
////            word.getSentences().forEach((k, sentences) -> {
////                sentences.forEach(sen -> sen.setStructurePartsEn(SentenceInsertPipesUtils.insertPipesCount(sen.getSentenceEn(), 3, 2, '|')));
////            });
////            wordService.updateById(word);
////            System.out.println("---更新成功："+ id);
////        });
//    }
//
//}
