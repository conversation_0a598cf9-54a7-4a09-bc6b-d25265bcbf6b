package org.nonamespace.word.server.service.order;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseService;
import org.nonamespace.word.server.domain.order.Orders;
import org.nonamespace.word.server.dto.order.OrderCreateDto;
import org.nonamespace.word.server.dto.order.OrderExportDto;
import org.nonamespace.word.server.dto.order.OrderPageDto;
import org.nonamespace.word.server.dto.order.PaymentDto;

import java.util.Date;
import java.util.List;

/**
 * 订单表Service接口
 * 
 * <AUTHOR>
 */
public interface IOrdersService extends MPJBaseService<Orders> {

    /**
     * 根据订单号查询订单
     * 
     * @param orderNo 订单号
     * @return 订单信息
     */
    Orders getByOrderNo(String orderNo);

    /**
     * 根据学生ID查询订单列表
     * 
     * @param studentId 学生ID
     * @return 订单列表
     */
    List<Orders> getByStudentId(String studentId);

    /**
     * 根据销售员ID查询订单列表
     * 
     * @param salerId 销售员ID
     * @return 订单列表
     */
    List<Orders> getBySalerId(String salerId);

    /**
     * 根据订单状态查询订单列表
     * 
     * @param orderStatus 订单状态
     * @return 订单列表
     */
    List<Orders> getByOrderStatus(String orderStatus);

    /**
     * 创建订单
     * @param orderCreateDto
     */
    Orders createOrder(OrderCreateDto orderCreateDto);

    /**
     * 订单支付
     */
    void nativePay(String orderTrxId);

    /**
     * 生成支付信息
     * @param orderTrxId 交易流水ID
     * @return 支付信息
     */
    PaymentDto.PayResp generatePayment(String orderTrxId);

    /**
     * 生成支付二维码
     * @param orderTrxId 交易流水ID
     * @return 二维码信息
     */
    PaymentDto.QRCodeResp generateQRCode(String orderTrxId);


    /**
     * 取消订单
     * @param orderId
     */
    void cancelOrder(String orderId);

    /**
     * 订单退款
     * @param orderId 订单ID
     * @param refundAmount 退款金额（分）
     * @param refundReason 退款原因
     */
    void refundOrder(String orderId, Long refundAmount, String refundReason);

    /**
     * 全额退款
     * @param orderId 订单ID
     * @param refundReason 退款原因
     */
    void fullRefundOrder(String orderId, String refundReason);

    Page<OrderPageDto.Resp> selectOrdersByParam(OrderPageDto.Req req);

    /**
     * 导出订单数据
     * @param req 导出请求参数
     * @return 导出数据列表
     */
    List<OrderExportDto.ExportResp> exportOrders(OrderExportDto.ExportReq req);

}