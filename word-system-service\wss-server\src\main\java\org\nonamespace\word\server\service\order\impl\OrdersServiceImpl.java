package org.nonamespace.word.server.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ruoyi.common.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.constants.OrderConstants;
import org.nonamespace.word.server.domain.Product;
import org.nonamespace.word.server.domain.order.Orders;
import org.nonamespace.word.server.domain.order.OrdersTrx;
import org.nonamespace.word.server.dto.order.OrderCreateDto;
import org.nonamespace.word.server.dto.order.OrderPageDto;
import org.nonamespace.word.server.dto.order.PaymentDto;
import org.nonamespace.word.server.mapper.order.OrdersMapper;
import org.nonamespace.word.server.service.IProductService;
import org.nonamespace.word.server.service.base.BaseService;
import org.nonamespace.word.server.service.order.IOrdersService;
import org.nonamespace.word.server.service.order.IOrdersTrxService;
import org.nonamespace.word.server.util.OrderTrxCodeUtil;
import org.nonamespace.word.thirdpart.allinpay.dto.PayDto;
import org.nonamespace.word.thirdpart.allinpay.model.NativePayOutput;
import org.nonamespace.word.thirdpart.allinpay.service.IAllinPayService;
import org.nonamespace.word.thirdpart.allinpay.service.IQRCodeService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 订单表Service业务层处理
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrdersServiceImpl extends MPJBaseServiceImpl<OrdersMapper, Orders> implements IOrdersService {

    private final IAllinPayService allinPayService;
    private final OrderTrxCodeUtil orderTrxCodeUtil;
    private final IOrdersTrxService ordersTrxService;
    private final IQRCodeService qrCodeService;
    private final IProductService productService;
    private final StringRedisTemplate redisTemplate;

    @Override
    public Orders getByOrderNo(String orderNo) {
        return this.lambdaQuery()
                .eq(Orders::getNo, orderNo)
                .one();
    }

    @Override
    public List<Orders> getByStudentId(String studentId) {
        return this.lambdaQuery()
                .eq(Orders::getStudentId, studentId)
                .orderByDesc(Orders::getCreateTime)
                .list();
    }

    @Override
    public List<Orders> getBySalerId(String salerId) {
        return this.lambdaQuery()
                .eq(Orders::getSalerId, salerId)
                .orderByDesc(Orders::getCreateTime)
                .list();
    }

    @Override
    public List<Orders> getByOrderStatus(String orderStatus) {
        return this.lambdaQuery()
                .eq(Orders::getOrderStatus, orderStatus)
                .orderByDesc(Orders::getCreateTime)
                .list();
    }

    private Product checkProductIsNotNull(String productId) {
        Product product = productService.getById(productId);
        Objects.requireNonNull(product, "该产品配置信息不存在，请确认");
        return product;
    }

    @Override
    public String createOrder(OrderCreateDto orderCreateDto) {
        Long userId = SecurityUtils.getUserId();
        // 同一个用户3s内只能请求一次
        String key = "order:create:locked:" + userId;
        if(redisTemplate.hasKey(key)) {
            throw new IllegalArgumentException("您最近3秒内已创建订单，请勿重复创建");
        }
        redisTemplate.opsForValue().set(key, orderCreateDto.getProductId(), 3, TimeUnit.SECONDS);
        try {
            String orderId = IdUtil.getSnowflakeNextIdStr();
            Product product = checkProductIsNotNull(orderCreateDto.getProductId());

            Orders orders = this.buildOrder(orderCreateDto, product);
            orders.setId(orderId);

            if(!orderCreateDto.isValidMultiTrxAmt(orders.getTotalAmt())) {
                throw new IllegalArgumentException("分期金额校验失败");
            }

            // 同时生成一笔交易初始流水
            List<OrdersTrx> ordersTrxes = new ArrayList<>();
            if(CollUtil.isNotEmpty(orderCreateDto.getMultiTrxAmts())) {
                orderCreateDto.getMultiTrxAmts().forEach(multiTrxAmt -> {
                    OrdersTrx ordersTrx = OrdersTrx.builder().orderId(orderId).orderNo(orders.getNo())
                            .cusTrxSeq(orderTrxCodeUtil.generalTrxSeq())
                            .trxIdx(multiTrxAmt.getIdx())
                            .trxAmt(multiTrxAmt.getAmt())
                            .trxStatus(OrderConstants.OrderStatus.UNPAID).build();
                    ordersTrxes.add(ordersTrx);
                });
            } else {
                OrdersTrx ordersTrx = OrdersTrx.builder().orderId(orderId).orderNo(orders.getNo())
                        .cusTrxSeq(orderTrxCodeUtil.generalTrxSeq())
                        .trxIdx(1)
                        .trxAmt(orders.getTotalAmt())
                        .trxStatus(OrderConstants.OrderStatus.UNPAID).build();
                ordersTrxes.add(ordersTrx);
            }
            if(CollUtil.isEmpty(ordersTrxes)){
                throw new IllegalArgumentException("订单交易流水不能为空");
            }

            // 保存订单
            this.save(orders);
            // 保存交易流水
            ordersTrxService.saveBatch(ordersTrxes);
            return orderId;
        } finally {
            redisTemplate.delete(key);
        }
    }


    /**
     * 交易流水支付
     * @param orderTrxId
     */
    @Override
    public void nativePay(String orderTrxId) {
        OrdersTrx ordersTrx = ordersTrxService.getById(orderTrxId);
        checkOrderTrxStatus(ordersTrx);

        // 获取订单信息
        Orders orders = this.getById(ordersTrx.getOrderId());
        if(orders == null) {
            throw new IllegalArgumentException("订单不存在");
        }

        try {
            NativePayOutput nativePayOutput = allinPayService.nativepay(PayDto.NativePayReq.builder()
                    .cusTrxSeq(ordersTrx.getCusTrxSeq())
                            .body(orders.getBody()).remark(orders.getRemark())
                            .trxamt(ordersTrx.getTrxAmt())
                    .build());

            // 生成二维码
            byte[] qrCode = qrCodeService.generateQRCode(nativePayOutput.getPayinfo(), 200, 200);
            // todo...
        } catch (Exception e) {
            log.error("交易流水生成支付信息异常", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void cancelOrder(String orderId) {
        Orders orders = this.getById(orderId);
        if(orders == null) {
            throw new IllegalArgumentException("订单不存在");
        }
        if(List.of(OrderConstants.OrderStatus.UNPAID, OrderConstants.OrderStatus.PAID, OrderConstants.OrderStatus.FULL_PAID, OrderConstants.OrderStatus.PART_PAID)
                .contains(orders.getOrderStatus())) {
            throw new IllegalArgumentException("当前订单状态不允许取消");
        }

        if(!orders.getOrderStatus().equalsIgnoreCase(OrderConstants.OrderStatus.UNPAID)) {
            log.info("当前订单[{}]有付款，订单取消并退款。", orderId);

//            allinPayService.cancel();

        }


        // 取消订单
        orders.setOrderStatus(OrderConstants.OrderStatus.CANCEL);
        this.updateById(orders);

        // 取消交易流水
        List<OrdersTrx> ordersTrxes = ordersTrxService.lambdaQuery()
                .eq(OrdersTrx::getOrderId, orderId)
                .list();
        if(CollUtil.isNotEmpty(ordersTrxes)) {
            ordersTrxes.forEach(ordersTrx -> {
                ordersTrx.setTrxStatus(OrderConstants.OrderStatus.CANCEL);
                ordersTrxService.updateById(ordersTrx);
            });
        }
    }

    @Override
    public Page<OrderPageDto.Resp> selectOrdersByParam(OrderPageDto.Req req) {
        Page<OrderPageDto.Resp> pageParam = new Page<>(req.getPageNum(), req.getPageSize());
        return this.baseMapper.selectOrdersByParam(pageParam, req);
    }


    private void checkOrderTrxStatus(OrdersTrx ordersTrx) {
        if(ordersTrx == null) {
            throw new IllegalArgumentException("交易流水不存在");
        }
        if(!ordersTrx.getTrxStatus().equalsIgnoreCase(OrderConstants.OrderStatus.UNPAID)) {
            throw new IllegalArgumentException("交易流水状态错误");
        }
        if(ordersTrx.getTrxAmt() <= 0) {
            throw new IllegalArgumentException("交易流水金额错误");
        }
    }

    private Orders buildOrder(OrderCreateDto orderCreateDto, Product product) {
        Orders order = new Orders();
        order.setNo(orderTrxCodeUtil.generalOrderCode(OrderConstants.TrxType.PAY));
        order.setSource(OrderConstants.Source.SYSTEM);
        order.setOrderStatus(OrderConstants.OrderStatus.UNPAID);

        order.setProducts(product);
        order.setProductId(product.getId());

        order.setTotalAmt(product.getSellingPrice());
        order.setBody(product.getName());
        order.setRemark(product.getName() + "待支付订单");

        order.setStudentId(orderCreateDto.getStudentId());
        order.setSalerId(String.valueOf(SecurityUtils.getUserId()));
        order.setAmtPaid(0L);
        order.setAmtUnpaid(order.getTotalAmt());
        order.setSignStatus(OrderConstants.SignStatus.UN_SIGN);
        order.setTrxMethod(CollUtil.isEmpty(orderCreateDto.getMultiTrxAmts()) ? "一次性支付" : "分期支付");
        return order;
    }

    @Override
    public PaymentDto.PayResp generatePayment(String orderTrxId) {
        OrdersTrx ordersTrx = ordersTrxService.getById(orderTrxId);
        checkOrderTrxStatus(ordersTrx);

        // 获取订单信息
        Orders orders = this.getById(ordersTrx.getOrderId());
        if(orders == null) {
            throw new IllegalArgumentException("订单不存在");
        }

        try {
            NativePayOutput nativePayOutput = allinPayService.nativepay(PayDto.NativePayReq.builder()
                    .cusTrxSeq(ordersTrx.getCusTrxSeq())
                    .body(orders.getBody()).remark(orders.getRemark())
                    .trxamt(ordersTrx.getTrxAmt())
                    .build());

            PaymentDto.PayResp payResp = new PaymentDto.PayResp();
            payResp.setPayInfo(nativePayOutput.getPayinfo());
            payResp.setPayUrl(nativePayOutput.getPayinfo());
            payResp.setOrderTrxId(orderTrxId);
            payResp.setCusTrxSeq(ordersTrx.getCusTrxSeq());
            payResp.setTrxAmt(ordersTrx.getTrxAmt());

            return payResp;
        } catch (Exception e) {
            log.error("生成支付信息异常", e);
            throw new RuntimeException("生成支付信息失败: " + e.getMessage());
        }
    }

    @Override
    public PaymentDto.QRCodeResp generateQRCode(String orderTrxId) {
        PaymentDto.PayResp payResp = generatePayment(orderTrxId);

        try {
            // 生成二维码
            byte[] qrCode = qrCodeService.generateQRCode(payResp.getPayInfo(), 200, 200);
            String qrCodeBase64 = java.util.Base64.getEncoder().encodeToString(qrCode);

            PaymentDto.QRCodeResp qrCodeResp = new PaymentDto.QRCodeResp();
            qrCodeResp.setQrCodeBase64(qrCodeBase64);
            qrCodeResp.setPayUrl(payResp.getPayUrl());

            return qrCodeResp;
        } catch (Exception e) {
            log.error("生成二维码异常", e);
            throw new RuntimeException("生成二维码失败: " + e.getMessage());
        }
    }

}