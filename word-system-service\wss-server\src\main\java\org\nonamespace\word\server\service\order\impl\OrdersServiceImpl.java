package org.nonamespace.word.server.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ruoyi.common.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.constants.OrderConstants;
import org.nonamespace.word.server.domain.Product;
import org.nonamespace.word.server.domain.StudentCourseConsumption;
import org.nonamespace.word.server.domain.StudentCourseHours;
import org.nonamespace.word.server.domain.order.OrderRefundRecord;
import org.nonamespace.word.server.domain.order.Orders;
import org.nonamespace.word.server.domain.order.OrdersTrx;
import org.nonamespace.word.server.dto.order.OrderCreateDto;
import org.nonamespace.word.server.dto.order.OrderExportDto;
import org.nonamespace.word.server.dto.order.OrderPageDto;
import org.nonamespace.word.server.dto.order.PaymentDto;
import org.nonamespace.word.server.mapper.order.OrdersMapper;
import org.nonamespace.word.server.service.IProductService;
import org.nonamespace.word.server.service.IStudentCourseHoursService;
import org.nonamespace.word.server.service.IStudentCourseConsumptionService;
import org.nonamespace.word.server.service.base.BaseService;
import org.nonamespace.word.server.service.order.IOrderRefundRecordService;
import org.nonamespace.word.server.service.order.IOrdersService;
import org.nonamespace.word.server.service.order.IOrdersTrxService;
import org.nonamespace.word.server.util.OrderTrxCodeUtil;
import org.nonamespace.word.thirdpart.allinpay.dto.PayDto;
import org.nonamespace.word.thirdpart.allinpay.dto.RefundDto;
import org.nonamespace.word.thirdpart.allinpay.model.NativePayOutput;
import org.nonamespace.word.thirdpart.allinpay.service.IAllinPayService;
import org.nonamespace.word.thirdpart.allinpay.service.IQRCodeService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 订单表Service业务层处理
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrdersServiceImpl extends MPJBaseServiceImpl<OrdersMapper, Orders> implements IOrdersService {

    private final IAllinPayService allinPayService;
    private final OrderTrxCodeUtil orderTrxCodeUtil;
    private final IOrdersTrxService ordersTrxService;
    private final IQRCodeService qrCodeService;
    private final IProductService productService;
    private final StringRedisTemplate redisTemplate;
    private final IStudentCourseHoursService studentCourseHoursService;
    private final IStudentCourseConsumptionService studentCourseConsumptionService;
    private final IOrderRefundRecordService orderRefundRecordService;

    @Override
    public Orders getByOrderNo(String orderNo) {
        return this.lambdaQuery()
                .eq(Orders::getNo, orderNo)
                .one();
    }

    @Override
    public List<Orders> getByStudentId(String studentId) {
        return this.lambdaQuery()
                .eq(Orders::getStudentId, studentId)
                .orderByDesc(Orders::getCreateTime)
                .list();
    }

    @Override
    public List<Orders> getBySalerId(String salerId) {
        return this.lambdaQuery()
                .eq(Orders::getSalerId, salerId)
                .orderByDesc(Orders::getCreateTime)
                .list();
    }

    @Override
    public List<Orders> getByOrderStatus(String orderStatus) {
        return this.lambdaQuery()
                .eq(Orders::getOrderStatus, orderStatus)
                .orderByDesc(Orders::getCreateTime)
                .list();
    }

    private Product checkProductIsNotNull(String productId) {
        Product product = productService.getById(productId);
        Objects.requireNonNull(product, "该产品配置信息不存在，请确认");
        return product;
    }

    @Override
    public Orders createOrder(OrderCreateDto orderCreateDto) {
        Long userId = SecurityUtils.getUserId();
        // 同一个用户3s内只能请求一次
        String key = "order:create:locked:" + userId;
        if(redisTemplate.hasKey(key)) {
            throw new IllegalArgumentException("您最近3秒内已创建订单，请勿重复创建");
        }
        redisTemplate.opsForValue().set(key, orderCreateDto.getProductId(), 3, TimeUnit.SECONDS);
        try {
            String orderId = IdUtil.getSnowflakeNextIdStr();
            Product product = checkProductIsNotNull(orderCreateDto.getProductId());

            Orders orders = this.buildOrder(orderCreateDto, product);
            orders.setId(orderId);

            if(!orderCreateDto.isValidMultiTrxAmt(orders.getTotalAmt())) {
                throw new IllegalArgumentException("分期金额校验失败");
            }

            // 同时生成一笔交易初始流水
            List<OrdersTrx> ordersTrxes = new ArrayList<>();
            if(CollUtil.isNotEmpty(orderCreateDto.getMultiTrxAmts())) {
                orderCreateDto.getMultiTrxAmts().forEach(multiTrxAmt -> {
                    OrdersTrx ordersTrx = OrdersTrx.builder().orderId(orderId).orderNo(orders.getNo())
                            .cusTrxSeq(orderTrxCodeUtil.generalTrxSeq())
                            .trxIdx(multiTrxAmt.getIdx())
                            .trxAmt(multiTrxAmt.getAmt())
                            .trxType(OrderConstants.TrxType.PAY)
                            .trxStatus(OrderConstants.OrderStatus.UNPAID).build();
                    ordersTrxes.add(ordersTrx);
                });
            } else {
                OrdersTrx ordersTrx = OrdersTrx.builder().orderId(orderId).orderNo(orders.getNo())
                        .cusTrxSeq(orderTrxCodeUtil.generalTrxSeq())
                        .trxIdx(1)
                        .trxAmt(orders.getTotalAmt())
                        .trxStatus(OrderConstants.OrderStatus.UNPAID).build();
                ordersTrxes.add(ordersTrx);
            }
            if(CollUtil.isEmpty(ordersTrxes)){
                throw new IllegalArgumentException("订单交易流水不能为空");
            }

            // 保存订单
            this.save(orders);
            // 保存交易流水
            ordersTrxService.saveBatch(ordersTrxes);
            return orders;
        } finally {
            redisTemplate.delete(key);
        }
    }


    /**
     * 交易流水支付
     * @param orderTrxId
     */
    @Override
    public void nativePay(String orderTrxId) {
        OrdersTrx ordersTrx = ordersTrxService.getById(orderTrxId);
        checkOrderTrxStatus(ordersTrx);

        // 获取订单信息
        Orders orders = this.getById(ordersTrx.getOrderId());
        if(orders == null) {
            throw new IllegalArgumentException("订单不存在");
        }

        try {
            NativePayOutput nativePayOutput = allinPayService.nativepay(PayDto.NativePayReq.builder()
                    .cusTrxSeq(ordersTrx.getCusTrxSeq())
                            .body(orders.getBody()).remark(orders.getRemark())
                            .trxamt(ordersTrx.getTrxAmt())
                    .build());

            // 生成二维码
            byte[] qrCode = qrCodeService.generateQRCode(nativePayOutput.getPayinfo(), 200, 200);
            // todo...
        } catch (Exception e) {
            log.error("交易流水生成支付信息异常", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelOrder(String orderId) {
        Orders orders = this.getById(orderId);
        if(orders == null) {
            throw new IllegalArgumentException("订单不存在");
        }

        // 检查订单状态是否允许取消
        if(List.of(OrderConstants.OrderStatus.CANCEL, OrderConstants.OrderStatus.REFUND)
                .contains(orders.getOrderStatus())) {
            throw new IllegalArgumentException("订单已取消或已退款，无法重复操作");
        }

        // 获取订单的交易流水
        List<OrdersTrx> ordersTrxes = ordersTrxService.lambdaQuery()
                .eq(OrdersTrx::getOrderId, orderId)
                .list();

        // 处理已支付的订单
        if(!OrderConstants.OrderStatus.UNPAID.equals(orders.getOrderStatus())) {
            log.info("当前订单[{}]有付款，订单取消并退款。", orderId);

            // 创建退款记录
            String operatorId = SecurityUtils.getUserId().toString();
            String operatorName = SecurityUtils.getUsername();
            OrderRefundRecord refundRecord = orderRefundRecordService.createRefundRecord(
                    orders, null, null, OrderConstants.RefundType.FULL,
                    orders.getAmtPaid(), "订单取消退款", operatorId, operatorName);

            // 处理支付撤销或退款
            for(OrdersTrx ordersTrx : ordersTrxes) {
                if(OrderConstants.TrxType.PAY.equals(ordersTrx.getTrxType()) &&
                   !OrderConstants.TrxStatus.CANCELLED.equals(ordersTrx.getTrxStatus()) &&
                   !OrderConstants.TrxStatus.REFUNDED.equals(ordersTrx.getTrxStatus())) {

                    try {
                        // 判断是否为当天交易，当天交易使用撤销，否则使用退款
                        Date today = new Date();
                        Date trxDate = ordersTrx.getCreateTime();
                        boolean isSameDay = DateUtil.isSameDay(today, trxDate);

                        if(isSameDay) {
                            // 当天交易使用撤销
                            log.info("执行当天交易撤销: orderTrxId={}", ordersTrx.getId());
                            allinPayService.cancel(PayDto.NativePayReq.builder()
                                    .cusTrxSeq(ordersTrx.getCusTrxSeq())
                                    .trxamt(ordersTrx.getTrxAmt())
                                    .build());
                        } else {
                            // 隔天交易使用退款
                            log.info("执行隔天交易退款: orderTrxId={}", ordersTrx.getId());
                            RefundDto.Req refundReq = new RefundDto.Req();
                            refundReq.setOrderId(orderId);
                            refundReq.setAmount(ordersTrx.getTrxAmt().intValue());
                            refundReq.setRemark("订单取消退款");
                            allinPayService.refund(refundReq);
                        }

                        // 更新交易流水状态
                        ordersTrx.setTrxStatus(OrderConstants.TrxStatus.CANCELLED);
                        ordersTrxService.updateById(ordersTrx);

                        // 创建退款交易记录
                        OrdersTrx refundTrx = createRefundTrxRecord(orders, ordersTrx, ordersTrx.getTrxAmt(), "订单取消退款");

                        // 更新退款记录的交易流水ID
                        if (refundRecord.getRefundTrxId() == null) {
                            orderRefundRecordService.updatePlatformInfo(refundRecord.getId(),
                                    refundTrx.getId(), "订单取消退款交易流水ID: " + refundTrx.getId());
                        }

                    } catch (Exception e) {
                        log.error("订单取消时处理支付撤销/退款失败: orderTrxId={}", ordersTrx.getId(), e);

                        // 更新退款记录状态为失败
                        orderRefundRecordService.updateRefundStatus(refundRecord.getId(),
                                OrderConstants.RefundStatus.FAILED, e.getMessage());

                        throw new RuntimeException("订单取消失败: " + e.getMessage());
                    }
                }
            }

            // 更新退款记录状态为成功
            orderRefundRecordService.updateRefundStatus(refundRecord.getId(),
                    OrderConstants.RefundStatus.SUCCESS, null);

            // 回退课时
            rollbackCourseHours(orders);
        }

        // 更新订单状态
        orders.setOrderStatus(OrderConstants.OrderStatus.CANCEL);
        orders.setAmtPaid(0L);
        orders.setAmtUnpaid(orders.getTotalAmt());
        this.updateById(orders);

        // 更新所有未处理的交易流水状态
        if(CollUtil.isNotEmpty(ordersTrxes)) {
            ordersTrxes.forEach(ordersTrx -> {
                if(!OrderConstants.TrxStatus.CANCELLED.equals(ordersTrx.getTrxStatus())) {
                    ordersTrx.setTrxStatus(OrderConstants.TrxStatus.CANCELLED);
                    ordersTrxService.updateById(ordersTrx);
                }
            });
        }

        log.info("订单取消成功: orderId={}, orderNo={}", orderId, orders.getNo());
    }

    /**
     * 创建退款交易记录
     */
    private OrdersTrx createRefundTrxRecord(Orders orders, OrdersTrx originalTrx, Long refundAmt, String remark) {
        OrdersTrx refundTrx = OrdersTrx.builder()
                .orderId(orders.getId())
                .orderNo(orders.getNo())
                .cusTrxSeq(orderTrxCodeUtil.generalOrderCode(OrderConstants.TrxType.REFUND))
                .trxIdx(originalTrx.getTrxIdx())
                .payType(originalTrx.getPayType())
                .trxAmt(refundAmt)
                .trxType(OrderConstants.TrxType.REFUND)
                .trxStatus(OrderConstants.TrxStatus.REFUNDED)
                .build();

        refundTrx.setId(IdUtil.getSnowflakeNextIdStr());
        refundTrx.setCreateTime(new Date());
        refundTrx.setUpdateTime(new Date());
        refundTrx.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
        refundTrx.setUpdateBy(String.valueOf(SecurityUtils.getUserId()));

        ordersTrxService.save(refundTrx);
        log.info("创建退款交易记录成功: refundTrxId={}, refundAmt={}", refundTrx.getId(), refundAmt);
        return refundTrx;
    }

    /**
     * 回退课时
     */
    private void rollbackCourseHours(Orders orders) {
        log.info("开始回退订单相关课时: orderId={}, studentId={}", orders.getId(), orders.getStudentId());

        try {
            // 获取订单关联的产品信息
            Product product = orders.getProducts();
            if (product == null && orders.getProductId() != null) {
                product = productService.getById(orders.getProductId());
            }

            if (product == null) {
                log.warn("订单未关联产品信息，无法回退课时: orderId={}", orders.getId());
                return;
            }

            // 根据产品信息回退课时
            String studentId = orders.getStudentId();
            String subject = product.getSubject();
            String specification = product.getCourseType();
            String nature = "正式课"; // 默认为正式课，可以根据产品类型调整

            // 计算需要回退的课时数
            BigDecimal purchasedHours = BigDecimal.valueOf(product.getQuantity() != null ? product.getQuantity() : 0);
            BigDecimal giftHours = BigDecimal.valueOf(product.getBonusHoursQuantity() != null ? product.getBonusHoursQuantity() : 0);

            if (purchasedHours.compareTo(BigDecimal.ZERO) <= 0 && giftHours.compareTo(BigDecimal.ZERO) <= 0) {
                log.info("订单产品无课时信息，无需回退: orderId={}, productId={}", orders.getId(), product.getId());
                return;
            }

            // 查找该订单创建的课时记录（通过批次号或时间范围）
            String batchNo = "ORDER_" + orders.getNo(); // 假设订单创建课时时使用订单号作为批次号

            // 方式1：通过批次号查找（如果订单创建课时时设置了批次号）
            List<StudentCourseHours> orderCourseHours = studentCourseHoursService.lambdaQuery()
                    .eq(StudentCourseHours::getStudentId, studentId)
                    .eq(StudentCourseHours::getSubject, subject)
                    .eq(StudentCourseHours::getSpecification, specification)
                    .eq(StudentCourseHours::getNature, nature)
                    .eq(StudentCourseHours::getBatchNo, batchNo)
                    .list();

            // 方式2：如果通过批次号找不到，尝试通过时间范围查找（订单创建时间前后1小时内）
            if (orderCourseHours.isEmpty()) {
                Date orderCreateTime = orders.getCreateTime();
                Date startTime = new Date(orderCreateTime.getTime() - 3600000); // 1小时前
                Date endTime = new Date(orderCreateTime.getTime() + 3600000);   // 1小时后

                orderCourseHours = studentCourseHoursService.lambdaQuery()
                        .eq(StudentCourseHours::getStudentId, studentId)
                        .eq(StudentCourseHours::getSubject, subject)
                        .eq(StudentCourseHours::getSpecification, specification)
                        .eq(StudentCourseHours::getNature, nature)
                        .between(StudentCourseHours::getCreateTime, startTime, endTime)
                        .orderByDesc(StudentCourseHours::getCreateTime)
                        .list();
            }

            if (orderCourseHours.isEmpty()) {
                log.warn("未找到订单对应的课时记录，无法回退: orderId={}, studentId={}, subject={}, specification={}",
                        orders.getId(), studentId, subject, specification);
                return;
            }

            // 回退课时：删除或调整课时记录
            for (StudentCourseHours courseHours : orderCourseHours) {
                // 检查课时是否已被消费
                if (courseHours.getConsumedTotalHours().compareTo(BigDecimal.ZERO) > 0) {
                    log.warn("课时包已有消费记录，无法完全回退: courseHoursId={}, consumedHours={}",
                            courseHours.getId(), courseHours.getConsumedTotalHours());

                    // 如果有消费，只能调整剩余课时为0，不能删除记录
                    courseHours.setRemainingHours(BigDecimal.ZERO);
                    courseHours.setStatus("cancelled"); // 标记为已取消
                    studentCourseHoursService.updateById(courseHours);

                    // 同时需要取消相关的课消记录
                    cancelCourseConsumptions(courseHours.getId(), "订单退款课时回退");

                } else {
                    // 如果没有消费，直接删除课时记录
                    studentCourseHoursService.removeById(courseHours.getId());
                    log.info("删除未消费的课时记录: courseHoursId={}", courseHours.getId());
                }
            }

            log.info("课时回退完成: orderId={}, 处理课时记录数={}", orders.getId(), orderCourseHours.size());

        } catch (Exception e) {
            log.error("课时回退失败: orderId={}", orders.getId(), e);
            // 课时回退失败不应该影响订单取消/退款的主流程，只记录错误日志
        }
    }

    /**
     * 取消课消记录
     */
    private void cancelCourseConsumptions(String courseHoursId, String reason) {
        try {
            // 查找该课时包的所有课消记录
            List<StudentCourseConsumption> consumptions = studentCourseConsumptionService.lambdaQuery()
                    .eq(StudentCourseConsumption::getCourseHoursId, courseHoursId)
                    .eq(StudentCourseConsumption::getStatus, "active")
                    .list();

            for (StudentCourseConsumption consumption : consumptions) {
                consumption.setStatus("cancelled");
                consumption.setRemark(consumption.getRemark() + " [" + reason + "]");
                studentCourseConsumptionService.updateById(consumption);
            }

            log.info("取消课消记录完成: courseHoursId={}, 取消记录数={}", courseHoursId, consumptions.size());

        } catch (Exception e) {
            log.error("取消课消记录失败: courseHoursId={}", courseHoursId, e);
        }
    }

    @Override
    public void refundOrder(String orderId, Long refundAmount, String refundReason) {
        Orders orders = this.getById(orderId);
        if(orders == null) {
            throw new IllegalArgumentException("订单不存在");
        }

        // 检查订单状态
        if(!List.of(OrderConstants.OrderStatus.PAID, OrderConstants.OrderStatus.FULL_PAID, OrderConstants.OrderStatus.PART_PAID)
                .contains(orders.getOrderStatus())) {
            throw new IllegalArgumentException("当前订单状态不支持退款操作");
        }

        // 检查退款金额
        if(refundAmount <= 0) {
            throw new IllegalArgumentException("退款金额必须大于0");
        }

        if(refundAmount > orders.getAmtPaid()) {
            throw new IllegalArgumentException("退款金额不能大于已支付金额");
        }

        // 获取已支付的交易流水
        List<OrdersTrx> paidTrxes = ordersTrxService.lambdaQuery()
                .eq(OrdersTrx::getOrderId, orderId)
                .eq(OrdersTrx::getTrxType, OrderConstants.TrxType.PAY)
                .ne(OrdersTrx::getTrxStatus, OrderConstants.TrxStatus.CANCELLED)
                .ne(OrdersTrx::getTrxStatus, OrderConstants.TrxStatus.REFUNDED)
                .list();

        if(paidTrxes.isEmpty()) {
            throw new IllegalArgumentException("未找到可退款的支付记录");
        }

        // 创建退款记录
        String operatorId = SecurityUtils.getUserId().toString();
        String operatorName = SecurityUtils.getUsername();
        String refundType = (refundAmount.equals(orders.getAmtPaid())) ?
                OrderConstants.RefundType.FULL : OrderConstants.RefundType.PARTIAL;

        OrderRefundRecord refundRecord = orderRefundRecordService.createRefundRecord(
                orders, paidTrxes.get(0), null, refundType, refundAmount, refundReason, operatorId, operatorName);

        // 执行退款
        long remainingRefundAmount = refundAmount;
        for(OrdersTrx paidTrx : paidTrxes) {
            if(remainingRefundAmount <= 0) {
                break;
            }

            Long currentRefundAmount = Math.min(remainingRefundAmount, paidTrx.getTrxAmt());

            try {
                // 调用支付平台退款接口
                RefundDto.Req refundReq = new RefundDto.Req();
                refundReq.setOrderId(orderId);
                refundReq.setAmount(currentRefundAmount.intValue());
                refundReq.setRemark(refundReason);

                allinPayService.refund(refundReq);

                // 创建退款交易记录
                OrdersTrx refundTrx = createRefundTrxRecord(orders, paidTrx, currentRefundAmount, refundReason);

                // 更新退款记录的交易流水ID
                if (refundRecord.getRefundTrxId() == null) {
                    orderRefundRecordService.updatePlatformInfo(refundRecord.getId(),
                            refundTrx.getId(), "退款交易流水ID: " + refundTrx.getId());
                }

                remainingRefundAmount -= currentRefundAmount;

                log.info("执行退款成功: orderTrxId={}, refundAmount={}", paidTrx.getId(), currentRefundAmount);

            } catch (Exception e) {
                log.error("执行退款失败: orderTrxId={}, refundAmount={}", paidTrx.getId(), currentRefundAmount, e);

                // 更新退款记录状态为失败
                orderRefundRecordService.updateRefundStatus(refundRecord.getId(),
                        OrderConstants.RefundStatus.FAILED, e.getMessage());

                throw new RuntimeException("退款失败: " + e.getMessage());
            }
        }

        // 更新退款记录状态为成功
        orderRefundRecordService.updateRefundStatus(refundRecord.getId(),
                OrderConstants.RefundStatus.SUCCESS, null);

        // 更新订单金额信息
        orders.setAmtPaid(orders.getAmtPaid() - refundAmount);
        orders.setAmtUnpaid(orders.getAmtUnpaid() + refundAmount);

        // 更新订单状态
        if(orders.getAmtPaid() <= 0) {
            orders.setOrderStatus(OrderConstants.OrderStatus.REFUND);
            // 全额退款时回退课时
            rollbackCourseHours(orders);
        } else {
            orders.setOrderStatus(OrderConstants.OrderStatus.PART_PAID);
        }

        this.updateById(orders);

        log.info("订单退款成功: orderId={}, refundAmount={}, newAmtPaid={}",
                orderId, refundAmount, orders.getAmtPaid());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void fullRefundOrder(String orderId, String refundReason) {
        Orders orders = this.getById(orderId);
        if(orders == null) {
            throw new IllegalArgumentException("订单不存在");
        }

        if(orders.getAmtPaid() <= 0) {
            throw new IllegalArgumentException("订单无已支付金额，无需退款");
        }

        // 调用部分退款方法，退款金额为已支付金额
        refundOrder(orderId, orders.getAmtPaid(), refundReason);
    }

    @Override
    public Page<OrderPageDto.Resp> selectOrdersByParam(OrderPageDto.Req req) {
        Page<OrderPageDto.Resp> pageParam = new Page<>(req.getPageNum(), req.getPageSize());
        return this.baseMapper.selectOrdersByParam(pageParam, req);
    }

    @Override
    public List<OrderExportDto.ExportResp> exportOrders(OrderExportDto.ExportReq req) {
        log.info("开始导出订单数据: req={}", req);

        // 构建查询条件
        LambdaQueryWrapper<Orders> queryWrapper = buildOrderExportQuery(req);

        // 限制导出数量
        int maxCount = req.getMaxExportCount() != null ? req.getMaxExportCount() : 10000;
        queryWrapper.last("LIMIT " + maxCount);

        // 查询订单数据
        List<Orders> orders = this.list(queryWrapper);

        if (CollUtil.isEmpty(orders)) {
            log.info("没有符合条件的订单数据");
            return new ArrayList<>();
        }

        log.info("查询到订单数据: count={}", orders.size());

        // 转换为导出格式
        List<OrderExportDto.ExportResp> exportData = new ArrayList<>();
        for (Orders order : orders) {
            OrderExportDto.ExportResp exportResp = convertToExportResp(order, req);
            exportData.add(exportResp);
        }

        log.info("订单数据导出完成: exportCount={}", exportData.size());
        return exportData;
    }

    /**
     * 构建订单导出查询条件
     */
    private LambdaQueryWrapper<Orders> buildOrderExportQuery(OrderExportDto.ExportReq req) {
        LambdaQueryWrapper<Orders> queryWrapper = new LambdaQueryWrapper<>();

        // 基本查询条件
        queryWrapper.eq(StrUtil.isNotBlank(req.getOrderNo()), Orders::getNo, req.getOrderNo())
                .eq(StrUtil.isNotBlank(req.getOrderStatus()), Orders::getOrderStatus, req.getOrderStatus())
                .eq(StrUtil.isNotBlank(req.getSource()), Orders::getSource, req.getSource())
                .eq(StrUtil.isNotBlank(req.getTrxMethod()), Orders::getTrxMethod, req.getTrxMethod())
                .eq(StrUtil.isNotBlank(req.getSignStatus()), Orders::getSignStatus, req.getSignStatus())
                .ge(req.getCreateTimeStart() != null, Orders::getCreateTime, req.getCreateTimeStart())
                .le(req.getCreateTimeEnd() != null, Orders::getCreateTime, req.getCreateTimeEnd())
                .ge(req.getLastPayTimeStart() != null, Orders::getLastPayTime, req.getLastPayTimeStart())
                .le(req.getLastPayTimeEnd() != null, Orders::getLastPayTime, req.getLastPayTimeEnd())
                .ge(req.getMinTotalAmt() != null, Orders::getTotalAmt, req.getMinTotalAmt())
                .le(req.getMaxTotalAmt() != null, Orders::getTotalAmt, req.getMaxTotalAmt());

        // 学生相关条件需要关联查询，这里先简化处理
        // TODO: 如果需要按学生姓名、手机号等查询，需要使用MPJ进行关联查询

        // 排序
        queryWrapper.orderByDesc(Orders::getCreateTime);

        return queryWrapper;
    }

    /**
     * 转换为导出响应对象
     */
    private OrderExportDto.ExportResp convertToExportResp(Orders order, OrderExportDto.ExportReq req) {
        OrderExportDto.ExportResp resp = new OrderExportDto.ExportResp();

        // 基本订单信息
        resp.setOrderId(order.getId());
        resp.setOrderNo(order.getNo());
        resp.setSource(order.getSource());
        resp.setBody(order.getBody());
        resp.setOrderStatus(order.getOrderStatus());
        resp.setTotalAmtYuan(formatAmountToYuan(order.getTotalAmt()));
        resp.setAmtPaidYuan(formatAmountToYuan(order.getAmtPaid()));
        resp.setAmtUnpaidYuan(formatAmountToYuan(order.getAmtUnpaid()));
        resp.setTrxMethod(order.getTrxMethod());
        resp.setSignStatus(order.getSignStatus());
        resp.setLastPayTime(order.getLastPayTime());
        resp.setCreateTime(order.getCreateTime());
        resp.setUpdateTime(order.getUpdateTime());
        resp.setRemark(order.getRemark());

        // 产品信息
        Product product = order.getProducts();
        if (product != null) {
            resp.setProductName(product.getName());
            resp.setProductType(product.getType());
            resp.setSubject(product.getSubject());
            resp.setCourseType(product.getCourseType());
            resp.setQuantity(product.getQuantity() != null ? product.getQuantity().toString() : "");
            resp.setBonusHours(product.getBonusHoursQuantity() != null ? product.getBonusHoursQuantity().toString() : "0");
            resp.setUnitPriceYuan(formatAmountToYuan(product.getUnitPrice()));
            resp.setOriginalPriceYuan(formatAmountToYuan(product.getOriginalPrice()));
            resp.setSellingPriceYuan(formatAmountToYuan(product.getSellingPrice()));
        }

        // 学生和销售员信息需要通过关联查询获取
        // TODO: 优化为批量查询以提高性能
        try {
            // 获取学生信息
            if (StrUtil.isNotBlank(order.getStudentId())) {
                // 这里需要注入用户服务来获取学生信息
                // 暂时设置为ID，后续优化
                resp.setStudentName("学生ID:" + order.getStudentId());
                resp.setStudentPhone("");
            }

            // 获取销售员信息
            if (StrUtil.isNotBlank(order.getSalerId())) {
                resp.setSalerName("销售员ID:" + order.getSalerId());
                resp.setSalerPhone("");
            }
        } catch (Exception e) {
            log.warn("获取用户信息失败: orderId={}, error={}", order.getId(), e.getMessage());
        }

        // 统计交易记录
        if (req.getIncludeTransactions() != null && req.getIncludeTransactions()) {
            List<OrdersTrx> transactions = ordersTrxService.getByOrderId(order.getId());
            resp.setTransactionCount(transactions.size());
            resp.setPaymentCount((int) transactions.stream().filter(t -> OrderConstants.TrxType.PAY.equals(t.getTrxType())).count());
            resp.setRefundCount((int) transactions.stream().filter(t -> OrderConstants.TrxType.REFUND.equals(t.getTrxType())).count());
        }

        return resp;
    }

    /**
     * 格式化金额为元（保留2位小数）
     */
    private String formatAmountToYuan(Long amountFen) {
        if (amountFen == null) {
            return "0.00";
        }
        return String.format("%.2f", amountFen / 100.0);
    }


    private void checkOrderTrxStatus(OrdersTrx ordersTrx) {
        if(ordersTrx == null) {
            throw new IllegalArgumentException("交易流水不存在");
        }
        if(!ordersTrx.getTrxStatus().equalsIgnoreCase(OrderConstants.OrderStatus.UNPAID)) {
            throw new IllegalArgumentException("交易流水状态错误");
        }
        if(ordersTrx.getTrxAmt() <= 0) {
            throw new IllegalArgumentException("交易流水金额错误");
        }
    }

    private Orders buildOrder(OrderCreateDto orderCreateDto, Product product) {
        Orders order = new Orders();
        order.setNo(orderTrxCodeUtil.generalOrderCode(OrderConstants.TrxType.PAY));
        order.setSource(OrderConstants.Source.SYSTEM);
        order.setOrderStatus(OrderConstants.OrderStatus.UNPAID);

        order.setProducts(product);
        order.setProductId(product.getId());

        order.setTotalAmt(product.getSellingPrice());
        order.setBody(product.getName());
        order.setRemark(product.getName() + "待支付订单");

        order.setStudentId(orderCreateDto.getStudentId());
        order.setSalerId(String.valueOf(SecurityUtils.getUserId()));
        order.setAmtPaid(0L);
        order.setAmtUnpaid(order.getTotalAmt());
        order.setSignStatus(OrderConstants.SignStatus.UN_SIGN);
        order.setTrxMethod(CollUtil.isEmpty(orderCreateDto.getMultiTrxAmts()) ? "一次性支付" : "分期支付");
        return order;
    }

    @Override
    public PaymentDto.PayResp generatePayment(String orderTrxId) {
        OrdersTrx ordersTrx = ordersTrxService.getById(orderTrxId);
        checkOrderTrxStatus(ordersTrx);

        // 获取订单信息
        Orders orders = this.getById(ordersTrx.getOrderId());
        if(orders == null) {
            throw new IllegalArgumentException("订单不存在");
        }

        try {
            NativePayOutput nativePayOutput = allinPayService.nativepay(PayDto.NativePayReq.builder()
                    .cusTrxSeq(ordersTrx.getCusTrxSeq())
                    .body(orders.getBody()).remark(orders.getRemark())
                    .trxamt(ordersTrx.getTrxAmt())
                    .build());

            if(!nativePayOutput.isSuccess()) {
                throw new RuntimeException("生成支付信息失败: " + nativePayOutput.getRetmsg());
            }

            PaymentDto.PayResp payResp = new PaymentDto.PayResp();
            payResp.setPayInfo(nativePayOutput.getPayinfo());
            payResp.setPayUrl(nativePayOutput.getPayinfo());
            payResp.setOrderTrxId(orderTrxId);
            payResp.setCusTrxSeq(ordersTrx.getCusTrxSeq());
            payResp.setTrxAmt(ordersTrx.getTrxAmt());

            return payResp;
        } catch (Exception e) {
            log.error("生成支付信息异常", e);
            throw new RuntimeException("生成支付信息失败: " + e.getMessage());
        }
    }

    @Override
    public PaymentDto.QRCodeResp generateQRCode(String orderTrxId) {
        PaymentDto.PayResp payResp = generatePayment(orderTrxId);

        try {
            // 生成二维码
            byte[] qrCode = qrCodeService.generateQRCode(payResp.getPayInfo(), 200, 200);
            String qrCodeBase64 = java.util.Base64.getEncoder().encodeToString(qrCode);

            PaymentDto.QRCodeResp qrCodeResp = new PaymentDto.QRCodeResp();
            qrCodeResp.setQrCodeBase64(qrCodeBase64);
            qrCodeResp.setPayUrl(payResp.getPayUrl());

            return qrCodeResp;
        } catch (Exception e) {
            log.error("生成二维码异常", e);
            throw new RuntimeException("生成二维码失败: " + e.getMessage());
        }
    }

}