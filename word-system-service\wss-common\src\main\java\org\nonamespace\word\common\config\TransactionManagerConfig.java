//package org.nonamespace.word.common.config;
//
//import jakarta.annotation.Resource;
//import org.aspectj.lang.annotation.Aspect;
//import org.springframework.aop.Advisor;
//import org.springframework.aop.aspectj.AspectJExpressionPointcut;
//import org.springframework.aop.support.DefaultPointcutAdvisor;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.transaction.PlatformTransactionManager;
//import org.springframework.transaction.TransactionDefinition;
//import org.springframework.transaction.interceptor.*;
//
//import java.util.Collections;
//import java.util.HashMap;
//import java.util.Map;
//
//@Aspect
//@Configuration
//public class TransactionManagerConfig {
//
//    private static final int TX_METHOD_TIMEOUT=5;
//    private static final String AOP_POINTCUT_EXPRESSION="execution(* org.nonamespace..service..*(..)) || execution(* org.nonamespace..facade..*(..)) || execution(* com.ruoyi..service..*(..))";
//
//
//    @Resource
//    private PlatformTransactionManager transactionManager;
//
//
//    @Bean
//    public TransactionInterceptor TxAdvice(){
//        NameMatchTransactionAttributeSource source = new NameMatchTransactionAttributeSource();
//        RuleBasedTransactionAttribute readOnlyRule = new RuleBasedTransactionAttribute();
//        readOnlyRule.setReadOnly(true);
//        readOnlyRule.setPropagationBehavior(TransactionDefinition.PROPAGATION_NOT_SUPPORTED);
//
//        RuleBasedTransactionAttribute requireRule = new RuleBasedTransactionAttribute();
//        requireRule.setRollbackRules(Collections.singletonList(new RollbackRuleAttribute(Exception.class)));
//        requireRule.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
//        requireRule.setTimeout(TX_METHOD_TIMEOUT);
//        Map<String, TransactionAttribute> txMap=new HashMap<>();
//
//        txMap.put("add*", requireRule);
//        txMap.put("save*", requireRule);
//        txMap.put("create*", requireRule);
//        txMap.put("insert*", requireRule);
//
//        txMap.put("update*", requireRule);
//        txMap.put("edit*", requireRule);
//
//        txMap.put("del*", requireRule);
//        txMap.put("delete*", requireRule);
//        txMap.put("remove*", requireRule);
//
//        txMap.put("get*",readOnlyRule);
//        txMap.put("query*", readOnlyRule);
//        txMap.put("find*", readOnlyRule);
//        txMap.put("select*", readOnlyRule);
//        source.setNameMap(txMap);
//        return new TransactionInterceptor(transactionManager, source);
//    }
//
//    @Bean
//    public Advisor txAdviceAdvisor(){
//        AspectJExpressionPointcut pointcut=new AspectJExpressionPointcut();
//        /*声明和设置需要拦截的方法,用切点语言描写*/
//        pointcut.setExpression(AOP_POINTCUT_EXPRESSION);
//        /*设置切面=切点pointcut+通知TxAdvice*/
//        return new DefaultPointcutAdvisor(pointcut, TxAdvice());
//    }
//
//}
