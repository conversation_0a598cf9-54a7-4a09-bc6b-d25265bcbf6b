package org.nonamespace.word.server.service.impl;


import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.page.PageDomain;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.utils.sql.SqlUtil;
import com.github.pagehelper.Page;

public class BaseServiceImpl<E>{

    public Page<E> startPage() {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
        Boolean reasonable = pageDomain.getReasonable();
        return (Page<E>)PageHelper.startPage(pageNum, pageSize, orderBy).setReasonable(reasonable);
    }

    public Page<E> getPage() {
        Page<Object> page = PageHelper.getLocalPage();
        if(page == null){
            return startPage();
        }

        return (Page<E>) page;
    }
}
