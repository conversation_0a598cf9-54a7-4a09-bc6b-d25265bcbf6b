package org.nonamespace.word.server.jobs;

import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.service.IWxSendMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 用于课程提醒定时任务
 *
 * <AUTHOR>
 * @date 2025/6/7 11:23
 */
@Slf4j
@Component("CourseRemindJobTask")
@ConditionalOnProperty(prefix = "course.remind.job", name = "enabled", havingValue = "true", matchIfMissing = true)
public class CourseRemindJobTask extends BaseJobTask {

    @Autowired
    private IWxSendMessageService wxSendMessageService;

    @Autowired
    public CourseRemindJobTask(StringRedisTemplate stringRedisTemplate) {
        super(stringRedisTemplate);
    }

    /**
     * 每天晚上20点，获取第二天的上课记录，组装模板推送记录
     * 定时任务19:58分执行，减少延迟
     */
    @Scheduled(cron = "0 58 19 * * ?")
    public void remindTomorrow() {
        log.info("[第二天课程推送] - 上课推送记录定时任务开始");
        executeJob("CourseRemindJobTask-remindTomorrow", (Void) -> {
            try {
                wxSendMessageService.buildTomorrowCourseWxMessage();
            } catch (Exception e) {
                log.error("[第二天课程推送] - 上课推送记录定时任务异常", e);
            }
        }, 10, TimeUnit.MINUTES);
    }


//    /**
//     * 每天晚上20点，获取创建时间为20-24点的，且开课时间大于now，且小于第二天24点的。状态为待开始的。
//     * 定时任务20-24点开始，每一分钟推送一次
//     */
//    @Scheduled(cron = "0 */1 20-23 * * ?")
//    public void remindAfter20Modify() {
//        log.info("[20点后调课实时推送] - 上课推送记录定时任务开始");
//        executeJob("CourseRemindJobTask-remindAfter20Modify", (Void) -> {
//            try {
//                wxSendMessageService.buildAfter20ModifyCourseList();
//            } catch (Exception e) {
//                log.error("[20点后调课实时推送] - 上课推送记录定时任务异常", e);
//            }
//        }, 10, TimeUnit.MINUTES);
//    }


    /**
     * 每天4点-23点，每隔2分钟执行一次
     *  拉取当前课前12分钟的课程。或者当天，20点过后创建的课程
     */
    @Scheduled(cron = "0 */2 * * * ?")
    public void remindToday() {
        log.info("[当天课程推送] - 上课推送记录定时任务开始");
        executeJob("CourseRemindJobTask-remindToday", (Void) -> {
            try {
                wxSendMessageService.buildToday12MinCourseList();
            } catch (Exception e) {
                log.error("[当天课程推送] - 上课推送记录定时任务异常", e);
            }
        }, 2, TimeUnit.MINUTES);
    }

}
