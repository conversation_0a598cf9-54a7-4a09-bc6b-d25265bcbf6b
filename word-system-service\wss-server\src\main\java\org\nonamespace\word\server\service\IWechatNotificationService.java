package org.nonamespace.word.server.service;

/**
 * 微信通知服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-29
 */
public interface IWechatNotificationService {

    /**
     * 发送预约课申请相关的微信通知
     * 
     * @param applicationId 申请ID
     * @param notificationType 通知类型
     * @return 是否发送成功
     */
    boolean sendCourseBookingNotification(String applicationId, String notificationType);

    /**
     * 批量发送通知
     * 
     * @param notificationType 通知类型
     * @param applicationIds 申请ID列表
     * @return 发送成功的数量
     */
    int batchSendNotifications(String notificationType, String... applicationIds);
}
