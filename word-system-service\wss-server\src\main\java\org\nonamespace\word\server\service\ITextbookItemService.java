package org.nonamespace.word.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.nonamespace.word.server.domain.TextbookItem;

/**
 * 教材项服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface ITextbookItemService extends IService<TextbookItem> {

    /**
     * 对比教材内容并更新数据库
     *
     * @param textbookId      教材ID
     * @param textbookContent 教材文本内容
     */
    void compareTextbookItem(String textbookId, String textbookContent);

    void updateDisplayOrder(String textbookId);
}
