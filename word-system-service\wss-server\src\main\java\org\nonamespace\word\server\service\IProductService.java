package org.nonamespace.word.server.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.nonamespace.word.server.domain.Product;
import org.nonamespace.word.server.dto.product.ProductDto;

import java.util.List;

/**
 * 产品信息Service接口
 * 使用MyBatis-Plus LambdaWrapper，不使用XML
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface IProductService extends IService<Product> {

    /**
     * 分页查询产品列表
     *
     * @param req 查询请求
     * @return 产品分页列表
     */
    IPage<ProductDto.BasicResp> getProductPage(ProductDto.GetListReq req);

    /**
     * 查询产品详细信息
     * 
     * @param productId 产品ID
     * @return 产品详细信息
     */
    ProductDto.DetailResp getProductDetail(String productId);

    /**
     * 创建产品
     * 
     * @param req 创建请求
     * @return 产品ID
     */
    String createProduct(ProductDto.CreateReq req);

    /**
     * 更新产品信息
     * 
     * @param req 更新请求
     * @return 是否成功
     */
    boolean updateProduct(ProductDto.UpdateReq req);

    /**
     * 删除产品
     * 
     * @param productId 产品ID
     * @return 是否成功
     */
    boolean deleteProduct(String productId);

    /**
     * 批量删除产品
     * 
     * @param productIds 产品ID列表
     * @return 是否成功
     */
    boolean deleteProducts(List<String> productIds);

    /**
     * 查询所有上架的产品列表
     * 
     * @return 产品列表
     */
    List<ProductDto.BasicResp> getAvailableProducts();

    /**
     * 根据类型查询产品列表
     * 
     * @param type 产品类型
     * @return 产品列表
     */
    List<ProductDto.BasicResp> getProductsByType(String type);

    /**
     * 根据学科查询产品列表
     * 
     * @param subject 学科
     * @return 产品列表
     */
    List<ProductDto.BasicResp> getProductsBySubject(String subject);

    /**
     * 根据标签查询产品列表
     * 
     * @param tag 标签
     * @return 产品列表
     */
    List<ProductDto.BasicResp> getProductsByTag(String tag);

    /**
     * 查询热门产品列表
     * 
     * @param limit 限制数量
     * @return 产品列表
     */
    List<ProductDto.BasicResp> getHotProducts(Integer limit);

    /**
     * 上架产品
     * 
     * @param productId 产品ID
     * @return 是否成功
     */
    boolean enableProduct(String productId);

    /**
     * 下架产品
     * 
     * @param productId 产品ID
     * @return 是否成功
     */
    boolean disableProduct(String productId);

    /**
     * 更新产品销售数量
     * 
     * @param productId 产品ID
     * @param quantity 销售数量
     * @return 是否成功
     */
    boolean updateSalesCount(String productId, Integer quantity);

    /**
     * 更新产品库存
     * 
     * @param productId 产品ID
     * @param quantity 库存变化数量（正数增加，负数减少）
     * @return 是否成功
     */
    boolean updateStock(String productId, Integer quantity);

    /**
     * 检查产品库存是否充足
     * 
     * @param productId 产品ID
     * @param quantity 需要数量
     * @return 是否充足
     */
    boolean checkStock(String productId, Integer quantity);
}
