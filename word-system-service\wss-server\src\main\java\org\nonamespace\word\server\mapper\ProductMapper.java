package org.nonamespace.word.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.nonamespace.word.server.domain.Product;

/**
 * 产品信息Mapper接口
 * 使用MyBatis-Plus LambdaWrapper，不使用XML
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Mapper
public interface ProductMapper extends BaseMapper<Product> {
    // 所有操作都通过MyBatis-Plus的LambdaWrapper实现
    // 不需要定义额外的方法
}
