package org.nonamespace.common.util;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

public class ConvertPageUtils {
    private ConvertPageUtils() {
    }

    public static <T, R> Page<R> convertToPage(Page<T> originalPage, Function<T, R> converter) {
        List<T> records = originalPage.getRecords();

        Page<R> convertedPage = new Page<>();
        BeanUtils.copyProperties(originalPage, convertedPage);
        if (CollectionUtils.isEmpty(records)) {
            return convertedPage;
        }

        List<R> convertedRecords = new ArrayList<>();
        records.stream().forEach(item -> {
            R convertedRecord = converter.apply(item);
            convertedRecords.add(convertedRecord);
        });
        convertedPage.setRecords(convertedRecords);

        return convertedPage;
    }
}
