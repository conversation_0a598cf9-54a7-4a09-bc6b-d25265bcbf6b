package org.nonamespace.word.server.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 教材结构模型类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TextbookStructure {
    private String name;
    private String text;
    private String type;
    private List<TextbookUnit> units = new ArrayList<>();
}
