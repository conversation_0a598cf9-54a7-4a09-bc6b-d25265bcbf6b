package org.nonamespace.word.server.domain;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nonamespace.word.server.entity.DataEntity;

import java.util.Date;

/**
 * 课程环节对象 course_section
 * 
 * <AUTHOR>
 * @date 2025-05-20
 */

@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "course_section", autoResultMap = true)
public class CourseSection extends DataEntity {


    /** 所属课程ID */
    @Excel(name = "所属课程ID")
    private String courseId;

    /** 环节标题 */
    @Excel(name = "环节标题")
    private String title;

    /** 环节类型 (例如: 学习,抗遗忘复习, 下课前复习) */
    @Excel(name = "环节类型 (例如: 学习,抗遗忘复习, 下课前复习)")
    private String type;

    /** 环节状态 (待开始, 进行中 , 已完成) */
    @Excel(name = "环节状态 (待开始, 进行中 , 已完成)")
    private String status;

    /** 环节在课程中的顺序 */
    @Excel(name = "环节在课程中的顺序")
    private Long orderIndex;

    /** 环节开始时间 */
    @Excel(name = "环节开始时间")
    private Date startTime;

    /** 环节结束时间 */
    @Excel(name = "环节结束时间")
    private Date endTime;

    /** 当前单词所在环节索引 */
    @Excel(name = "当前单词所在环节索引")
    private Long currentWordIndex;

    /** 当前单词在环节单词进度列表中的索引 (从0开始) */
    @Excel(name = "当前单词在环节单词进度列表中的索引 (从0开始)")
    private Long currentStepIndex;

    /** 关联的课表ID */
    @Excel(name = "关联的课表ID")
    private String textbookId;

    /** 关联的课本单元ID (适用于学习环节) */
    @Excel(name = "关联的课本单元ID (适用于学习环节)")
    private String textbookUnitId;

    /** 关联的复习计划ID (适用于抗遗忘复习环节) */
    @Excel(name = "关联的复习计划ID (适用于抗遗忘复习环节)")
    private String reviewScheduleId;

    /** 环节单词信息 */
    @Excel(name = "环节单词信息")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONObject words = new JSONObject();

    private Long statTotalWords; // 环节单词总数
    private Long statLearnedWords; // 已学习单词数
    private Long statCorrectWords; // 当前单词数
    private Long statErrorWords; // 错误单词数
}
