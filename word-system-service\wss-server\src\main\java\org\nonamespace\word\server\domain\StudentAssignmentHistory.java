package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nonamespace.word.server.entity.DataEntity;

import java.util.Date;

/**
 * 学生分配历史表
 * 用于记录学生分配给销售人员的历史记录
 * 
 * <AUTHOR>
 * @date 2025-07-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "student_assignment_history", autoResultMap = true)
public class StudentAssignmentHistory extends DataEntity {

    /**
     * 学生ID
     */
    @TableField("student_id")
    private String studentId;

    /**
     * 操作类型 (ASSIGN: 分配, UNASSIGN: 取消分配, TRANSFER: 转移)
     */
    @TableField("operation_type")
    private String operationType;

    /**
     * 原销售人员ID
     */
    @TableField("from_sales_id")
    private String fromSalesId;

    /**
     * 目标销售人员ID
     */
    @TableField("to_sales_id")
    private String toSalesId;

    /**
     * 原销售组ID
     */
    @TableField("from_sales_group_id")
    private String fromSalesGroupId;

    /**
     * 目标销售组ID
     */
    @TableField("to_sales_group_id")
    private String toSalesGroupId;

    /**
     * 操作原因
     */
    @TableField("reason")
    private String reason;

    /**
     * 操作时间
     */
    @TableField("operation_time")
    private Date operationTime;

    /**
     * 操作人ID
     */
    @TableField("operator_id")
    private String operatorId;
}
