package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nonamespace.word.server.entity.DataEntity;

/**
 * //TODO
 *
 * <AUTHOR>
 * @date 2025/6/4 14:06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "sys_user_openid", autoResultMap = true)
public class SysUserOpenid extends DataEntity {

    private String userId;
    private String userType;
    private String userName;
    private String phonenumber;
    private String openid;
    private String unionid;
}
