<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.nonamespace.word.server.mapper.order.OrderRefundRecordMapper">

    <resultMap type="org.nonamespace.word.server.dto.order.RefundRecordDto$Resp" id="RefundRecordRespResult">
        <result property="id" column="id"/>
        <result property="orderId" column="order_id"/>
        <result property="refundNo" column="refund_no"/>
        <result property="refundType" column="refund_type"/>
        <result property="refundTypeDesc" column="refund_type_desc"/>
        <result property="refundAmountYuan" column="refund_amount_yuan"/>
        <result property="refundReason" column="refund_reason"/>
        <result property="refundStatus" column="refund_status"/>
        <result property="refundStatusDesc" column="refund_status_desc"/>
        <result property="products" column="products"/>
        <result property="orders" column="orders"/>
        <result property="ordersTrxs" column="orders_trxs"/>
        <result property="refundMethod" column="refund_method"/>
        <result property="refundMethodDesc" column="refund_method_desc"/>
        <result property="refundTime" column="refund_time"/>
        <result property="createTime" column="create_time"/>
        <result property="errorMessage" column="error_message"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <resultMap type="org.nonamespace.word.server.dto.order.RefundRecordDto$DetailResp" id="RefundRecordDetailResult">
        <result property="id" column="id"/>
        <result property="orderId" column="order_id"/>
        <result property="refundNo" column="refund_no"/>
        <result property="originalTrxId" column="original_trx_id"/>
        <result property="refundTrxId" column="refund_trx_id"/>
        <result property="refundType" column="refund_type"/>
        <result property="refundTypeDesc" column="refund_type_desc"/>
        <result property="refundAmountYuan" column="refund_amount_yuan"/>
        <result property="refundReason" column="refund_reason"/>
        <result property="refundStatus" column="refund_status"/>
        <result property="refundStatusDesc" column="refund_status_desc"/>
        <result property="products" column="products"/>
        <result property="orders" column="orders"/>
        <result property="ordersTrxs" column="orders_trxs"/>
        <result property="refundMethod" column="refund_method"/>
        <result property="refundMethodDesc" column="refund_method_desc"/>
        <result property="platformRefundId" column="platform_refund_id"/>
        <result property="platformResponse" column="platform_response"/>
        <result property="refundTime" column="refund_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="errorMessage" column="error_message"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <select id="selectRefundRecordsByParam" parameterType="org.nonamespace.word.server.dto.order.RefundRecordDto$QueryReq" resultMap="RefundRecordRespResult">
        SELECT
            r.id,
            r.order_id,
            r.refund_no,
            r.refund_type,
            CASE r.refund_type
                WHEN 'partial' THEN '部分退款'
                WHEN 'full' THEN '全额退款'
                ELSE r.refund_type
            END as refund_type_desc,
            CONCAT(FORMAT(r.refund_amount / 100, 2)) as refund_amount_yuan,
            r.refund_reason,
            r.refund_status,
            CASE r.refund_status
                WHEN 'processing' THEN '处理中'
                WHEN 'success' THEN '成功'
                WHEN 'failed' THEN '失败'
                ELSE r.refund_status
            END as refund_status_desc,
            r.products,
            r.orders,
            r.orders_trxs,
            r.refund_method,
            CASE r.refund_method
                WHEN 'original' THEN '原路退回'
                WHEN 'manual' THEN '手动退款'
                ELSE r.refund_method
            END as refund_method_desc,
            r.refund_time,
            r.create_time,
            r.error_message,
            r.remark
        FROM order_refunds r
        WHERE r.deleted = false
        <if test="req.refundNo != null and req.refundNo != ''">
            AND r.refund_no LIKE CONCAT('%', #{req.refundNo}::text, '%')
        </if>
        <if test="req.orderId != null and req.orderId != ''">
            AND r.order_id = #{req.orderId}
        </if>
        <if test="req.refundType != null and req.refundType != ''">
            AND r.refund_type = #{req.refundType}
        </if>
        <if test="req.refundStatus != null and req.refundStatus != ''">
            AND r.refund_status = #{req.refundStatus}
        </if>
        <if test="req.studentId != null and req.studentId != ''">
            AND r.student_id = #{req.studentId}
        </if>
        <if test="req.salerId != null and req.salerId != ''">
            AND r.saler_id = #{req.salerId}
        </if>
        <if test="req.productId != null and req.productId != ''">
            AND r.product_id = #{req.productId}
        </if>
        <if test="req.refundTimeStart != null">
            AND r.refund_time &gt;= #{req.refundTimeStart}
        </if>
        <if test="req.refundTimeEnd != null">
            AND r.refund_time &lt;= #{req.refundTimeEnd}
        </if>
        <if test="req.createTimeStart != null">
            AND r.create_time &gt;= #{req.createTimeStart}
        </if>
        <if test="req.createTimeEnd != null">
            AND r.create_time &lt;= #{req.createTimeEnd}
        </if>
        <if test="req.minRefundAmount != null">
            AND r.refund_amount &gt;= #{req.minRefundAmount}
        </if>
        <if test="req.maxRefundAmount != null">
            AND r.refund_amount &lt;= #{req.maxRefundAmount}
        </if>
        ORDER BY
        <choose>
            <when test="req.orderBy == 'refund_time'">r.refund_time</when>
            <when test="req.orderBy == 'refund_amount'">r.refund_amount</when>
            <when test="req.orderBy == 'approval_time'">r.approval_time</when>
            <otherwise>r.create_time</otherwise>
        </choose>
        <choose>
            <when test="req.orderDirection == 'ASC'">ASC</when>
            <otherwise>DESC</otherwise>
        </choose>
    </select>

    <select id="selectRefundRecordDetail" parameterType="string" resultMap="RefundRecordDetailResult">
        SELECT
            r.*,
            CASE r.refund_type
                WHEN 'partial' THEN '部分退款'
                WHEN 'full' THEN '全额退款'
                ELSE r.refund_type
            END as refund_type_desc,
            CONCAT(FORMAT(r.refund_amount / 100, 2)) as refund_amount_yuan,
            CASE r.refund_status
                WHEN 'processing' THEN '处理中'
                WHEN 'success' THEN '成功'
                WHEN 'failed' THEN '失败'
                ELSE r.refund_status
            END as refund_status_desc,
            CASE r.refund_method
                WHEN 'original' THEN '原路退回'
                WHEN 'manual' THEN '手动退款'
                ELSE r.refund_method
            END as refund_method_desc
        FROM order_refunds r
        WHERE r.id = #{refundRecordId} AND r.deleted = false
    </select>
</mapper>