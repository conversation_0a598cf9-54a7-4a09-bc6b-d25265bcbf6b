package org.nonamespace.word.server.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.nonamespace.word.server.domain.Course;
import org.nonamespace.word.server.dto.course.CourseSendMessageDto;

import java.util.List;

/**
 * 课程Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Mapper
public interface CourseMapper extends MPJBaseMapper<Course> {

    /**
     *
     * @return
     */
    List<Course> getTomorrowCourseList();

    /**
     *
     * @return
     */
    List<Course> getToday12MinCourseList();

    List<Course> getAfter20ModifyCourseList();

}
