<template>
  <div class="product-management-container">
    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline label-width="80px">
        <el-form-item label="产品名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入产品名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="学科" prop="subject">
          <el-select
            v-model="searchForm.subject"
            placeholder="请选择学科"
            clearable
            style="width: 120px"
            @change="handleSubjectChange"
          >
            <el-option label="英语" value="英语" />
            <el-option label="语文" value="语文" />
            <el-option label="数学" value="数学" />
            <el-option label="物理" value="物理" />
            <el-option label="化学" value="化学" />
          </el-select>
        </el-form-item>
        <el-form-item label="课型" prop="courseType">
          <el-select
            v-model="searchForm.courseType"
            placeholder="请选择课型"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="option in availableSpecifications"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="上架" value="上架" />
            <el-option label="下架" value="下架" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增产品
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 产品列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="productList"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="产品名称" prop="name" min-width="150" />
        <el-table-column label="学科" prop="subject" width="100" align="center" />
        <el-table-column label="课型" prop="courseType" width="120" align="center" />
        <el-table-column label="适用年级" prop="applicableGrades" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-if="row.applicableGrades && row.applicableGrades.length">
              {{ row.applicableGrades.join(', ') }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="单价" prop="unitPrice" width="100" align="center">
          <template #default="{ row }">
            ¥{{ (row.unitPrice / 100).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="数量" prop="quantity" width="80" align="center" />
        <el-table-column label="原价" prop="originalPrice" width="100" align="center">
          <template #default="{ row }">
            ¥{{ (row.originalPrice / 100).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="售价" prop="sellingPrice" width="100" align="center">
          <template #default="{ row }">
            ¥{{ (row.sellingPrice / 100).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === '上架' ? 'success' : 'danger'" size="small">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="createTime" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              link
              @click="handleUpdate(row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="row.status === '下架'"
              type="success"
              link
              @click="handleEnable(row)"
            >
              上架
            </el-button>
            <el-button
              v-if="row.status === '上架'"
              type="warning"
              link
              @click="handleDisable(row)"
            >
              下架
            </el-button>
            <el-button
              type="danger"
              link
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.pageNum"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑产品对话框 -->
    <CreateEditProductDialog
      v-model="dialogVisible"
      :product="currentProduct"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup lang="ts" name="ProductManagement">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/date.js'
import {
  getProductListApi,
  getProductDetailApi,
  deleteProductApi,
  enableProductApi,
  disableProductApi
} from '@/api/management/product'
import CreateEditProductDialog from './components/CreateEditProductDialog.vue'

// 响应式数据
const loading = ref(false)
const productList = ref([])
const dialogVisible = ref(false)
const currentProduct = ref(null)

// 课型选项配置
const allSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" },
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

const englishSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" }
]

const otherSubjectSpecifications = [
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

// 可用的课型选项
const availableSpecifications = ref(allSpecifications)

// 搜索表单
const searchForm = reactive({
  name: '',
  courseType: '',
  subject: '',
  status: ''
})

// 分页数据
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 学科变化处理
const handleSubjectChange = (subject: string) => {
  searchForm.courseType = ''
  if (!subject) {
    // 没有选择学科，显示所有课型
    availableSpecifications.value = allSpecifications
  } else if (subject === '英语') {
    // 英语学科，显示英语相关课型
    availableSpecifications.value = englishSpecifications
  } else {
    // 其他学科，只显示通用课
    availableSpecifications.value = otherSubjectSpecifications
  }
}

// 方法
const fetchData = async () => {
  try {
    loading.value = true
    const queryParams = {
      ...searchForm,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    }
    const response = await getProductListApi(queryParams)
    if (response.data) {
      productList.value = response.data.records || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('获取产品列表失败:', error)
    ElMessage.error('获取产品列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.pageNum = 1
  fetchData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    courseType: '',
    subject: '',
    status: ''
  })
  handleSearch()
}

const handleAdd = () => {
  currentProduct.value = null
  dialogVisible.value = true
}

const handleUpdate = async (row) => {
  try {
    const response = await getProductDetailApi(row.id)
    if (response.data) {
      currentProduct.value = response.data
      dialogVisible.value = true
    }
  } catch (error) {
    ElMessage.error('获取产品详情失败')
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('是否确认删除该产品？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await deleteProductApi(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleEnable = async (row) => {
  try {
    await ElMessageBox.confirm('确认要上架该产品吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })
    await enableProductApi(row.id)
    ElMessage.success('上架成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('上架失败')
    }
  }
}

const handleDisable = async (row) => {
  try {
    await ElMessageBox.confirm('确认要下架该产品吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })
    await disableProductApi(row.id)
    ElMessage.success('下架成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('下架失败')
    }
  }
}

const handleSelectionChange = (selection) => {
  // 处理选择变化
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.pageNum = 1
  fetchData()
}

const handleCurrentChange = (page) => {
  pagination.pageNum = page
  fetchData()
}

const handleDialogSuccess = () => {
  fetchData()
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.product-management-container {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;

  .el-form {
    .el-form-item {
      margin-bottom: 16px;
    }
  }
}

.table-card {
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
