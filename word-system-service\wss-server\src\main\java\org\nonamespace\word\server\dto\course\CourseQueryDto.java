package org.nonamespace.word.server.dto.course;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import java.util.List;
import java.math.BigDecimal;

/**
 * 课程查询DTO
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
public class CourseQueryDto {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QueryRequest {
        
        // 查询条件
        private String subject; // 学科(英语)
        private String specification; // 课型(单词课、题型课)
        private String type; // 类型(学习课、复习课)
        private String courseType; // 性质(正式课、试听课)
        private List<String> courseStatuses; // 课程状态（待开始、进行中、已完成、停课、调课）
        private String teacherId; // 老师ID
        private String teacherName; // 老师姓名
        private String studentId; // 学生ID
        private String studentName; // 学生姓名
        
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date startDate; // 上课开始日期
        
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date endDate; // 上课结束日期
        
        private Boolean antiForgetCompleted; // 抗遗忘是否完成
        private List<String> exceptionTypes; // 异常类型(调课、停课、不准时、时长偏差)
        private Boolean hasCourseConsumption; // 是否产生课消
        
        // 分页参数
        private Integer pageNum = 1;
        private Integer pageSize = 10;
        private String orderBy = "scheduledStartTime";
        private String orderDirection = "desc";
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QueryResponse {
        
        private String id; // 课程ID
        
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date courseDate; // 日期
        
        private String subject; // 学科
        private String specification; // 课型
        private String type; // 类型
        private String courseType; // 性质
        private String courseStatus; // 课程状态
        private String teacherId; // 老师ID
        private String teacherName; // 老师姓名
        private String studentId; // 学生ID
        private String studentName; // 学生姓名
        
//        @JsonFormat(pattern = "HH:mm")
        private Date scheduledStartTime; // 排课上课时间
        
//        @JsonFormat(pattern = "HH:mm")
        private Date scheduledEndTime; // 排课下课时间
        
        private Long scheduledDuration; // 排课时长(分钟)
        
//        @JsonFormat(pattern = "HH:mm")
        private Date actualStartTime; // 实际上课时间
        
//        @JsonFormat(pattern = "HH:mm")
        private Date actualEndTime; // 实际下课时间
        
        private Long actualDuration; // 实际时长(分钟)
        
        // 抗遗忘完成情况
        private AntiForgetStatus antiForgetStatus;
        
        private List<String> exceptionTypes; // 异常类型
        private BigDecimal courseConsumption; // 课消(小时)
        
        // 操作权限
        private Boolean canStart; // 可以上课
        private Boolean canCancel; // 可以停课
        private Boolean canReschedule; // 可以调课
        private Boolean canDownloadMaterial; // 可以下载资料
        private Boolean canViewReport; // 可以查看报告
        private Boolean canEnter; // 可以进入课堂
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AntiForgetStatus {
        private ReviewStatus d2Status; // D2复习状态
        private ReviewStatus d4Status; // D4复习状态
        private ReviewStatus d7Status; // D7复习状态
        private ReviewStatus d14Status; // D14复习状态
        private ReviewStatus d21Status; // D21复习状态
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReviewStatus {
        private String status; // 完成状态: 未开始、进行中、已完成、已跳过
        private String color; // 显示颜色: success、warning、danger、info
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StatisticsResponse {
        private Long totalStudents; // 学生数
        private Long totalCourses; // 排课数
        private Long completedCourses; // 完课数
        private Long cancelledCourses; // 停课数
        private Long rescheduledCourses; // 调课数
        private String antiForgetProgress; // 抗遗忘进度(已完成/总数)
        private BigDecimal totalCourseConsumption; // 课消数(小时)
    }
}
