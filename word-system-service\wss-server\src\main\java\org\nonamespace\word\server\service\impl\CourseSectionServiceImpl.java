package org.nonamespace.word.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.nonamespace.word.server.domain.CourseSection;
import org.nonamespace.word.server.mapper.CourseSectionMapper;
import org.nonamespace.word.server.service.ICourseSectionService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 课程环节Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Service
public class CourseSectionServiceImpl extends ServiceImpl<CourseSectionMapper, CourseSection> implements ICourseSectionService {

    @Override
    public List<CourseSection> selectByCourseId(String courseId) {
        return list(new LambdaQueryWrapper<CourseSection>()
                .eq(CourseSection::getCourseId, courseId)
                .orderByAsc(CourseSection::getOrderIndex));
    }

    @Override
    public boolean createSection(CourseSection courseSection) {
        courseSection.setCreateTime(new Date());
        courseSection.setUpdateTime(new Date());
        return save(courseSection);
    }

    @Override
    public boolean updateStatus(String sectionId, String status) {
        CourseSection section = new CourseSection();
        section.setId(sectionId);
        section.setStatus(status);
        section.setUpdateTime(new Date());
        
        if ("进行中".equals(status)) {
            section.setStartTime(new Date());
        } else if ("已完成".equals(status)) {
            section.setEndTime(new Date());
        }
        
        return updateById(section);
    }

    @Override
    public CourseSection getCurrentSection(String courseId) {
        return getOne(new LambdaQueryWrapper<CourseSection>()
                .eq(CourseSection::getCourseId, courseId)
                .eq(CourseSection::getStatus, "进行中")
                .last("LIMIT 1"));
    }
}
