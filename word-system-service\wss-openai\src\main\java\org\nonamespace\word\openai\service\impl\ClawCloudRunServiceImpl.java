package org.nonamespace.word.openai.service.impl;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import org.nonamespace.word.openai.service.IClawCloudRunService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class ClawCloudRunServiceImpl implements IClawCloudRunService {

    @Value("${clawcloud.url}")
    private String url;
    @Value("${clawcloud.voice.us}")
    private String usVoice;
    @Value("${clawcloud.voice.uk}")
    private String ukVoice;

    @Override
    public byte[] enrich(String text, int type) {

        // 模型
        String model = type == 0 ? usVoice : ukVoice;
        HttpResponse httpResponse = HttpUtil.createPost(url).body(JSONUtil.toJsonStr(Map.of("text", text, "voice", model))).execute();
        if(httpResponse == null || httpResponse.getStatus() != 200) {
            throw new RuntimeException("调用 ClawCloudRun 接口 http请求 失败:" + (httpResponse != null ?  httpResponse.body() : null));
        }
        return httpResponse.bodyBytes();
    }


}
