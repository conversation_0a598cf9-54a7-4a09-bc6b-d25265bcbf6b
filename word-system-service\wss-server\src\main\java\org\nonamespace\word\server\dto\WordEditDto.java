package org.nonamespace.word.server.dto;


import com.fasterxml.jackson.annotation.JsonAlias;
import jakarta.validation.constraints.NotBlank;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Range;
import org.nonamespace.word.server.domain.Word;

import java.util.List;
import java.util.Map;

/**
 * 单词更新DTO
 */
@Data
@Builder
public class WordEditDto {

    /**
     * 请求实体类
     */
    @Getter
    @Setter
    public static class Req {
        /* 单词Id */
        @NotBlank(message = "单词Id不能为空")
        private String id;
        /* 音节 */
        @NotBlank(message = "音节不能为空")
        private String syllables;
        /* 英式音标 */
        @JsonAlias({"phoneticUk", "phonetic_uk"})
        private String phoneticUk;
        /* 美式音标 */
        private String phoneticUs;
        /* 视频文件 */
        private Map<String, String> videoFile;
        private String audioUkUrl;
        private String audioUsUrl;
        /* 词义(可包含多个词性及对应中文解释,如{pos: [{"pos": "n.", "def": "猫"}, {"pos": "v.", "def": "抓"}], practices:["", ""])}) */
        private Map<String, Word.Meanings> meanings;
        /* 例句 ([{"sentence_en": "...", "sentence_cn": "...", "audio_uk_url": "...", "audio_us_url": "...", "structure_parts_en": [["...", "..."]]}]) */
        private Map<String, List<Word.Sentences>> sentences;
        /* 标签 */
        private List<String> tags;
        /* 难度等级 */
        @Range(min = 1, max = 5, message = "难度等级范围在1-5之间")
        private Integer difficulty;
        /* 修改描述 */
        @JsonAlias({"changeDescription", "change_description"})
        private String changeDescription;



    }


}
