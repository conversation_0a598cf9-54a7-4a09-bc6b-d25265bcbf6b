package org.nonamespace.word.rest.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import org.nonamespace.word.server.dto.management.teachermatch.TeacherMatchDto;
import org.nonamespace.word.server.service.ITeacherMatchService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 教师匹配控制器
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
@RestController
@RequestMapping("/management/teacher-match")
@RequiredArgsConstructor
@Slf4j
@Validated
public class TeacherMatchController extends BaseController {

    private final ITeacherMatchService teacherMatchService;

    /**
     * 根据学生信息匹配教师
     */
    @PostMapping("/match")
    @Log(title = "匹配老师", businessType = BusinessType.UPDATE)
    public AjaxResult matchTeachers(@Validated @RequestBody TeacherMatchDto.MatchTeachersReq request) {
        try {
            TeacherMatchDto.MatchTeachersResp result = teacherMatchService.matchTeachers(request);
            return success(result);
        } catch (Exception e) {
            log.error("匹配教师失败", e);
            return error("匹配教师失败: " + e.getMessage());
        }
    }

    /**
     * 获取教师详细时间安排（周视图）
     */
    @GetMapping("/teacher/{teacherId}/weekly-schedule")
    public AjaxResult getTeacherWeeklySchedule(
            @PathVariable String teacherId,
            @RequestParam String startDate) {
        try {
            TeacherMatchDto.TeacherWeeklySchedule result = teacherMatchService.getTeacherWeeklySchedule(teacherId, startDate);
            return success(result);
        } catch (Exception e) {
            log.error("获取教师时间安排失败", e);
            return error("获取教师时间安排失败: " + e.getMessage());
        }
    }
}
