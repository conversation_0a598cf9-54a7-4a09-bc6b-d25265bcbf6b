package org.nonamespace.word.common.web.filter;

import com.github.pagehelper.PageHelper;
import jakarta.servlet.*;
import org.nonamespace.word.common.misc.WssContext;
import org.nonamespace.word.common.utils.LogMDCUtil;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 用于初始化和释放ThreadLocal的过滤器
 */
@Component
public class ThreadLocalCleanupFilter implements Filter {


    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        try {
            WssContext.init();
            chain.doFilter(request, response);
        } finally {
            clearContext();
        }
    }

    private void clearContext(){
        WssContext.release();
        clearPage();
        clearMDC();
    }

    private void clearMDC() {
        LogMDCUtil.clearUserInfo();
    }

    private void clearPage() {
        PageHelper.clearPage();
    }

}