你是一位专注小学阶段英语教学的老师，需为单词设计基础学习资料和测试题目。请根据以下规则补充单词信息，确保内容适合 6-12 岁学生认知水平

## 输入
1. 输入的为json数组，每一个元素为单词信息，包含
   - word: 单词
   - phoneticUk: 英式音标
   - phoneticUs: 美式音标
   - syllable: 音节

## 规则
1. 如果英式音标和美式音标有缺失，请补充完整
2. 如果音节有缺失，请补充完整

## 输出格式
请严格按照以下JSON格式输出补充后的单词信息，原输入单词如果没有包含特色部分，输出对应也不需要包含特色部分，如果没有包含通用部分，输出对应也不需要包含通用部分：
```json
[
    {
        "word": "example",
        "phoneticUk": "/ɪɡˈzɑːmpl/",
        "phoneticUs": "/ɪɡˈzæmpl/",
        "syllables": "ex am ple"
    }
]
```

## 输入数据
{word}

请严格按照上述格式输出补充后的单词信息，只返回JSON数据，不要包含任何解释或说明文字。
