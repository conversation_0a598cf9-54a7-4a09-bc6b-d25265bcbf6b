package org.nonamespace.word.openai.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.openai.model.WordInfo;
import org.nonamespace.word.openai.service.ISiliconflowService;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 硅基流动
 *
 * <AUTHOR>
 * @date 2025/5/20 9:32
 */
@Slf4j
@Service
public class SiliconflowServiceImpl implements ISiliconflowService {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Value("classpath:/prompts/word-enrich.st")
    private Resource wordEnrichSt;
    @Value("classpath:/prompts/word-enrich-basic.st")
    private Resource wordEnrichBasicSt;
    @Value("classpath:/prompts/word-enrich-meanings.st")
    private Resource wordEnrichMeaningsSt;
    @Value("classpath:/prompts/word-enrich-sentences.st")
    private Resource wordEnrichSentencesSt;

    @Value("classpath:/prompts/word-meamings-replace.txt")
    private Resource wordFillPormpt;


    private final ChatClient chatClient;
    public SiliconflowServiceImpl(ChatClient.Builder chatClientBuilder) {
        this.chatClient = chatClientBuilder.build();
    }

    @Override
    public List<WordInfo> generateWordMeanings(List<String> words) throws JsonProcessingException {
        String wordStr = CollUtil.join(words, "\r\n");
        try {
            SystemMessage systemPromptTemplate = new SystemMessage(wordFillPormpt);
            UserMessage userMessage = new UserMessage(wordStr);
            // 调用AI服务
            String chatResponseContent = chatClient.prompt(new Prompt(List.of(systemPromptTemplate, userMessage))).call().content();
            return parseResponse(chatResponseContent);
        } catch (Exception e) {
            log.error("单词释义替换失败", e);
            throw new RuntimeException("单词释义替换失败: " + e.getMessage(), e);
        }


    }

    @Override
    public List<WordInfo> enrich(List<WordInfo> words) {
        return executeEnrich(words, wordEnrichSt);
    }

    @Override
    public List<WordInfo> enrichBasic(List<WordInfo> words) {
        return this.executeEnrich(words, wordEnrichBasicSt);
    }

    @Override
    public List<WordInfo> enrichMeanings(List<WordInfo> words) {
        return executeEnrich(words, wordEnrichMeaningsSt);
    }

    @Override
    public List<WordInfo> enrichSentences(List<WordInfo> words) {
        return executeEnrich(words, wordEnrichSentencesSt);
    }


    private List<WordInfo> executeEnrich(List<WordInfo> words, Resource st) {
        try {
            // 将单词列表转换为JSON字符串
            String wordsJson = objectMapper.writeValueAsString(words);

            SystemMessage systemPromptTemplate = new SystemMessage(st);
            UserMessage userMessage = new UserMessage("输入单词为：" + wordsJson);

            // 调用AI服务
            String chatResponseContent = chatClient.prompt(new Prompt(List.of(systemPromptTemplate, userMessage))).call().content();
            // 解析响应
            return parseResponse(chatResponseContent);
        } catch (Exception e) {
            log.error("单词信息补充失败", e);
            throw new RuntimeException("单词信息补充失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析AI响应
     */
    private List<WordInfo> parseResponse(String response) {
        try {
            // 提取JSON部分
            String jsonContent = extractJsonFromResponse(response);

            // 解析JSON
            return objectMapper.readValue(jsonContent, new TypeReference<List<WordInfo>>() {});
        } catch (JsonProcessingException e) {
            log.error("JSON解析失败", e);
            throw new RuntimeException("JSON解析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从AI响应中提取JSON部分
     */
    private String extractJsonFromResponse(String response) {
        // 尝试直接解析，如果是纯JSON
        try {
            objectMapper.readTree(response);
            return response;
        } catch (JsonProcessingException e) {
            // 不是纯JSON，尝试提取JSON部分
            Pattern pattern = Pattern.compile("```json\\s*(.+?)\\s*```", Pattern.DOTALL);
            Matcher matcher = pattern.matcher(response);

            if (matcher.find()) {
                return matcher.group(1);
            }

            // 如果没有找到JSON标记，尝试直接提取[]包围的内容
            pattern = Pattern.compile("\\[\\s*\\{.+?\\}\\s*\\]", Pattern.DOTALL);
            matcher = pattern.matcher(response);

            if (matcher.find()) {
                return matcher.group(0);
            }

            // 如果还是没找到，返回原始响应
            return response;
        }
    }
}
