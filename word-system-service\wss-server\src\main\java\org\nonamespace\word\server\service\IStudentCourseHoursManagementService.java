package org.nonamespace.word.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.nonamespace.word.server.dto.management.student.StudentCourseHoursDto;

import java.util.List;

/**
 * 学生课时管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IStudentCourseHoursManagementService {
    
    /**
     * 分页查询学生课时记录
     * 
     * @param request 查询请求
     * @return 分页结果
     */
    Page<StudentCourseHoursDto.CourseHoursResponse> queryStudentCourseHours(StudentCourseHoursDto.QueryRequest request);
    
    /**
     * 获取学生课时详情
     *
     * @param studentId 学生ID
     * @param subject 学科
     * @param specification 课型
     * @return 课时详情
     */
    StudentCourseHoursDto.CourseHoursResponse getStudentCourseHoursDetail(String studentId, String subject, String specification);

    /**
     * 根据课时包ID获取课时包详情
     *
     * @param courseHoursId 课时包ID
     * @return 课时包详情
     */
    StudentCourseHoursDto.CourseHoursResponse getCourseHoursById(String courseHoursId);

    /**
     * 手动调整学生课时
     *
     * @param request 调整请求
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 是否成功
     */
    boolean adjustStudentCourseHours(StudentCourseHoursDto.AdjustRequest request, String operatorId, String operatorName);

    /**
     * 更新学生课时（直接设置课时值）
     *
     * @param request 更新请求
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 是否成功
     */
    boolean updateStudentCourseHours(StudentCourseHoursDto.UpdateRequest request, String operatorId, String operatorName);

    /**
     * 录入课消
     *
     * @param request 录入课消请求
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 是否成功
     */
    boolean recordCourseConsumption(StudentCourseHoursDto.RecordConsumptionRequest request, String operatorId, String operatorName);

    /**
     * 新增学生课时
     *
     * @param request 新增请求
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 是否成功
     */
    boolean addStudentCourseHours(StudentCourseHoursDto.AddCourseHoursRequest request, String operatorId, String operatorName);
    
    /**
     * 分页查询课消记录
     * 
     * @param request 查询请求
     * @return 分页结果
     */
    Page<StudentCourseHoursDto.ConsumptionResponse> queryConsumptionRecords(StudentCourseHoursDto.ConsumptionQueryRequest request);
    
    /**
     * 分页查询调整历史记录
     *
     * @param request 查询请求
     * @return 分页结果
     */
    Page<StudentCourseHoursDto.AdjustmentHistoryResponse> queryAdjustmentHistory(StudentCourseHoursDto.AdjustmentHistoryQueryRequest request);

    /**
     * 导出学生课时数据
     *
     * @param request 查询请求
     * @return 导出数据列表
     */
    List<StudentCourseHoursDto.ExportResponse> exportStudentCourseHours(StudentCourseHoursDto.QueryRequest request);
}
