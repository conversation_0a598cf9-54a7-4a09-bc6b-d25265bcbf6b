package org.nonamespace.word.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.nonamespace.word.server.domain.StudentCourseHours;

import java.math.BigDecimal;
import java.util.List;

/**
 * 学生课时记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IStudentCourseHoursService extends IService<StudentCourseHours> {
    
    /**
     * 根据学生ID、学科、课型查询课时记录
     *
     * @param studentId 学生ID
     * @param subject 学科
     * @param specification 课型
     * @return 课时记录
     */
    StudentCourseHours getByStudentAndSubjectAndType(String studentId, String subject, String specification);
    
    /**
     * 创建或更新课时记录
     *
     * @param studentId 学生ID
     * @param subject 学科
     * @param specification 课型
     * @param nature 性质
     * @param totalHours 总课时
     * @param remainingHours 剩余课时
     * @return 课时记录
     */
    StudentCourseHours createOrUpdateHours(String studentId, String subject, String specification,
                                         String nature, BigDecimal totalHours, BigDecimal remainingHours);

    /**
     * 消费课时
     *
     * @param studentId 学生ID
     * @param subject 学科
     * @param specification 课型
     * @param consumedHours 消费课时数
     * @return 是否成功
     */
    boolean consumeHours(String studentId, String subject, String specification, BigDecimal consumedHours);

    /**
     * 消费课时（带课程信息，会自动记录课消记录）
     *
     * @param studentId 学生ID
     * @param subject 学科
     * @param specification 课型
     * @param nature 课程性质（试听课、正式课）
     * @param consumedHours 消费课时数
     * @param courseId 课程ID
     * @param teacherId 教师ID
     * @param remark 备注
     * @return 是否成功
     */
    boolean consumeHours(String studentId, String subject, String specification, String nature, BigDecimal consumedHours,
                        String courseId, String teacherId, String remark);

    /**
     * 检查课时余额是否足够
     *
     * @param studentId 学生ID
     * @param subject 学科
     * @param specification 课型
     * @param nature 课程性质（试听课、正式课）
     * @param requiredHours 需要的课时数
     * @return 检查结果
     */
    CourseHoursCheckResult checkHoursBalance(String studentId, String subject, String specification, String nature, BigDecimal requiredHours);

    /**
     * 课时余额检查结果
     */
    class CourseHoursCheckResult {
        private boolean sufficient;
        private BigDecimal remainingHours;
        private BigDecimal requiredHours;
        private String message;

        public CourseHoursCheckResult(boolean sufficient, BigDecimal remainingHours, BigDecimal requiredHours, String message) {
            this.sufficient = sufficient;
            this.remainingHours = remainingHours;
            this.requiredHours = requiredHours;
            this.message = message;
        }

        public boolean isSufficient() {
            return sufficient;
        }

        public BigDecimal getRemainingHours() {
            return remainingHours;
        }

        public BigDecimal getRequiredHours() {
            return requiredHours;
        }

        public String getMessage() {
            return message;
        }
    }

    /**
     * 手动调整课时（分别调整购买和赠送课时）
     *
     * @param studentId 学生ID
     * @param subject 学科
     * @param specification 课型
     * @param purchasedHoursAdjustment 购买课时调整数（正数为增加，负数为减少）
     * @param giftHoursAdjustment 赠送课时调整数（正数为增加，负数为减少）
     * @param adjustmentReason 调整原因
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 是否成功
     */
    boolean adjustHours(String studentId, String subject, String specification,
                       BigDecimal purchasedHoursAdjustment, BigDecimal giftHoursAdjustment,
                       String adjustmentReason, String operatorId, String operatorName);

    /**
     * 根据课时包ID调整课时（包括单价修改）
     *
     * @param courseHoursId 课时包ID
     * @param purchasedHoursAdjustment 购买课时调整数（正数为增加，负数为减少）
     * @param giftHoursAdjustment 赠送课时调整数（正数为增加，负数为减少）
     * @param unitPrice 新的单价
     * @param adjustmentReason 调整原因
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 是否成功
     */
    boolean adjustHoursByCourseHoursId(String courseHoursId,
                                      BigDecimal purchasedHoursAdjustment, BigDecimal giftHoursAdjustment,
                                      BigDecimal unitPrice, String adjustmentReason,
                                      String operatorId, String operatorName);

    /**
     * 创建新的课时记录（用于导入）
     *
     * @param studentId 学生ID
     * @param subject 学科
     * @param specification 课型
     * @param nature 性质
     * @param totalHours 总课时
     * @param remainingHours 剩余课时
     * @param unitPrice 单价
     * @param batchNo 批次号
     * @return 课时记录
     */
    StudentCourseHours createNewHoursRecord(String studentId, String subject, String specification,
                                          String nature, BigDecimal totalHours, BigDecimal remainingHours,
                                          BigDecimal unitPrice, String batchNo);

    /**
     * 创建新的课时记录（详细版本，用于导入）
     *
     * @param studentId 学生ID
     * @param subject 学科
     * @param specification 课型
     * @param nature 性质
     * @param totalHours 总课时
     * @param remainingHours 剩余课时
     * @param purchasedHours 购买课时
     * @param giftHours 赠送课时
     * @param purchasedRemainingHours 剩余购买课时
     * @param giftRemainingHours 剩余赠送课时
     * @param unitPrice 单价
     * @param batchNo 批次号
     * @return 课时记录
     */
    StudentCourseHours createNewHoursRecordWithDetails(String studentId, String subject, String specification,
                                                     String nature, BigDecimal totalHours, BigDecimal remainingHours,
                                                     BigDecimal purchasedHours, BigDecimal giftHours,
                                                     BigDecimal purchasedRemainingHours, BigDecimal giftRemainingHours,
                                                     BigDecimal unitPrice, String batchNo);

    /**
     * 获取学生某学科某课型的所有课时记录（按导入时间排序，用于FIFO消费）
     *
     * @param studentId 学生ID
     * @param subject 学科
     * @param specification 课型
     * @return 课时记录列表
     */
    List<StudentCourseHours> getHoursRecordsByStudentAndSubjectAndType(String studentId, String subject, String specification);
}
