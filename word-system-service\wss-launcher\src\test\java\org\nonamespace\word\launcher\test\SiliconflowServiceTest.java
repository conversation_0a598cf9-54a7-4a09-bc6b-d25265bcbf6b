package org.nonamespace.word.launcher.test;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.nonamespace.word.openai.model.WordInfo;
import org.nonamespace.word.openai.service.ISiliconflowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@SpringBootTest
@RunWith(SpringRunner.class)
public class SiliconflowServiceTest {

    @Autowired
    private ISiliconflowService siliconflowService;

    @Test
    public void testSiliconflowService() throws Exception {
        WordInfo wordInfo = new WordInfo();
        wordInfo.setWord("alibaba");
        long start0 = System.currentTimeMillis();
        List<WordInfo> res = siliconflowService.enrichMeanings(List.of(wordInfo));
        long end0 = System.currentTimeMillis();
        System.out.println(res);
        System.out.println("===耗时：" + (end0 - start0));

    }

}
