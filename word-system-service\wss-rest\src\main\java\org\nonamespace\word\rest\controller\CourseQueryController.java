package org.nonamespace.word.rest.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.dto.course.CourseQueryDto;
import org.nonamespace.word.server.service.ICourseQueryService;
import org.nonamespace.word.server.service.ICourseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 课程查询Controller
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@RestController
@RequestMapping("/course-query")
@Slf4j
public class CourseQueryController extends BaseController {

    @Autowired
    private ICourseQueryService courseQueryService;

    @Autowired
    private ICourseService courseService;

    /**
     * 分页查询课程信息
     */
    @PreAuthorize("@ss.hasPermi('course:query:list')")
    @PostMapping("/list")
    @Log(title = "课程查询", businessType = BusinessType.OTHER)
    public TableDataInfo list(@RequestBody CourseQueryDto.QueryRequest request) {
        try {
            log.info("接收到课程查询请求: {}", request);
            
            Page<CourseQueryDto.QueryResponse> page = courseQueryService.queryCourses(request);
            
            TableDataInfo dataTable = new TableDataInfo();
            dataTable.setCode(200);
            dataTable.setMsg("查询成功");
            dataTable.setRows(page.getRecords());
            dataTable.setTotal(page.getTotal());
            
            return dataTable;
        } catch (Exception e) {
            log.error("课程查询失败", e);
            return getDataTableError("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取课程统计信息
     */
    @PreAuthorize("@ss.hasPermi('course:query:statistics')")
    @PostMapping("/statistics")
    public AjaxResult statistics(@RequestBody CourseQueryDto.QueryRequest request) {
        try {
            log.info("接收到课程统计请求: {}", request);
            
            CourseQueryDto.StatisticsResponse statistics = courseQueryService.getStatistics(request);
            
            return success(statistics);
        } catch (Exception e) {
            log.error("课程统计失败", e);
            return error("统计失败: " + e.getMessage());
        }
    }

    /**
     * 课程操作 - 上课
     */
    @PreAuthorize("@ss.hasPermi('course:query:start')")
    @PostMapping("/start/{courseId}")
    @Log(title = "开始上课", businessType = BusinessType.UPDATE)
    public AjaxResult startCourse(@PathVariable String courseId) {
        try {
            log.info("开始上课，课程ID: {}", courseId);
            courseService.startCourse(courseId);
            return success("上课成功");
        } catch (Exception e) {
            log.error("上课失败，课程ID: {}", courseId, e);
            return error("上课失败: " + e.getMessage());
        }
    }

    /**
     * 课程操作 - 停课
     */
    @PreAuthorize("@ss.hasPermi('course:query:cancel')")
    @PostMapping("/cancel/{courseId}")
    @Log(title = "停课", businessType = BusinessType.UPDATE)
    public AjaxResult cancelCourse(@PathVariable String courseId, @RequestBody(required = false) String reason) {
        try {
            log.info("停课，课程ID: {}, 原因: {}", courseId, reason);
            // 这里需要实现停课逻辑，暂时返回提示信息
            return success("停课功能需要进一步实现");
        } catch (Exception e) {
            log.error("停课失败，课程ID: {}", courseId, e);
            return error("停课失败: " + e.getMessage());
        }
    }

    /**
     * 课程操作 - 调课
     */
    @PreAuthorize("@ss.hasPermi('course:query:reschedule')")
    @PostMapping("/reschedule/{courseId}")
    @Log(title = "调课", businessType = BusinessType.UPDATE)
    public AjaxResult rescheduleCourse(@PathVariable String courseId, @RequestBody String rescheduleData) {
        try {
            log.info("调课，课程ID: {}, 调课数据: {}", courseId, rescheduleData);
            return success("调课功能需要进一步实现");
        } catch (Exception e) {
            log.error("调课失败，课程ID: {}", courseId, e);
            return error("调课失败: " + e.getMessage());
        }
    }

    /**
     * 课程操作 - 下载资料
     */
    @PreAuthorize("@ss.hasPermi('course:query:download')")
    @GetMapping("/download/{courseId}")
    public AjaxResult downloadMaterial(@PathVariable String courseId) {
        try {
            log.info("下载资料，课程ID: {}", courseId);
            return success("资料下载功能需要进一步实现");
        } catch (Exception e) {
            log.error("下载资料失败，课程ID: {}", courseId, e);
            return error("下载失败: " + e.getMessage());
        }
    }

    /**
     * 课程操作 - 查看报告
     */
    @PreAuthorize("@ss.hasPermi('course:query:report')")
    @GetMapping("/report/{courseId}")
    public AjaxResult viewReport(@PathVariable String courseId) {
        try {
            log.info("查看报告，课程ID: {}", courseId);
            return success("报告查看功能需要进一步实现");
        } catch (Exception e) {
            log.error("查看报告失败，课程ID: {}", courseId, e);
            return error("查看报告失败: " + e.getMessage());
        }
    }

    /**
     * 获取查询条件的选项数据
     */
    @PreAuthorize("@ss.hasPermi('course:query:options')")
    @GetMapping("/options")
    public AjaxResult getQueryOptions() {
        try {
            log.info("获取查询条件选项");

            // 构建基础选项数据
            java.util.Map<String, Object> options = new java.util.HashMap<>();

            // 学科选项
            options.put("subjects", java.util.Arrays.asList(
                java.util.Map.of("label", "英语", "value", "英语")
            ));

            // 课型选项
            options.put("specifications", java.util.Arrays.asList(
                java.util.Map.of("label", "单词课", "value", "单词课"),
                java.util.Map.of("label", "题型课", "value", "题型课")
            ));

            // 类型选项
            options.put("types", java.util.Arrays.asList(
                java.util.Map.of("label", "学习课", "value", "学习课"),
                java.util.Map.of("label", "复习课", "value", "复习课")
            ));

            // 性质选项
            options.put("courseTypes", java.util.Arrays.asList(
                java.util.Map.of("label", "正式课", "value", "正式课"),
                java.util.Map.of("label", "试听课", "value", "试听课")
            ));

            // 课程状态选项
            options.put("courseStatuses", java.util.Arrays.asList(
                java.util.Map.of("label", "待开始", "value", "待开始"),
                java.util.Map.of("label", "进行中", "value", "进行中"),
                java.util.Map.of("label", "已完成", "value", "已完成"),
                java.util.Map.of("label", "停课", "value", "停课"),
                java.util.Map.of("label", "调课", "value", "调课")
            ));

            // 异常类型选项
            options.put("exceptionTypes", java.util.Arrays.asList(
                java.util.Map.of("label", "调课", "value", "调课"),
                java.util.Map.of("label", "停课", "value", "停课"),
                java.util.Map.of("label", "不准时", "value", "不准时"),
                java.util.Map.of("label", "时长偏差", "value", "时长偏差")
            ));

            return success(options);
        } catch (Exception e) {
            log.error("获取查询选项失败", e);
            return error("获取选项失败: " + e.getMessage());
        }
    }

    /**
     * 创建错误的数据表响应
     */
    private TableDataInfo getDataTableError(String message) {
        TableDataInfo dataTable = new TableDataInfo();
        dataTable.setCode(500);
        dataTable.setMsg(message);
        dataTable.setRows(null);
        dataTable.setTotal(0L);
        return dataTable;
    }
}
