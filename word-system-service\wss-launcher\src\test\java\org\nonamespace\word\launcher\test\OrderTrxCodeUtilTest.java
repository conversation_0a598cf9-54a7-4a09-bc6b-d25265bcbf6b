package org.nonamespace.word.launcher.test;

import cn.hutool.core.thread.ThreadUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.nonamespace.word.server.constants.OrderConstants;
import org.nonamespace.word.server.util.OrderTrxCodeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest
@RunWith(SpringRunner.class)
public class OrderTrxCodeUtilTest {

    @Autowired
    private OrderTrxCodeUtil orderCodeUtil;

    @Test
    public void testGeneralOrderCode() {
        for (int i = 0; i < 100; i++) {
            ThreadUtil.execute(() -> {
                String orderCode = orderCodeUtil.generalOrderCode(OrderConstants.TrxType.PAY);
                System.out.println(orderCode);
            });
        }
    }

}
