package org.nonamespace.word.server.dto.management.coursebooking;

import lombok.Data;

/**
 * 候选老师DTO
 *
 * <AUTHOR> Assistant
 * @date 2025-01-01
 */
@Data
public class TeacherCandidateDto {

    /**
     * 老师ID
     */
    private String teacherId;

    /**
     * 老师姓名
     */
    private String teacherName;

    /**
     * 老师手机号
     */
    private String teacherPhone;

    /**
     * 教学组ID
     */
    private String teachingGroupId;

    /**
     * 教学组名称
     */
    private String teachingGroupName;

    /**
     * 当前学生数
     */
    private Integer currentStudents;

    /**
     * 匹配度
     */
    private Double matchPercentage;

    /**
     * 是否被选中
     */
    private Boolean selected;
}
