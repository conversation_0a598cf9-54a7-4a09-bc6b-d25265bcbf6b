package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nonamespace.word.server.entity.DataEntity;

import java.util.Date;

/**
 * 师生关系实体类
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("teacher_student_relation")
public class TeacherStudentRelation extends DataEntity {

    /**
     * 教师ID
     */
    private String teacherId;

    /**
     * 学生ID
     */
    private String studentId;

    /**
     * 关系类型 (teaching: 教学, tutoring: 辅导)
     */
    private String relationType;

    /**
     * 学科 (英语、数学、语文等)
     */
    private String subject;

    /**
     * 课型
     */
    private String specification;

    /**
     * 关系开始日期
     */
    private Date startDate;

    /**
     * 关系结束日期
     */
    private Date endDate;

    /**
     * 状态 (active: 活跃, inactive: 停用)
     */
    private String status;
}
