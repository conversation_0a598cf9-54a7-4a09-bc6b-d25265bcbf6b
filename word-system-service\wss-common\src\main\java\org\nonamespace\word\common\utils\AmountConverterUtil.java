package org.nonamespace.word.common.utils;

import java.util.HashMap;
import java.util.Map;

public class AmountConverterUtil {

    private static final String[] CN_NUMBERS = {"零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"};
    private static final String[] CN_UNITS = {"分", "角", "元", "拾", "佰", "仟", "万", "拾", "佰", "仟", "亿"};
    private static final String[] LOWER_NUMBERS = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};

    private static final Map<Character, String> NUMBER_MAP = new HashMap<>();
    private static final Map<Character, String> UNIT_MAP = new HashMap<>();

    static {
        // 初始化数字映射
        for (int i = 0; i < CN_NUMBERS.length; i++) {
            NUMBER_MAP.put((char)('0' + i), CN_NUMBERS[i]);
        }

        // 初始化单位映射
        for (int i = 0; i < CN_UNITS.length; i++) {
            UNIT_MAP.put((char)('0' + i), CN_UNITS[i]);
        }
    }

    /**
     * 将分转换为大写金额和小写金额
     * @param fenAmount 以分为单位的金额
     * @return 包含大写金额和小写金额的数组
     */
    public static String[] convertAmount(long fenAmount) {
        String uppercase = convertToUppercase(fenAmount);
        String lowercase = convertToLowercase(fenAmount);
        return new String[]{uppercase, lowercase};
    }

    /**
     * 转换为中文大写金额
     * @param fenAmount 以分为单位的金额
     * @return 中文大写金额字符串
     */
    public static String convertToUppercase(long fenAmount) {
        // 处理零金额
        if (fenAmount == 0) {
            return "零元整";
        }

        // 处理负金额
        boolean negative = false;
        if (fenAmount < 0) {
            negative = true;
            fenAmount = -fenAmount;
        }

        // 将分转换为元（保留两位小数）
        String yuanStr = String.format("%.2f", fenAmount / 100.0);
        // 去掉小数点，转换为整数部分和小数部分
        String amountStr = yuanStr.replace(".", "");

        // 处理金额字符串
        StringBuilder result = new StringBuilder();
        if (negative) {
            result.append("负");
        }

        int len = amountStr.length();
        boolean lastZero = false; // 上一位是否为0
        boolean hasValue = false; // 当前段是否有非0值

        // 从最低位（分）开始处理
        for (int i = 0; i < len; i++) {
            char digit = amountStr.charAt(len - 1 - i);
            String numStr = NUMBER_MAP.get(digit);
            String unitStr = UNIT_MAP.get((char)('0' + i));

            // 处理当前位
            if (digit == '0') {
                // 处理连续零的情况
                if (!lastZero && i != 0 && i != 1) {
                    result.insert(0, "零");
                }
                lastZero = true;
            } else {
                // 处理万、亿单位
                if (i == 6 || i == 10) {
                    result.insert(0, unitStr);
                }
                result.insert(0, unitStr).insert(0, numStr);
                lastZero = false;
                hasValue = true;
            }

            // 处理元、角、分单位
            if (i == 1) {
                if (digit != '0' || (len == 3 && amountStr.charAt(len - 3) != '0')) {
                    result.insert(0, "角");
                }
            } else if (i == 2) {
                if (hasValue || fenAmount >= 100) {
                    result.insert(0, "元");
                }
            }
        }

        // 处理整元的情况
        if (amountStr.endsWith("00")) {
            result.append("整");
        }

        return result.toString();
    }

    /**
     * 转换为带单位的小写金额
     * @param fenAmount 以分为单位的金额
     * @return 小写金额字符串（带元角分单位）
     */
    public static String convertToLowercase(long fenAmount) {
        if (fenAmount == 0) {
            return "零元";
        }

        boolean negative = fenAmount < 0;
        if (negative) {
            fenAmount = -fenAmount;
        }

        long yuan = fenAmount / 100;        // 元部分
        long jiao = (fenAmount % 100) / 10; // 角部分
        long fen = fenAmount % 10;          // 分部分

        StringBuilder result = new StringBuilder();
        if (negative) {
            result.append("负");
        }

        // 处理元部分
        if (yuan > 0) {
            result.append(convertToChineseNumber(yuan)).append("元");
        } else if (jiao > 0 || fen > 0) {
            result.append("零元");
        }

        // 处理角部分
        if (jiao > 0) {
            result.append(LOWER_NUMBERS[(int)jiao]).append("角");
        }

        // 处理分部分
        if (fen > 0) {
            result.append(LOWER_NUMBERS[(int)fen]).append("分");
        }

        // 处理整元的情况
        if (yuan > 0 && jiao == 0 && fen == 0) {
            result.append("整");
        }

        // 处理只有角或分的情况
        if (yuan == 0) {
            if (jiao > 0 && fen == 0) {
                result.append("整");
            }
        }

        return result.toString();
    }

    /**
     * 将数字转换为中文数字表示
     * @param number 数字
     * @return 中文数字字符串
     */
    private static String convertToChineseNumber(long number) {
        if (number == 0) {
            return "零";
        }

        String[] units = {"", "十", "百", "千", "万", "十", "百", "千", "亿"};
        StringBuilder result = new StringBuilder();
        int unitIndex = 0;
        boolean lastZero = true;

        while (number > 0) {
            int digit = (int)(number % 10);
            if (digit != 0) {
                if (unitIndex >= 4 && unitIndex % 4 == 0 && lastZero) {
                    result.insert(0, units[unitIndex]);
                }
                result.insert(0, LOWER_NUMBERS[digit] + units[unitIndex]);
                lastZero = false;
            } else {
                if (!lastZero) {
                    result.insert(0, "零");
                }
                lastZero = true;
            }
            number /= 10;
            unitIndex++;
        }

        // 处理"一十"开头的特殊情况
        if (result.toString().startsWith("一十")) {
            result.delete(0, 1);
        }

        return result.toString();
    }

    public static void main(String[] args) {
        // 测试用例
        long[] testAmounts = {
                0,
                5,
                10,
                123,
                1000,
                1001,
                12345,
                1000000,
                100000000,
                123456789,
                -123456
        };

        System.out.println("金额转换测试：");
        System.out.println("====================================================");
        System.out.printf("%-15s %-25s %-20s%n", "输入(分)", "大写金额", "小写金额");
        System.out.println("----------------------------------------------------");

        for (long amount : testAmounts) {
            String[] result = convertAmount(amount);
            System.out.printf("%-15d %-25s %-20s%n", amount, result[0], result[1]);
        }

        System.out.println("====================================================");
    }
}