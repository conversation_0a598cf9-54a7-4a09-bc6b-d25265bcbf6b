<template>
  <div class="refund-records-container">
    <!-- 统计卡片 -->
    <div v-if="overview" class="statistics-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ overview.todayStats?.totalRefundCount || 0 }}</div>
              <div class="stat-label">今日退款笔数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">¥{{ overview.todayStats?.totalRefundAmountYuan || '0.00' }}</div>
              <div class="stat-label">今日退款金额</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ overview.monthStats?.totalRefundCount || 0 }}</div>
              <div class="stat-label">本月退款笔数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number text-warning">{{ overview.pendingApprovalCount || 0 }}</div>
              <div class="stat-label">待审批</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="queryParams" inline label-width="80px">
        <el-form-item label="订单号">
          <el-input
            v-model="queryParams.orderNo"
            placeholder="请输入订单号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="学生姓名">
          <el-input
            v-model="queryParams.studentName"
            placeholder="请输入学生姓名"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="退款类型">
          <el-select v-model="queryParams.refundType" placeholder="请选择退款类型" clearable>
            <el-option label="部分退款" value="partial" />
            <el-option label="全额退款" value="full" />
          </el-select>
        </el-form-item>
        <el-form-item label="退款状态">
          <el-select v-model="queryParams.refundStatus" placeholder="请选择退款状态" clearable>
            <el-option label="处理中" value="processing" />
            <el-option label="成功" value="success" />
            <el-option label="失败" value="failed" />
          </el-select>
        </el-form-item>
        <el-form-item label="审批状态">
          <el-select v-model="queryParams.approvalStatus" placeholder="请选择审批状态" clearable>
            <el-option label="待审批" value="pending" />
            <el-option label="已审批" value="approved" />
            <el-option label="已拒绝" value="rejected" />
            <el-option label="自动审批" value="auto_approved" />
          </el-select>
        </el-form-item>
        <el-form-item label="退款时间">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
          <el-button v-if="isManager" type="warning" @click="handleStatistics">
            <el-icon><DataAnalysis /></el-icon>
            统计分析
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 退款记录列表 -->
    <el-card class="table-card">
      <!-- 批量操作工具栏 -->
      <div v-if="isManager" class="batch-operations" style="margin-bottom: 16px;">
        <el-button 
          type="success" 
          :disabled="selectedRecords.length === 0"
          @click="handleBatchApprove('approved')"
        >
          批量通过 ({{ selectedRecords.length }})
        </el-button>
        <el-button 
          type="danger" 
          :disabled="selectedRecords.length === 0"
          @click="handleBatchApprove('rejected')"
        >
          批量拒绝 ({{ selectedRecords.length }})
        </el-button>
      </div>

      <el-table
        v-loading="loading"
        :data="refundRecordList"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column v-if="isManager" type="selection" width="55" align="center" />
        <el-table-column label="订单号" prop="orderNo" width="180" show-overflow-tooltip />
        <el-table-column label="退款类型" prop="refundTypeDesc" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getRefundTypeTagType(row.refundType)">
              {{ row.refundTypeDesc }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="退款金额" prop="refundAmountYuan" width="120" align="center">
          <template #default="{ row }">
            <span class="amount-text">¥{{ row.refundAmountYuan }}</span>
          </template>
        </el-table-column>
        <el-table-column label="退款状态" prop="refundStatusDesc" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getRefundStatusTagType(row.refundStatus)">
              {{ row.refundStatusDesc }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="学生姓名" prop="studentName" width="120" />
        <el-table-column label="销售员" prop="salerName" width="120" />
        <el-table-column label="操作人" prop="operatorName" width="120" />
        <el-table-column label="审批状态" prop="approvalStatusDesc" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getApprovalStatusTagType(row.approvalStatus)">
              {{ row.approvalStatusDesc }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="退款时间" prop="refundTime" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.refundTime) }}
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="createTime" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              link
              @click="handleDetail(row)"
            >
              查看详情
            </el-button>
            <el-button
              v-if="canApprove(row)"
              type="success"
              link
              @click="handleApprove(row, 'approved')"
            >
              通过
            </el-button>
            <el-button
              v-if="canApprove(row)"
              type="danger"
              link
              @click="handleApprove(row, 'rejected')"
            >
              拒绝
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="queryParams.pageNum"
          :page-size="queryParams.pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 退款记录详情对话框 -->
    <el-dialog
      title="退款记录详情"
      v-model="detailVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="refundRecordDetail" class="refund-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ refundRecordDetail.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="退款类型">{{ refundRecordDetail.refundTypeDesc }}</el-descriptions-item>
          <el-descriptions-item label="退款金额">¥{{ refundRecordDetail.refundAmountYuan }}</el-descriptions-item>
          <el-descriptions-item label="退款状态">{{ refundRecordDetail.refundStatusDesc }}</el-descriptions-item>
          <el-descriptions-item label="学生姓名">{{ refundRecordDetail.studentName }}</el-descriptions-item>
          <el-descriptions-item label="销售员">{{ refundRecordDetail.salerName }}</el-descriptions-item>
          <el-descriptions-item label="操作人">{{ refundRecordDetail.operatorName }}</el-descriptions-item>
          <el-descriptions-item label="审批状态">{{ refundRecordDetail.approvalStatusDesc }}</el-descriptions-item>
          <el-descriptions-item label="退款时间">{{ formatDateTime(refundRecordDetail.refundTime) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(refundRecordDetail.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="退款原因" :span="2">{{ refundRecordDetail.refundReason }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 审批对话框 -->
    <el-dialog
      title="退款审批"
      v-model="approvalVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form ref="approvalForm" :model="approvalForm" :rules="approvalRules" label-width="100px">
        <el-form-item label="审批结果">
          <el-tag :type="currentApprovalResult === 'approved' ? 'success' : 'danger'">
            {{ currentApprovalResult === 'approved' ? '通过' : '拒绝' }}
          </el-tag>
        </el-form-item>
        <el-form-item label="审批备注" prop="approvalRemark">
          <el-input
            v-model="approvalForm.approvalRemark"
            type="textarea"
            :rows="4"
            placeholder="请输入审批备注"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="approvalVisible = false">取 消</el-button>
        <el-button type="primary" :loading="approvalSubmitting" @click="submitApproval">
          确 定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="RefundRecords">
import { onMounted, reactive, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Download, DataAnalysis } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/date.js'
import {
  getRefundRecordList,
  getManagerRefundRecordList,
  getManagerRefundOverview,
  getRefundRecordDetail,
  getManagerRefundRecordDetail,
  approveRefundRecord,
  batchApproveRefundRecords,
  exportRefundRecords,
  exportManagerRefundRecords
} from '@/api/management/refund-records'

// 响应式数据
const loading = ref(false)
const refundRecordList = ref([])
const total = ref(0)
const dateRange = ref([])
const selectedRecords = ref([])
const overview = ref(null)
const detailVisible = ref(false)
const approvalVisible = ref(false)
const statisticsVisible = ref(false)
const currentRefundRecordId = ref('')
const currentRefundRecord = ref(null)
const currentApprovalResult = ref('')
const refundRecordDetail = ref(null)
const approvalSubmitting = ref(false)

// 审批表单
const approvalForm = reactive({
  approvalRemark: ''
})

const approvalRules = {
  approvalRemark: [
    { required: true, message: '请输入审批备注', trigger: 'blur' },
    { min: 5, max: 200, message: '审批备注长度在5到200个字符', trigger: 'blur' }
  ]
}

// 判断是否为管理员（这里需要根据实际权限判断）
const isManager = ref(true) // 临时设置，实际应该从用户权限获取

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  orderNo: '',
  studentName: '',
  refundType: '',
  refundStatus: '',
  approvalStatus: '',
  refundTimeStart: '',
  refundTimeEnd: ''
})

// 方法
const getList = async () => {
  try {
    loading.value = true
    
    // 处理时间范围
    if (dateRange.value && dateRange.value.length === 2) {
      queryParams.refundTimeStart = dateRange.value[0]
      queryParams.refundTimeEnd = dateRange.value[1]
    } else {
      queryParams.refundTimeStart = ''
      queryParams.refundTimeEnd = ''
    }
    
    // 根据权限选择API
    const api = isManager.value ? getManagerRefundRecordList : getRefundRecordList
    const response = await api(queryParams)
    
    if (response.code === 200) {
      refundRecordList.value = response.rows || []
      total.value = response.total || 0
    } else {
      ElMessage.error(response.msg || '获取退款记录失败')
    }
  } catch (error) {
    console.error('获取退款记录失败:', error)
    ElMessage.error('获取退款记录失败')
  } finally {
    loading.value = false
  }
}

const getOverview = async () => {
  if (!isManager.value) return
  
  try {
    const response = await getManagerRefundOverview()
    if (response.code === 200) {
      overview.value = response.data
    }
  } catch (error) {
    console.error('获取概览数据失败:', error)
  }
}

const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

const resetQuery = () => {
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    orderNo: '',
    studentName: '',
    refundType: '',
    refundStatus: '',
    approvalStatus: '',
    refundTimeStart: '',
    refundTimeEnd: ''
  })
  dateRange.value = []
  getList()
}

const handleSizeChange = (size) => {
  queryParams.pageSize = size
  queryParams.pageNum = 1
  getList()
}

const handleCurrentChange = (page) => {
  queryParams.pageNum = page
  getList()
}

const handleSelectionChange = (selection) => {
  selectedRecords.value = selection
}

const handleDetail = async (row) => {
  try {
    currentRefundRecordId.value = row.id

    // 获取详情数据
    const api = isManager.value ? getManagerRefundRecordDetail : getRefundRecordDetail
    const response = await api(row.id)

    if (response.code === 200) {
      refundRecordDetail.value = response.data
      detailVisible.value = true
    } else {
      ElMessage.error(response.msg || '获取详情失败')
    }
  } catch (error) {
    console.error('获取退款记录详情失败:', error)
    ElMessage.error('获取详情失败')
  }
}

const handleApprove = (row, result) => {
  currentRefundRecord.value = row
  currentApprovalResult.value = result
  approvalForm.approvalRemark = ''
  approvalVisible.value = true
}

const submitApproval = async () => {
  try {
    approvalSubmitting.value = true

    const approvalData = {
      refundRecordId: currentRefundRecord.value.id,
      approvalResult: currentApprovalResult.value,
      approvalRemark: approvalForm.approvalRemark
    }

    const response = await approveRefundRecord(approvalData)

    if (response.code === 200) {
      ElMessage.success('审批成功')
      approvalVisible.value = false
      getList()
      getOverview()
    } else {
      ElMessage.error(response.msg || '审批失败')
    }
  } catch (error) {
    console.error('审批失败:', error)
    ElMessage.error('审批失败')
  } finally {
    approvalSubmitting.value = false
  }
}

const handleApprovalSuccess = () => {
  getList()
  getOverview()
}

const handleBatchApprove = async (result) => {
  if (selectedRecords.value.length === 0) {
    ElMessage.warning('请选择要审批的记录')
    return
  }
  
  try {
    await ElMessageBox.confirm(`确认要批量${result === 'approved' ? '通过' : '拒绝'}选中的退款记录吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const approvalList = selectedRecords.value.map(record => ({
      refundRecordId: record.id,
      approvalResult: result,
      approvalRemark: `批量${result === 'approved' ? '通过' : '拒绝'}`
    }))
    
    const response = await batchApproveRefundRecords(approvalList)
    
    if (response.code === 200) {
      ElMessage.success(`批量${result === 'approved' ? '通过' : '拒绝'}成功`)
      getList()
      getOverview()
      selectedRecords.value = []
    } else {
      ElMessage.error(response.msg || '批量审批失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量审批失败:', error)
      ElMessage.error('批量审批失败')
    }
  }
}

const handleExport = async () => {
  try {
    const exportParams = { ...queryParams }
    if (dateRange.value && dateRange.value.length === 2) {
      exportParams.refundTimeStart = dateRange.value[0]
      exportParams.refundTimeEnd = dateRange.value[1]
    }
    
    const api = isManager.value ? exportManagerRefundRecords : exportRefundRecords
    await api(exportParams)
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

const handleStatistics = () => {
  ElMessage.info('统计分析功能开发中...')
}

// 工具方法
const getRefundTypeTagType = (type) => {
  const typeMap = {
    'partial': 'warning',
    'full': 'danger'
  }
  return typeMap[type] || 'info'
}

const getRefundStatusTagType = (status) => {
  const statusMap = {
    'processing': 'warning',
    'success': 'success',
    'failed': 'danger'
  }
  return statusMap[status] || 'info'
}

const getApprovalStatusTagType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'auto_approved': 'primary'
  }
  return statusMap[status] || 'info'
}

const canApprove = (row) => {
  return isManager.value && row.approvalStatus === 'pending'
}

// 生命周期
onMounted(() => {
  getList()
  getOverview()
})
</script>

<style scoped>
.refund-records-container {
  padding: 20px;
}

.statistics-cards {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-content {
  padding: 20px 0;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-number.text-warning {
  color: #E6A23C;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.search-card {
  margin-bottom: 20px;
}

.table-card .batch-operations {
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.amount-text {
  font-weight: 500;
  color: #E6A23C;
}
</style>
