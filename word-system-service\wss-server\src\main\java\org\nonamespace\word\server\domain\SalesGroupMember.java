package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nonamespace.word.server.entity.DataEntity;

import java.util.Date;

/**
 * 销售组成员关系实体类
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "sales_group_member", autoResultMap = true)
public class SalesGroupMember extends DataEntity {

    /**
     * 销售组ID
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 销售人员ID
     */
    @TableField("sales_id")
    private String salesId;

    /**
     * 角色类型 (leader: 组长, member: 普通成员)
     */
    @TableField("role_type")
    private String roleType;

    /**
     * 加入时间
     */
    @TableField("join_time")
    private Date joinTime;

    /**
     * 状态 (active: 活跃, inactive: 停用)
     */
    @TableField("status")
    private String status;
}
