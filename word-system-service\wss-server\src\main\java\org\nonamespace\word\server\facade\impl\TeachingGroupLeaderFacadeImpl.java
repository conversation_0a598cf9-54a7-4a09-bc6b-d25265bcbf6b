package org.nonamespace.word.server.facade.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.dto.management.booking.CourseBookingDto;
import org.nonamespace.word.server.dto.management.teachingleader.TeachingGroupLeaderDto;
import org.nonamespace.word.server.facade.ITeachingGroupLeaderFacade;
import org.nonamespace.word.server.service.ITeachingGroupLeaderService;
import org.nonamespace.word.server.util.SystemDataQueryUtil;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 教学组长业务门面实现
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TeachingGroupLeaderFacadeImpl implements ITeachingGroupLeaderFacade {

    private final ITeachingGroupLeaderService teachingGroupLeaderService;
    private final SystemDataQueryUtil systemDataQueryUtil;

    @Override
    public IPage<CourseBookingDto.BasicResp> getPendingApplicationsForLeader(TeachingGroupLeaderDto.GetPendingApplicationsReq req) {
        log.info("获取预约课申请列表: pageNum={}, pageSize={}", req.getPageNum(), req.getPageSize());

        // 获取当前用户ID
        String currentUserId = SecurityUtils.getUserId().toString();

        // 统一查询方法，根据用户角色动态添加数据访问约束
        IPage<CourseBookingDto.BasicResp> result = teachingGroupLeaderService.getApplicationsWithRoleConstraints(currentUserId, req);

        log.info("获取预约课申请列表成功: total={}", result.getTotal());
        return result;
    }

    @Override
    public boolean reviewCourseBookingApplication(TeachingGroupLeaderDto.ReviewApplicationReq req) {
        log.info("教学组长审核预约课申请: applicationId={}, result={}", req.getApplicationId(), req.getReviewResult());

        // 获取当前用户ID
        String currentUserId = SecurityUtils.getUserId().toString();

        // 检查权限
        if (!teachingGroupLeaderService.hasReviewPermission(currentUserId, req.getApplicationId())) {
            throw new RuntimeException("没有权限审核该申请");
        }

        // 执行审核
        boolean success = teachingGroupLeaderService.reviewApplication(req, currentUserId);

        if (success) {
            log.info("教学组长审核预约课申请成功: applicationId={}", req.getApplicationId());
        } else {
            log.warn("教学组长审核预约课申请失败: applicationId={}", req.getApplicationId());
        }

        return success;

    }

    @Override
    public TeachingGroupLeaderDto.BatchReviewResp batchReviewApplications(TeachingGroupLeaderDto.BatchReviewReq req) {
        log.info("教学组长批量审核预约课申请: applicationCount={}", req.getApplicationIds().size());

        // 获取当前用户ID
        String currentUserId = SecurityUtils.getUserId().toString();

        // 执行批量审核
        TeachingGroupLeaderDto.BatchReviewResp result = teachingGroupLeaderService.batchReviewApplications(req, currentUserId);

        log.info("教学组长批量审核完成: success={}, failed={}", result.getSuccessCount(), result.getFailedCount());
        return result;
    }

    @Override
    public TeachingGroupLeaderDto.ReviewStatsResp getReviewStats() {
        log.info("获取教学组长审核统计信息");

        // 获取当前用户ID
        String currentUserId = SecurityUtils.getUserId().toString();

        // 检查用户权限
        if (systemDataQueryUtil.isAdminOrHr()) {
            // Admin和HR可以查看所有统计信息
            log.info("Admin/HR用户查看所有审核统计信息");
            TeachingGroupLeaderDto.ReviewStatsResp result = teachingGroupLeaderService.getAllReviewStats();
            log.info("获取所有审核统计信息成功");
            return result;
        }

        // 获取教学组ID
        String teachingGroupId = teachingGroupLeaderService.getTeachingGroupIdByLeader(currentUserId);
        if (teachingGroupId == null) {
            throw new RuntimeException("当前用户不是教学组长");
        }

        // 获取统计信息
        TeachingGroupLeaderDto.ReviewStatsResp result = teachingGroupLeaderService.getReviewStatsForGroup(teachingGroupId);

        log.info("获取教学组长审核统计信息成功");
        return result;
    }

    @Override
    public List<TeachingGroupLeaderDto.GroupTeacherResp> getGroupTeachers(String groupId) {
        log.info("获取教学组教师列表: groupId={}", groupId);

        String targetGroupId = groupId;

        // 如果没有指定groupId，获取当前用户的教学组
        if (targetGroupId == null) {
            String currentUserId = SecurityUtils.getUserId().toString();
            targetGroupId = teachingGroupLeaderService.getTeachingGroupIdByLeader(currentUserId);
            if (targetGroupId == null) {
                throw new RuntimeException("当前用户不是教学组长");
            }
        }

        // 获取教师列表
        List<TeachingGroupLeaderDto.GroupTeacherResp> result = teachingGroupLeaderService.getGroupTeachers(targetGroupId);

        log.info("获取教学组教师列表成功: count={}", result.size());
        return result;
    }

    @Override
    public List<TeachingGroupLeaderDto.GroupTeacherResp> getAppliedTeachers(String applicationId, String groupId) {
        log.info("获取指定申请中本组内的候选教师列表: applicationId={}, groupId={}", applicationId, groupId);

        String targetGroupId = groupId;

        // 如果没有指定groupId，获取当前用户的教学组
        if (targetGroupId == null) {
            String currentUserId = SecurityUtils.getUserId().toString();
            targetGroupId = teachingGroupLeaderService.getTeachingGroupIdByLeader(currentUserId);
            if (targetGroupId == null) {
                throw new RuntimeException("当前用户不是教学组长");
            }
        }

        // 获取指定申请中本组内的候选教师列表
        List<TeachingGroupLeaderDto.GroupTeacherResp> result = teachingGroupLeaderService.getAppliedTeachers(applicationId, targetGroupId);

        log.info("获取指定申请中本组内候选教师列表成功: applicationId={}, count={}", applicationId, result.size());
        return result;
    }

    @Override
    public List<TeachingGroupLeaderDto.TrialTimeSlotResp> getTeacherAvailableSlots(String teacherId, String applicationId) {
        log.info("获取教师在指定申请试听课时间的可选时间段: teacherId={}, applicationId={}", teacherId, applicationId);

        // 获取可选的试听课时间段
        List<TeachingGroupLeaderDto.TrialTimeSlotResp> result = teachingGroupLeaderService.getTeacherAvailableSlots(teacherId, applicationId);
        result.removeIf(x->!x.getAvailable());

        log.info("获取教师可选试听课时间段成功: teacherId={}, applicationId={}, count={}", teacherId, applicationId, result.size());
        return result;
    }

    @Override
    public boolean assignTeacherToApplication(TeachingGroupLeaderDto.AssignTeacherReq req) {
        log.info("分配教师到申请: applicationId={}, teacherId={}", req.getApplicationId(), req.getTeacherId());

        // 获取当前用户ID
        String currentUserId = SecurityUtils.getUserId().toString();

        // 执行分配
        boolean success = teachingGroupLeaderService.assignTeacherToApplication(req, currentUserId);

        if (success) {
            log.info("分配教师到申请成功: applicationId={}", req.getApplicationId());
        } else {
            log.warn("分配教师到申请失败: applicationId={}", req.getApplicationId());
        }

        return success;

    }

    @Override
    public IPage<TeachingGroupLeaderDto.ReviewHistoryResp> getReviewHistory(TeachingGroupLeaderDto.GetReviewHistoryReq req) {
        log.info("获取审核历史记录: pageNum={}, pageSize={}", req.getPageNum(), req.getPageSize());

        // 获取当前用户ID
        String currentUserId = SecurityUtils.getUserId().toString();

        // 获取教学组ID
        String teachingGroupId = teachingGroupLeaderService.getTeachingGroupIdByLeader(currentUserId);
        if (teachingGroupId == null) {
            throw new RuntimeException("当前用户不是教学组长");
        }

        // 获取审核历史
        IPage<TeachingGroupLeaderDto.ReviewHistoryResp> result = teachingGroupLeaderService.getReviewHistory(teachingGroupId, req);

        log.info("获取审核历史记录成功: total={}", result.getTotal());
        return result;

    }

}
