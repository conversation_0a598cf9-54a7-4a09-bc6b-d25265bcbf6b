package org.nonamespace.word.openai.service.impl;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.openai.config.NebiusConfig;
import org.nonamespace.word.openai.model.WordInfo;
import org.nonamespace.word.openai.model.XAICompletionRequest;
import org.nonamespace.word.openai.service.INebiusService;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class NebiusServiceImpl implements INebiusService {

    @Resource
    private NebiusConfig nebiusConfig;
    @Value("classpath:/prompts/word-enrich.st")
    private org.springframework.core.io.Resource wordEnrichSt;
    @Value("classpath:/prompts/word-enrich-basic.st")
    private org.springframework.core.io.Resource wordEnrichBasicSt;
    @Value("classpath:/prompts/word-enrich-meanings.st")
    private org.springframework.core.io.Resource wordEnrichMeaningsSt;
    @Value("classpath:/prompts/word-enrich-sentences.st")
    private org.springframework.core.io.Resource wordEnrichSentencesSt;
    @Value("classpath:/prompts/word-meamings-replace.txt")
    private org.springframework.core.io.Resource wordFillPormpt;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private static final String CHAT_COMPLETIONS_URL = "/v1/chat/completions";



    @Override
    public List<WordInfo> enrich(List<WordInfo> words) {
        return executeEnrich(words, wordEnrichSt);
    }

    @Override
    public List<WordInfo> enrichBasic(List<WordInfo> words) {
        return this.executeEnrich(words, wordEnrichBasicSt);
    }

    @Override
    public List<WordInfo> enrichMeanings(List<WordInfo> words) {
        return executeEnrich(words, wordEnrichMeaningsSt);
    }

    @Override
    public List<WordInfo> enrichSentences(List<WordInfo> words) {
        return executeEnrich(words, wordEnrichSentencesSt);
    }

    private List<WordInfo> executeEnrich(List<WordInfo> words, org.springframework.core.io.Resource st) {
        try {
            // 将单词列表转换为JSON字符串
            String wordsJson = objectMapper.writeValueAsString(words);
            HttpResponse response = executeHttp(wordsJson, st);

            return parseResponse(response.body());
        } catch (Exception e) {
            log.error("单词信息补充失败", e);
            throw new RuntimeException("单词信息补充失败: " + e.getMessage(), e);
        }
    }


    private HttpResponse executeHttp(String wordsJson, org.springframework.core.io.Resource prompt) {
        try {
            // 将单词列表转换为JSON字符串
            String completionUrl = nebiusConfig.getBaseUrl() + CHAT_COMPLETIONS_URL;

            SystemMessage systemMessage = new SystemMessage(prompt);
            UserMessage userMessage = new UserMessage("输入单词为：" + wordsJson);
            // 系统角色
            XAICompletionRequest.Message xaiSystemMessage = XAICompletionRequest.Message.builder().role("system").content(systemMessage.getText()).build();
            XAICompletionRequest.Message xaiUserMessage = XAICompletionRequest.Message.builder().role("user").content(userMessage.getText()).build();

            // 调用AI服务
            XAICompletionRequest body = XAICompletionRequest.builder()
                    .model(nebiusConfig.getModel())
                    .reasoning_effort(nebiusConfig.getReasoningEffort())
//                    .temperature(nebiusConfig.getTemperature())
                    .messages(List.of(xaiSystemMessage, xaiUserMessage)).build();

            // 创建代理对象
            Proxy proxy = null;
            if(nebiusConfig.getProxy() != null && nebiusConfig.enableProxy()) {
                proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(nebiusConfig.getProxy().getHost(), nebiusConfig.getProxy().getPort()));
            }

            if(proxy != null) {
                return HttpUtil.createPost(completionUrl)
                        .bearerAuth(nebiusConfig.getApiKey())
                        .setProxy(proxy)
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
                        .body(JSONUtil.toJsonStr(body))
                        .execute();
            }

            return HttpUtil.createPost(completionUrl)
                    .bearerAuth(nebiusConfig.getApiKey())
                    .contentType(MediaType.APPLICATION_JSON_VALUE)
                    .body(JSONUtil.toJsonStr(body))
                    .execute();

        } catch (Exception e) {
            log.error("单词信息补充失败", e);
            throw new RuntimeException("单词信息补充失败: " + e.getMessage(), e);
        }

    }

    /**
     * 解析AI响应
     */
    private List<WordInfo> parseResponse(String response) {
        try {
            // 提取JSON部分
            String jsonContent = extractJsonFromResponse(response);
            jsonContent = jsonContent.replace("\\n", "\n")
                    .replace("\\t", "\t")
                    .replace("\\r", "\r")
                    .replace("\\\"", "\"")
                    .replace("\\\\", "\\");
            // 解析JSON
            return objectMapper.readValue(jsonContent, new TypeReference<List<WordInfo>>() {});
        } catch (JsonProcessingException e) {
            log.error("JSON解析失败", e);
            throw new RuntimeException("JSON解析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从AI响应中提取JSON部分
     */
    private String extractJsonFromResponse(String response) {
        // 尝试直接解析，如果是纯JSON
        Pattern pattern = Pattern.compile("```json\\s*(.+?)\\s*```", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(response);

        if (matcher.find()) {
            return matcher.group(1);
        }

        // 如果没有找到JSON标记，尝试直接提取[]包围的内容
        pattern = Pattern.compile("\\[\\s*\\{.+?\\}\\s*\\]", Pattern.DOTALL);
        matcher = pattern.matcher(response);

        if (matcher.find()) {
            return matcher.group(0);
        }

        // 如果还是没找到，返回原始响应
        return response;
    }
}
