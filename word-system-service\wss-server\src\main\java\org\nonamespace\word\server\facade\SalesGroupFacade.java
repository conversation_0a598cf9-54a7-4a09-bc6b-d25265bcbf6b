package org.nonamespace.word.server.facade;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.nonamespace.word.server.dto.sales.SalesGroupDto;

import java.util.List;

/**
 * 销售组业务门面接口
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
public interface SalesGroupFacade {

    IPage<SalesGroupDto.Resp> getOptions(SalesGroupDto.GetListReq req);

    /**
     * 分页查询销售组列表
     * 
     * @param req 查询条件
     * @return 销售组分页列表
     */
    IPage<SalesGroupDto.Resp> getSalesGroupPage(SalesGroupDto.GetListReq req);

    /**
     * 根据ID查询销售组详情
     * 
     * @param id 销售组ID
     * @return 销售组详情
     */
    SalesGroupDto.Resp getSalesGroupById(String id);

    /**
     * 创建销售组
     * 
     * @param req 创建请求
     * @return 销售组ID
     */
    String createSalesGroup(SalesGroupDto.CreateReq req);

    /**
     * 更新销售组
     * 
     * @param req 更新请求
     * @return 是否成功
     */
    boolean updateSalesGroup(SalesGroupDto.UpdateReq req);

    /**
     * 删除销售组
     * 
     * @param id 销售组ID
     * @return 是否成功
     */
    boolean deleteSalesGroup(String id);

    /**
     * 批量删除销售组
     * 
     * @param ids 销售组ID列表
     * @return 是否成功
     */
    boolean deleteSalesGroups(List<String> ids);

    /**
     * 获取销售组统计信息
     * 
     * @return 统计信息
     */
    SalesGroupDto.StatsResp getSalesGroupStats();

    /**
     * 分页查询销售组成员列表
     * 
     * @param req 查询条件
     * @return 成员分页列表
     */
    IPage<SalesGroupDto.MemberResp> getSalesGroupMembersPage(SalesGroupDto.GetMembersReq req);

    /**
     * 查询销售组所有成员
     * 
     * @param groupId 销售组ID
     * @return 成员列表
     */
    List<SalesGroupDto.MemberResp> getSalesGroupMembers(String groupId);

    /**
     * 添加成员到销售组
     * 
     * @param req 添加成员请求
     * @return 是否成功
     */
    boolean addSalesGroupMembers(SalesGroupDto.AddMembersReq req);

    /**
     * 从销售组移除成员
     * 
     * @param req 移除成员请求
     * @return 是否成功
     */
    boolean removeSalesGroupMember(SalesGroupDto.RemoveMemberReq req);

    /**
     * 设置销售组组长
     * 
     * @param req 设置组长请求
     * @return 是否成功
     */
    boolean setSalesGroupLeader(SalesGroupDto.SetLeaderReq req);
}
