<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.nonamespace.word.server.mapper.order.OrderRefundRecordMapper">

    <resultMap type="org.nonamespace.word.server.dto.order.RefundRecordDto$Resp" id="RefundRecordRespResult">
        <result property="id" column="id"/>
        <result property="orderId" column="order_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="refundType" column="refund_type"/>
        <result property="refundTypeDesc" column="refund_type_desc"/>
        <result property="refundAmountYuan" column="refund_amount_yuan"/>
        <result property="refundReason" column="refund_reason"/>
        <result property="refundStatus" column="refund_status"/>
        <result property="refundStatusDesc" column="refund_status_desc"/>
        <result property="studentName" column="student_name"/>
        <result property="studentPhone" column="student_phone"/>
        <result property="salerName" column="saler_name"/>
        <result property="productName" column="product_name"/>
        <result property="subject" column="subject"/>
        <result property="courseType" column="course_type"/>
        <result property="refundMethod" column="refund_method"/>
        <result property="refundMethodDesc" column="refund_method_desc"/>
        <result property="operatorName" column="operator_name"/>
        <result property="operatorRole" column="operator_role"/>
        <result property="approvalStatus" column="approval_status"/>
        <result property="approvalStatusDesc" column="approval_status_desc"/>
        <result property="approverName" column="approver_name"/>
        <result property="approvalTime" column="approval_time"/>
        <result property="approvalRemark" column="approval_remark"/>
        <result property="refundTime" column="refund_time"/>
        <result property="createTime" column="create_time"/>
        <result property="errorMessage" column="error_message"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <resultMap type="org.nonamespace.word.server.dto.order.RefundRecordDto$DetailResp" id="RefundRecordDetailResult">
        <result property="id" column="id"/>
        <result property="orderId" column="order_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="originalTrxId" column="original_trx_id"/>
        <result property="refundTrxId" column="refund_trx_id"/>
        <result property="refundType" column="refund_type"/>
        <result property="refundTypeDesc" column="refund_type_desc"/>
        <result property="refundAmountYuan" column="refund_amount_yuan"/>
        <result property="refundReason" column="refund_reason"/>
        <result property="refundStatus" column="refund_status"/>
        <result property="refundStatusDesc" column="refund_status_desc"/>
        <result property="studentName" column="student_name"/>
        <result property="studentPhone" column="student_phone"/>
        <result property="salerName" column="saler_name"/>
        <result property="productName" column="product_name"/>
        <result property="subject" column="subject"/>
        <result property="courseType" column="course_type"/>
        <result property="refundMethod" column="refund_method"/>
        <result property="refundMethodDesc" column="refund_method_desc"/>
        <result property="platformRefundId" column="platform_refund_id"/>
        <result property="platformResponse" column="platform_response"/>
        <result property="operatorId" column="operator_id"/>
        <result property="operatorName" column="operator_name"/>
        <result property="operatorRole" column="operator_role"/>
        <result property="approvalStatus" column="approval_status"/>
        <result property="approvalStatusDesc" column="approval_status_desc"/>
        <result property="approverId" column="approver_id"/>
        <result property="approverName" column="approver_name"/>
        <result property="approvalTime" column="approval_time"/>
        <result property="approvalRemark" column="approval_remark"/>
        <result property="refundTime" column="refund_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="errorMessage" column="error_message"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <select id="selectRefundRecordsByParam" parameterType="org.nonamespace.word.server.dto.order.RefundRecordDto$QueryReq" resultMap="RefundRecordRespResult">
        SELECT
            r.id,
            r.order_id,
            r.order_no,
            r.refund_type,
            CASE r.refund_type
                WHEN 'partial' THEN '部分退款'
                WHEN 'full' THEN '全额退款'
                ELSE r.refund_type
            END as refund_type_desc,
            CONCAT(FORMAT(r.refund_amount / 100, 2)) as refund_amount_yuan,
            r.refund_reason,
            r.refund_status,
            CASE r.refund_status
                WHEN 'processing' THEN '处理中'
                WHEN 'success' THEN '成功'
                WHEN 'failed' THEN '失败'
                ELSE r.refund_status
            END as refund_status_desc,
            r.student_name,
            r.student_phone,
            r.saler_name,
            r.product_name,
            r.subject,
            r.course_type,
            r.refund_method,
            CASE r.refund_method
                WHEN 'original' THEN '原路退回'
                WHEN 'manual' THEN '手动退款'
                ELSE r.refund_method
            END as refund_method_desc,
            r.operator_name,
            r.operator_role,
            r.approval_status,
            CASE r.approval_status
                WHEN 'pending' THEN '待审批'
                WHEN 'approved' THEN '已审批'
                WHEN 'rejected' THEN '已拒绝'
                WHEN 'auto_approved' THEN '自动审批'
                ELSE r.approval_status
            END as approval_status_desc,
            r.approver_name,
            r.approval_time,
            r.approval_remark,
            r.refund_time,
            r.create_time,
            r.error_message,
            r.remark
        FROM order_refund_records r
        WHERE r.del_flag = '0'
        <if test="req.orderNo != null and req.orderNo != ''">
            AND r.order_no LIKE CONCAT('%', #{req.orderNo}::text, '%')
        </if>
        <if test="req.orderId != null and req.orderId != ''">
            AND r.order_id = #{req.orderId}
        </if>
        <if test="req.refundType != null and req.refundType != ''">
            AND r.refund_type = #{req.refundType}
        </if>
        <if test="req.refundStatus != null and req.refundStatus != ''">
            AND r.refund_status = #{req.refundStatus}
        </if>
        <if test="req.approvalStatus != null and req.approvalStatus != ''">
            AND r.approval_status = #{req.approvalStatus}
        </if>
        <if test="req.studentName != null and req.studentName != ''">
            AND r.student_name LIKE CONCAT('%', #{req.studentName}::text, '%')
        </if>
        <if test="req.studentPhone != null and req.studentPhone != ''">
            AND r.student_phone LIKE CONCAT('%', #{req.studentPhone}::text, '%')
        </if>
        <if test="req.salerName != null and req.salerName != ''">
            AND r.saler_name LIKE CONCAT('%', #{req.salerName}::text, '%')
        </if>
        <if test="req.operatorName != null and req.operatorName != ''">
            AND r.operator_name LIKE CONCAT('%', #{req.operatorName}::text, '%')
        </if>
        <if test="req.subject != null and req.subject != ''">
            AND r.subject = #{req.subject}
        </if>
        <if test="req.courseType != null and req.courseType != ''">
            AND r.course_type = #{req.courseType}
        </if>
        <if test="req.refundTimeStart != null">
            AND r.refund_time &gt;= #{req.refundTimeStart}
        </if>
        <if test="req.refundTimeEnd != null">
            AND r.refund_time &lt;= #{req.refundTimeEnd}
        </if>
        <if test="req.createTimeStart != null">
            AND r.create_time &gt;= #{req.createTimeStart}
        </if>
        <if test="req.createTimeEnd != null">
            AND r.create_time &lt;= #{req.createTimeEnd}
        </if>
        <if test="req.minRefundAmount != null">
            AND r.refund_amount &gt;= #{req.minRefundAmount}
        </if>
        <if test="req.maxRefundAmount != null">
            AND r.refund_amount &lt;= #{req.maxRefundAmount}
        </if>
        ORDER BY
        <choose>
            <when test="req.orderBy == 'refund_time'">r.refund_time</when>
            <when test="req.orderBy == 'refund_amount'">r.refund_amount</when>
            <when test="req.orderBy == 'approval_time'">r.approval_time</when>
            <otherwise>r.create_time</otherwise>
        </choose>
        <choose>
            <when test="req.orderDirection == 'ASC'">ASC</when>
            <otherwise>DESC</otherwise>
        </choose>
    </select>

    <select id="selectRefundRecordDetail" parameterType="string" resultMap="RefundRecordDetailResult">
        SELECT
            r.*,
            CASE r.refund_type
                WHEN 'partial' THEN '部分退款'
                WHEN 'full' THEN '全额退款'
                ELSE r.refund_type
            END as refund_type_desc,
            CONCAT(FORMAT(r.refund_amount / 100, 2)) as refund_amount_yuan,
            CASE r.refund_status
                WHEN 'processing' THEN '处理中'
                WHEN 'success' THEN '成功'
                WHEN 'failed' THEN '失败'
                ELSE r.refund_status
            END as refund_status_desc,
            CASE r.refund_method
                WHEN 'original' THEN '原路退回'
                WHEN 'manual' THEN '手动退款'
                ELSE r.refund_method
            END as refund_method_desc,
            CASE r.approval_status
                WHEN 'pending' THEN '待审批'
                WHEN 'approved' THEN '已审批'
                WHEN 'rejected' THEN '已拒绝'
                WHEN 'auto_approved' THEN '自动审批'
                ELSE r.approval_status
            END as approval_status_desc
        FROM order_refund_records r
        WHERE r.id = #{refundRecordId} AND r.del_flag = '0'
    </select>
</mapper>