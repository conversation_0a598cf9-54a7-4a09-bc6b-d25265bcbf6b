package org.nonamespace.word.rest.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.nonamespace.word.rest.base.controller.BaseController;
import org.nonamespace.word.server.dto.management.student.StudentCourseHoursDto;
import org.nonamespace.word.server.service.IStudentCourseHoursManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import static org.nonamespace.word.common.utils.ExceptionUtils.getErrorMessage;

/**
 * 学生课时管理控制器
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@RestController
@RequestMapping("/management/student-course-hours")
public class StudentCourseHoursManagementController extends BaseController {

    @Autowired
    private IStudentCourseHoursManagementService studentCourseHoursManagementService;

    /**
     * 分页查询学生课时记录
     */
    @PreAuthorize("@ss.hasPermi('management:student-course-hours:list')")
    @GetMapping("/list")
    @Log(title = "课时查询", businessType = BusinessType.UPDATE)
    public TableDataInfo list(StudentCourseHoursDto.QueryRequest request) {
        try {
            Page<StudentCourseHoursDto.CourseHoursResponse> page = studentCourseHoursManagementService.queryStudentCourseHours(request);
            return getDataTable(page);
        } catch (Exception e) {
            log.error("查询学生课时记录失败", e);
            return pageError(500, "查询学生课时记录失败: " + getErrorMessage(e));
        }
    }

    /**
     * 手动调整学生课时
     */
    @PreAuthorize("@ss.hasPermi('management:student-course-hours:adjust')")
    @Log(title = "学生课时调整", businessType = BusinessType.UPDATE)
    @PostMapping("/adjust")
    public AjaxResult adjust(@Validated @RequestBody StudentCourseHoursDto.AdjustRequest request) {
        try {
            String operatorId = SecurityUtils.getUserId().toString();
            String operatorName = SecurityUtils.getUsername();

            boolean result = studentCourseHoursManagementService.adjustStudentCourseHours(request, operatorId, operatorName);

            if (result) {
                return AjaxResult.success("课时调整成功");
            } else {
                return AjaxResult.error("课时调整失败");
            }
        } catch (Exception e) {
            log.error("课时调整失败", e);
            return AjaxResult.error("课时调整失败: " + e.getMessage());
        }
    }

    /**
     * 更新学生课时（直接设置课时值）
     */
    @PreAuthorize("@ss.hasPermi('management:student-course-hours:update')")
    @Log(title = "学生课时更新", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public AjaxResult update(@Validated @RequestBody StudentCourseHoursDto.UpdateRequest request) {
        try {
            String operatorId = SecurityUtils.getUserId().toString();
            String operatorName = SecurityUtils.getUsername();

            boolean result = studentCourseHoursManagementService.updateStudentCourseHours(request, operatorId, operatorName);

            if (result) {
                return AjaxResult.success("课时更新成功");
            } else {
                return AjaxResult.error("课时更新失败");
            }
        } catch (Exception e) {
            log.error("课时更新失败", e);
            return AjaxResult.error("课时更新失败: " + e.getMessage());
        }
    }

    /**
     * 录入课消
     */
    @PreAuthorize("@ss.hasPermi('management:student-course-hours:consume')")
    @Log(title = "录入课消", businessType = BusinessType.INSERT)
    @PostMapping("/consume")
    public AjaxResult consume(@Validated @RequestBody StudentCourseHoursDto.RecordConsumptionRequest request) {
        try {
            String operatorId = SecurityUtils.getUserId().toString();
            String operatorName = SecurityUtils.getUsername();

            boolean result = studentCourseHoursManagementService.recordCourseConsumption(request, operatorId, operatorName);

            if (result) {
                return AjaxResult.success("课消录入成功");
            } else {
                return AjaxResult.error("课消录入失败");
            }
        } catch (Exception e) {
            log.error("课消录入失败", e);
            return AjaxResult.error("课消录入失败: " + e.getMessage());
        }
    }

    /**
     * 导出学生课时数据
     */
    @PreAuthorize("@ss.hasPermi('management:student-course-hours:export')")
    @Log(title = "导出学生课时", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@RequestBody StudentCourseHoursDto.QueryRequest request, HttpServletResponse response) {
        try {
            List<StudentCourseHoursDto.ExportResponse> exportData = studentCourseHoursManagementService.exportStudentCourseHours(request);

            // 创建Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("学生课时数据");

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "学生姓名", "学生手机", "老师", "学科", "课型", "性质",
                "总课时", "购买课时", "赠送课时", "剩余购买课时", "剩余赠送课时",
                "剩余课时", "已消耗课时", "单价", "批次号", "创建时间"
            };

            // 创建标题样式
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            headerStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 设置标题
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // 创建数据行
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            for (int i = 0; i < exportData.size(); i++) {
                Row row = sheet.createRow(i + 1);
                StudentCourseHoursDto.ExportResponse data = exportData.get(i);

                row.createCell(0).setCellValue(data.getStudentName() != null ? data.getStudentName() : "");
                row.createCell(1).setCellValue(data.getStudentPhone() != null ? data.getStudentPhone() : "");
                row.createCell(2).setCellValue(data.getTeacherInfo() != null ? data.getTeacherInfo() : "");
                row.createCell(3).setCellValue(data.getSubject() != null ? data.getSubject() : "");
                row.createCell(4).setCellValue(data.getSpecification() != null ? data.getSpecification() : "");
                row.createCell(5).setCellValue(data.getNature() != null ? data.getNature() : "");
                row.createCell(6).setCellValue(data.getTotalHours() != null ? data.getTotalHours().doubleValue() : 0);
                row.createCell(7).setCellValue(data.getPurchasedHours() != null ? data.getPurchasedHours().doubleValue() : 0);
                row.createCell(8).setCellValue(data.getGiftHours() != null ? data.getGiftHours().doubleValue() : 0);
                row.createCell(9).setCellValue(data.getRemainingPurchasedHours() != null ? data.getRemainingPurchasedHours().doubleValue() : 0);
                row.createCell(10).setCellValue(data.getRemainingGiftHours() != null ? data.getRemainingGiftHours().doubleValue() : 0);
                row.createCell(11).setCellValue(data.getRemainingHours() != null ? data.getRemainingHours().doubleValue() : 0);
                row.createCell(12).setCellValue(data.getConsumedTotalHours() != null ? data.getConsumedTotalHours().doubleValue() : 0);
                row.createCell(13).setCellValue(data.getUnitPrice() != null ? data.getUnitPrice().doubleValue() : 0);
                row.createCell(14).setCellValue(data.getBatchNo() != null ? data.getBatchNo() : "");
                row.createCell(15).setCellValue(data.getCreateTime() != null ? dateFormat.format(data.getCreateTime()) : "");
            }

            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // 设置响应头
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
            String fileName = "学生课时数据_" + timestamp + ".xlsx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);

            // 写入响应
            workbook.write(response.getOutputStream());
            workbook.close();

            log.info("导出学生课时数据成功，共导出{}条记录", exportData.size());

        } catch (Exception e) {
            log.error("导出学生课时数据失败", e);
            try {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500,\"msg\":\"导出失败: " + e.getMessage() + "\"}");
            } catch (IOException ioException) {
                log.error("写入错误响应失败", ioException);
            }
        }
    }

    /**
     * 新增学生课时
     */
    @PreAuthorize("@ss.hasPermi('management:student-course-hours:add')")
    @Log(title = "学生课时新增", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody StudentCourseHoursDto.AddCourseHoursRequest request) {
        try {
            String operatorId = SecurityUtils.getUserId().toString();
            String operatorName = SecurityUtils.getUsername();

            boolean result = studentCourseHoursManagementService.addStudentCourseHours(request, operatorId, operatorName);

            if (result) {
                return AjaxResult.success("课时新增成功");
            } else {
                return AjaxResult.error("课时新增失败");
            }
        } catch (Exception e) {
            log.error("课时新增失败", e);
            return AjaxResult.error("课时新增失败: " + e.getMessage());
        }
    }

    /**
     * 获取学生课时详情
     */
    @PreAuthorize("@ss.hasPermi('management:student-course-hours:list')")
    @GetMapping("/detail")
    public AjaxResult getStudentCourseHoursDetail(@RequestParam String studentId,
                                                 @RequestParam String subject,
                                                 @RequestParam String specification) {
        try {
            StudentCourseHoursDto.CourseHoursResponse detail = studentCourseHoursManagementService.getStudentCourseHoursDetail(studentId, subject, specification);
            return AjaxResult.success(detail);
        } catch (Exception e) {
            log.error("获取学生课时详情失败", e);
            return AjaxResult.error("获取学生课时详情失败: " + e.getMessage());
        }
    }

    /**
     * 根据课时包ID获取课时包详情
     */
    @PreAuthorize("@ss.hasPermi('management:student-course-hours:list')")
    @GetMapping("/{courseHoursId}")
    public AjaxResult getCourseHoursById(@PathVariable String courseHoursId) {
        try {
            StudentCourseHoursDto.CourseHoursResponse detail = studentCourseHoursManagementService.getCourseHoursById(courseHoursId);
            return AjaxResult.success(detail);
        } catch (Exception e) {
            log.error("获取课时包详情失败", e);
            return AjaxResult.error("获取课时包详情失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询课消记录
     */
    @PreAuthorize("@ss.hasPermi('management:student-course-hours:consumption')")
    @GetMapping("/consumption/list")
    public TableDataInfo consumptionList(StudentCourseHoursDto.ConsumptionQueryRequest request) {
        try {
            Page<StudentCourseHoursDto.ConsumptionResponse> page = studentCourseHoursManagementService.queryConsumptionRecords(request);
            return getDataTable(page);
        } catch (Exception e) {
            log.error("查询课消记录失败", e);
            return pageError(500, "查询课消记录失败: " + getErrorMessage(e));
        }
    }

    /**
     * 分页查询调整历史记录
     */
    @PreAuthorize("@ss.hasPermi('management:student-course-hours:history')")
    @GetMapping("/adjustment/history")
    public TableDataInfo adjustmentHistory(StudentCourseHoursDto.AdjustmentHistoryQueryRequest request) {
        try {
            Page<StudentCourseHoursDto.AdjustmentHistoryResponse> page = studentCourseHoursManagementService.queryAdjustmentHistory(request);
            return getDataTable(page);
        } catch (Exception e) {
            log.error("查询调整历史记录失败", e);
            return pageError(500, "查询调整历史记录失败: " + getErrorMessage(e));
        }
    }





}
