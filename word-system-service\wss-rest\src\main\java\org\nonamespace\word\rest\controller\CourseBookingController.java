package org.nonamespace.word.rest.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.dto.management.booking.CourseBookingDto;
import org.nonamespace.word.server.facade.ICourseBookingFacade;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 预约课申请Controller
 * 遵循编码规范：Controller只负责参数验证和调用Facade层
 *
 * <AUTHOR> Assistant
 * @date 2025-01-01
 */
@Slf4j
@RestController
@RequestMapping("/course-booking")
@RequiredArgsConstructor
@Validated
public class CourseBookingController {

    private final ICourseBookingFacade courseBookingFacade;

    /**
     * 提交预约课申请（原有接口，保持兼容性）
     */
    @PreAuthorize("@ss.hasPermi('sales:student:booking')")
    @Log(title = "预约课申请", businessType = BusinessType.INSERT)
    @PostMapping("/apply")
    public AjaxResult submitApplication(@Valid @RequestBody CourseBookingDto.CreateReq request) {
        try {
            log.info("收到预约课申请: studentId={}, preferredTeachers={}",
                    request.getStudentId(), request.getPreferredTeachers());

            String applicationId = courseBookingFacade.createCourseBooking(request);

            log.info("预约课申请提交成功: applicationId={}", applicationId);
            return AjaxResult.success("申请提交成功", applicationId);
        } catch (Exception e) {
            log.error("提交预约课申请失败", e);
            return AjaxResult.error("申请提交失败: " + e.getMessage());
        }
    }

    /**
     * 提交预约课申请（支持试听课时间）
     */
    @PreAuthorize("@ss.hasPermi('sales:student:booking')")
    @Log(title = "预约课申请（试听课时间）", businessType = BusinessType.INSERT)
    @PostMapping("/apply-with-trial-time")
    public AjaxResult submitApplicationWithTrialTime(@Valid @RequestBody CourseBookingDto.CreateReq request) {
        try {
            log.info("收到预约课申请（试听课时间）: studentId={}, preferredTeachers={}, trialTime={}",
                    request.getStudentId(), request.getPreferredTeachers(), request.getTrialClassTime());

            String applicationId = courseBookingFacade.createCourseBookingWithTrialTime(request);

            log.info("预约课申请提交成功: applicationId={}", applicationId);
            return AjaxResult.success("申请提交成功", applicationId);
        } catch (Exception e) {
            log.error("提交预约课申请失败", e);
            return AjaxResult.error("申请提交失败: " + e.getMessage());
        }
    }

    /**
     * 获取申请详情
     */
    @PreAuthorize("@ss.hasPermi('course:booking:query')")
    @GetMapping("/{id}")
    public AjaxResult getApplicationDetail(@PathVariable String id) {
        try {
            log.info("获取申请详情: applicationId={}", id);

            CourseBookingDto.DetailResp detail = courseBookingFacade.getCourseBookingDetail(id);

            log.info("获取申请详情成功: applicationId={}, status={}", id, detail.getStatus());
            return AjaxResult.success(detail);
        } catch (Exception e) {
            log.error("获取申请详情失败", e);
            return AjaxResult.error("获取申请详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取申请列表（分页）
     */
    @PreAuthorize("@ss.hasPermi('course:booking:list')")
    @GetMapping("/list")
    public AjaxResult getApplicationList(@Valid CourseBookingDto.GetListReq request) {
        try {
            log.info("获取申请列表: req={}", request);

            IPage<CourseBookingDto.BasicResp> result = courseBookingFacade.getCourseBookingPage(request);

            log.info("获取申请列表成功: total={}", result.getTotal());
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("获取申请列表失败", e);
            return AjaxResult.error("获取申请列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取预约课申请统计信息
     */
    @PreAuthorize("@ss.hasPermi('course:booking:stats')")
    @GetMapping("/stats")
    public AjaxResult getStats() {
        try {
            log.info("获取预约课申请统计信息");

            CourseBookingDto.StatsResp stats = courseBookingFacade.getCourseBookingStats();

            log.info("获取统计信息成功");
            return AjaxResult.success(stats);
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            return AjaxResult.error("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取可选教师列表
     */
    @PreAuthorize("@ss.hasPermi('course:booking:teachers')")
    @GetMapping("/available-teachers")
    public AjaxResult getAvailableTeachers(@Valid CourseBookingDto.AvailableTeachersReq request) {
        try {
            log.info("获取可选教师列表: req={}", request);

            java.util.List<CourseBookingDto.AvailableTeacherResp> teachers =
                courseBookingFacade.getAvailableTeachers(request);

            log.info("获取可选教师列表成功: count={}", teachers.size());
            return AjaxResult.success(teachers);
        } catch (Exception e) {
            log.error("获取可选教师列表失败", e);
            return AjaxResult.error("获取可选教师列表失败: " + e.getMessage());
        }
    }

    /**
     * 确认预约课申请
     */
    @PreAuthorize("@ss.hasPermi('course:booking:approve')")
    @Log(title = "确认预约课申请", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/confirm")
    public AjaxResult confirmApplication(@PathVariable String id,
                                       @Valid @RequestBody CourseBookingDto.ApprovalReq request) {
        try {
            log.info("确认预约课申请: applicationId={}, approvedTeacherId={}",
                    id, request.getApprovedTeacherId());

            request.setApplicationId(id);
            boolean success = courseBookingFacade.approveCourseBooking(request);

            if (success) {
                log.info("确认预约课申请成功: applicationId={}", id);
                return AjaxResult.success("申请确认成功");
            } else {
                return AjaxResult.error("申请确认失败");
            }
        } catch (Exception e) {
            log.error("确认预约课申请失败", e);
            return AjaxResult.error("申请确认失败: " + e.getMessage());
        }
    }

    /**
     * 拒绝预约课申请
     */
    @PreAuthorize("@ss.hasPermi('course:booking:reject')")
    @Log(title = "拒绝预约课申请", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/reject")
    public AjaxResult rejectApplication(@PathVariable String id,
                                      @Valid @RequestBody CourseBookingDto.RejectionReq request) {
        try {
            log.info("拒绝预约课申请: applicationId={}, reason={}",
                    id, request.getRejectionReason());

            request.setApplicationId(id);
            boolean success = courseBookingFacade.rejectCourseBooking(request);

            if (success) {
                log.info("拒绝预约课申请成功: applicationId={}", id);
                return AjaxResult.success("申请拒绝成功");
            } else {
                return AjaxResult.error("申请拒绝失败");
            }
        } catch (Exception e) {
            log.error("拒绝预约课申请失败", e);
            return AjaxResult.error("申请拒绝失败: " + e.getMessage());
        }
    }

    /**
     * 作废预约课申请
     */
    @PreAuthorize("@ss.hasAnyRoles('admin,hr,sales')")
    @Log(title = "作废预约课申请", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/void")
    public AjaxResult voidApplication(@PathVariable String id,
                                    @Valid @RequestBody CourseBookingDto.VoidReq request) {
        try {
            log.info("作废预约课申请: applicationId={}, reason={}",
                    id, request.getVoidReason());

            request.setApplicationId(id);
            boolean success = courseBookingFacade.voidCourseBooking(request);

            if (success) {
                log.info("作废预约课申请成功: applicationId={}", id);
                return AjaxResult.success("申请作废成功");
            } else {
                return AjaxResult.error("申请作废失败");
            }
        } catch (Exception e) {
            log.error("作废预约课申请失败", e);
            return AjaxResult.error("申请作废失败: " + e.getMessage());
        }
    }

    /**
     * 检查是否可以催促申请
     */
    @PreAuthorize("@ss.hasPermi('course:booking:remind')")
    @GetMapping("/{id}/can-remind")
    public AjaxResult canRemindApplication(@PathVariable String id) {
        try {
            log.info("检查催促权限: applicationId={}", id);

            // 通过Facade获取申请详情来检查状态
            CourseBookingDto.DetailResp detail = courseBookingFacade.getCourseBookingDetail(id);

            // 检查申请状态
            if (!"PENDING".equals(detail.getStatus())) {
                return AjaxResult.success(java.util.Map.of(
                    "canRemind", false,
                    "reason", "申请已处理，无需催促"
                ));
            }

            // 简化时间检查逻辑，实际可以在Facade层实现更复杂的逻辑
            log.info("可以催促申请: applicationId={}", id);
            return AjaxResult.success(java.util.Map.of(
                "canRemind", true,
                "reason", "可以催促"
            ));
        } catch (Exception e) {
            log.error("检查催促权限失败", e);
            return AjaxResult.error("检查催促权限失败: " + e.getMessage());
        }
    }

    /**
     * 催促处理申请
     */
    @PreAuthorize("@ss.hasPermi('course:booking:remind')")
    @Log(title = "催促处理申请", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/remind")
    public AjaxResult remindApplication(@PathVariable String id) {
        try {
            log.info("催促处理申请: applicationId={}", id);

            // 发送催促通知
            boolean success = courseBookingFacade.sendWechatNotification(id, "REMIND");

            if (success) {
                log.info("催促通知发送成功: applicationId={}", id);
                return AjaxResult.success("催促成功");
            } else {
                return AjaxResult.error("催促失败");
            }
        } catch (Exception e) {
            log.error("催促处理申请失败", e);
            return AjaxResult.error("催促失败: " + e.getMessage());
        }
    }

    /**
     * 获取申请历史（兼容旧API路径）
     */
    @PreAuthorize("@ss.hasPermi('course:booking:list')")
    @GetMapping("/history")
    public AjaxResult getApplicationHistory(@RequestParam(required = false) String studentId,
                                          @RequestParam(required = false) String status,
                                          @RequestParam(defaultValue = "1") Integer pageNum,
                                          @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            log.info("获取申请历史: studentId={}, status={}", studentId, status);

            CourseBookingDto.GetListReq request = new CourseBookingDto.GetListReq();
            request.setStudentId(studentId);  // 现在可以正确设置studentId
            request.setStatus(status);
            request.setPageNum(pageNum);
            request.setPageSize(pageSize);

            IPage<CourseBookingDto.BasicResp> result = courseBookingFacade.getCourseBookingPage(request);

            log.info("获取申请历史成功: total={}", result.getTotal());
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("获取申请历史失败", e);
            return AjaxResult.error("获取申请历史失败: " + e.getMessage());
        }
    }

    /**
     * 发送微信通知
     */
    @PreAuthorize("@ss.hasPermi('course:booking:notify')")
    @PostMapping("/{id}/notify")
    public AjaxResult sendNotification(@PathVariable String id,
                                     @RequestParam String notificationType) {
        try {
            log.info("发送微信通知: applicationId={}, type={}", id, notificationType);

            boolean success = courseBookingFacade.sendWechatNotification(id, notificationType);

            if (success) {
                log.info("发送微信通知成功: applicationId={}", id);
                return AjaxResult.success("通知发送成功");
            } else {
                return AjaxResult.error("通知发送失败");
            }
        } catch (Exception e) {
            log.error("发送微信通知失败", e);
            return AjaxResult.error("发送微信通知失败: " + e.getMessage());
        }
    }
}
