package org.nonamespace.word.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.nonamespace.word.server.domain.StudentCourseConsumption;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 学生课消记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IStudentCourseConsumptionService extends IService<StudentCourseConsumption> {
    
    /**
     * 记录课消
     *
     * @param studentId 学生ID
     * @param subject 学科
     * @param specification 课型
     * @param consumedHours 消费课时数
     * @param consumptionTime 消费时间
     * @param courseId 课程ID
     * @param courseHoursId 课时记录ID
     * @param teacherId 老师ID
     * @param remark 备注
     * @return 课消记录
     */
    StudentCourseConsumption recordConsumption(String studentId, String subject, String specification,
                                             BigDecimal consumedHours, Date consumptionTime,
                                             String courseId, String courseHoursId, String teacherId, String remark);

    /**
     * 记录课消（包含课程性质）
     *
     * @param studentId 学生ID
     * @param subject 学科
     * @param specification 课型
     * @param nature 课程性质（试听课、正式课）
     * @param consumedHours 消费课时数
     * @param consumptionTime 消费时间
     * @param courseId 课程ID
     * @param courseHoursId 课时记录ID
     * @param teacherId 老师ID
     * @param remark 备注
     * @return 课消记录
     */
    StudentCourseConsumption recordConsumption(String studentId, String subject, String specification, String nature,
                                             BigDecimal consumedHours, Date consumptionTime,
                                             String courseId, String courseHoursId, String teacherId, String remark);
}
