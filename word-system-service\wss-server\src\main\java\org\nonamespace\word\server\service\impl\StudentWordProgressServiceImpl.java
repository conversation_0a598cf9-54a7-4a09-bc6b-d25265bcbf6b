package org.nonamespace.word.server.service.impl;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.nonamespace.word.server.domain.Textbook;
import org.nonamespace.word.server.domain.TextbookItem;
import org.nonamespace.word.server.domain.Word;
import org.nonamespace.word.server.dto.LastWordDto;
import org.nonamespace.word.server.dto.TextbookItemTreeQueryDto;
import org.nonamespace.word.server.enums.TextBookNodeEnum;
import org.nonamespace.word.server.service.IWordService;
import org.nonamespace.word.server.vo.TextbookTreeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.nonamespace.word.server.mapper.StudentWordProgressMapper;
import org.nonamespace.word.server.domain.StudentWordProgress;
import org.nonamespace.word.server.service.IStudentWordProgressService;

/**
 * 学生单词学习进度Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Slf4j
@Service
public class StudentWordProgressServiceImpl extends ServiceImpl<StudentWordProgressMapper, StudentWordProgress> implements IStudentWordProgressService {

    @Autowired
    private TextbookItemService itemService;

    @Autowired
    private IWordService wordService;

    @Override
    public LastWordDto getProgressByStudentId(String studentId,String textbookId) {
        StudentWordProgress studentWordProgresses = this.getByNotDelBook(studentId,textbookId);
        LastWordDto lastWordDto = new LastWordDto();
        if(studentWordProgresses != null){
            TextbookItem item = itemService.getById(studentWordProgresses.getTextbookItemId());
            Word word = wordService.getById(studentWordProgresses.getWordId());
            lastWordDto.setTextbookId(studentWordProgresses.getTextbookId())
                    .setUnitItemId(item.getPid())
                    .setWordItemId(studentWordProgresses.getTextbookItemId())
                    .setWord(word.getWord());
        }
        return lastWordDto;
    }

    @Override
    public StudentWordProgress getByNotDelBook(String studentId,String textbookId) {
        return baseMapper.getByNotDelBook(studentId,textbookId);
    }
}
