package org.nonamespace.word.server.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;

import jakarta.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * Excel导出工具类
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Slf4j
public class ExcelExportUtil {

    /**
     * 导出Excel到响应流
     * 
     * @param response HTTP响应
     * @param fileName 文件名
     * @param sheetName 工作表名
     * @param headers 表头映射（字段名 -> 显示名称）
     * @param dataList 数据列表
     * @param <T> 数据类型
     */
    public static <T> void exportToResponse(HttpServletResponse response, String fileName, 
                                          String sheetName, LinkedHashMap<String, String> headers, 
                                          List<T> dataList) throws IOException {
        
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        
        // 防止中文乱码
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName);
        
        // 创建Excel并写入响应流
        try (ExcelWriter writer = ExcelUtil.getWriter()) {
            writeExcelData(writer, sheetName, headers, dataList);
            writer.flush(response.getOutputStream());
        }
    }

    /**
     * 导出Excel到字节数组
     * 
     * @param sheetName 工作表名
     * @param headers 表头映射
     * @param dataList 数据列表
     * @param <T> 数据类型
     * @return Excel文件字节数组
     */
    public static <T> byte[] exportToBytes(String sheetName, LinkedHashMap<String, String> headers, 
                                         List<T> dataList) throws IOException {
        
        try (ByteArrayOutputStream out = new ByteArrayOutputStream();
             ExcelWriter writer = ExcelUtil.getWriter()) {
            
            writeExcelData(writer, sheetName, headers, dataList);
            writer.flush(out);
            return out.toByteArray();
        }
    }

    /**
     * 写入Excel数据
     */
    private static <T> void writeExcelData(ExcelWriter writer, String sheetName, 
                                         LinkedHashMap<String, String> headers, List<T> dataList) {
        
        // 设置工作表名称
        if (StrUtil.isNotBlank(sheetName)) {
            writer.renameSheet(sheetName);
        }
        
        // 写入表头
        List<String> headerNames = new ArrayList<>(headers.values());
        writer.writeHeadRow(headerNames);
        
        // 设置表头样式
        setHeaderStyle(writer, headerNames.size());
        
        // 写入数据
        if (CollUtil.isNotEmpty(dataList)) {
            List<String> fieldNames = new ArrayList<>(headers.keySet());
            
            for (int i = 0; i < dataList.size(); i++) {
                T data = dataList.get(i);
                List<Object> rowData = new ArrayList<>();
                
                for (String fieldName : fieldNames) {
                    Object value = getFieldValue(data, fieldName);
                    rowData.add(formatCellValue(value));
                }
                
                writer.writeRow(rowData);
                
                // 设置数据行样式
                setDataRowStyle(writer, i + 1, fieldNames.size());
            }
        }
        
        // 自动调整列宽
        autoSizeColumns(writer, headers.size());
    }

    /**
     * 获取字段值
     */
    private static Object getFieldValue(Object obj, String fieldName) {
        try {
            // 支持嵌套字段访问，如 "user.name"
            if (fieldName.contains(".")) {
                String[] parts = fieldName.split("\\.");
                Object current = obj;
                for (String part : parts) {
                    if (current == null) {
                        return null;
                    }
                    current = ReflectUtil.getFieldValue(current, part);
                }
                return current;
            } else {
                return ReflectUtil.getFieldValue(obj, fieldName);
            }
        } catch (Exception e) {
            log.warn("获取字段值失败: fieldName={}, error={}", fieldName, e.getMessage());
            return null;
        }
    }

    /**
     * 格式化单元格值
     */
    private static Object formatCellValue(Object value) {
        if (value == null) {
            return "";
        }
        
        if (value instanceof Date) {
            return DateUtil.formatDateTime((Date) value);
        }
        
        return value;
    }

    /**
     * 设置表头样式
     */
    private static void setHeaderStyle(ExcelWriter writer, int columnCount) {
        try {
            Workbook workbook = writer.getWorkbook();
            CellStyle headerStyle = workbook.createCellStyle();
            
            // 设置背景色
            headerStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            
            // 设置字体
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerFont.setColor(IndexedColors.WHITE.getIndex());
            headerStyle.setFont(headerFont);
            
            // 设置边框
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            
            // 设置对齐
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            
            // 应用样式到表头行
            Sheet sheet = writer.getSheet();
            Row headerRow = sheet.getRow(0);
            if (headerRow != null) {
                for (int i = 0; i < columnCount; i++) {
                    Cell cell = headerRow.getCell(i);
                    if (cell != null) {
                        cell.setCellStyle(headerStyle);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("设置表头样式失败: {}", e.getMessage());
        }
    }

    /**
     * 设置数据行样式
     */
    private static void setDataRowStyle(ExcelWriter writer, int rowIndex, int columnCount) {
        try {
            Workbook workbook = writer.getWorkbook();
            CellStyle dataStyle = workbook.createCellStyle();
            
            // 设置边框
            dataStyle.setBorderTop(BorderStyle.THIN);
            dataStyle.setBorderBottom(BorderStyle.THIN);
            dataStyle.setBorderLeft(BorderStyle.THIN);
            dataStyle.setBorderRight(BorderStyle.THIN);
            
            // 设置对齐
            dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            
            // 交替行背景色
            if (rowIndex % 2 == 0) {
//                dataStyle.setFillForegroundColor(IndexedColors.LIGHT_GREY.getIndex());
                dataStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            }
            
            // 应用样式到数据行
            Sheet sheet = writer.getSheet();
            Row dataRow = sheet.getRow(rowIndex);
            if (dataRow != null) {
                for (int i = 0; i < columnCount; i++) {
                    Cell cell = dataRow.getCell(i);
                    if (cell != null) {
                        cell.setCellStyle(dataStyle);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("设置数据行样式失败: rowIndex={}, error={}", rowIndex, e.getMessage());
        }
    }

    /**
     * 自动调整列宽
     */
    private static void autoSizeColumns(ExcelWriter writer, int columnCount) {
        try {
            Sheet sheet = writer.getSheet();
            for (int i = 0; i < columnCount; i++) {
                sheet.autoSizeColumn(i);
                // 设置最大列宽，防止过宽
                int columnWidth = sheet.getColumnWidth(i);
                if (columnWidth > 15000) {
                    sheet.setColumnWidth(i, 15000);
                }
            }
        } catch (Exception e) {
            log.warn("自动调整列宽失败: {}", e.getMessage());
        }
    }

    /**
     * 创建订单导出表头映射
     */
    public static LinkedHashMap<String, String> createOrderExportHeaders() {
        LinkedHashMap<String, String> headers = new LinkedHashMap<>();
        headers.put("orderNo", "订单号");
        headers.put("source", "订单来源");
        headers.put("body", "订单标题");
        headers.put("orderStatus", "订单状态");
        headers.put("totalAmtYuan", "订单总金额(元)");
        headers.put("amtPaidYuan", "已支付金额(元)");
        headers.put("amtUnpaidYuan", "未支付金额(元)");
        headers.put("studentName", "学生姓名");
        headers.put("studentPhone", "学生手机号");
        headers.put("salerName", "销售员姓名");
        headers.put("trxMethod", "交易方式");
        headers.put("signStatus", "签署状态");
        headers.put("productName", "产品名称");
        headers.put("subject", "学科");
        headers.put("courseType", "课型");
        headers.put("quantity", "课时数量");
        headers.put("bonusHours", "赠送课时");
        headers.put("unitPriceYuan", "单价(元)");
        headers.put("lastPayTime", "最后支付时间");
        headers.put("createTime", "创建时间");
        headers.put("remark", "备注");
        return headers;
    }

    /**
     * 创建交易流水导出表头映射
     */
    public static LinkedHashMap<String, String> createOrderTrxExportHeaders() {
        LinkedHashMap<String, String> headers = new LinkedHashMap<>();
        headers.put("orderNo", "订单号");
        headers.put("cusTrxSeq", "商户流水号");
        headers.put("trxIdx", "交易索引");
        headers.put("trxTypeDesc", "交易类型");
        headers.put("trxStatusDesc", "交易状态");
        headers.put("payTypeDesc", "支付类型");
        headers.put("trxAmtYuan", "交易金额(元)");
        headers.put("studentName", "学生姓名");
        headers.put("studentPhone", "学生手机号");
        headers.put("salerName", "销售员姓名");
        headers.put("orderBody", "订单标题");
        headers.put("orderStatus", "订单状态");
        headers.put("productName", "产品名称");
        headers.put("subject", "学科");
        headers.put("courseType", "课型");
        headers.put("createTime", "交易时间");
        headers.put("updateTime", "更新时间");
        headers.put("remark", "备注");
        return headers;
    }
}
