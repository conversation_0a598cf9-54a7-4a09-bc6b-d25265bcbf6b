package org.nonamespace.word.server.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.nonamespace.word.server.domain.Textbook;
import org.nonamespace.word.server.dto.TextbookItemTreeQueryDto;
import org.nonamespace.word.server.dto.TextbookTreeItemDto;

/**
 * 词定义 (统一教材与词): 定义各种词Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface TextbookMapper extends BaseMapper<Textbook> {

    List<TextbookTreeItemDto> getTextbookTree(TextbookItemTreeQueryDto searchDto);
}
