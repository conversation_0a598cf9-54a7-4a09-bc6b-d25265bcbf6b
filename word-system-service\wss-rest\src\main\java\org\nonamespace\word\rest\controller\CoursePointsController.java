package org.nonamespace.word.rest.controller;


import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import org.nonamespace.word.server.dto.CoursePointsAwardDto;
import org.nonamespace.word.server.service.ICourseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 课程积分
 */
@RestController
@RequestMapping("/word/course/points")
public class CoursePointsController extends BaseController {

    @Autowired
    private ICourseService courseService;


    /**
     * 积分派发
     * @param id
     * @param req
     * @return
     */
    @PutMapping("/award/{id}")
    public AjaxResult award(@PathVariable("id") String id, @RequestBody CoursePointsAwardDto.Req req) {
        courseService.awardPoints(id, req);
        return AjaxResult.success();
    }

}
