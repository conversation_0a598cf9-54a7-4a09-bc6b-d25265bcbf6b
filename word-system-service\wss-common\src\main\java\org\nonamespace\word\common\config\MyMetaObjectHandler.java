package org.nonamespace.word.common.config;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.ruoyi.common.utils.SecurityUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class MyMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {

        Date now = new Date();
        if(metaObject.hasGetter("createTime") && metaObject.getValue("createTime") == null) {
            this.strictInsertFill(metaObject, "createTime", Date.class, now);
        }
        if(metaObject.hasGetter("updateTime") && metaObject.getValue("updateTime") == null) {
            this.strictInsertFill(metaObject, "updateTime", Date.class, now);
        }

        if(metaObject.hasGetter("version") && metaObject.getValue("version") == null) {
            this.strictInsertFill(metaObject, "version", Integer.class, 1);
        }

        if(metaObject.getValue("id") == null){
            this.strictInsertFill(metaObject, "id", String.class, IdUtil.getSnowflakeNextIdStr());
        }

        String userId = userId();
        if(userId!=null) {
            if (metaObject.getValue("createBy") == null) {
                this.strictInsertFill(metaObject, "createBy", String.class, userId);
            }

            if (metaObject.getValue("updateBy") == null) {
                this.strictInsertFill(metaObject, "updateBy", String.class, userId);
            }
        }


        // 如果有其他需要插入时填充的字段，也在这里处理
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.strictUpdateFill(metaObject, "updateTime", Date.class, new Date());

        if(metaObject.getValue("updateBy") == null) {
            String userId = userId();
            if(userId!= null) {
                this.strictInsertFill(metaObject, "updateBy", String.class, userId);
            }
        }
        // 如果有其他需要更新时填充的字段，也在这里处理
    }
    
    private String userId(){
        try {
            return String.valueOf(SecurityUtils.getUserId());
        } catch (Exception e) {
            return null;
        }

    }
}