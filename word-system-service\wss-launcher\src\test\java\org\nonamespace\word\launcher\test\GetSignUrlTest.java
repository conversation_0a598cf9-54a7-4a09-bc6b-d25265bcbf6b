package org.nonamespace.word.launcher.test;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.nonamespace.word.thirdpart.esign.exception.ESignException;
import org.nonamespace.word.thirdpart.esign.model.GetSignUrlInput;
import org.nonamespace.word.thirdpart.esign.model.GetSignUrlOutput;
import org.nonamespace.word.thirdpart.esign.service.IESignFlowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 获取签署页面链接测试
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest
public class GetSignUrlTest {

    @Autowired
    private IESignFlowService eSignService;

    /**
     * 测试获取签署页面链接
     */
    @Test
    public void testGetSignUrl() throws ESignException {
        // 构建请求参数
        GetSignUrlInput input = new GetSignUrlInput();
        
        // 设置签署流程ID（需要替换为实际的签署流程ID）
        input.setSignFlowId("23c7b3afd73f4f9590598598126b1a82");
        
        // 设置是否需要登录（可选）
        input.setNeedLogin(false);
        
        // 设置链接类型（可选）
        input.setUrlType(0); // 0-签署链接，1-预览链接
        
        // 设置操作人信息（可选）
        GetSignUrlInput.Operator operator = new GetSignUrlInput.Operator();
        operator.setPsnAccount("<EMAIL>");
        input.setOperator(operator);
        
        // 设置重定向配置（可选）
        GetSignUrlInput.RedirectConfig redirectConfig = new GetSignUrlInput.RedirectConfig();
        redirectConfig.setRedirectUrl("https://example.com/callback");
        redirectConfig.setRedirectDelayTime(3);
        input.setRedirectConfig(redirectConfig);
        
        // 设置客户端类型（可选）
        input.setClientType("H5");
        
        // 设置AppScheme（可选）
        input.setAppScheme("myapp://");
        
        try {
            // 调用接口
            GetSignUrlOutput output = eSignService.getSignUrl(input);
            
            // 验证结果
            log.info("获取签署页面链接成功");
            log.info("响应码: {}", output.getCode());
            log.info("响应消息: {}", output.getMessage());
            
            if (output.getData() != null) {
                log.info("短链接: {}", output.getData().getShortUrl());
                log.info("长链接: {}", output.getData().getUrl());
            }
            
        } catch (ESignException e) {
            log.error("获取签署页面链接失败: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 测试获取签署页面链接 - 最小参数
     */
    @Test
    public void testGetSignUrlMinimal() throws ESignException {
        // 构建最小请求参数
        GetSignUrlInput input = new GetSignUrlInput();
        input.setSignFlowId("test-sign-flow-id");
        
        try {
            // 调用接口
            GetSignUrlOutput output = eSignService.getSignUrl(input);
            
            // 验证结果
            log.info("获取签署页面链接成功（最小参数）");
            log.info("响应码: {}", output.getCode());
            log.info("响应消息: {}", output.getMessage());
            
            if (output.getData() != null) {
                log.info("短链接: {}", output.getData().getShortUrl());
                log.info("长链接: {}", output.getData().getUrl());
            }
            
        } catch (ESignException e) {
            log.error("获取签署页面链接失败（最小参数）: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 测试获取签署页面链接 - 参数校验
     */
    @Test
    public void testGetSignUrlValidation() {
        try {
            // 测试空参数
            eSignService.getSignUrl(null);
        } catch (ESignException.ParameterException e) {
            log.info("空参数校验通过: {}", e.getMessage());
        } catch (ESignException e) {
            log.error("意外异常: {}", e.getMessage());
        }
        
        try {
            // 测试空签署流程ID
            GetSignUrlInput input = new GetSignUrlInput();
            eSignService.getSignUrl(input);
        } catch (ESignException.ParameterException e) {
            log.info("空签署流程ID校验通过: {}", e.getMessage());
        } catch (ESignException e) {
            log.error("意外异常: {}", e.getMessage());
        }
        
        try {
            // 测试无效重定向URL
            GetSignUrlInput input = new GetSignUrlInput();
            input.setSignFlowId("test-sign-flow-id");
            
            GetSignUrlInput.RedirectConfig redirectConfig = new GetSignUrlInput.RedirectConfig();
            redirectConfig.setRedirectUrl("invalid-url");
            input.setRedirectConfig(redirectConfig);
            
            eSignService.getSignUrl(input);
        } catch (ESignException.ParameterException e) {
            log.info("无效重定向URL校验通过: {}", e.getMessage());
        } catch (ESignException e) {
            log.error("意外异常: {}", e.getMessage());
        }
    }
}