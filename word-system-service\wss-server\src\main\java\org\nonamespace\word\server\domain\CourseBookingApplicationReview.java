package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nonamespace.word.server.entity.DataEntity;

import java.util.Date;

/**
 * 预约课申请审核记录实体
 *
 * <AUTHOR> Assistant
 * @date 2025-01-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("course_booking_application_review")
public class CourseBookingApplicationReview extends DataEntity {

    /**
     * 申请ID
     */
    @TableField("application_id")
    private String applicationId;

    /**
     * 教学组ID
     */
    @TableField("teaching_group_id")
    private String teachingGroupId;

    /**
     * 审核人ID
     */
    @TableField("reviewer_id")
    private String reviewerId;

    /**
     * 审核结果：已通过、已拒绝
     */
    @TableField("review_result")
    private String reviewResult;

    /**
     * 审核意见
     */
    @TableField("review_comment")
    private String reviewComment;

    /**
     * 拒绝原因
     */
    @TableField("rejection_reason")
    private String rejectionReason;

    /**
     * 分配的教师ID（通过时）
     */
    @TableField("assigned_teacher_id")
    private String assignedTeacherId;

    /**
     * 分配的时间段（JSON格式）
     */
    @TableField("assigned_time_slot")
    private String assignedTimeSlot;

    /**
     * 审核时间
     */
    @TableField("review_time")
    private Date reviewTime;

    /**
     * 审核结果枚举
     */
    public enum ReviewResult {
        APPROVED("已通过", "已通过"),
        REJECTED("已拒绝", "已拒绝");

        private final String code;
        private final String description;

        ReviewResult(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
