package org.nonamespace.word.common.utils;

import java.util.*;
import java.util.regex.Pattern;

public class SentenceSplitter {
    // 按空格和英文常见标点分割
//    private static final Pattern SPLIT_PATTERN = Pattern.compile("[\\s.,;?!\"()\\[\\]{}]+");
    private static final Pattern SPLIT_PATTERN = Pattern.compile("\\s+");

    public static List<String> insertPipesCount(String sentence, int resultCount, int pipeCount, String splitter) {
        Set<String> usedInsertionCombinations = new HashSet<>();
        for (int i = 0; i < resultCount; i++) {
            List<String> list = splitAndRandomGroup(sentence, pipeCount);
            usedInsertionCombinations.add(String.join(splitter, list));
        }
        return new ArrayList<>(usedInsertionCombinations);
    }



        /**
         * 把句子随机分成n段，尽量使每一段长度接近
         */
    public static List<String> splitAndRandomGroup(String sentence, int partCount) {
        // 1. 分词
        String[] tokens = SPLIT_PATTERN.split(sentence);
        List<String> words = Arrays.stream(tokens)
                .filter(s -> !s.isEmpty())
                .toList();

        if (words.size() < partCount) {
//            throw new IllegalArgumentException("单词数量太少，无法分成 " + partCount + " 段");
            partCount = words.size();
        }

        int n = words.size();
        int minPartSize = n / partCount;
        int remainder = n % partCount;

        // 2. 构建基础区间分割点（比如n=11, partCount=3，区间=[0,4,8,11]，每段长度4/4/3）
        List<Integer> boundaries = new ArrayList<>();
        boundaries.add(0);
        int cursor = 0;
        for (int i = 0; i < partCount; i++) {
            int thisPartSize = minPartSize + (i < remainder ? 1 : 0); // 前remainder段多一个
            cursor += thisPartSize;
            boundaries.add(cursor);
        }

        // 3. 在基础分割点±1随机微调分割点（但不能让段变为空）
        Random random = new Random();
        for (int i = 1; i < boundaries.size() - 1; i++) {
            int left = boundaries.get(i - 1) + 1;
            int right = boundaries.get(i + 1) - 1;
            int base = boundaries.get(i);

            // 微调允许在[base-1, base+1]区间，并且保证不越界
            int min = Math.max(left, base - 1);
            int max = Math.min(right, base + 1);
            int newBoundary = (min == max) ? min : min + random.nextInt(max - min + 1);

            boundaries.set(i, newBoundary);
        }

        // 4. 分段
        List<String> result = new ArrayList<>();
        for (int i = 0; i < boundaries.size() - 1; i++) {
            int from = boundaries.get(i);
            int to = boundaries.get(i + 1);
            result.add(String.join(" ", words.subList(from, to)));
        }
        return result;
    }

    public static void main(String[] args) {
        String sentence = "More efforts, as reported, will be made in the years ahead to accelerate the supply-side structural reform. ";
        List<String> segments = splitAndRandomGroup(sentence, 3);

        System.out.println("随机且尽量均匀分段结果：");
        for (int i = 0; i < segments.size(); i++) {
            System.out.println("Part " + (i + 1) + ": " + segments.get(i));
        }
        System.out.println("============================================");
        System.out.println(insertPipesCount(sentence, 5, 3, "|"));
    }
}
