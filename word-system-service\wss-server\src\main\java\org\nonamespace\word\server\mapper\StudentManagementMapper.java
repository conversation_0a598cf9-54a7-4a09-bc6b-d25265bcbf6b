package org.nonamespace.word.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.nonamespace.word.server.domain.UserStudentExt;
import org.nonamespace.word.server.dto.management.student.StudentDto;

import java.util.List;
import java.util.Set;

/**
 * 学生管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Mapper
public interface StudentManagementMapper extends BaseMapper<UserStudentExt> {

    /**
     * 分页查询学生列表
     *
     * @param page 分页参数
     * @param req 查询条件
     * @return 学生分页列表
     */
    IPage<StudentDto.BasicResp> selectStudentPage(Page<StudentDto.BasicResp> page, @Param("req") StudentDto.GetListReq req, @Param("teacherIds") Set<String> teacherIds);

    /**
     * 分页查询学生列表的COUNT查询
     *
     * @param req 查询条件
     * @param teacherIds 教师ID集合
     * @return 总数
     */
    long selectStudentPageCount(@Param("req") StudentDto.GetListReq req, @Param("teacherIds") Set<String> teacherIds);

    /**
     * 查询学生列表（不分页，用于手动分页）
     *
     * @param req 查询条件
     * @param teacherIds 教师ID集合
     * @param offset 偏移量
     * @return 学生列表
     */
    List<StudentDto.BasicResp> selectStudentPageList(@Param("req") StudentDto.GetListReq req, @Param("teacherIds") Set<String> teacherIds, @Param("offset") int offset);

    /**
     * 查询学生详细信息
     * 
     * @param studentId 学生ID
     * @return 学生详细信息
     */
    StudentDto.DetailResp selectStudentDetail(@Param("studentId") String studentId);

    /**
     * 根据教师ID查询学生列表
     * 
     * @param teacherId 教师ID
     * @return 学生列表
     */
    List<StudentDto.BasicResp> selectStudentsByTeacher(@Param("teacherId") String teacherId);

    /**
     * 查询可分配的学生列表（未分配教师的学生）
     * 
     * @return 可分配学生列表
     */
    List<StudentDto.AvailableResp> selectAvailableStudents();

    /**
     * 查询学生统计信息
     * 
     * @return 统计信息
     */
    StudentDto.StatsResp selectStudentStats();

    /**
     * 查询学生课程统计
     * 
     * @param studentId 学生ID
     * @return 课程统计
     */
    StudentDto.CourseStatsResp selectStudentCourseStats(@Param("studentId") String studentId);

    /**
     * 查询学生最近课程
     * 
     * @param studentId 学生ID
     * @param limit 限制数量
     * @return 最近课程列表
     */
    List<StudentDto.RecentCourseResp> selectStudentRecentCourses(@Param("studentId") String studentId, @Param("limit") Integer limit);

    /**
     * 查询学生课表
     * 
     * @param studentId 学生ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 课表列表
     */
    List<StudentDto.ScheduleResp> selectStudentSchedule(@Param("studentId") String studentId, 
                                                       @Param("startDate") String startDate, 
                                                       @Param("endDate") String endDate);

    /**
     * 创建学生信息
     * 
     * @param student 学生信息
     * @return 影响行数
     */
    int insertStudent(@Param("student") UserStudentExt student);

    /**
     * 更新学生信息
     * 
     * @param student 学生信息
     * @return 影响行数
     */
    int updateStudent(@Param("student") UserStudentExt student);

    /**
     * 删除学生信息（软删除）
     * 
     * @param studentId 学生ID
     * @return 影响行数
     */
    int deleteStudent(@Param("studentId") String studentId);

    /**
     * 批量删除学生信息（软删除）
     * 
     * @param studentIds 学生ID列表
     * @return 影响行数
     */
    int deleteStudents(@Param("studentIds") List<String> studentIds);
}
