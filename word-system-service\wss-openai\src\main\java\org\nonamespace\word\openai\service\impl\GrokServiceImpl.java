package org.nonamespace.word.openai.service.impl;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.openai.config.XAIConfig;
import org.nonamespace.word.openai.model.WordInfo;
import org.nonamespace.word.openai.model.XAICompletionRequest;
import org.nonamespace.word.openai.model.XAICompletionResponse;
import org.nonamespace.word.openai.service.IGrokService;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.List;

/**
 * grok api
 *
 * <AUTHOR>
 * @date 2025/5/20 9:34
 */
@Slf4j
@Service
public class GrokServiceImpl implements IGrokService {

    @Resource
    private XAIConfig xaiConfig;
    @Value("classpath:/prompts/word-enrich.st")
    private org.springframework.core.io.Resource wordEnrichSt;
    @Value("classpath:/prompts/word-enrich-meanings.st")
    private org.springframework.core.io.Resource wordEnrichMeaningsSt;
    @Value("classpath:/prompts/word-enrich-basic.st")
    private org.springframework.core.io.Resource wordEnrichBasicSt;
    @Value("classpath:/prompts/word-enrich-sentences.st")
    private org.springframework.core.io.Resource wordEnrichSentencesSt;
    @Value("classpath:/prompts/word-meamings-replace.txt")
    private org.springframework.core.io.Resource wordFillPormpt;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private static final String CHAT_COMPLETIONS_URL = "/v1/chat/completions";

    @Override
    public List<WordInfo> generateWordMeanings(List<String> words) {
        try {
            // 将单词列表转换为JSON字符串
            String wordsJson = objectMapper.writeValueAsString(words);
            HttpResponse response = executeHttp(wordsJson, wordFillPormpt);
            XAICompletionResponse xaiCompletionResponse = JSONUtil.toBean(response.body(), XAICompletionResponse.class);
            // 解析响应
            // todo... 这里先忽略异常
            return xaiCompletionResponse.getChoices().getFirst().getMessage().getWordInfo();
        } catch (Exception e) {
            log.error("单词信息补充失败", e);
            throw new RuntimeException("单词信息补充失败: " + e.getMessage(), e);
        }
    }


    @Override
    public List<WordInfo> enrich(List<WordInfo> words) {
        return this.executeEnrich(words, wordEnrichSt);
    }

    @Override
    public List<WordInfo> enrichBasic(List<WordInfo> words) {
        return this.executeEnrich(words, wordEnrichBasicSt);
    }

    @Override
    public List<WordInfo> enrichMeanings(List<WordInfo> words) {
        return this.executeEnrich(words, wordEnrichMeaningsSt);
    }

    @Override
    public List<WordInfo> enrichSentences(List<WordInfo> words) {
        return this.executeEnrich(words, wordEnrichSentencesSt);
    }

    private List<WordInfo> executeEnrich(List<WordInfo> words, org.springframework.core.io.Resource st){
        try {
            // 将单词列表转换为JSON字符串
            String wordsJson = objectMapper.writeValueAsString(words);
            HttpResponse response = executeHttp(wordsJson, st);

            XAICompletionResponse xaiCompletionResponse = JSONUtil.toBean(response.body(), XAICompletionResponse.class);
            // 解析响应
            // todo... 这里先忽略异常
            return xaiCompletionResponse.getChoices().getFirst().getMessage().getWordInfo();
        } catch (Exception e) {
            log.error("单词信息补充失败", e);
            throw new RuntimeException("单词信息补充失败: " + e.getMessage(), e);
        }

    }


    private HttpResponse executeHttp(String wordsJson, org.springframework.core.io.Resource prompt) {
        try {
            // 将单词列表转换为JSON字符串
            String completionUrl = xaiConfig.getBaseUrl() + CHAT_COMPLETIONS_URL;

            SystemMessage systemMessage = new SystemMessage(prompt);
            UserMessage userMessage = new UserMessage("输入单词为：" + wordsJson);
            // 系统角色
            XAICompletionRequest.Message xaiSystemMessage = XAICompletionRequest.Message.builder().role("system").content(systemMessage.getText()).build();
            XAICompletionRequest.Message xaiUserMessage = XAICompletionRequest.Message.builder().role("user").content(userMessage.getText()).build();

            // 调用AI服务
            XAICompletionRequest body = XAICompletionRequest.builder()
                    .model(xaiConfig.getModel())
                    .reasoning_effort(xaiConfig.getReasoningEffort())
//                    .temperature(xaiConfig.getTemperature())
                    .messages(List.of(xaiSystemMessage, xaiUserMessage)).build();

            // 创建代理对象
            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(xaiConfig.getProxy().getHost(), xaiConfig.getProxy().getPort()));

            return HttpUtil.createPost(completionUrl)
                    .bearerAuth(xaiConfig.getApiKey())
                    .setProxy(proxy)
                    .contentType(MediaType.APPLICATION_JSON_VALUE)
                    .body(JSONUtil.toJsonStr(body))
                    .execute();

        } catch (Exception e) {
            log.error("单词信息补充失败", e);
            throw new RuntimeException("单词信息补充失败: " + e.getMessage(), e);
        }

    }
}
