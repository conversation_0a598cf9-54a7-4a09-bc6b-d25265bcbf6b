package org.nonamespace.word.server.dto.curriculum;

import lombok.Data;

import java.util.List;

@Data
public class CurriculumUsersDto {
    @Data
    public static class Req {
        private String teacherId;
        private String name;
        private String type;
        private String keyword;
    }

    @Data
    public static class Resp {
        Integer total;
        List<User> rows;
        Integer pageNum = 1;
        Integer pageSize = 20;
    }

    @Data
    public static class User {
        String id;
        String name;
        String phone;
        String avatar;
        String grade;
    }
}
