package org.nonamespace.word.server.domain;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.*;
import lombok.experimental.Accessors;
import org.nonamespace.word.server.entity.DataEntity;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * 微信模板消息推送
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "wx_send_message", autoResultMap = true)
public class WxSendMessage extends DataEntity {

    private String openid;
    private String unionid;
    private String userId;
    private Boolean status;
    private Integer tryTimes;

    @TableField(typeHandler = JacksonTypeHandler.class)
    @Builder.Default
    private List<String> responses = CollUtil.newArrayList();
    private String templateId;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private WxTemplateMessage content;
    private String appid;
    private String trigger;
    private String messageType;
    private String relationId;
    private String userName;
    private String remark;


    /**
     * 课堂总结
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ReportData reportData;


    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WxTemplateMessage {
        /**
         * 接收者openid
         */
        private String toUser;

        /**
         * 模板ID
         */
        private String templateId;

        /**
         * 点击模板消息后跳转的链接
         */
        private String url;

        /**
         * 标题
         */
        private String first;

        /**
         * 课程名称
         */
        private String keyword1;

        /**
         * 学习日期
         */
        private String keyword2;

        /**
         * 完成进度
         */
        private String keyword3;

        /**
         * 学习情况
         */
        private String keyword4;

        /**
         * 备注
         */
        private String remark;
    }


    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReportData {
        // 课程类型
        private String courseType;
        // 词汇总结信息
        private Map<String, List<CountData>> wordCountMap;
        // 总的学习单词数量。
        @Builder.Default
        private long learningWordCnt = 0;
        // 总的复习单词数量。
        @Builder.Default
        private long reviewWordCnt = 0;
        // 听说训练时长，分钟
        private Long listenerTrainingTime;
        // 学习总时长，分钟
        private Long courseTotalTime;
        // 实现目标进度
        private BigDecimal goalsRate;
        // 总单词数量
        private Long wordTotalCnt;
        // 句型数量， 单词数量
        private Long sentencesCnt;
        // 总阅读量，例句的单词数量
        private Long sentencesWordCnt;
        // 未学习前：知识掌握率XX% （第1、第3环节的总正确率，单词和句子的英翻中题目正确率，
        private String beforeCorrectRateStr;
        // 学习后：知识掌握率XX%（下课前复习的正确率）
        private String reviewCorrectRateStr;

        // 抗遗忘复习的话，
        // 第X次课的第几次复习：复习XX词，正确率XX
        // 第X次课的第几次复习：复习XX词，正确率XX
        private List<ReviewWordData> reviewWordDataList;

        // 本次课程中所有出现错误的单词列表
        private List<String> errorWordList;


        @Getter
        @Setter
        @NoArgsConstructor
        @AllArgsConstructor
        public static class CountData {
            private String type;
            private Long count;
        }


        @Getter
        @Setter
        @NoArgsConstructor
        @AllArgsConstructor
        public static class ReviewWordData {
            private String title;
            private Long count;
            private String correctRate;
        }

    }

}
