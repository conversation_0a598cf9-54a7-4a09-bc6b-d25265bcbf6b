package org.nonamespace.word.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.misc.WssContext;
import org.nonamespace.word.server.domain.*;
import org.nonamespace.word.server.dto.curriculum.CurriculumUsersDto;
import org.nonamespace.word.server.dto.management.teachermatch.TeacherMatchDto;
import org.nonamespace.word.server.dto.management.teachinggroup.TeacherDto;
import org.nonamespace.word.server.dto.management.teachinggroup.TeachingGroupDto;
import org.nonamespace.word.server.service.*;
import org.nonamespace.word.server.util.SystemDataQueryUtil;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 教师管理Service实现类
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TeacherManagementServiceImpl implements ITeacherManagementService {

    // 常量定义
    private static final String ACTIVE_STATUS = "active";
    private static final String INACTIVE_STATUS = "inactive";
    private static final String DELETED_STATUS = "deleted";
    private static final String TEACHING_RELATION_TYPE = "teaching";
    private static final String MEMBER_ROLE_TYPE = "member";
    private static final String NORMAL_USER_STATUS = "0";

    // 依赖注入
    private final ITeacherProfileService teacherProfileService;
    private final ISysUserService sysUserService;
    private final IUserService userService;
    private final ITeachingGroupService teachingGroupService;
    private final IEnhancedTeachingGroupService enhancedTeachingGroupService;
    private final ITeacherStudentRelationService teacherStudentRelationService;
    private final UserStudentExtService userStudentExtService;
    private final ITeachingGroupMemberService teachingGroupMemberService;
    private final SystemDataQueryUtil systemDataQueryUtil;
    private final ITeacherTimeSlotService teacherTimeSlotService;
    private final CourseServiceImpl courseServiceImpl;

    @Override
    public TeacherDto.DetailResp getTeacherDetail(String teacherId) {
        return teacherProfileService.selectTeacherDetail(teacherId);
    }

    @Override
    public List<TeacherDto.AvailableResp> getAvailableTeachers() {
        return teacherProfileService.selectAvailableTeachers();
    }

    @Override
    public IPage<TeacherDto.BasicResp> getTeachersPage(TeacherDto.GetListReq req) {
        Page<TeacherDto.BasicResp> page = new Page<>(req.getPageNum(), req.getPageSize());

        // 直接使用 MPJLambdaWrapper 进行一次性关联查询，包含权限控制
        IPage<TeacherDto.BasicResp> result = selectTeachersPageWithDirectJoin(page, req);

        // 为每个教师计算实时统计数据
        if (result.getRecords() != null && !result.getRecords().isEmpty()) {
            enrichTeachersWithStatistics(result.getRecords());
        }

        return result;
    }

    @Override
    public List<TeacherDto.UserRoleResp> getAllTeachers() {
        return teacherProfileService.selectAllTeachers();
    }

    @Override
    public List<TeacherDto.TimeSlotResp> getTeacherTimeSlots(String teacherId) {
        log.info("获取教师时间表: teacherId={}", teacherId);

        if (!isValidTeacherId(teacherId)) {
            log.warn("教师ID为空或无效: teacherId={}", teacherId);
            return new ArrayList<>();
        }

        try {
            return teacherTimeSlotService.selectByTeacherId(teacherId);
        } catch (Exception e) {
            log.error("获取教师时间表失败: teacherId={}", teacherId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, List<TeacherDto.TimeSlotResp>> getTeacherTimeSlotsMap(List<String> teacherIds) {
        log.info("批量获取教师时间表: teacherIds={}", teacherIds);

        if (teacherIds == null || teacherIds.isEmpty()) {
            log.warn("教师ID列表为空");
            return new HashMap<>();
        }

        // 过滤无效的教师ID
        List<String> validTeacherIds = teacherIds.stream()
            .filter(this::isValidTeacherId)
            .collect(Collectors.toList());

        if (validTeacherIds.isEmpty()) {
            log.warn("没有有效的教师ID");
            return new HashMap<>();
        }

        try {
            // 批量查询所有教师的时间段
            List<TeacherDto.TimeSlotResp> allTimeSlots = teacherTimeSlotService.selectByTeacherIds(validTeacherIds);

//            log.info("批量查询教师时间段结果: 总数={}, 详情={}", allTimeSlots.size(),
//                allTimeSlots.stream().collect(Collectors.groupingBy(
//                    TeacherDto.TimeSlotResp::getTeacherId,
//                    Collectors.mapping(slot -> slot.getWeekday() + ":" + slot.getStartTime() + "-" + slot.getEndTime() + "(" + slot.getStatus() + ")", Collectors.toList())
//                )));

            // 按教师ID分组，返回所有时间段（不过滤状态，让匹配逻辑自己处理）
            return allTimeSlots.stream()
                .collect(Collectors.groupingBy(TeacherDto.TimeSlotResp::getTeacherId));
        } catch (Exception e) {
            log.error("批量获取教师时间表失败: teacherIds={}", validTeacherIds, e);
            return new HashMap<>();
        }
    }

    @Override
    public boolean updateTeacherTimeSlotsWithPermissionCheck(TeacherDto.UpdateTimeSlotsReq req) {
        try {
            String teacherId = req.getTeacherId();

            // 权限检查
            if (!canEditTeacherTimeSlots(teacherId)) {
                throw new RuntimeException("没有权限编辑该教师的时间表");
            }

            // 调用原有的更新方法
            return updateTeacherTimeSlots(req);
        } catch (Exception e) {
            log.error("更新教师时间表失败", e);
            throw new RuntimeException("更新教师时间表失败: " + e.getMessage());
        }
    }

    @Override
    public boolean updateTeacherTimeSlots(TeacherDto.UpdateTimeSlotsReq req) {
        try {
            return teacherTimeSlotService.updateTeacherTimeSlots(req);
        } catch (Exception e) {
            log.error("更新教师时间表失败", e);
            throw new RuntimeException("更新教师时间表失败: " + e.getMessage());
        }
    }

    @Override
    public TeacherDto.TeachingInfoResp getTeachingInfo(String teacherId) {
        return teacherProfileService.selectTeachingInfo(teacherId);
    }

    @Override
    public List<TeacherDto.TeachingInfoResp> getTeachingInfoBatch(List<String> teacherIds) {
        if (teacherIds == null || teacherIds.isEmpty()) {
            return new ArrayList<>();
        }
        return teacherProfileService.selectTeachingInfoBatch(teacherIds);
    }

    @Override
    public List<TeacherDto.DetailResp> getTeacherDetailBatch(List<String> teacherIds) {
        return teacherProfileService.selectTeacherDetailBatch(teacherIds);
    }

    @Override
    public boolean updateTeacherInfo(TeacherDto.UpdateTeacherReq req) {
            String teacherId = req.getTeacherId();

            // 检查手机号是否被其他用户使用（排除当前用户）
            if (isPhoneNumberUsedByOtherUser(req.getPhone(), teacherId)) {
                throw new RuntimeException("手机号已被其他用户使用");
            }

            // 1. 更新sys_user表中的基本信息
            updateSysUserInfo(req, teacherId);

            // 2. 更新或插入teacher_profile表中的详细信息
            updateOrCreateTeacherProfile(req, teacherId);

            return true;
    }

    @Override
    public boolean updateTeacherSummerSchedule(TeacherDto.UpdateSummerScheduleReq req) {
        try {
            // 验证暑期课上课时间类型
            if (!isValidSummerScheduleType(req.getSummerScheduleType())) {
                throw new RuntimeException("无效的暑期课上课时间类型: " + req.getSummerScheduleType());
            }

            // 查询教师档案是否存在
            TeacherProfile profile = teacherProfileService.lambdaQuery()
                    .eq(TeacherProfile::getTeacherId, req.getTeacherId())
                    .eq(TeacherProfile::getDeleted, false)
                    .one();

            if (profile == null) {
                throw new RuntimeException("教师档案不存在");
            }

            // 只更新暑期课上课时间字段
            boolean success = teacherProfileService.lambdaUpdate()
                    .eq(TeacherProfile::getTeacherId, req.getTeacherId())
                    .eq(TeacherProfile::getDeleted, false)
                    .set(TeacherProfile::getSummerScheduleType, req.getSummerScheduleType())
                    .update();

            if (success) {
                log.info("更新教师暑期课上课时间成功: teacherId={}, summerScheduleType={}",
                        req.getTeacherId(), req.getSummerScheduleType());
            }

            return success;
        } catch (Exception e) {
            log.error("更新教师暑期课上课时间失败: teacherId={}", req.getTeacherId(), e);
            throw new RuntimeException("更新教师暑期课上课时间失败: " + e.getMessage());
        }
    }

    /**
     * 验证暑期课上课时间类型是否有效
     */
    private boolean isValidSummerScheduleType(String type) {
        return "full".equals(type) || "golden".equals(type) || "other".equals(type);
    }

    @Override
    public String createTeacher(TeacherDto.CreateTeacherReq req) {
            if (userService.isPhoneNumberExists(req.getPhone().trim())) {
                throw new RuntimeException("手机号已被使用");
            }

            // 1. 创建sys_user记录
            SysUser user = new SysUser();
            user.setUserName(req.getPhone()); // 手机号作为用户名
            user.setNickName(req.getNickname());
            user.setPhonenumber(req.getPhone());
            user.setEmail(req.getEmail());
            user.setPassword(SecurityUtils.encryptPassword(userService.getDefaultPassword())); // 默认密码
            user.setStatus(NORMAL_USER_STATUS); // 正常状态
            user.setSex(req.getGender());

            user.setRoleIds(new Long[]{systemDataQueryUtil.getTeacherRole().getRoleId()});

            // 设置部门ID（如果有教学组）
            if ( StrUtil.isNotEmpty(req.getGroupId())) {
                Long deptId = getDeptIdByGroupId(req.getGroupId());
                if (deptId != null) {
                    user.setDeptId(deptId);
                    log.info("为教师设置部门ID: teacherId={}, groupId={}, deptId={}",
                            req.getPhone(), req.getGroupId(), deptId);
                }
            }else{
                // 如果没有指定教学组，则使用默认部门
                Long defaultDeptId = systemDataQueryUtil.getTeachingCenterDept().getDeptId();
                if (defaultDeptId != null) {
                    user.setDeptId(defaultDeptId);
                    log.info("为教师设置默认部门ID: teacherId={}, deptId={}", req.getPhone(), defaultDeptId);
                } else {
                    log.warn("没有找到默认部门ID，教师将没有部门关联");
                }
            }

            userService.save(user);

            user = sysUserService.selectUserByPhone(req.getPhone());

            Long teacherId = user.getUserId();

            // 2. 创建teacher_profile记录
            TeacherProfile profile = new TeacherProfile();
            profile.setRealName(req.getName());
            profile.setTeacherId(String.valueOf(teacherId));
            // 同步sys_user表的字段
            profile.setNickName(req.getNickname());
            profile.setPhonenumber(req.getPhone());
            profile.setGender(req.getGender());
            profile.setAge(req.getAge());
            profile.setCurrentLocation(req.getCurrentLocation());
            profile.setEmploymentType(req.getEmploymentType());
            profile.setCurrentStatus(req.getCurrentStatus());

            // 教育背景
            profile.setEducation(req.getEducation());
            profile.setGraduateSchool(req.getGraduateSchool());
            profile.setMajor(req.getMajor());
            profile.setUniversityType(req.getUniversityType());
            profile.setIsNormalUniversity(req.getIsNormalUniversity());
            profile.setStudyAbroad(req.getStudyAbroad());
            profile.setStudyAbroadCountry(req.getStudyAbroadCountry());

            // 教学资质
            profile.setTeachingCertificateLevel(req.getTeachingCertificateLevel());
            profile.setSubjects(req.getSubjects());
            profile.setTrainingSubjects(req.getTrainingSubjects());
            profile.setEnglishQualification(req.getEnglishQualification());
            profile.setMandarinQualification(req.getMandarinQualification());
            profile.setCommunicationAbility(req.getCommunicationAbility());

            // 教学经历
            profile.setTeachingExperience(req.getTeachingExperience());
            profile.setTaughtCourses(req.getTaughtCourses());
            profile.setTeachingYears(req.getTeachingYears());
            profile.setAwards(req.getAwards());

            // 教学风格和适配
            profile.setTeachingStyle(req.getTeachingStyle());
            profile.setEnglishPronunciation(req.getEnglishPronunciation());
            profile.setSuitableGrades(req.getSuitableGrades());
            profile.setSuitableLevels(req.getSuitableLevels());
            profile.setSuitablePersonality(req.getSuitablePersonality());

            // 暑期课上课时间
            profile.setSummerScheduleType(req.getSummerScheduleType());

            // 新增字段
            profile.setFormalEntryDate(req.getFormalEntryDate());
            profile.setQualificationCertificates(req.getQualificationCertificates());
            profile.setDemoVideos(req.getDemoVideos());

            profile.setIntroduction(req.getIntroduction());
            profile.setStatus("active");
            profile.setDeleted(false);

            boolean profileResult = teacherProfileService.save(profile);
            if (!profileResult) {
                throw new RuntimeException("创建教师档案失败");
            }

            // 3. 如果指定了教学组，添加到教学组
            if(StrUtil.isNotEmpty(req.getGroupId())){
                TeachingGroupDto.AssignTeachersReq assignReq = new TeachingGroupDto.AssignTeachersReq();
                assignReq.setGroupId(req.getGroupId());
                assignReq.setTeacherIds(List.of(String.valueOf(user.getUserId())));
                enhancedTeachingGroupService.assignTeachersWithRoleCheck(assignReq);
            }

            return String.valueOf(teacherId);
    }

    @Override
    public TeacherDto.ImportResult importTeachers(List<TeacherDto.ImportTeacherData> teacherDataList) {
        TeacherDto.ImportResult result = new TeacherDto.ImportResult();
        result.setTotalCount(teacherDataList.size());
        result.setSuccessCount(0);
        result.setFailureCount(0);
        result.setErrorMessages(new ArrayList<>());
        result.setFailedRecords(new ArrayList<>());

        for (int i = 0; i < teacherDataList.size(); i++) {
            TeacherDto.ImportTeacherData data = teacherDataList.get(i);
            try {
                // 校验电话号码是否已存在
                boolean phoneExists = userService.lambdaQuery()
                        .eq(SysUser::getPhonenumber, data.getPhone())
                        .exists();

                if (phoneExists) {
                    result.setFailureCount(result.getFailureCount() + 1);
                    result.getErrorMessages().add(String.format("第%d行：电话号码 %s 已存在", i + 1, data.getPhone()));
                    result.getFailedRecords().add(data);
                    continue;
                }

                // 创建教师
                TeacherDto.CreateTeacherReq createReq = new TeacherDto.CreateTeacherReq();
                createReq.setName(data.getName());
                createReq.setNickname(data.getNickname());
                createReq.setPhone(data.getPhone());
                createReq.setGender(data.getGender());

                String teacherId = createTeacher(createReq);
                if (teacherId != null) {
                    result.setSuccessCount(result.getSuccessCount() + 1);
                } else {
                    result.setFailureCount(result.getFailureCount() + 1);
                    result.getErrorMessages().add(String.format("第%d行：创建教师失败", i + 1));
                    result.getFailedRecords().add(data);
                }
            } catch (Exception e) {
                log.error("导入教师失败，第{}行：{}", i + 1, e.getMessage(), e);
                result.setFailureCount(result.getFailureCount() + 1);
                result.getErrorMessages().add(String.format("第%d行：%s", i + 1, e.getMessage()));
                result.getFailedRecords().add(data);
            }
        }

        return result;
    }

    @Override
    public List<TeacherDto.ExportData> exportTeachers(TeacherDto.GetListReq req) {
        // 构建查询条件
        LambdaQueryChainWrapper<TeacherProfile> query = teacherProfileService.lambdaQuery()
                .eq(TeacherProfile::getDeleted, false);

        // 添加搜索条件
        if (StrUtil.isNotEmpty(req.getName())) {
            query.like(TeacherProfile::getRealName, req.getName());
        }
        if (StrUtil.isNotEmpty(req.getPhone())) {
            query.like(TeacherProfile::getPhonenumber, req.getPhone());
        }
        if (StrUtil.isNotEmpty(req.getStatus())) {
            query.eq(TeacherProfile::getStatus, req.getStatus());
        }

        List<TeacherProfile> profiles = query.list();

        return profiles.stream().map(profile -> {
            TeacherDto.ExportData exportData = new TeacherDto.ExportData();
            exportData.setName(profile.getRealName());
            exportData.setGender(convertGenderToText(profile.getGender()));
            exportData.setPhone(profile.getPhonenumber());
            exportData.setEducation(profile.getEducation());
            exportData.setMajor(profile.getMajor());
            exportData.setUniversityType(profile.getUniversityType());
            exportData.setTeachingCertificateLevel(profile.getTeachingCertificateLevel());
            exportData.setEnglishQualification(profile.getEnglishQualification());
            return exportData;
        }).collect(Collectors.toList());
    }

    @Override
    public boolean updateTeacherStatus(String teacherId, String status) {
        try {
            // 更新教师档案状态
            boolean profileUpdated = teacherProfileService.lambdaUpdate()
                    .eq(TeacherProfile::getTeacherId, teacherId)
                    .set(TeacherProfile::getStatus, StrUtil.equals(ACTIVE_STATUS, status) ? ACTIVE_STATUS : INACTIVE_STATUS)
                    .update();

            return profileUpdated;

            // 更新用户状态
//            boolean userUpdated = userService.lambdaUpdate()
//                    .eq(SysUser::getUserId, teacherId)
//                    .set(SysUser::getStatus, "active".equals(status) ? "0" : "1")
//                    .update();

//            log.info("更新教师状态：teacherId={}, status={}, profileUpdated={}, userUpdated={}",
//                    teacherId, status, profileUpdated, userUpdated);

//            return profileUpdated && userUpdated;
        } catch (Exception e) {
            log.error("更新教师状态失败：teacherId={}, status={}", teacherId, status, e);
            throw new RuntimeException("更新教师状态失败: " + e.getMessage());
        }
    }

    /**
     * 转换性别代码为文本
     */
    private String convertGenderToText(String gender) {
        if (gender == null) return "未知";
        switch (gender) {
            case "0": return "男";
            case "1": return "女";
            default: return "未知";
        }
    }

    @Override
    public List<TeacherDto.ScheduleResp> getTeacherSchedule(String teacherId, String startDate, String endDate) {
        try {
            // 查询教师课表，这里需要根据实际的课程表结构来实现
            // 暂时返回空列表，实际应该查询course或course_schedule_info表
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("获取教师课表失败", e);
            throw new RuntimeException("获取教师课表失败: " + e.getMessage());
        }
    }

    @Override
    public boolean assignStudentsToTeacher(TeacherDto.AssignStudentsReq req) {
        try {
            String teacherId = req.getTeacherId();
            List<TeacherDto.StudentAssignment> newAssignments = req.getStudentAssignments() != null ?
                    req.getStudentAssignments() : new ArrayList<>();

            log.info("更新教师学生分配: teacherId={}, assignments={}", teacherId, newAssignments.size());

            // 1. 获取当前教师的所有活跃学生关系
            List<TeacherStudentRelation> currentRelations = getAssignRelations(teacherId);

            // 2. 构建当前关系的唯一标识（学生ID+学科+课型）
            Set<String> currentKeys = currentRelations.stream()
                    .map(relation -> relation.getStudentId() + "_" + relation.getSubject() + "_" + relation.getSpecification())
                    .collect(Collectors.toSet());

            // 3. 构建新分配的唯一标识
            Set<String> newKeys = newAssignments.stream()
                    .map(assignment -> assignment.getStudentId() + "_" + assignment.getSubject() + "_" + assignment.getCourseType())
                    .collect(Collectors.toSet());

            log.info("当前关系: {}, 新分配关系: {}", currentKeys, newKeys);

            // 4. 计算需要新增和移除的关系
            Set<String> toAdd = newKeys.stream()
                    .filter(key -> !currentKeys.contains(key))
                    .collect(Collectors.toSet());

            Set<String> toRemove = currentKeys.stream()
                    .filter(key -> !newKeys.contains(key))
                    .collect(Collectors.toSet());

            log.info("需要新增的关系: {}, 需要移除的关系: {}", toAdd, toRemove);

            // 5. 移除不再分配的学生关系（设置为inactive）
            if (!toRemove.isEmpty()) {
                for (String key : toRemove) {
                    String[] parts = key.split("_");
                    String studentId = parts[0];
                    String subject = parts[1];
                    String courseType = parts[2];

                    teacherStudentRelationService.lambdaUpdate()
                            .set(TeacherStudentRelation::getDeleted, true)
                            .set(TeacherStudentRelation::getStatus, INACTIVE_STATUS)
                            .set(TeacherStudentRelation::getEndDate, WssContext.now())
                            .set(TeacherStudentRelation::getUpdateTime, WssContext.now())
                            .set(TeacherStudentRelation::getUpdateBy, WssContext.userId())
                            .eq(TeacherStudentRelation::getTeacherId, teacherId)
                            .eq(TeacherStudentRelation::getStudentId, studentId)
                            .eq(TeacherStudentRelation::getSubject, subject)
                            .eq(TeacherStudentRelation::getSpecification, courseType)
                            .eq(TeacherStudentRelation::getStatus, ACTIVE_STATUS)
                            .eq(TeacherStudentRelation::getDeleted, false)
                            .update();


                    courseServiceImpl.deleteNotStartedCourses(teacherId, studentId, subject, courseType, "移除学生关系时自动取消排课");
                }
                log.info("已移除 {} 个学生关系", toRemove.size());
            }

            // 6. 新增学生关系
            if (!toAdd.isEmpty()) {
                List<TeacherStudentRelation> newRelations = new ArrayList<>();
                for (String key : toAdd) {
                    String[] parts = key.split("_");
                    String studentId = parts[0];
                    String subject = parts[1];
                    String courseType = parts[2];

                    TeacherStudentRelation newRelation = new TeacherStudentRelation();
                    newRelation.setId(IdUtil.getSnowflakeNextIdStr());
                    newRelation.setTeacherId(teacherId);
                    newRelation.setStudentId(studentId);
                    newRelation.setRelationType(TEACHING_RELATION_TYPE);
                    newRelation.setSubject(subject);
                    newRelation.setSpecification(courseType);
                    newRelation.setStatus(ACTIVE_STATUS);
                    newRelation.setStartDate(WssContext.now());
                    newRelation.setCreateTime(WssContext.now());
                    newRelation.setUpdateTime(WssContext.now());
                    newRelation.setDeleted(false);
                    newRelations.add(newRelation);
                }

                teacherStudentRelationService.saveBatch(newRelations);
                log.info("已新增 {} 个学生关系", toAdd.size());
            }

            return true;
        } catch (Exception e) {
            log.error("分配学生给教师失败", e);
            throw new RuntimeException("分配学生给教师失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前教师的所有活跃学生关系
     *
     * @param teacherId 教师ID
     * @return 活跃的师生关系列表
     */
    private List<TeacherStudentRelation> getAssignRelations(String teacherId) {
        List<String> teacherIds = new ArrayList<>();
        if(StrUtil.isEmpty(teacherId)){
            teacherIds.addAll(teacherSelectLists().stream().map(x->x.getId()).toList());
        }else{teacherIds.add(teacherId);
        }
        return teacherStudentRelationService.lambdaQuery()
                .in(CollUtil.isNotEmpty(teacherIds), TeacherStudentRelation::getTeacherId, teacherIds)
                .eq(TeacherStudentRelation::getStatus, ACTIVE_STATUS)
                .eq(TeacherStudentRelation::getDeleted, false)
                .list();
    }

    @Override
    public List<TeacherDto.StudentResp> getTeacherStudents(String teacherId) {
        // 查询教师的学生列表
        log.info("获取教师学生列表: teacherId={}", teacherId);

        List<TeacherStudentRelation> tsrs = getAssignRelations(teacherId);
        if (tsrs == null || tsrs.isEmpty()) {
            return new ArrayList<>();
        }

        Set<String> studentIds = tsrs.stream().map(TeacherStudentRelation::getStudentId).collect(Collectors.toSet());

        // 查询学生基础信息
        List<UserStudentExt> studentExts = userStudentExtService.lambdaQuery()
                .in(UserStudentExt::getStudentId, studentIds)
                .eq(UserStudentExt::getStatus, ACTIVE_STATUS)
                .list();

        List<TeacherDto.StudentResp> students = new ArrayList<>();

        for (UserStudentExt studentExt : studentExts) {
            TeacherDto.StudentResp student = new TeacherDto.StudentResp();
            student.setId(String.valueOf(studentExt.getStudentId()));
            student.setName(studentExt.getName());
            student.setPhone(studentExt.getPhone());
            student.setGrade(studentExt.getGrade());
            student.setSchool(studentExt.getSchool());
            student.setSubject(tsrs.stream()
                    .filter(tsr -> tsr.getStudentId().equals(studentExt.getStudentId()))
                    .map(TeacherStudentRelation::getSubject)
                    .findFirst().orElse(""));
            student.setCourseType(tsrs.stream()
                    .filter(tsr -> tsr.getStudentId().equals(studentExt.getStudentId()))
                    .map(TeacherStudentRelation::getSpecification)
                    .findFirst().orElse(""));
            students.add(student);
        }


        return students;
    }

    @Override
    public boolean deleteTeacher(String teacherId) {
        if(!systemDataQueryUtil.isAdmin()){
            throw new RuntimeException("已被禁止删除老师");
        }
        try {
            // 1. 软删除教师档案
            TeacherProfile profile = teacherProfileService.lambdaQuery()
                    .eq(TeacherProfile::getTeacherId, teacherId)
                    .eq(TeacherProfile::getDeleted, false)
                    .one();

            if (profile != null) {
                profile.setDeleted(true);
                profile.setStatus(DELETED_STATUS);
                profile.setUpdateTime(null);
                profile.setUpdateBy(null);
                teacherProfileService.updateById(profile);
            }

            // 2. 软删除用户记录
            sysUserService.deleteUserById(Long.valueOf(teacherId));

            // 3. 清理师生关系

            teacherStudentRelationService.lambdaUpdate()
                    .set(TeacherStudentRelation::getDeleted, true)
                    .set(TeacherStudentRelation::getEndDate, WssContext.now())
                    .eq(TeacherStudentRelation::getTeacherId, teacherId)
                    .update();

            // 4. 清理教学组关系
            teachingGroupMemberService.lambdaUpdate()
                    .set(TeachingGroupMember::getDeleted, true)
                    .eq(TeachingGroupMember::getTeacherId, teacherId)
                    .update();


            log.info("删除教师成功: teacherId={}", teacherId);
            return true;
        } catch (Exception e) {
            log.error("删除教师失败", e);
            throw new RuntimeException("删除教师失败: " + e.getMessage());
        }
    }

    @Override
    public boolean batchDeleteTeachers(List<String> teacherIds) {
        try {
            if (teacherIds == null || teacherIds.isEmpty()) {
                return true;
            }

            for (String teacherId : teacherIds) {
                deleteTeacher(teacherId);
            }

            log.info("批量删除教师成功: teacherIds={}", teacherIds);
            return true;
        } catch (Exception e) {
            log.error("批量删除教师失败", e);
            throw new RuntimeException("批量删除教师失败: " + e.getMessage());
        }
    }

    @Override
    public boolean resetTeacherPassword(String teacherId) {
        try {
            log.info("重置教师密码: teacherId={}", teacherId);

            // 检查权限
            checkResetPasswordPermission(teacherId);

            boolean success = userService.resetPassword(Long.valueOf(teacherId));

            if (success) {
                log.info("重置教师密码成功: teacherId={}", teacherId);
            } else {
                log.error("重置教师密码失败: teacherId={}", teacherId);
            }

            return success;
        } catch (Exception e) {
            log.error("重置教师密码失败: teacherId={}", teacherId, e);
            throw new RuntimeException("重置教师密码失败: " + e.getMessage());
        }
    }

    /**
     * 根据教学组ID获取对应的部门ID
     *
     * @param groupId 教学组ID
     * @return 部门ID，如果教学组不存在或没有关联部门则返回null
     */
    private Long getDeptIdByGroupId(String groupId) {
        try {
            TeachingGroup group = teachingGroupService.getById(groupId);
            if (group != null && group.getDeptId() != null) {
                log.debug("找到教学组对应的部门ID: groupId={}, deptId={}", groupId, group.getDeptId());
                return group.getDeptId();
            } else {
                log.warn("教学组不存在或没有关联部门: groupId={}", groupId);
                return null;
            }
        } catch (Exception e) {
            log.error("根据教学组ID获取部门ID失败: groupId={}", groupId, e);
            return null;
        }
    }

    @Override
    public List<TeacherDto.SelectOption> teacherSelectLists() {
        log.debug("获取可选老师列表，当前用户ID: {}", SecurityUtils.getUserId());

        try {
            List<TeacherDto.SelectOption> result = new ArrayList<>();

            // 1. admin和人力查询所有老师
            if (systemDataQueryUtil.isAdminOrHr()) {
                log.debug("当前用户为admin或人力，查询所有老师");
                List<SysUser> allTeachers = userService.lambdaQuery()
                        .select(SysUser::getUserId, SysUser::getNickName)
                        .inSql(SysUser::getUserId, "select user_id from sys_user_role sur where sur.role_id in (select role_id from sys_role sr where sr.role_name='老师' and del_flag='0')")
                        .eq(SysUser::getStatus, NORMAL_USER_STATUS)
                        .list();

                result = allTeachers.stream()
                        .map(user -> {
                            TeacherDto.SelectOption option = new TeacherDto.SelectOption();
                            option.setId(user.getUserId().toString());
                            option.setName(user.getNickName());
                            return option;
                        })
                        .collect(Collectors.toList());

            } else if (systemDataQueryUtil.isTeacherGroupManager()) {
                // 2. 教学组长和教务查看所在组内老师
                log.debug("当前用户为教学组长或教务，查询所在组内老师");
                String currentUserId = SecurityUtils.getUserId().toString();

                // 获取当前用户管理的教学组
                List<TeachingGroup> managedGroups = teachingGroupService.lambdaQuery()
                        .eq(TeachingGroup::getDeleted, false)
                        .and(wrapper -> wrapper.eq(TeachingGroup::getAdminId, currentUserId)
                                .or()
                                .eq(TeachingGroup::getLeaderId, currentUserId))
                        .list();

                if (!managedGroups.isEmpty()) {
                    Set<String> groupIds = managedGroups.stream()
                            .map(TeachingGroup::getId)
                            .collect(Collectors.toSet());

                    // 获取这些教学组内的老师
                    List<String> teacherIds = teachingGroupMemberService.lambdaQuery()
                            .select(TeachingGroupMember::getTeacherId)
                            .in(TeachingGroupMember::getGroupId, groupIds)
                            .eq(TeachingGroupMember::getDeleted, false)
                            .list()
                            .stream()
                            .map(TeachingGroupMember::getTeacherId)
                            .collect(Collectors.toList());

                    if (!teacherIds.isEmpty()) {
                        List<SysUser> groupTeachers = userService.lambdaQuery()
                                .select(SysUser::getUserId, SysUser::getNickName)
                                .in(SysUser::getUserId, teacherIds.stream().map(Long::valueOf).collect(Collectors.toList()))
                                .eq(SysUser::getStatus, NORMAL_USER_STATUS)
                                .list();

                        result = groupTeachers.stream()
                                .map(user -> {
                                    TeacherDto.SelectOption option = new TeacherDto.SelectOption();
                                    option.setId(user.getUserId().toString());
                                    option.setName(user.getNickName());
                                    return option;
                                })
                                .collect(Collectors.toList());
                    }
                }

            } else if (systemDataQueryUtil.isTeacher()) {
                // 3. 老师只能查自己
                log.debug("当前用户为老师，只能查看自己");
                String currentUserId = SecurityUtils.getUserId().toString();

                SysUser currentUser = userService.lambdaQuery()
                        .select(SysUser::getUserId, SysUser::getNickName)
                        .eq(SysUser::getUserId, Long.valueOf(currentUserId))
                        .eq(SysUser::getStatus, NORMAL_USER_STATUS)
                        .one();

                if (currentUser != null) {
                    TeacherDto.SelectOption option = new TeacherDto.SelectOption();
                    option.setId(currentUser.getUserId().toString());
                    option.setName(currentUser.getNickName());
                    result.add(option);
                }

            } else {
                // 4. 其他角色返回空
                log.debug("当前用户无权限查看老师列表");
            }

            log.debug("获取可选老师列表完成，数量: {}", result.size());
            return result;

        } catch (Exception e) {
            log.error("获取可选老师列表失败", e);
            throw new RuntimeException("获取可选老师列表失败: " + e.getMessage());
        }
    }

    @Override
    public List<CurriculumUsersDto.User> teachers(CurriculumUsersDto.Req req){



        LambdaQueryChainWrapper<SysUser> lqm = userService.lambdaQuery().select(SysUser::getUserId, SysUser::getUserName, SysUser::getNickName, SysUser::getPhonenumber)
                .inSql(SysUser::getUserId, " select user_id from sys_user_role sur where sur.role_id in (select role_id from sys_role sr where sr.role_name='老师' and del_flag='0')");

        List<SysUser> users = new ArrayList<>();

        if(systemDataQueryUtil.isAdminOrHr()){
            users.addAll(lqm.list());
        }else {

            if (StrUtil.isNotEmpty(req.getTeacherId())) {
                lqm.in(SysUser::getUserId, Long.valueOf(req.getTeacherId()));
            } else if (WssContext.isAdmin()) {
                // 管理员查询所有教师
                // 不需要额外条件，lqm已经设置了查询所有老师
            } else if (systemDataQueryUtil.isTeacherGroupManager()) {
                List<TeachingGroup> list = teachingGroupService.lambdaQuery()
                        .eq(TeachingGroup::getAdminId, SecurityUtils.getUserId().toString())
                        .or()
                        .eq(TeachingGroup::getLeaderId, SecurityUtils.getUserId().toString()).list();

                if (!list.isEmpty()) {

                    List<TeachingGroupMember> members = teachingGroupMemberService.lambdaQuery()
                            .in(TeachingGroupMember::getGroupId, list.stream().map(TeachingGroup::getId).collect(Collectors.toSet()))
                            .eq(TeachingGroupMember::getRoleType, MEMBER_ROLE_TYPE)
                            .list();
                    Set<Long> userIds = members.stream().map(x -> Long.parseLong(x.getTeacherId())).collect(Collectors.toSet());
                    if(systemDataQueryUtil.isTeacher()){
                        userIds.add(SecurityUtils.getUserId());
                    }

                    // 没有符合的老师，设置一个查不出数据的条件
                    if(userIds.isEmpty()){
                        userIds.add(-1L);
                    }
                    lqm.in(SysUser::getUserId, userIds);
                }
            } else if (systemDataQueryUtil.isTeacher()) {
                // 教师只能查询自己的信息
                lqm.eq(SysUser::getUserId, SecurityUtils.getUserId());
            }


            users.addAll(lqm.list());
        }

        List<CurriculumUsersDto.User> userDto = users.stream().map(x -> {
            CurriculumUsersDto.User user = new CurriculumUsersDto.User();
            user.setId(String.valueOf(x.getUserId()));
            user.setName(userService.getUserDisplayName(x));
//            user.setPhone(DesensitizedUtil.mobilePhone(x.getPhonenumber()));
            return user;
        }).collect(Collectors.toList());

        return userDto;
    }

    @Override
    public List<CurriculumUsersDto.User> students(CurriculumUsersDto.Req req){

//        if(StrUtil.isEmpty(req.getTeacherId())){
//            throw new IllegalArgumentException("请选择老师");
//        }

        if(!systemDataQueryUtil.isAdminOrHr() && !systemDataQueryUtil.isTeacherGroupManager() && systemDataQueryUtil.isTeacher()){
            req.setTeacherId(systemDataQueryUtil.getCurrentUserId());
        }

        List<TeacherStudentRelation> tsrs = getAssignRelations(req.getTeacherId());

        if(tsrs.isEmpty()){
            throw new IllegalArgumentException("当前教师没有分配学生");
        }

        // 获取学生ID集合
        Set<String> studentIds = tsrs.stream()
                .map(TeacherStudentRelation::getStudentId)
                .collect(Collectors.toSet());

        // 直接从 user_student_ext 表查询学生信息
        List<UserStudentExt> students = userStudentExtService.lambdaQuery()
                .in(UserStudentExt::getStudentId, studentIds)
                .eq(UserStudentExt::getDeleted, false)
                .list();

        List<CurriculumUsersDto.User> userDto = students.stream().map(student -> {
            CurriculumUsersDto.User user = new CurriculumUsersDto.User();
            user.setId(String.valueOf(student.getStudentId()));
            user.setName(student.getName());
            return user;
        }).collect(Collectors.toList());

        return userDto;
    }

    @Override
    public TeacherDto.TimeSlotUpdateCheckResp checkTeacherTimeSlotUpdateTime(String teacherId) {
        try {

            TeacherDto.TimeSlotUpdateCheckResp response = new TeacherDto.TimeSlotUpdateCheckResp();
            response.setTeacherId(teacherId);

            boolean exists = teacherProfileService.lambdaQuery()
                    .eq(TeacherProfile::getTeacherId, teacherId)
                    .eq(TeacherProfile::getDeleted, false)
                    .exists();

            if(!exists) {
                response.setNeedsUpdate(false);
                response.setMessage("当前用户不是老师");
                return response;
            }


            // 查询教师最后更新时间
            Date lastUpdateTime = teacherTimeSlotService.lambdaQuery()
                    .eq(TeacherTimeSlot::getTeacherId, teacherId)
                    .eq(TeacherTimeSlot::getDeleted, false)
                    .orderByDesc(TeacherTimeSlot::getUpdateTime)
                    .last(" limit 1 ")
                    .list()
                    .stream()
                    .findFirst()
                    .map(TeacherTimeSlot::getUpdateTime)
                    .orElse(null);

            if (lastUpdateTime == null) {
                // 没有时间表记录，需要更新
                response.setLastUpdateTime(null);
                response.setDaysSinceLastUpdate(null);
                response.setNeedsUpdate(true);
                response.setMessage("尚未设置可上课时间，请设置！");
            } else {
                // 计算距离上次更新的天数
                long diffInMillis = System.currentTimeMillis() - lastUpdateTime.getTime();
                int daysSinceLastUpdate = (int) (diffInMillis / (1000 * 60 * 60 * 24));

                response.setLastUpdateTime(lastUpdateTime);
                response.setDaysSinceLastUpdate(daysSinceLastUpdate);
                response.setNeedsUpdate(daysSinceLastUpdate > 3);

                if (daysSinceLastUpdate > 3) {
                    response.setMessage("今天还未更新课点，请更新！");
                } else {
                    response.setMessage("课点更新正常");
                }
            }

            return response;
        } catch (Exception e) {
            log.error("检查教师时间表更新时间失败: teacherId={}", teacherId, e);
            TeacherDto.TimeSlotUpdateCheckResp errorResponse = new TeacherDto.TimeSlotUpdateCheckResp();
            errorResponse.setTeacherId(teacherId);
            errorResponse.setNeedsUpdate(false);
            errorResponse.setMessage("检查失败");
            return errorResponse;
        }
    }

    /**
     * 检查当前用户是否有权限编辑指定教师的时间段
     *
     * @param teacherId 教师ID
     * @return 是否有权限
     */
    private boolean canEditTeacherTimeSlots(String teacherId) {
        try {
            // 1. 管理员和HR有权限编辑所有教师的时间段
            if (systemDataQueryUtil.isAdminOrHr()) {
                return true;
            }

            // 2. 教师只能编辑自己的时间段
            if (systemDataQueryUtil.isTeacher()) {
                return Objects.equals(teacherId, String.valueOf(SecurityUtils.getUserId()));
            }

            // 3. 教学组长和教务可以编辑所属教学组内教师的时间段
            if (systemDataQueryUtil.isTeacherGroupManager()) {
                return canManageTeacherInGroup(teacherId);
            }

            // 4. 其他角色没有权限
            return false;
        } catch (Exception e) {
            log.error("检查教师时间段编辑权限失败: teacherId={}", teacherId, e);
            return false;
        }
    }

    /**
     * 检查当前用户是否可以管理指定教师（在教学组范围内）
     *
     * @param teacherId 教师ID
     * @return 是否可以管理
     */
    private boolean canManageTeacherInGroup(String teacherId) {
        try {
            String currentUserId = SecurityUtils.getUserId().toString();

            // 查询当前用户管理的教学组
            List<TeachingGroup> managedGroups = teachingGroupService.lambdaQuery()
                    .and(q -> q.eq(TeachingGroup::getAdminId, currentUserId)
                            .or()
                            .eq(TeachingGroup::getLeaderId, currentUserId))
                    .list();

            if (managedGroups.isEmpty()) {
                return false;
            }

            // 检查目标教师是否在这些教学组中
            List<String> groupIds = managedGroups.stream()
                    .map(TeachingGroup::getId)
                    .collect(Collectors.toList());

            boolean isInManagedGroup = teachingGroupMemberService.lambdaQuery()
                    .eq(TeachingGroupMember::getTeacherId, teacherId)
                    .in(TeachingGroupMember::getGroupId, groupIds)
                    .eq(TeachingGroupMember::getDeleted, false)
                    .exists();

            return isInManagedGroup;
        } catch (Exception e) {
            log.error("检查教学组管理权限失败: teacherId={}", teacherId, e);
            return false;
        }
    }

    // ==================== 私有工具方法 ====================

    /**
     * 验证教师ID是否有效
     *
     * @param teacherId 教师ID
     * @return 是否有效
     */
    private boolean isValidTeacherId(String teacherId) {
        return teacherId != null && !"undefined".equals(teacherId) && !teacherId.trim().isEmpty();
    }







    /**
     * 为教师列表添加实时统计数据
     * 包括学生数、总课时、已完成课时、剩余课时
     *
     * @param teachers 教师列表
     */
    private void enrichTeachersWithStatistics(List<TeacherDto.BasicResp> teachers) {
        if (teachers == null || teachers.isEmpty()) {
            return;
        }

        try {
            // 提取所有教师ID
            List<String> teacherIds = teachers.stream()
                    .map(TeacherDto.BasicResp::getId)
                    .collect(Collectors.toList());

            // 批量查询教师统计数据
            Map<String, TeacherStatistics> statisticsMap = batchCalculateTeacherStatistics(teacherIds);

            // 为每个教师设置统计数据
            for (TeacherDto.BasicResp teacher : teachers) {
                TeacherStatistics stats = statisticsMap.get(teacher.getId());
                if (stats != null) {
                    teacher.setStudentCount(stats.getStudentCount());
                    teacher.setTotalHours(stats.getTotalHours());
                    teacher.setCompletedHours(stats.getCompletedHours());
                    teacher.setRemainingHours(stats.getRemainingHours());
                } else {
                    // 设置默认值
                    teacher.setStudentCount(0);
                    teacher.setTotalHours(0);
                    teacher.setCompletedHours(0);
                    teacher.setRemainingHours(0);
                }
            }
        } catch (Exception e) {
            log.error("计算教师统计数据失败", e);
            // 出错时设置默认值
            for (TeacherDto.BasicResp teacher : teachers) {
                teacher.setStudentCount(0);
                teacher.setTotalHours(0);
                teacher.setCompletedHours(0);
                teacher.setRemainingHours(0);
            }
        }
    }

    /**
     * 批量计算教师统计数据 - 高性能版本
     * 使用3次批量查询替代N+1查询，大幅提升性能
     *
     * @param teacherIds 教师ID列表
     * @return 教师统计数据映射
     */
    private Map<String, TeacherStatistics> batchCalculateTeacherStatistics(List<String> teacherIds) {
        Map<String, TeacherStatistics> result = new HashMap<>();

        if (teacherIds == null || teacherIds.isEmpty()) {
            return result;
        }

        try {
            log.debug("开始批量计算教师统计数据: teacherIds={}", teacherIds);
            long startTime = System.currentTimeMillis();

            // 1. 批量查询所有教师的师生关系（1次查询）
            List<TeacherStudentRelation> allRelations = teacherStudentRelationService.lambdaQuery()
                    .in(TeacherStudentRelation::getTeacherId, teacherIds)
                    .eq(TeacherStudentRelation::getStatus, ACTIVE_STATUS)
                    .eq(TeacherStudentRelation::getDeleted, false)
                    .list();

            log.debug("查询到师生关系数量: {}", allRelations.size());

            // 2. 按教师ID分组师生关系
            Map<String, List<TeacherStudentRelation>> teacherRelationsMap = allRelations.stream()
                    .collect(Collectors.groupingBy(TeacherStudentRelation::getTeacherId));

            // 3. 获取所有学生ID（去重）
            Set<String> allStudentIds = allRelations.stream()
                    .map(TeacherStudentRelation::getStudentId)
                    .collect(Collectors.toSet());

            log.debug("涉及学生数量: {}", allStudentIds.size());

            // 4. 批量查询所有学生的课时信息（1次查询）
            Map<String, UserStudentExt> studentInfoMap = new HashMap<>();
            if (!allStudentIds.isEmpty()) {
                List<UserStudentExt> allStudents = userStudentExtService.lambdaQuery()
                        .in(UserStudentExt::getStudentId, allStudentIds)
                        .eq(UserStudentExt::getStatus, ACTIVE_STATUS)
                        .list();

                studentInfoMap = allStudents.stream()
                        .collect(Collectors.toMap(student -> student.getStudentId().toString(), student -> student));
            }

            log.debug("查询到学生信息数量: {}", studentInfoMap.size());

            // 5. 为每个教师计算统计数据（内存计算，无数据库查询）
            for (String teacherId : teacherIds) {
                TeacherStatistics stats = new TeacherStatistics();

                List<TeacherStudentRelation> teacherRelations = teacherRelationsMap.get(teacherId);
                if (teacherRelations == null || teacherRelations.isEmpty()) {
                    // 没有学生的教师
                    stats.setStudentCount(0);
                    stats.setTotalHours(0);
                    stats.setCompletedHours(0);
                    stats.setRemainingHours(0);
                } else {
                    // 获取该教师的学生信息
                    List<UserStudentExt> teacherStudents = teacherRelations.stream()
                            .map(TeacherStudentRelation::getStudentId)
                            .distinct()
                            .map(studentInfoMap::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());

                    // 计算统计数据
                    int studentCount = teacherStudents.size();
                    int totalHours = teacherStudents.stream()
                            .mapToInt(student -> student.getTotalHours() != null ? student.getTotalHours() : 0)
                            .sum();
                    int completedHours = teacherStudents.stream()
                            .mapToInt(student -> student.getConsumedHours() != null ? student.getConsumedHours() : 0)
                            .sum();
                    int remainingHours = Math.max(0, totalHours - completedHours);

                    stats.setStudentCount(studentCount);
                    stats.setTotalHours(totalHours);
                    stats.setCompletedHours(completedHours);
                    stats.setRemainingHours(remainingHours);
                }

                result.put(teacherId, stats);
            }

            long endTime = System.currentTimeMillis();
            log.debug("批量计算教师统计数据完成，耗时: {}ms, 教师数量: {}", endTime - startTime, teacherIds.size());

        } catch (Exception e) {
            log.error("批量计算教师统计数据失败: teacherIds={}", teacherIds, e);
            // 出错时为所有教师设置默认值
            for (String teacherId : teacherIds) {
                TeacherStatistics stats = new TeacherStatistics();
                stats.setStudentCount(0);
                stats.setTotalHours(0);
                stats.setCompletedHours(0);
                stats.setRemainingHours(0);
                result.put(teacherId, stats);
            }
        }

        return result;
    }

    /**
     * 教师统计数据内部类
     */
    private static class TeacherStatistics {
        private Integer studentCount;
        private Integer totalHours;
        private Integer completedHours;
        private Integer remainingHours;

        public Integer getStudentCount() { return studentCount; }
        public void setStudentCount(Integer studentCount) { this.studentCount = studentCount; }
        public Integer getTotalHours() { return totalHours; }
        public void setTotalHours(Integer totalHours) { this.totalHours = totalHours; }
        public Integer getCompletedHours() { return completedHours; }
        public void setCompletedHours(Integer completedHours) { this.completedHours = completedHours; }
        public Integer getRemainingHours() { return remainingHours; }
        public void setRemainingHours(Integer remainingHours) { this.remainingHours = remainingHours; }
    }

    /**
     * 使用服务层进行分页查询
     * 通过权限控制获取允许查询的教学组ID集合
     *
     * @param page 分页参数
     * @param req 查询条件
     * @return 教师分页列表
     */
    private IPage<TeacherDto.BasicResp> selectTeachersPageWithDirectJoin(Page<TeacherDto.BasicResp> page,
                                                                         TeacherDto.GetListReq req) {

        // 根据权限获取允许查询的教学组ID集合
        Set<String> allowedGroupIds = getPermittedGroupIds(req);

        // 调用服务层进行分页查询
        return teacherProfileService.selectTeachersPage(page, req, allowedGroupIds);
    }

    /**
     * 根据权限获取允许查询的教学组ID集合
     *
     * @param req 查询条件
     * @return 允许查询的教学组ID集合
     */
    private Set<String> getPermittedGroupIds(TeacherDto.GetListReq req) {
        // 检查是否有全部教师权限
        if (systemDataQueryUtil.hasAllTeacherRoles() || systemDataQueryUtil.isAdminOrHr()) {
            // 有全部权限，但如果指定了教学组，仍然应用该筛选条件
            if (StrUtil.isNotEmpty(req.getGroupId())) {
                return Set.of(req.getGroupId());
            }
            // 没有指定教学组，返回null表示不限制
            return null;
        }

        // 获取当前用户ID
        String currentUserId = SecurityUtils.getUserId().toString();

        if (systemDataQueryUtil.isTeacherGroupManager()) {
            // 教学组管理员只能查看自己管理的教学组的教师
            List<TeachingGroup> managedGroups = teachingGroupService.lambdaQuery()
                    .eq(TeachingGroup::getDeleted, false)
                    .and(wrapper -> wrapper.eq(TeachingGroup::getAdminId, currentUserId)
                            .or()
                            .eq(TeachingGroup::getLeaderId, currentUserId))
                    .list();

            Set<String> groupIds = managedGroups.stream()
                    .map(TeachingGroup::getId)
                    .collect(Collectors.toSet());

            // 如果指定了教学组，检查是否在权限范围内
            if (StrUtil.isNotEmpty(req.getGroupId())) {
                if (!groupIds.contains(req.getGroupId())) {
                    throw new IllegalArgumentException("无权限查看该教学组的教师");
                }
                return Set.of(req.getGroupId());
            }

            return groupIds;

        } else if (systemDataQueryUtil.isTeacher()) {
            // 普通教师只能查看自己的信息
            if (req.getTeacherId() !=null && !Objects.equals(req.getTeacherId(), currentUserId)) {
                throw new IllegalArgumentException("只能查询自己的信息");
            }
            // 返回空集合，在服务层会特殊处理
            return Collections.emptySet();

        } else {
            // 其他角色无权限
            throw new IllegalArgumentException("无权限查看教师信息");
        }
    }



    /**
     * 检查手机号是否被其他用户使用（排除指定用户）
     *
     * @param phoneNumber 手机号
     * @param excludeUserId 要排除的用户ID
     * @return 是否被其他用户使用
     */
    private boolean isPhoneNumberUsedByOtherUser(String phoneNumber, String excludeUserId) {
        if (StrUtil.isBlank(phoneNumber)) {
            return false;
        }

        return userService.isPhoneNumberUsedByOtherUser(phoneNumber, Long.valueOf(excludeUserId));
    }

    /**
     * 更新sys_user表中的基本信息
     *
     * @param req 更新请求
     * @param teacherId 教师ID
     */
    private void updateSysUserInfo(TeacherDto.UpdateTeacherReq req, String teacherId) {
        userService.lambdaUpdate()
                .set(SysUser::getUserName, req.getPhone())
                .set(SysUser::getNickName, req.getNickname())
                .set(SysUser::getPhonenumber, req.getPhone())
                .set(SysUser::getEmail, req.getEmail())
                .eq(SysUser::getUserId, Long.valueOf(teacherId))
                .update();
    }

    /**
     * 更新或创建教师档案
     *
     * @param req 更新请求
     * @param teacherId 教师ID
     */
    private void updateOrCreateTeacherProfile(TeacherDto.UpdateTeacherReq req, String teacherId) {
        TeacherProfile profile = teacherProfileService.lambdaQuery()
                .eq(TeacherProfile::getTeacherId, teacherId)
                .eq(TeacherProfile::getDeleted, false)
                .one();

        if (profile == null) {
            // 创建新的档案
            profile = createNewTeacherProfile(req, teacherId);
            boolean insertResult = teacherProfileService.save(profile);
            if (!insertResult) {
                throw new RuntimeException("创建教师档案失败");
            }
        } else {
            // 更新现有档案
            updateExistingTeacherProfile(profile, req);
            boolean updateResult = teacherProfileService.updateById(profile);
            if (!updateResult) {
                throw new RuntimeException("更新教师档案失败");
            }
        }
    }

    /**
     * 创建新的教师档案
     *
     * @param req 创建请求
     * @param teacherId 教师ID
     * @return 教师档案
     */
    private TeacherProfile createNewTeacherProfile(TeacherDto.UpdateTeacherReq req, String teacherId) {
        TeacherProfile profile = new TeacherProfile();
        profile.setTeacherId(teacherId);
        setTeacherProfileFields(profile, req);
        profile.setStatus(ACTIVE_STATUS);
        profile.setDeleted(false);
        return profile;
    }

    /**
     * 更新现有教师档案
     *
     * @param profile 现有档案
     * @param req 更新请求
     */
    private void updateExistingTeacherProfile(TeacherProfile profile, TeacherDto.UpdateTeacherReq req) {
        setTeacherProfileFields(profile, req);
    }

    /**
     * 设置教师档案字段
     *
     * @param profile 教师档案
     * @param req 请求数据
     */
    private void setTeacherProfileFields(TeacherProfile profile, TeacherDto.UpdateTeacherReq req) {
        // 基础信息
        profile.setRealName(req.getName());
        profile.setNickName(req.getNickname());
        profile.setPhonenumber(req.getPhone());
        profile.setGender(req.getGender());
        profile.setAge(req.getAge());
        profile.setCurrentLocation(req.getCurrentLocation());
        profile.setEmploymentType(req.getEmploymentType());
        profile.setCurrentStatus(req.getCurrentStatus());

        // 教育背景
        profile.setEducation(req.getEducation());
        profile.setGraduateSchool(req.getGraduateSchool());
        profile.setMajor(req.getMajor());
        profile.setUniversityType(req.getUniversityType());
        profile.setIsNormalUniversity(req.getIsNormalUniversity());
        profile.setStudyAbroad(req.getStudyAbroad());
        profile.setStudyAbroadCountry(req.getStudyAbroadCountry());

        // 教学资质
        profile.setTeachingCertificateLevel(req.getTeachingCertificateLevel());
        profile.setSubjects(req.getSubjects());
        profile.setTrainingSubjects(req.getTrainingSubjects());
        profile.setEnglishQualification(req.getEnglishQualification());
        profile.setMandarinQualification(req.getMandarinQualification());
        profile.setCommunicationAbility(req.getCommunicationAbility());

        // 教学经历
        profile.setTeachingExperience(req.getTeachingExperience());
        profile.setTaughtCourses(req.getTaughtCourses());
        profile.setTeachingYears(req.getTeachingYears());
        profile.setAwards(req.getAwards());

        // 教学风格和适配
        profile.setTeachingStyle(req.getTeachingStyle());
        profile.setEnglishPronunciation(req.getEnglishPronunciation());
        profile.setSuitableGrades(req.getSuitableGrades());
        profile.setSuitableLevels(req.getSuitableLevels());
        profile.setSuitablePersonality(req.getSuitablePersonality());

        // 暑期课上课时间
        profile.setSummerScheduleType(req.getSummerScheduleType());
        profile.setTrainingSubjects(req.getTrainingSubjects());

        // 新增字段
        profile.setFormalEntryDate(req.getFormalEntryDate());
        profile.setQualificationCertificates(req.getQualificationCertificates());
        profile.setDemoVideos(req.getDemoVideos());

        profile.setIntroduction(req.getIntroduction());
        profile.setOther(req.getOther());
    }

    @Override
    public List<TeacherDto.BasicResp> getFilteredActiveTeachers(TeacherMatchDto.MatchTeachersReq request) {
        log.info("开始获取符合筛选条件的活跃教师列表，筛选条件: {}", request);
        long startTime = System.currentTimeMillis();

        try {
            // 使用优化的数据库查询，直接在数据库层面进行筛选
            List<TeacherDto.BasicResp> teachers = teacherProfileService.selectFilteredActiveTeachers(request);

            long endTime = System.currentTimeMillis();
            log.info("获取符合筛选条件的活跃教师列表完成，耗时: {}ms, 教师数量: {}", endTime - startTime, teachers.size());

            return teachers;
        } catch (Exception e) {
            log.error("获取符合筛选条件的活跃教师列表失败", e);
            throw new RuntimeException("获取符合筛选条件的活跃教师列表失败: " + e.getMessage());
        }
    }

    @Override
    public Set<String> getManagedTeacherIds() {
        try {
            log.debug("获取当前用户管理的教师ID集合");
            if (!systemDataQueryUtil.isTeacherGroupManager()) {
                log.debug("当前用户不是教学组管理员，返回空集合");
                return Set.of();
            }

            String currentUserId = systemDataQueryUtil.getCurrentUserId();
            if (currentUserId == null) {
                log.debug("无法获取当前用户ID，返回空集合");
                return Set.of();
            }

            // 查询当前用户管理的教学组
            List<TeachingGroup> managedGroups = teachingGroupService.lambdaQuery()
                    .eq(TeachingGroup::getDeleted, false)
                    .and(wrapper -> wrapper.eq(TeachingGroup::getAdminId, currentUserId)
                            .or()
                            .eq(TeachingGroup::getLeaderId, currentUserId))
                    .list();

            if (managedGroups.isEmpty()) {
                log.debug("当前用户没有管理的教学组，返回空集合");
                return Set.of();
            }

            // 获取教学组ID集合
            Set<String> groupIds = managedGroups.stream()
                    .map(TeachingGroup::getId)
                    .collect(Collectors.toSet());

            log.debug("当前用户管理的教学组数量: {}, 教学组IDs: {}", managedGroups.size(), groupIds);

            // 查询这些教学组中的所有教师成员
            List<TeachingGroupMember> members = teachingGroupMemberService.lambdaQuery()
                    .in(TeachingGroupMember::getGroupId, groupIds)
                    .eq(TeachingGroupMember::getDeleted, false)
                    .eq(TeachingGroupMember::getStatus, "active")
                    .list();

            // 提取教师ID集合
            Set<String> teacherIds = members.stream()
                    .map(TeachingGroupMember::getTeacherId)
                    .collect(Collectors.toSet());

            log.debug("管理的教师数量: {}, 教师IDs: {}", teacherIds.size(), teacherIds);
            return teacherIds;

        } catch (Exception e) {
            log.error("获取管理的教师ID失败", e);
            return Set.of();
        }
    }

    /**
     * 检查重置密码权限
     * 管理员和人力可以重置所有人，组长和教务可以重置组内老师，其他人禁止操作
     *
     * @param teacherId 教师ID
     */
    private void checkResetPasswordPermission(String teacherId) {
        try {
            log.debug("检查重置密码权限: teacherId={}", teacherId);

            // 1. 管理员和人力可以重置所有人的密码
            if (systemDataQueryUtil.isAdminOrHr()) {
                log.debug("管理员或人力角色，有权限重置所有教师密码");
                return;
            }

            // 2. 组长和教务可以重置组内老师的密码
            if (systemDataQueryUtil.isTeacherGroupManager()) {
                log.debug("教学组管理员角色，检查是否可以管理该教师");

                // 检查该教师是否在当前用户管理的教学组中
                if (canManageTeacherInGroup(teacherId)) {
                    log.debug("该教师在当前用户管理的教学组中，有权限重置密码");
                    return;
                } else {
                    log.warn("该教师不在当前用户管理的教学组中，无权限重置密码: teacherId={}", teacherId);
                    throw new RuntimeException("无权限重置该教师的密码");
                }
            }

            // 3. 其他角色没有权限
            log.warn("当前用户角色无权限重置教师密码: teacherId={}", teacherId);
            throw new RuntimeException("无权限重置教师密码");

        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            log.error("检查重置密码权限失败: teacherId={}", teacherId, e);
            throw new RuntimeException("权限检查失败");
        }
    }
}
