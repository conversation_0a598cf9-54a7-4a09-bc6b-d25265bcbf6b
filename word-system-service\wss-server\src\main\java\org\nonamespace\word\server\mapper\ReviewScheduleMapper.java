package org.nonamespace.word.server.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.nonamespace.word.server.domain.ReviewSchedule;
import org.nonamespace.word.server.dto.ReviewScheduleQueryDto;

/**
 * 抗遗忘复习计划Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
public interface ReviewScheduleMapper extends BaseMapper<ReviewSchedule> {

    /**
     * 分页查询抗遗忘复习计划（关联学生信息）
     *
     * @param page 分页参数
     * @param req 查询条件
     * @return 分页结果
     */
    Page<ReviewScheduleQueryDto.Resp> selectPageWithStudent(Page<ReviewScheduleQueryDto.Resp> page, @Param("req") ReviewScheduleQueryDto.Req req);

    /**
     * 查询抗遗忘复习计划列表（关联学生信息）
     *
     * @param req 查询条件
     * @return 复习计划列表
     */
    List<ReviewScheduleQueryDto.Resp> selectListWithStudent(@Param("req") ReviewScheduleQueryDto.Req req);

}
