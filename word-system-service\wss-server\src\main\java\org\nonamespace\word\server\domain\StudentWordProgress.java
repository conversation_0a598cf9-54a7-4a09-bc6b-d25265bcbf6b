package org.nonamespace.word.server.domain;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nonamespace.word.server.entity.BaseEntity;
import org.nonamespace.word.server.entity.DataEntity;

import java.util.Date;

/**
 * 学生单词学习进度对象 student_word_progress
 * 
 * <AUTHOR>
 * @date 2025-05-20
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StudentWordProgress extends DataEntity {

    /** 学生ID */
    private String studentId;

    /** 词表ID */
    @Excel(name = "词表ID")
    private String textbookId;

    /** 关联的词表词汇项ID */
    private String textbookItemId;

    /** 单词ID */
    @Excel(name = "单词ID")
    private String wordId;

    /** 掌握状态（掌握、错误、D2、D4） */
    @Excel(name = "掌握状态", readConverterExp = "掌握、错误、D2、D4")
    private String status;

    /** 最近学习时间 (包括课堂学习和复习) */
    @Excel(name = "最近学习时间 (包括课堂学习和复习)")
    private Date lastStudiedAt;

    /** 最近一次在出现的课堂 */
    @Excel(name = "最近一次在出现的课堂")
    private String lastReviewInCourseId;

    /** 首次学习该词的课堂ID  */
    @Excel(name = "首次学习该词的课堂ID ")
    private String learnedInCourseId;

    /** 是否错过 */
    @Excel(name = "是否错过")
    private Boolean mistakes;

}
