package org.nonamespace.word.server.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 年级枚举类
 * <p>
 * 该枚举类用于表示不同的年级，包括小学、初中和高中各个年级。
 * </p>
 */
@Getter
public enum GradeEnum {
    GRADE_1("一年级", 1),
    GRADE_2("二年级", 2),
    GRADE_3("三年级", 3),
    GRADE_4("四年级", 4),
    GRADE_5("五年级", 5),
    GRADE_6("六年级", 6),
    GRADE_7("七年级", 7),
    GRADE_8("八年级", 8),
    GRADE_9("九年级", 9),
    GRADE_10("高一", 10),
    GRADE_11("高二", 11),
    GRADE_12("高三", 12),
    NULL(null, null);

    private final String text;
    private final Integer value;

    GradeEnum(String text, Integer value) {
        this.text = text;
        this.value = value;
    }


    public static GradeEnum fromText(String text) {
        for (GradeEnum grade : GradeEnum.values()) {
            if (grade.getText().equals(text)) {
                return grade;
            }
        }
        return NULL;
    }

    public static GradeEnum fromValue(Integer value) {
        for (GradeEnum grade : GradeEnum.values()) {
            if (Objects.equals(grade.getValue(), value)) {
                return grade;
            }
        }
        return NULL;
    }

    public static String toStage(Integer value) {
        if (value == null) {
            return "小学";
        }

        if (value >= 1 && value <= 6) {
            return "小学";
        } else if (value >= 7 && value <= 9) {
            return "初中";
        } else if (value >= 10 && value <= 12) {
            return "高中";
        }

        return "高中";
    }

    public static String toStage(String gradeText) {
        return toStage(fromText(gradeText));
    }

    public static String toStage(GradeEnum grade) {
        return toStage(grade == null ? null : grade.getValue());
    }


}