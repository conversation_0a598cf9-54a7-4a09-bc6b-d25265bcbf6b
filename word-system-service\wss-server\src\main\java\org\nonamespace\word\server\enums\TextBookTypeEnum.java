package org.nonamespace.word.server.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

import java.util.Arrays;

@Getter
public enum TextBookTypeEnum implements IEnum<String> {

    SCHOOL("1", "学校词表"),
    SPECIAL("2", "特色词表"),
    STUDENT("3", "学生词表");

    private final String value;
    private final String text;


    TextBookTypeEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    public static TextBookTypeEnum getByDefault(String value, TextBookTypeEnum defaultValue) {
        return Arrays.stream(values()).filter(e -> e.getValue().equals(value)).findFirst().orElse(defaultValue);
    }

    public static TextBookTypeEnum getByValue(String type) {
        return getByDefault(type, null);
    }

    @Override
    public String getValue() {
        return this.value;
    }
}
