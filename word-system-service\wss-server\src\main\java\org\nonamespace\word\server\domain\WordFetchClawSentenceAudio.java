package org.nonamespace.word.server.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@TableName(value = "word_fetch_claw_sentence_audio", autoResultMap = true)
public class WordFetchClawSentenceAudio {

    private String wordId;
    private String word;
    private String sentenceType;
    private String sentenceEn;
    private String audioUkUrl;
    private String audioUsUrl;

    public WordFetchClawSentenceAudio() {

    }

    public WordFetchClawSentenceAudio(String wordId, String word, String sentenceType, String sentenceEn, String audioUkUrl, String audioUsUrl) {
        this.wordId = wordId;
        this.word = word;
        this.sentenceType = sentenceType;
        this.sentenceEn = sentenceEn;
        this.audioUkUrl = audioUkUrl;
        this.audioUsUrl = audioUsUrl;
    }
}
