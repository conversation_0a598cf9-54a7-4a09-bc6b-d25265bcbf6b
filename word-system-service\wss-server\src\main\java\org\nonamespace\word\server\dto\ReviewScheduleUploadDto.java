package org.nonamespace.word.server.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.springframework.web.multipart.MultipartFile;
import java.util.Date;
import java.util.List;

/**
 * 抗遗忘复习上传DTO
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@Data
public class ReviewScheduleUploadDto {

    /**
     * 上传请求参数
     */
    @Data
    public static class UploadReq {

        /** 复习计划ID */
        @NotBlank(message = "复习计划ID不能为空")
        private String reviewScheduleId;

        /** 上传的图片文件 */
        @NotNull(message = "图片文件不能为空")
        private org.springframework.web.multipart.MultipartFile[] images;

        /** 说明文字 */
        @Size(max = 500, message = "说明文字不能超过500字符")
        private String description;
    }

    /**
     * 上传响应参数
     */
    @Data
    public static class UploadResp {
        
        /** 复习计划ID */
        private String reviewScheduleId;
        
        /** 上传状态 */
        private String status;
        
        /** 上传时间 */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date uploadedTime;
        
        /** 消息 */
        private String message;
    }

    /**
     * 查看上传内容请求参数
     */
    @Data
    public static class ViewReq {
        
        /** 复习计划ID */
        @NotBlank(message = "复习计划ID不能为空")
        private String reviewScheduleId;
    }

    /**
     * 查看上传内容响应参数
     */
    @Data
    public static class ViewResp {
        
        /** 复习计划ID */
        private String reviewScheduleId;
        
        /** 复习任务名称 */
        private String reviewName;
        
        /** 复习类型 */
        private String reviewType;
        
        /** 学生姓名 */
        private String studentName;
        
        /** 上传的图片URL列表 */
        private List<String> imageUrls;
        
        /** 说明文字 */
        private String description;
        
        /** 上传时间 */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date uploadedTime;
        
        /** 是否已上传 */
        private Boolean hasUploaded;
    }
}
