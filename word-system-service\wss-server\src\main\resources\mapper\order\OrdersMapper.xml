<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.nonamespace.word.server.mapper.order.OrdersMapper">


    <select id="selectOrdersByParam" resultType="org.nonamespace.word.server.dto.order.OrderPageDto$Resp">
        SELECT
        o.*,
        su1.nick_name AS student_name,
        su2.nick_name AS saler_name
        FROM
        orders o
        LEFT JOIN
        sys_user su1 ON CAST(su1.user_id AS VARCHAR) = o.student_id
        LEFT JOIN
        sys_user su2 ON CAST(su2.user_id AS VARCHAR) = o.saler_id
        <where>
            <if test="req.orderNo != null and req.orderNo != ''">
                AND o.no LIKE CONCAT('%', #{req.orderNo}::text, '%')
            </if>
            <if test="req.orderStatus != null and req.orderStatus != ''">
                AND o.order_status = #{req.orderStatus}
            </if>
            <if test="req.trxMethod != null and req.trxMethod != ''">
                AND o.trx_method = #{req.trxMethod}
            </if>
            <if test="req.studentName != null and req.studentName != ''">
                AND su1.nick_name LIKE CONCAT('%', #{req.studentName}::text, '%')
            </if>
            <if test="req.salerName != null and req.salerName != ''">
                AND su2.nick_name LIKE CONCAT('%', #{req.salerName}::text, '%')
            </if>
            <if test="req.beginTime != null">
                AND o.create_time &gt;= #{req.beginTime}
            </if>
            <if test="req.endTime != null">
                AND o.create_time &lt;= #{req.endTime}
            </if>
        </where>
        ORDER BY o.create_time DESC
    </select>
</mapper>