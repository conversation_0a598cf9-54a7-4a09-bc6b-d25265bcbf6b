package org.nonamespace.word.server.service;

import org.nonamespace.word.server.dto.management.teacherschedule.TeacherScheduleDashboardDto;

import java.time.LocalDate;
import java.util.Map;

/**
 * 老师可排课时间看板服务接口
 * 
 * <AUTHOR>
 * @since 2025-06-19
 */
public interface ITeacherScheduleDashboardService {

    /**
     * 获取老师可排课时间看板数据
     * 
     * @param request 查询请求
     * @return 看板数据
     */
    TeacherScheduleDashboardDto.DashboardResp getDashboard(TeacherScheduleDashboardDto.QueryReq request);

    /**
     * 获取快捷日期范围
     * 
     * @param quickRange 快捷范围类型
     * @return 开始和结束日期的Map，key为startDate和endDate
     */
    Map<String, String> getQuickDateRange(TeacherScheduleDashboardDto.QuickDateRange quickRange);

    /**
     * 计算单个教师在指定日期的可排课课次
     * 
     * @param teacherId 教师ID
     * @param date 日期
     * @return 可排课课次
     */
    Integer calculateTeacherAvailableSlots(String teacherId, LocalDate date);

    /**
     * 验证日期范围是否有效
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 是否有效
     */
    boolean validateDateRange(String startDate, String endDate);

    /**
     * 获取教学组趋势数据
     *
     * @param request 教学组趋势查询请求
     * @return 教学组趋势数据
     */
    TeacherScheduleDashboardDto.GroupTrendResp getGroupTrend(TeacherScheduleDashboardDto.GroupTrendReq request);

    /**
     * 获取指定日期的教师详细数据
     *
     * @param request 查询请求
     * @return 指定日期的教师详细数据
     */
    TeacherScheduleDashboardDto.DayTeacherDetailResp getDayTeacherDetail(TeacherScheduleDashboardDto.DayTeacherDetailReq request);
}
