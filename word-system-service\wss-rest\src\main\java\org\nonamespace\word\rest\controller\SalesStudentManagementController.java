package org.nonamespace.word.rest.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.dto.management.student.SalesStudentDto;
import org.nonamespace.word.server.facade.ISalesStudentManagementFacade;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

/**
 * 销售版学生管理Controller
 * 
 * <AUTHOR>
 * @date 2025-06-29
 */
@Slf4j
@RestController
@RequestMapping("/sales/student")
@RequiredArgsConstructor
@Validated
public class SalesStudentManagementController extends BaseController {

    private final ISalesStudentManagementFacade salesStudentManagementFacade;

    /**
     * 分页查询学生列表（销售版）
     */
    @PreAuthorize("@ss.hasPermi('sales:student:list')")
    @GetMapping("/list")
    public AjaxResult list(SalesStudentDto.GetListReq req) {
        try {
            IPage<SalesStudentDto.BasicResp> page = salesStudentManagementFacade.getSalesStudentPage(req);
            return AjaxResult.success(page);
        } catch (Exception e) {
            log.error("查询学生列表失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 根据ID查询学生详情（销售版）
     */
    @PreAuthorize("@ss.hasPermi('sales:student:query')")
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable String id) {
        try {
            SalesStudentDto.DetailResp result = salesStudentManagementFacade.getSalesStudentDetail(id);
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("查询学生详情失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 创建学生（销售版）
     */
    @PreAuthorize("@ss.hasPermi('sales:student:add')")
    @Log(title = "销售学生管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody SalesStudentDto.CreateReq req) {
        try {
            String id = salesStudentManagementFacade.createSalesStudent(req);
            return AjaxResult.success("创建成功", id);
        } catch (Exception e) {
            log.error("创建学生失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 更新学生信息（销售版）
     */
    @PreAuthorize("@ss.hasPermi('sales:student:edit')")
    @Log(title = "销售学生管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody SalesStudentDto.UpdateReq req) {
        try {
            boolean success = salesStudentManagementFacade.updateSalesStudent(req);
            return success ? AjaxResult.success("更新成功") : AjaxResult.error("更新失败");
        } catch (Exception e) {
            log.error("更新学生失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 删除学生（销售版）
     */
    @PreAuthorize("@ss.hasPermi('sales:student:remove')")
    @Log(title = "销售学生管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        try {
            List<String> studentIds = List.of(ids);
            boolean success = salesStudentManagementFacade.deleteSalesStudents(studentIds);
            return success ? AjaxResult.success("删除成功") : AjaxResult.error("删除失败");
        } catch (Exception e) {
            log.error("删除学生失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 导入学生（销售版）
     */
    @PreAuthorize("@ss.hasPermi('sales:student:import')")
    @Log(title = "销售学生管理", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public AjaxResult importStudents(@RequestParam("file") MultipartFile file,
                                   @RequestParam(value = "salesId", required = false) String salesId) {
        try {
            SalesStudentDto.ImportResp result = salesStudentManagementFacade.importSalesStudents(file, salesId);
            return AjaxResult.success("导入完成", result);
        } catch (Exception e) {
            log.error("导入学生失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 下载学生导入模板
     */
    @PreAuthorize("@ss.hasPermi('sales:student:import')")
    @PostMapping("/template")
    public void downloadTemplate(HttpServletResponse response) {
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("学生导入模板", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            // 获取模板数据并写入响应
            byte[] template = salesStudentManagementFacade.downloadImportTemplate();
            response.getOutputStream().write(template);
            response.getOutputStream().flush();

            log.info("学生导入模板下载成功");
        } catch (Exception e) {
            log.error("生成导入模板失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 分配学生给销售
     */
    @PreAuthorize("@ss.hasPermi('sales:student:assign')")
    @Log(title = "销售学生管理", businessType = BusinessType.UPDATE)
    @PostMapping("/assign")
    public AjaxResult assignStudent(@Valid @RequestBody SalesStudentDto.AssignReq req) {
        try {
            boolean success = salesStudentManagementFacade.assignStudentToSales(req);
            return success ? AjaxResult.success("分配成功") : AjaxResult.error("分配失败");
        } catch (Exception e) {
            log.error("分配学生失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 批量分配学生给销售
     */
    @PreAuthorize("@ss.hasPermi('sales:student:assign')")
    @Log(title = "销售学生管理", businessType = BusinessType.UPDATE)
    @PostMapping("/batch-assign")
    public AjaxResult batchAssignStudents(@Valid @RequestBody SalesStudentDto.BatchAssignReq req) {
        try {
            boolean success = salesStudentManagementFacade.batchAssignStudentsToSales(req);
            return success ? AjaxResult.success("批量分配成功") : AjaxResult.error("批量分配失败");
        } catch (Exception e) {
            log.error("批量分配学生失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 取消学生分配
     */
    @PreAuthorize("@ss.hasPermi('sales:student:assign')")
    @Log(title = "销售学生管理", businessType = BusinessType.UPDATE)
    @PostMapping("/unassign/{studentId}")
    public AjaxResult unassignStudent(@PathVariable String studentId) {
        try {
            boolean success = salesStudentManagementFacade.unassignStudent(studentId);
            return success ? AjaxResult.success("取消分配成功") : AjaxResult.error("取消分配失败");
        } catch (Exception e) {
            log.error("取消学生分配失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取销售版学生统计信息
     */
    @PreAuthorize("@ss.hasPermi('sales:student:list')")
    @GetMapping("/stats")
    public AjaxResult getStats() {
        try {
            SalesStudentDto.StatsResp result = salesStudentManagementFacade.getSalesStudentStats();
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("获取学生统计信息失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取可分配的学生列表
     */
    @PreAuthorize("@ss.hasPermi('sales:student:list')")
    @GetMapping("/available")
    public AjaxResult getAvailableStudents() {
        try {
            List<SalesStudentDto.AvailableResp> result = salesStudentManagementFacade.getAvailableStudents();
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("获取可分配学生列表失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取销售人员选项
     */
    @PreAuthorize("@ss.hasPermi('sales:student:list')")
    @GetMapping("/sales-options")
    public AjaxResult getSalesOptions(@RequestParam(value = "groupId", required = false) String groupId) {
        try {
            List<SalesStudentDto.SalesOptionResp> result = salesStudentManagementFacade.getSalesOptions(groupId);
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("获取销售人员选项失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取销售组选项
     */
    @PreAuthorize("@ss.hasPermi('sales:student:list')")
    @GetMapping("/group-options")
    public AjaxResult getSalesGroupOptions() {
        try {
            List<SalesStudentDto.GroupOptionResp> result = salesStudentManagementFacade.getSalesGroupOptions();
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("获取销售组选项失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }
}
