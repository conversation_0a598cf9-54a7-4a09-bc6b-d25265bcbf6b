package org.nonamespace.word.server.dto;

import lombok.Data;
import lombok.experimental.Accessors;
import org.nonamespace.word.server.domain.StudentWordTest;

import java.util.Date;

/**
 * <AUTHOR> Created on 2025/06/02 16:56
 */
@Data
@Accessors(chain = true)
public class WordTestDto {
    private String id;

    private Long studentId;

    private Date createTime;

    private String testedTime;

    private Integer testedWordNum;

    private Integer successWordNum;

    private String successRate;

    private String result;

    private Integer estimatedWordNum;

    private String status;

    private String courseId;

    private String suggestions;

    private String consumTime;

    private StudentWordTest.TestDetailInfo testDetailInfo;

    /** 错词讲义PDF下载地址 */
    private String errorHandoutPdfUrl;

    /** 错题练习PDF下载地址 */
    private String errorExercisePdfUrl;
}
