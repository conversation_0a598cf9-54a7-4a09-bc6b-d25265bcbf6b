package org.nonamespace.word.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.nonamespace.word.server.domain.StudentCourseHoursAdjustment;

import java.math.BigDecimal;

/**
 * 学生课时调整历史记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IStudentCourseHoursAdjustmentService extends IService<StudentCourseHoursAdjustment> {
    
    /**
     * 记录课时调整历史
     * 
     * @param studentId 学生ID
     * @param subject 学科
     * @param specification 课型
     * @param nature 性质
     * @param adjustmentType 调整类型
     * @param adjustmentHours 调整课时数
     * @param beforeTotalHours 调整前总课时
     * @param afterTotalHours 调整后总课时
     * @param beforeRemainingHours 调整前剩余课时
     * @param afterRemainingHours 调整后剩余课时
     * @param adjustmentReason 调整原因
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 调整记录
     */
    StudentCourseHoursAdjustment recordAdjustment(String studentId, String subject, String specification, String nature,
                                                String adjustmentType, BigDecimal adjustmentHours,
                                                BigDecimal purchasedHoursAdjustment, BigDecimal giftHoursAdjustment,
                                                BigDecimal beforeTotalHours, BigDecimal afterTotalHours,
                                                BigDecimal beforeRemainingHours, BigDecimal afterRemainingHours,
                                                BigDecimal beforePurchasedHours, BigDecimal afterPurchasedHours,
                                                BigDecimal beforeGiftHours, BigDecimal afterGiftHours,
                                                String adjustmentReason, String operatorId, String operatorName);
}
