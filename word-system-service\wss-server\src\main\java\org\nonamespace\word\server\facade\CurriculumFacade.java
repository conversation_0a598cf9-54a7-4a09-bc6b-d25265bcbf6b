package org.nonamespace.word.server.facade;

import org.nonamespace.word.server.dto.curriculum.*;

import java.io.InputStream;
import java.util.List;

public interface CurriculumFacade {
    void importCurriculum(InputStream inputStream);

    /**
     * 课表查询接口 (Get Course Schedule)
     * @param req
     * @return
     */
    List<CurriculemGetScheduleDto.Course> getSchedule(CurriculemGetScheduleDto.Req req);

    /**
     * 创建课表接口 (Create Course Schedule)
     *
     * @param req
     * @return
     */
    List<String> createSchedule(CurriculumCreateScheduleDto.Req req);


    /**
     * 停课接口 (Cancel Course)
     * @param req
     */
    void cancelCourse(CurriculeCancelScheduleDto.Req req);

    /**
     * 调课接口 (Reschedule Course)
     * @param req
     */
    void reschedule(CurriculumCourseRescheduleDto.Req req);

    /**
     * 消课接口
     * @param req
     */
    void consumeCourse(CurriculumConsumeCourseDto.Req req);
}
