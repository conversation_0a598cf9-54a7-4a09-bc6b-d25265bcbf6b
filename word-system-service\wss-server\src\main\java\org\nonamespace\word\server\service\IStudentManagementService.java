package org.nonamespace.word.server.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.nonamespace.word.server.dto.management.student.StudentDto;

import java.util.List;

/**
 * 学生管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
public interface IStudentManagementService {

    /**
     * 分页查询学生列表
     * 
     * @param req 查询请求
     * @return 学生分页列表
     */
    IPage<StudentDto.BasicResp> getStudentPage(StudentDto.GetListReq req);

    /**
     * 查询学生详细信息
     * 
     * @param studentId 学生ID
     * @return 学生详细信息
     */
    StudentDto.DetailResp getStudentDetail(String studentId);

    /**
     * 创建学生
     * 
     * @param req 创建请求
     * @return 学生ID
     */
    String createStudent(StudentDto.CreateReq req);

    /**
     * 更新学生信息
     * 
     * @param req 更新请求
     * @return 是否成功
     */
    boolean updateStudent(StudentDto.UpdateReq req);

    /**
     * 删除学生
     * 
     * @param studentId 学生ID
     * @return 是否成功
     */
    boolean deleteStudent(String studentId);

    /**
     * 批量删除学生
     * 
     * @param studentIds 学生ID列表
     * @return 是否成功
     */
    boolean deleteStudents(List<String> studentIds);

    /**
     * 获取学生统计信息
     * 
     * @return 统计信息
     */
    StudentDto.StatsResp getStudentStats();

    /**
     * 获取学生课表
     * 
     * @param studentId 学生ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 课表列表
     */
    List<StudentDto.ScheduleResp> getStudentSchedule(String studentId, String startDate, String endDate);

    /**
     * 获取可分配的学生列表（未分配教师的学生）
     * 
     * @return 可分配学生列表
     */
    List<StudentDto.AvailableResp> getAvailableStudents();

    /**
     * 获取学生课程统计
     * 
     * @param studentId 学生ID
     * @return 课程统计
     */
    StudentDto.CourseStatsResp getStudentCourseStats(String studentId);

    /**
     * 获取学生最近课程
     * 
     * @param studentId 学生ID
     * @param limit 限制数量
     * @return 最近课程列表
     */
    List<StudentDto.RecentCourseResp> getStudentRecentCourses(String studentId, Integer limit);

    /**
     * 根据教师ID获取学生列表
     * 
     * @param teacherId 教师ID
     * @return 学生列表
     */
    List<StudentDto.BasicResp> getStudentsByTeacher(String teacherId);

    /**
     * 分配教师给学生
     * 
     * @param studentId 学生ID
     * @param teacherId 教师ID
     * @return 是否成功
     */
    boolean assignTeacherToStudent(String studentId, String teacherId);

    /**
     * 取消学生的教师分配
     * 
     * @param studentId 学生ID
     * @return 是否成功
     */
    boolean unassignTeacherFromStudent(String studentId);
}
