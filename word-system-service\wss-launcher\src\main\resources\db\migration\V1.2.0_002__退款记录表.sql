-- =====================================================
-- 退款记录表创建SQL
-- 版本: V1.2.0_002
-- 描述: 创建退款记录表，用于记录所有退款操作的详细信息
-- 作者: WSS-AGENT
-- 日期: 2025-08-07
-- =====================================================

-- =====================================================
-- 1. 创建退款记录表
-- =====================================================

CREATE TABLE IF NOT EXISTS words.order_refund_records (
    id varchar(64) NOT NULL,
    order_id VARCHAR(64) NOT NULL,
    order_no VARCHAR(100) NOT NULL,
    original_trx_id VARCHAR(64),
    refund_trx_id VARCHAR(64),
    refund_type VARCHAR(20) NOT NULL,
    refund_amount BIGINT NOT NULL,
    refund_reason VARCHAR(500) NOT NULL,
    refund_status VARCHAR(20) NOT NULL DEFAULT 'processing',
    student_id VARCHAR(64),
    student_name VARCHAR(100),
    student_phone VARCHAR(20),
    saler_id VARCHAR(64),
    saler_name VARCHAR(100),
    product_id VARCHAR(64),
    product_name VARCHAR(200),
    subject VARCHAR(50),
    course_type VARCHAR(50),
    refund_method VARCHAR(50),
    platform_refund_id VARCHAR(100),
    platform_response TEXT,
    operator_id VARCHAR(64) NOT NULL,
    operator_name VARCHAR(100) NOT NULL,
    operator_role VARCHAR(50),
    approval_status VARCHAR(20) DEFAULT 'auto_approved',
    approver_id VARCHAR(64),
    approver_name VARCHAR(100),
    approval_time TIMESTAMP,
    approval_remark VARCHAR(500),
    refund_time TIMESTAMP,
    error_message TEXT,
    remark TEXT,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(64) NOT NULL,
    update_by VARCHAR(64) NOT NULL,
	deleted bool DEFAULT false NOT NULL,

    PRIMARY KEY (id)
) WITH (OIDS = FALSE);

COMMENT ON TABLE words.order_refund_records IS '退款记录表';
COMMENT ON COLUMN words.order_refund_records.id IS '退款记录ID';
COMMENT ON COLUMN words.order_refund_records.order_id IS '订单ID';
COMMENT ON COLUMN words.order_refund_records.order_no IS '订单号';
COMMENT ON COLUMN words.order_refund_records.original_trx_id IS '原交易流水ID';
COMMENT ON COLUMN words.order_refund_records.refund_trx_id IS '退款交易流水ID';
COMMENT ON COLUMN words.order_refund_records.refund_type IS '退款类型：partial-部分退款，full-全额退款';
COMMENT ON COLUMN words.order_refund_records.refund_amount IS '退款金额（分）';
COMMENT ON COLUMN words.order_refund_records.refund_reason IS '退款原因';
COMMENT ON COLUMN words.order_refund_records.refund_status IS '退款状态：processing-处理中，success-成功，failed-失败';
COMMENT ON COLUMN words.order_refund_records.student_id IS '学生ID';
COMMENT ON COLUMN words.order_refund_records.student_name IS '学生姓名';
COMMENT ON COLUMN words.order_refund_records.student_phone IS '学生手机号';
COMMENT ON COLUMN words.order_refund_records.saler_id IS '销售员ID';
COMMENT ON COLUMN words.order_refund_records.saler_name IS '销售员姓名';
COMMENT ON COLUMN words.order_refund_records.product_id IS '产品ID';
COMMENT ON COLUMN words.order_refund_records.product_name IS '产品名称';
COMMENT ON COLUMN words.order_refund_records.subject IS '学科';
COMMENT ON COLUMN words.order_refund_records.course_type IS '课型';
COMMENT ON COLUMN words.order_refund_records.refund_method IS '退款方式：original-原路退回，manual-手动退款';
COMMENT ON COLUMN words.order_refund_records.platform_refund_id IS '支付平台退款ID';
COMMENT ON COLUMN words.order_refund_records.platform_response IS '支付平台响应信息';
COMMENT ON COLUMN words.order_refund_records.operator_id IS '操作人ID';
COMMENT ON COLUMN words.order_refund_records.operator_name IS '操作人姓名';
COMMENT ON COLUMN words.order_refund_records.operator_role IS '操作人角色';
COMMENT ON COLUMN words.order_refund_records.approval_status IS '审批状态：pending-待审批，approved-已审批，rejected-已拒绝，auto_approved-自动审批';
COMMENT ON COLUMN words.order_refund_records.approver_id IS '审批人ID';
COMMENT ON COLUMN words.order_refund_records.approver_name IS '审批人姓名';
COMMENT ON COLUMN words.order_refund_records.approval_time IS '审批时间';
COMMENT ON COLUMN words.order_refund_records.approval_remark IS '审批备注';
COMMENT ON COLUMN words.order_refund_records.refund_time IS '退款完成时间';
COMMENT ON COLUMN words.order_refund_records.error_message IS '错误信息（退款失败时）';
COMMENT ON COLUMN words.order_refund_records.remark IS '备注';
COMMENT ON COLUMN words.order_refund_records.create_time IS '创建时间';
COMMENT ON COLUMN words.order_refund_records.update_time IS '更新时间';
COMMENT ON COLUMN words.order_refund_records.create_by IS '创建人';
COMMENT ON COLUMN words.order_refund_records.update_by IS '更新人';
COMMENT ON COLUMN words.order_refund_records.del_flag IS '删除标志（0代表存在 2代表删除）';


-- =====================================================
-- 2. 创建退款统计视图
-- =====================================================

CREATE OR REPLACE VIEW words.v_refund_statistics AS
SELECT
    DATE_TRUNC('day', create_time) as refund_date,
    refund_type,
    refund_status,
    COUNT(*) as refund_count,
    SUM(refund_amount) as total_refund_amount,
    AVG(refund_amount) as avg_refund_amount,
    operator_name,
    subject,
    course_type
FROM words.order_refund_records
WHERE deleted=false
GROUP BY DATE_TRUNC('day', create_time), refund_type, refund_status, operator_name, subject, course_type;

-- =====================================================
-- 3. 添加退款记录相关权限
-- =====================================================

-- 3.1 退款记录查看权限
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (4010, '退款记录', 2000, 15, 'refund-records', 'management/refund-records/index', '', 1, 0, 'C', '0', '0', 'refund:records:list', 'money', 'admin', CURRENT_TIMESTAMP, '退款记录管理页面');

-- 3.2 退款记录管理权限按钮
INSERT INTO words.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES
(4011, '退款记录查询', 4010, 1, '', '', '', 1, 0, 'F', '0', '0', 'refund:records:query', '#', 'admin', CURRENT_TIMESTAMP, '退款记录查询权限'),
(4012, '退款记录详情', 4010, 2, '', '', '', 1, 0, 'F', '0', '0', 'refund:records:detail', '#', 'admin', CURRENT_TIMESTAMP, '退款记录详情权限'),
(4013, '退款记录导出', 4010, 3, '', '', '', 1, 0, 'F', '0', '0', 'refund:records:export', '#', 'admin', CURRENT_TIMESTAMP, '退款记录导出权限'),
(4014, '退款记录统计', 4010, 4, '', '', '', 1, 0, 'F', '0', '0', 'refund:records:statistics', '#', 'admin', CURRENT_TIMESTAMP, '退款记录统计权限'),
(4015, '退款记录审批', 4010, 5, '', '', '', 1, 0, 'F', '0', '0', 'refund:records:approve', '#', 'admin', CURRENT_TIMESTAMP, '退款记录审批权限');

-- =====================================================
-- 4. 角色权限关联配置
-- =====================================================

-- 4.1 为超级管理员角色分配所有退款记录权限
INSERT INTO words.sys_role_menu (role_id, menu_id) VALUES
(1, 4010), -- 超级管理员 - 退款记录页面
(1, 4011), -- 超级管理员 - 退款记录查询
(1, 4012), -- 超级管理员 - 退款记录详情
(1, 4013), -- 超级管理员 - 退款记录导出
(1, 4014), -- 超级管理员 - 退款记录统计
(1, 4015); -- 超级管理员 - 退款记录审批

-- 4.2 为人力角色分配所有退款记录权限
INSERT INTO words.sys_role_menu (role_id, menu_id) VALUES
(2, 4010), -- 人力 - 退款记录页面
(2, 4011), -- 人力 - 退款记录查询
(2, 4012), -- 人力 - 退款记录详情
(2, 4013), -- 人力 - 退款记录导出
(2, 4014), -- 人力 - 退款记录统计
(2, 4015); -- 人力 - 退款记录审批

-- 4.3 为销售总监角色分配退款记录权限
INSERT INTO words.sys_role_menu (role_id, menu_id) VALUES
(9, 4010), -- 销售总监 - 退款记录页面
(9, 4011), -- 销售总监 - 退款记录查询
(9, 4012), -- 销售总监 - 退款记录详情
(9, 4013), -- 销售总监 - 退款记录导出
(9, 4014); -- 销售总监 - 退款记录统计

-- 4.4 为销售组长角色分配基础退款记录权限
INSERT INTO words.sys_role_menu (role_id, menu_id) VALUES
(10, 4010), -- 销售组长 - 退款记录页面
(10, 4011), -- 销售组长 - 退款记录查询
(10, 4012); -- 销售组长 - 退款记录详情

-- =====================================================
-- 5. 初始化数据和索引优化
-- =====================================================

-- 更新菜单ID序列
SELECT SETVAL('words.sys_menu_menu_id_seq', 4100, TRUE);

-- 执行完成提示
SELECT '退款记录表创建完成！' AS result;