package org.nonamespace.word.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.nonamespace.word.server.domain.Course;
import org.nonamespace.word.server.domain.WxSendMessage;
import org.nonamespace.word.server.dto.course.CourseSendMessageDto;

import java.util.List;

public interface IWxSendMessageService extends IService<WxSendMessage> {

    /**
     * 下课模板消息
     * @param course  课程
     */
    void generalEndCourseWxMessage(Course course);

    /**
     * 复习课下课模板消息
     * @param course
     */
    void generalEndReviewWxMessage(Course course);

    void buildTomorrowCourseWxMessage();

    void buildToday12MinCourseList();

    void buildAfter20ModifyCourseList();

    void buildRightNowCourseList(List<Course> list);

}
