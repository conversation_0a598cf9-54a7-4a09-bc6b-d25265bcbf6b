import request from '@/utils/request'

// 查询退款记录列表
export function getRefundRecordList(query) {
  return request({
    url: '/refund-records/list',
    method: 'get',
    params: query
  })
}

// 查询退款记录详情
export function getRefundRecordDetail(refundRecordId) {
  return request({
    url: `/refund-records/${refundRecordId}`,
    method: 'get'
  })
}

// 审批退款记录
export function approveRefundRecord(data) {
  return request({
    url: '/refund-records/approve',
    method: 'post',
    data: data
  })
}

// 查询退款统计数据
export function getRefundStatistics(params) {
  return request({
    url: '/refund-records/statistics',
    method: 'get',
    params: params
  })
}

// 查询待审批的退款记录数量
export function getPendingApprovalCount() {
  return request({
    url: '/refund-records/pending-count',
    method: 'get'
  })
}

// 导出退款记录
export function exportRefundRecords(query) {
  return request({
    url: '/refund-records/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

// 查询订单的退款记录
export function getRefundRecordsByOrderId(orderId) {
  return request({
    url: `/refund-records/order/${orderId}`,
    method: 'get'
  })
}

// ==================== 管理员接口 ====================

// 管理员查询退款记录列表
export function getManagerRefundRecordList(query) {
  return request({
    url: '/refund-records-manager/list',
    method: 'get',
    params: query
  })
}

// 管理员查询退款记录详情
export function getManagerRefundRecordDetail(refundRecordId) {
  return request({
    url: `/refund-records-manager/${refundRecordId}`,
    method: 'get'
  })
}

// 管理员批量审批退款记录
export function batchApproveRefundRecords(data) {
  return request({
    url: '/refund-records-manager/batch-approve',
    method: 'post',
    data: data
  })
}

// 管理员查询退款统计数据
export function getManagerRefundStatistics(params) {
  return request({
    url: '/refund-records-manager/statistics',
    method: 'get',
    params: params
  })
}

// 管理员导出退款记录
export function exportManagerRefundRecords(query) {
  return request({
    url: '/refund-records-manager/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

// 管理员查询退款记录概览
export function getManagerRefundOverview() {
  return request({
    url: '/refund-records-manager/overview',
    method: 'get'
  })
}

// 管理员批量更新退款记录状态
export function batchUpdateRefundStatus(refundRecordIds, refundStatus) {
  return request({
    url: '/refund-records-manager/batch-update-status',
    method: 'post',
    data: {
      refundRecordIds: refundRecordIds,
      refundStatus: refundStatus
    }
  })
}
