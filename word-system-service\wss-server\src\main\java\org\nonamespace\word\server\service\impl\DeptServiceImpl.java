package org.nonamespace.word.server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.system.service.ISysDeptService;
import lombok.RequiredArgsConstructor;
import org.nonamespace.word.common.misc.WssContext;
import org.nonamespace.word.server.mapper.SeqIdMapper;
import org.nonamespace.word.server.mapper.DeptMapper;
import org.nonamespace.word.server.service.IDeptService;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class DeptServiceImpl extends ServiceImpl<DeptMapper, SysDept> implements IDeptService {
    private final SeqIdMapper seqIdMapper;
    private final ISysDeptService sysDeptService;

    @Override
    public Long nextDeptId() {
        return seqIdMapper.getNextDeptId();
    }

    @Override
    public boolean save(SysDept sysDept){
        if (sysDept.getDeptId() == null) {
            sysDept.setDeptId(nextDeptId());
        }
        if (sysDept.getParentId() == null) {
            sysDept.setParentId(0L); // 默认父部门为0
        }
        if (sysDept.getOrderNum() == null) {
            sysDept.setOrderNum(0); // 默认排序为0
        }
        if (sysDept.getStatus() == null) {
            sysDept.setStatus("0"); // 默认状态为启用
        }
        if (sysDept.getCreateBy() == null) {
            sysDept.setCreateBy(WssContext.userId());
        }
        if (sysDept.getCreateTime() == null) {
            sysDept.setCreateTime(WssContext.now());
        }
        if (sysDept.getUpdateBy() == null) {
            sysDept.setUpdateBy(WssContext.userId());
        }
        if (sysDept.getUpdateTime() == null) {
            sysDept.setUpdateTime(WssContext.now());
        }
        if (sysDept.getDelFlag() == null) {
            sysDept.setDelFlag("0"); // 默认未删除
        }
        if (sysDept.getDeptName() == null || sysDept.getDeptName().isEmpty()) {
            throw new IllegalArgumentException("部门名称不能为空");
        }

        return sysDeptService.insertDept(sysDept) == 1;
    }
}
