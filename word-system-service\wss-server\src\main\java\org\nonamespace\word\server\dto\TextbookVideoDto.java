package org.nonamespace.word.server.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.web.multipart.MultipartFile;

/**
 * 教材单词视频DTO
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public class TextbookVideoDto {

    /**
     * 上传视频请求DTO
     */
    @Data
    @Accessors(chain = true)
    public static class UploadReq {
        
        /** 教材项ID */
        @NotBlank(message = "教材项ID不能为空")
        private String textbookItemId;
        
        /** 视频文件 */
        private MultipartFile videoFile;
        
        /** 视频描述 */
        private String description;
    }

    /**
     * 视频信息响应DTO
     */
    @Data
    @Accessors(chain = true)
    public static class VideoResp {
        
        /** 教材项ID */
        private String textbookItemId;
        
        /** 单词 */
        private String word;
        
        /** 单词ID */
        private String wordId;
        
        /** 视频URL */
        private String videoUrl;
        
        /** 视频文件名 */
        private String videoFileName;
        
        /** 上传时间 */
        private String uploadTime;
        
        /** 文件大小 */
        private Long fileSize;
    }

    /**
     * 删除视频请求DTO
     */
    @Data
    @Accessors(chain = true)
    public static class DeleteReq {
        
        /** 教材项ID */
        @NotBlank(message = "教材项ID不能为空")
        private String textbookItemId;
    }

    /**
     * 查询视频请求DTO
     */
    @Data
    @Accessors(chain = true)
    public static class QueryReq {
        
        /** 教材ID */
        private String textbookId;
        
        /** 教材项ID */
        private String textbookItemId;
        
        /** 单词ID */
        private String wordId;
        
        /** 关键词搜索 */
        private String keyword;
    }
}
