package org.nonamespace.word.server.enums;

/**
 * 复习计划状态枚举类
 * <p>
 * 该枚举类用于表示复习计划的不同状态，包括待开始、进行中、已完成和已跳过。
 * </p>
 */
public enum ReviewScheduleTypeEnum {
    D2("D2", 1),
    D4("D4", 3),
    D7("D7", 6),
    D14("D14", 13),
    D21("D21", 20);
    private final String type;
    private final int days;
    ReviewScheduleTypeEnum(String type, int days) {
        this.type = type;
        this.days = days;
    }
    public String getType() {
        return type;
    }
    public int getDays() {
        return days;
    }

}
