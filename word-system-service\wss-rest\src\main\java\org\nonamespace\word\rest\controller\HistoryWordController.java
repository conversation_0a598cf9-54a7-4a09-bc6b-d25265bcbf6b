//package org.nonamespace.word.rest.controller;
//
//import java.util.List;
//
//import jakarta.servlet.http.HttpServletResponse;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.PutMapping;
//import org.springframework.web.bind.annotation.DeleteMapping;
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//import com.ruoyi.common.annotation.Log;
//import com.ruoyi.common.core.controller.BaseController;
//import com.ruoyi.common.core.domain.AjaxResult;
//import com.ruoyi.common.enums.BusinessType;
//import org.nonamespace.word.server.domain.HistoryWord;
//import org.nonamespace.word.server.service.IHistoryWordService;
//import com.ruoyi.common.utils.poi.ExcelUtil;
//
///**
// * 单词历史: 存储单词的修改历史版本Controller
// * 
// * <AUTHOR>
// * @date 2025-05-13
// */
//@RestController
//@RequestMapping("/word/word")
//public class HistoryWordController extends BaseController
//{
//    @Autowired
//    private IHistoryWordService historyWordService;
//
//
//    /**
//     * 导出单词历史: 存储单词的修改历史版本列表
//     */
//    @PreAuthorize("@ss.hasPermi('word:word:export')")
//    @Log(title = "单词历史: 存储单词的修改历史版本", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, HistoryWord historyWord)
//    {
//        List<HistoryWord> list = historyWordService.selectHistoryWordList(historyWord);
//        ExcelUtil<HistoryWord> util = new ExcelUtil<HistoryWord>(HistoryWord.class);
//        util.exportExcel(response, list, "单词历史: 存储单词的修改历史版本数据");
//    }
//
//    /**
//     * 获取单词历史: 存储单词的修改历史版本详细信息
//     */
//    @PreAuthorize("@ss.hasPermi('word:word:query')")
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") String id)
//    {
//        return success(historyWordService.selectHistoryWordById(id));
//    }
//
//    /**
//     * 新增单词历史: 存储单词的修改历史版本
//     */
//    @PreAuthorize("@ss.hasPermi('word:word:add')")
//    @Log(title = "单词历史: 存储单词的修改历史版本", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody HistoryWord historyWord)
//    {
//        return toAjax(historyWordService.insertHistoryWord(historyWord));
//    }
//
//    /**
//     * 修改单词历史: 存储单词的修改历史版本
//     */
//    @PreAuthorize("@ss.hasPermi('word:word:edit')")
//    @Log(title = "单词历史: 存储单词的修改历史版本", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody HistoryWord historyWord)
//    {
//        return toAjax(historyWordService.updateHistoryWord(historyWord));
//    }
//
//    /**
//     * 删除单词历史: 存储单词的修改历史版本
//     */
//    @PreAuthorize("@ss.hasPermi('word:word:remove')")
//    @Log(title = "单词历史: 存储单词的修改历史版本", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable String[] ids)
//    {
//        return toAjax(historyWordService.deleteHistoryWordByIds(ids));
//    }
//}
