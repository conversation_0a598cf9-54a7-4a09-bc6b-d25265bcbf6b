package org.nonamespace.word.server.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 抗遗忘复习查询DTO
 * 
 * <AUTHOR>
 * @date 2025-06-07
 */
public class ReviewScheduleQueryDto {

    /**
     * 查询请求参数
     */
    @Data
    public static class Req {
        
        /** 关键字搜索（复习任务名称） */
        private String keyword;
        
        /** 老师ID */
        private String teacherId;
        
        /** 学生ID */
        private String studentId;
        
        /** 复习类型列表 (如 D2, D4, D7, D14, D21) */
        private List<String> reviewTypes;
        
        /** 复习状态列表 (待开始, 进行中, 已完成, 已跳过) */
        private List<String> statuses;
        
        /** 复习时间范围 - 开始时间 */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date startTime;
        
        /** 复习时间范围 - 结束时间 */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date endTime;
        
        /** 页码 */
        private Integer pageNum = 1;
        
        /** 每页大小 */
        private Integer pageSize = 10;
        
        /** 排序字段 */
        private String orderBy = "scheduled_time";
        
        /** 排序方向 */
        private String orderDirection = "ASC";
    }

    /**
     * 查询响应参数
     */
    @Data
    public static class Resp {
        
        /** 复习计划ID */
        private String id;
        
        /** 学生ID */
        private String studentId;
        
        /** 学生姓名 */
        private String studentName;
        
        /** 关联的原始学习课程ID */
        private String courseId;
        
        /** 复习类型 */
        private String reviewType;
        
        /** 复习任务名称 */
        private String name;
        
        /** 计划复习时间 */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date scheduledTime;
        
        /** 实际开始复习时间 */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date actualStartTime;
        
        /** 实际完成复习时间 */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date actualEndTime;
        
        /** 复习状态 */
        private String status;
        
        /** 本次复习的单词ID列表 */
        private List<String> wordIds;
        
        /** 本次复习的词表项ID列表 */
        private List<String> textbookItemIds;
        
        /** 版本号 */
        private Long version;
        
        /** 统计信息 - 单词总数 */
        private Long statWordTotal;
        
        /** 统计信息 - 单词正确数 */
        private Long statWordCorrect;
        
        /** 统计信息 - 单词错误数 */
        private Long statWordIncorrect;
        
        /** 统计信息 - 步骤总数 */
        private Long statStepTotal;
        
        /** 统计信息 - 步骤正确数 */
        private Long statStepCorrect;
        
        /** 统计信息 - 步骤错误数 */
        private Long statStepIncorrect;
        
        /** 复习课程ID */
        private String reviewCourseId;
        
        /** 是否自主复习 */
        private Boolean reviewByOneself;
        
        /** 复习执行人ID */
        private String reviewByUserId;
        
        /** 创建时间 */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date createTime;
        
        /** 更新时间 */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date updateTime;

        /** 上传的图片URL列表 */
        private List<String> uploadedImages;

        /** 上传的说明文字 */
        private String uploadedDescription;

        /** 上传时间 */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date uploadedTime;
    }
}
