package org.nonamespace.word.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.nonamespace.word.server.domain.StudentWordProgress;
import org.nonamespace.word.server.dto.LastWordDto;
import org.nonamespace.word.server.vo.TextbookTreeVo;

/**
 * 学生单词学习进度Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface IStudentWordProgressService extends IService<StudentWordProgress>
{

    LastWordDto getProgressByStudentId(String studentId,String textbookId);

    StudentWordProgress getByNotDelBook(String studentId,String textbookId);
}
