package org.nonamespace.word.launcher.test;


import org.junit.Test;
import org.junit.runner.RunWith;
import org.nonamespace.word.server.domain.Course;
import org.nonamespace.word.server.service.ICourseService;
import org.nonamespace.word.server.service.IWxSendMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest
@RunWith(SpringRunner.class)
public class WxSendMessageServiceTest {

    @Autowired
    private IWxSendMessageService wxSendMessageService;
    @Autowired
    private ICourseService courseService;

    @Test
    public void testEndCourseMessage() {
        Course course = courseService.getById("1950543861815898113");
        wxSendMessageService.generalEndCourseWxMessage(course);
    }

    @Test
    public void testEndReviewMessage() {
        Course course = courseService.getById("1943665012696883200");
        wxSendMessageService.generalEndReviewWxMessage(course);
    }
}
