package org.nonamespace.word.server.model;


import lombok.Data;

@Data
public class ESignDocTemplateStructureV1 {

    /**
     * 签订合同日期
     */
    private String signDate;

    /**
     * 学生姓名
     */
    private String studentName;

    /**
     * 个人章/签名
     */
    private String personalSign;

    /**
     * 家长姓名
     */
    private String parentName;

    /**
     * 家长手机
     */
    private String parentPhone;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 大写金额
     */
    private String upperMoney;

    /**
     * 小写金额
     */
    private String lowerMoney;

    /**
     * 正价课课时数
     */
    private String classHours;

    /**
     * 赠送课时数
     */
    private String giftClassHours;

    /**
     * 单次课价格
     */
    private String unitPrice;

}
