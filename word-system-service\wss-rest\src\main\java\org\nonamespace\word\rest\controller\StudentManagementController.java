package org.nonamespace.word.rest.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.dto.management.student.StudentDto;
import org.nonamespace.word.server.service.IStudentManagementService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 学生管理Controller
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@RestController
@RequestMapping("/management/students")
@RequiredArgsConstructor
@Slf4j
@Validated
public class StudentManagementController extends BaseController {

    private final IStudentManagementService studentManagementService;

    /**
     * 分页查询学生列表
     */
    @GetMapping
    public AjaxResult getStudents(StudentDto.GetListReq req) {
        try {
            IPage<StudentDto.BasicResp> page = studentManagementService.getStudentPage(req);
            return success(page);
        } catch (Exception e) {
            log.error("获取学生列表失败", e);
            return error("获取学生列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取学生详细信息
     */
    @GetMapping("/{studentId}")
    public AjaxResult getStudentDetail(@PathVariable String studentId) {
        try {
            StudentDto.DetailResp detail = studentManagementService.getStudentDetail(studentId);
            if (detail == null) {
                return error("学生不存在");
            }
            return success(detail);
        } catch (Exception e) {
            log.error("获取学生详细信息失败", e);
            return error("获取学生详细信息失败: " + e.getMessage());
        }
    }

    /**
     * 创建学生
     */
    @PostMapping
    public AjaxResult createStudent(@Valid @RequestBody StudentDto.CreateReq req) {
        try {
            String studentId = studentManagementService.createStudent(req);
            return success(studentId);
        } catch (Exception e) {
            log.error("创建学生失败", e);
            return error("创建学生失败: " + e.getMessage());
        }
    }

    /**
     * 更新学生信息
     */
    @PutMapping("/{studentId}")
    public AjaxResult updateStudent(@PathVariable String studentId, @Valid @RequestBody StudentDto.UpdateReq req) {
        try {
            req.setId(studentId);
            boolean success = studentManagementService.updateStudent(req);
            return success ? success() : error("更新学生信息失败");
        } catch (Exception e) {
            log.error("更新学生信息失败", e);
            return error("更新学生信息失败: " + e.getMessage());
        }
    }

    /**
     * 删除学生
     */
    @DeleteMapping("/{studentId}")
    @PreAuthorize("@ss.hasPermi('management:students:delete')")
    public AjaxResult deleteStudent(@PathVariable String studentId) {
        try {
            boolean success = studentManagementService.deleteStudent(studentId);
            return success ? success() : error("删除学生失败");
        } catch (Exception e) {
            log.error("删除学生失败", e);
            return error("删除学生失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除学生
     */
    @DeleteMapping
    @PreAuthorize("@ss.hasPermi('management:students:delete')")
    public AjaxResult deleteStudents(@RequestBody List<String> studentIds) {
        try {
            boolean success = studentManagementService.deleteStudents(studentIds);
            return success ? success() : error("批量删除学生失败");
        } catch (Exception e) {
            log.error("批量删除学生失败", e);
            return error("批量删除学生失败: " + e.getMessage());
        }
    }

    /**
     * 获取学生统计信息
     */
    @GetMapping("/stats")
    public AjaxResult getStudentStats() {
        try {
            StudentDto.StatsResp stats = studentManagementService.getStudentStats();
            return success(stats);
        } catch (Exception e) {
            log.error("获取学生统计信息失败", e);
            return error("获取学生统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取学生课表
     */
    @GetMapping("/{studentId}/schedule")
    public AjaxResult getStudentSchedule(@PathVariable String studentId,
                                       @RequestParam(required = false) String startDate,
                                       @RequestParam(required = false) String endDate) {
        try {
            List<StudentDto.ScheduleResp> schedule = studentManagementService.getStudentSchedule(studentId, startDate, endDate);
            return success(schedule);
        } catch (Exception e) {
            log.error("获取学生课表失败", e);
            return error("获取学生课表失败: " + e.getMessage());
        }
    }

    /**
     * 获取可分配的学生列表
     */
    @GetMapping("/available")
    public AjaxResult getAvailableStudents() {
        try {
            List<StudentDto.AvailableResp> students = studentManagementService.getAvailableStudents();
            return success(students);
        } catch (Exception e) {
            log.error("获取可分配学生列表失败", e);
            return error("获取可分配学生列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取学生课程统计
     */
    @GetMapping("/{studentId}/course-stats")
    public AjaxResult getStudentCourseStats(@PathVariable String studentId) {
        try {
            StudentDto.CourseStatsResp stats = studentManagementService.getStudentCourseStats(studentId);
            return success(stats);
        } catch (Exception e) {
            log.error("获取学生课程统计失败", e);
            return error("获取学生课程统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取学生最近课程
     */
    @GetMapping("/{studentId}/recent-courses")
    public AjaxResult getStudentRecentCourses(@PathVariable String studentId,
                                            @RequestParam(defaultValue = "5") Integer limit) {
        try {
            List<StudentDto.RecentCourseResp> courses = studentManagementService.getStudentRecentCourses(studentId, limit);
            return success(courses);
        } catch (Exception e) {
            log.error("获取学生最近课程失败", e);
            return error("获取学生最近课程失败: " + e.getMessage());
        }
    }

    /**
     * 根据教师获取学生列表
     */
    @GetMapping("/by-teacher/{teacherId}")
    public AjaxResult getStudentsByTeacher(@PathVariable String teacherId) {
        try {
            List<StudentDto.BasicResp> students = studentManagementService.getStudentsByTeacher(teacherId);
            return success(students);
        } catch (Exception e) {
            log.error("根据教师获取学生列表失败", e);
            return error("根据教师获取学生列表失败: " + e.getMessage());
        }
    }

    /**
     * 分配教师给学生
     */
    @PutMapping("/{studentId}/assign-teacher/{teacherId}")
    public AjaxResult assignTeacherToStudent(@PathVariable String studentId, @PathVariable String teacherId) {
        try {
            boolean success = studentManagementService.assignTeacherToStudent(studentId, teacherId);
            return success ? success() : error("分配教师给学生失败");
        } catch (Exception e) {
            log.error("分配教师给学生失败", e);
            return error("分配教师给学生失败: " + e.getMessage());
        }
    }

    /**
     * 取消学生的教师分配
     */
    @DeleteMapping("/{studentId}/teacher")
    public AjaxResult unassignTeacherFromStudent(@PathVariable String studentId) {
        try {
            boolean success = studentManagementService.unassignTeacherFromStudent(studentId);
            return success ? success() : error("取消学生教师分配失败");
        } catch (Exception e) {
            log.error("取消学生教师分配失败", e);
            return error("取消学生教师分配失败: " + e.getMessage());
        }
    }

    /**
     * 导出学生列表
     */
    @GetMapping("/export")
    public AjaxResult exportStudents(StudentDto.GetListReq req) {
        try {
            // 实现导出功能 - 基于现有的学生查询逻辑
            // 设置大的页面大小以获取所有数据
            req.setPageNum(1);
            req.setPageSize(10000); // 设置足够大的页面大小

            IPage<StudentDto.BasicResp> page = studentManagementService.getStudentPage(req);
            List<StudentDto.BasicResp> students = page.getRecords();

            if (students.isEmpty()) {
                return error("没有数据可导出");
            }

            // 设置表头
            String[] headers = {"学生ID", "姓名", "手机号", "性别", "年级", "学校", "班级", "家长姓名", "家长手机", "状态",
                              "销售姓名", "销售组", "总课时", "已消费课时", "剩余课时", "创建时间"};

            // 生成文件名
            String fileName = "学生列表_" + System.currentTimeMillis() + ".xlsx";

            // 实际导出功能需要集成Excel工具库（如EasyExcel）
            // 这里返回导出数据的统计信息
            return success(Map.of(
                "message", "导出数据准备完成",
                "fileName", fileName,
                "totalCount", students.size(),
                "headers", headers,
                "note", "实际导出功能需要集成Excel工具库"
            ));
        } catch (Exception e) {
            log.error("导出学生列表失败", e);
            return error("导出学生列表失败: " + e.getMessage());
        }
    }
}
