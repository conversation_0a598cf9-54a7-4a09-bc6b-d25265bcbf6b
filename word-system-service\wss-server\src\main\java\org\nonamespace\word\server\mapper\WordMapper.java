package org.nonamespace.word.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.nonamespace.word.server.domain.Word;
import org.nonamespace.word.server.dto.WordPageDto;

import java.util.List;

/**
 * 单词: 核心词库，存储所有单词的基本信息及例句Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface WordMapper extends BaseMapper<Word> {


    /**
     * 根据参数条件，查询单词分页列表
     * @param req
     * @return
     */
    public List<WordPageDto.Resp> selectPageByParam(WordPageDto.Req req);

    List<Word> listUnRichBasic();

    /**
     * 获取单词音标之类未补全的单词
     * @return
     */
    List<Word> listUnRichWords();

    List<Word> listUnRichMeanings();

    List<Word> listUnRichSentences();


    /**
     * 获取单词例句未补全的单词
     * @return
     */
    List<Word> listUnRichSentencesAudio();

    List<Word> selectRandomList(@Param("count") int count);

    /**
     * 查找出sentences Practices 内容包含英文字符的单词，需要补全
     * @return
     */
    List<Word> selectSentencesPracticesContainsEn();
}
