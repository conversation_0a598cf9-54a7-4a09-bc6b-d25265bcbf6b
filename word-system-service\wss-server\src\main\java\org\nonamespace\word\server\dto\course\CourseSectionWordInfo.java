package org.nonamespace.word.server.dto.course;

import lombok.Data;
import org.nonamespace.word.server.domain.Word;

@Data
public class CourseSectionWordInfo {
    private String id;
    private String word;
    private String syllables;
    private String phoneticUk;
    private String phoneticUs;
    private String difficulty;
    private String videoUrl;
    private String audioUkUrl;
    private String audioUsUrl;
    private Integer displayOrder;
    private Word.Meanings meanings = new Word.Meanings();
    private Word.Sentences sentences = new Word.Sentences();
}