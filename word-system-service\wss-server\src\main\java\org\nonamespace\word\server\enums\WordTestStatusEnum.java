package org.nonamespace.word.server.enums;

/**
 * 单词测验状态
 */
public enum WordTestStatusEnum {
    //待开始, 进行中, 已完成, 已跳过
    WAIT_START("待开始"),
    IN_PROGRESS("进行中"),
    COMPLETED("已完成");
    private final String value;
    WordTestStatusEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static WordTestStatusEnum getByDefault(String value, WordTestStatusEnum defaultValue) {
        for (WordTestStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return defaultValue;
    }

}
