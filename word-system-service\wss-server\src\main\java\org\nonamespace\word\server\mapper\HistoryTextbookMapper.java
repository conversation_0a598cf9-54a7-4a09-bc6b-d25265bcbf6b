package org.nonamespace.word.server.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.nonamespace.word.server.domain.HistoryTextbook;

/**
 * 词历史: 存储词的修改历史版本Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface HistoryTextbookMapper extends BaseMapper<HistoryTextbook>
{
    /**
     * 查询词历史: 存储词的修改历史版本
     * 
     * @param id 词历史: 存储词的修改历史版本主键
     * @return 词历史: 存储词的修改历史版本
     */
    public HistoryTextbook selectHistoryTextbookById(String id);

    /**
     * 查询词历史: 存储词的修改历史版本列表
     * 
     * @param historyTextbook 词历史: 存储词的修改历史版本
     * @return 词历史: 存储词的修改历史版本集合
     */
    public List<HistoryTextbook> selectHistoryTextbookList(HistoryTextbook historyTextbook);

    /**
     * 新增词历史: 存储词的修改历史版本
     * 
     * @param historyTextbook 词历史: 存储词的修改历史版本
     * @return 结果
     */
    public int insertHistoryTextbook(HistoryTextbook historyTextbook);

    /**
     * 修改词历史: 存储词的修改历史版本
     * 
     * @param historyTextbook 词历史: 存储词的修改历史版本
     * @return 结果
     */
    public int updateHistoryTextbook(HistoryTextbook historyTextbook);

    /**
     * 删除词历史: 存储词的修改历史版本
     * 
     * @param id 词历史: 存储词的修改历史版本主键
     * @return 结果
     */
    public int deleteHistoryTextbookById(String id);

    /**
     * 批量删除词历史: 存储词的修改历史版本
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHistoryTextbookByIds(String[] ids);
}
