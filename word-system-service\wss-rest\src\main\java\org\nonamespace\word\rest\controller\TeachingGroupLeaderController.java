package org.nonamespace.word.rest.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.dto.management.teachingleader.TeachingGroupLeaderDto;
import org.nonamespace.word.server.facade.ITeachingGroupLeaderFacade;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 教学组长审核Controller
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@RestController
@RequestMapping("/teaching-group-leader")
@RequiredArgsConstructor
@Validated
public class TeachingGroupLeaderController extends org.nonamespace.word.rest.controller.BaseController {

    private final ITeachingGroupLeaderFacade teachingGroupLeaderFacade;

    /**
     * 获取教学组长待审核的预约课申请列表
     */
//    @PreAuthorize("@ss.hasAnyRoles('admin,hr,teaching_group_leader,sales,sales_director,sales_group_leader')")
    @PreAuthorize("@ss.hasPermi('course:booking:query')")
    @GetMapping("/pending-applications")
    @Log(title = "试听课申请查询", businessType = BusinessType.OTHER)
    public AjaxResult getPendingApplications(TeachingGroupLeaderDto.GetPendingApplicationsReq req) {
        return handleOperation("获取待审核预约课申请列表", () -> {
            validatePagination(req.getPageNum(), req.getPageSize());
            return teachingGroupLeaderFacade.getPendingApplicationsForLeader(req);
        });
    }

    /**
     * 教学组长审核预约课申请
     */
    @PreAuthorize("@ss.hasPermi('course:booking:review')")
    @Log(title = "试听课申请审核", businessType = BusinessType.UPDATE)
    @PostMapping("/review-application")
    public AjaxResult reviewApplication(@Valid @RequestBody TeachingGroupLeaderDto.ReviewApplicationReq req) {
        try {
            boolean success = teachingGroupLeaderFacade.reviewCourseBookingApplication(req);
            return success ? AjaxResult.success("审核成功") : AjaxResult.error("审核失败");
        } catch (Exception e) {
            log.error("教学组长审核预约课申请失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 批量审核预约课申请
     */
    @PreAuthorize("@ss.hasPermi('course:booking:review')")
    @Log(title = "试听课申请查询批量审核", businessType = BusinessType.UPDATE)
    @PostMapping("/batch-review")
    public AjaxResult batchReview(@Valid @RequestBody TeachingGroupLeaderDto.BatchReviewReq req) {
        try {
            TeachingGroupLeaderDto.BatchReviewResp result = teachingGroupLeaderFacade.batchReviewApplications(req);
            return AjaxResult.success("批量审核完成", result);
        } catch (Exception e) {
            log.error("批量审核预约课申请失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取教学组长审核统计信息
     */
    // @PreAuthorize("@ss.hasAnyRoles('admin,hr,teaching_group_leader')")
    @PreAuthorize("@ss.hasPermi('course:booking:stats')")
    @GetMapping("/review-stats")
    public AjaxResult getReviewStats() {
        try {
            TeachingGroupLeaderDto.ReviewStatsResp result = teachingGroupLeaderFacade.getReviewStats();
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("获取审核统计信息失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取教学组下的教师列表
     */
    // @PreAuthorize("@ss.hasRole('teaching_group_leader')")
    @PreAuthorize("@ss.hasPermi('course:booking:review')")
    @GetMapping("/group-teachers")
    public AjaxResult getGroupTeachers(@RequestParam(required = false) String groupId) {
        try {
            List<TeachingGroupLeaderDto.GroupTeacherResp> result = teachingGroupLeaderFacade.getGroupTeachers(groupId);
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("获取教学组教师列表失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取指定申请中本组内的候选教师列表（用于通过申请页面）
     */
    @PreAuthorize("@ss.hasPermi('course:booking:review')")
    @GetMapping("/applied-teachers")
    public AjaxResult getAppliedTeachers(@RequestParam String applicationId, @RequestParam(required = false) String groupId) {
        try {
            List<TeachingGroupLeaderDto.GroupTeacherResp> result = teachingGroupLeaderFacade.getAppliedTeachers(applicationId, groupId);
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("获取指定申请中本组内候选教师列表失败: applicationId={}", applicationId, e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取教师在指定申请试听课时间的可选时间段
     */
    @PreAuthorize("@ss.hasPermi('course:booking:review')")
    @GetMapping("/teacher-available-slots")
    public AjaxResult getTeacherAvailableSlots(@RequestParam String teacherId, @RequestParam String applicationId) {
        try {
            List<TeachingGroupLeaderDto.TrialTimeSlotResp> result = teachingGroupLeaderFacade.getTeacherAvailableSlots(teacherId, applicationId);
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("获取教师可选时间段失败: teacherId={}, applicationId={}", teacherId, applicationId, e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 分配教师和时间段
     */
    @PreAuthorize("@ss.hasPermi('course:booking:review')")
    @Log(title = "分配教师", businessType = BusinessType.UPDATE)
    @PostMapping("/assign-teacher")
    public AjaxResult assignTeacher(@Valid @RequestBody TeachingGroupLeaderDto.AssignTeacherReq req) {
        try {
            boolean success = teachingGroupLeaderFacade.assignTeacherToApplication(req);
            return success ? AjaxResult.success("分配成功") : AjaxResult.error("分配失败");
        } catch (Exception e) {
            log.error("分配教师失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取审核历史记录
     */
    @PreAuthorize("@ss.hasPermi('course:booking:query')")
    @GetMapping("/review-history")
    public AjaxResult getReviewHistory(TeachingGroupLeaderDto.GetReviewHistoryReq req) {
        try {
            IPage<TeachingGroupLeaderDto.ReviewHistoryResp> page = teachingGroupLeaderFacade.getReviewHistory(req);
            return AjaxResult.success(page);
        } catch (Exception e) {
            log.error("获取审核历史记录失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

}
