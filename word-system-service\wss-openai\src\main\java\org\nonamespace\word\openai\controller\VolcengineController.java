package org.nonamespace.word.openai.controller;

import com.ruoyi.common.annotation.Anonymous;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.openai.model.VolcengineTtsResponse;
import org.nonamespace.word.openai.service.IVolcengineService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Base64;

/**
 * 火山模型
 *
 * <AUTHOR>
 * @date 2025/5/20 9:54
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/ai/word/volcengine")
@Anonymous
public class VolcengineController {

    private final IVolcengineService volcengineService;

    @Value("${word.audio.save-path:audio}")
    private String audioSavePath;

    /**
     * 语音合成
     * @param word
     * @param type
     * @return
     * @throws IOException
     */
    @PostMapping("/enrich")
    public ResponseEntity<VolcengineTtsResponse> enrich(String word, int type) throws IOException {
        log.info("Received word enrichment request: {}", word);
        VolcengineTtsResponse enrich = volcengineService.enrich(word, type);

        // 确保目录存在
        File directory = new File(audioSavePath, word);
        if (!directory.exists()) {
            directory.mkdirs();
        }

        // 构建文件路径
        String fileName = String.format("%s_%d.mp3", word, type);
        String filePath = new File(directory, fileName).getPath();

        // 保存文件
        byte[] audioUsBytes = Base64.getMimeDecoder().decode(enrich.getData());
        Files.write(Paths.get(filePath), audioUsBytes);
        return ResponseEntity.ok(enrich);
    }
}
