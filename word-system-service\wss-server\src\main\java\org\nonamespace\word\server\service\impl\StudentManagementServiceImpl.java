package org.nonamespace.word.server.service.impl;

import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.misc.WssContext;
import org.nonamespace.word.server.domain.TeacherStudentRelation;
import org.nonamespace.word.server.domain.TeachingGroup;
import org.nonamespace.word.server.domain.TeachingGroupMember;
import org.nonamespace.word.server.domain.UserStudentExt;
import org.nonamespace.word.server.dto.management.student.StudentDto;
import org.nonamespace.word.server.mapper.StudentManagementMapper;
import org.nonamespace.word.server.mapper.TeacherStudentRelationMapper;
import org.nonamespace.word.server.service.*;
import org.nonamespace.word.server.util.SystemDataQueryUtil;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 学生管理Service实现类
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StudentManagementServiceImpl implements IStudentManagementService {

    private final StudentManagementMapper studentManagementMapper;
    private final TeacherStudentRelationMapper teacherStudentRelationMapper;
    private final ITeacherStudentRelationService teacherStudentRelationService;
    private final ISysUserService sysUserService;
    private final IUserService userService;
    private final SystemDataQueryUtil systemDataQueryUtil;
    private final ITeachingGroupService teachingGroupService;
    private final TeachingGroupMemberServiceImpl teachingGroupMemberServiceImpl;
    private final UserStudentExtService userStudentExtService;

    @Override
    public IPage<StudentDto.BasicResp> getStudentPage(StudentDto.GetListReq req) {
        Set<String> teacherIds = null;

        if (!systemDataQueryUtil.hasAllStudentRoles()) {
            teacherIds = new HashSet<>();

            LambdaQueryChainWrapper<TeachingGroup> lqw = teachingGroupService.lambdaQuery();
            if (systemDataQueryUtil.isAdminOrHr()) {
                // 管理员查询所有教学组
                // 不需要额外条件
            } else if (systemDataQueryUtil.isTeacherGroupManager()) {
                lqw.and(q -> q.eq(TeachingGroup::getAdminId, SecurityUtils.getUserId().toString())
                        .or()
                        .eq(TeachingGroup::getLeaderId, SecurityUtils.getUserId().toString()));
            } else if (systemDataQueryUtil.isTeacher()) {
                teacherIds.add(SecurityUtils.getUserId().toString());
                lqw.isNull(TeachingGroup::getId);
            } else {
                // 其他角色不允许查询
                return new Page<>();
            }

            List<TeachingGroup> groups = lqw.list();
            if (!groups.isEmpty()) {
                List<TeachingGroupMember> tgms = teachingGroupMemberServiceImpl.lambdaQuery()
                        .in(TeachingGroupMember::getGroupId, groups.stream().map(TeachingGroup::getId).collect(Collectors.toSet()))
                        .list();

                if (!tgms.isEmpty()) {
                    teacherIds.addAll(tgms.stream().map(x -> x.getTeacherId()).collect(Collectors.toSet()));
                }
            }

            if (teacherIds.isEmpty()) {
                // 如果没有教师ID，返回空结果
                return new Page<>();
            }
        }

        // 手动分页，避免 MyBatis-Plus 自动生成 COUNT SQL 的解析问题
        return getStudentPageManually(req, teacherIds);
    }

    /**
     * 手动分页查询学生列表
     *
     * @param req 查询条件
     * @param teacherIds 教师ID集合
     * @return 分页结果
     */
    private IPage<StudentDto.BasicResp> getStudentPageManually(StudentDto.GetListReq req, Set<String> teacherIds) {
        // 1. 先查询总数
        long total = studentManagementMapper.selectStudentPageCount(req, teacherIds);

        // 2. 创建分页对象
        Page<StudentDto.BasicResp> page = new Page<>(req.getPageNum(), req.getPageSize());
        page.setTotal(total);

        if (total == 0) {
            return page;
        }

        // 3. 计算偏移量
        int offset = (req.getPageNum() - 1) * req.getPageSize();

        // 4. 查询数据 - 使用带分页参数的查询，避免触发MyBatis-Plus分页插件
        List<StudentDto.BasicResp> records = studentManagementMapper.selectStudentPageList(req, teacherIds, offset);

        // 5. 设置分页结果
        page.setRecords(records);

        return page;
    }

    @Override
    public StudentDto.DetailResp getStudentDetail(String studentId) {
        try {
            StudentDto.DetailResp resp = studentManagementMapper.selectStudentDetail(studentId);
            if(systemDataQueryUtil.isTeacher()){
                resp.setPhone(DesensitizedUtil.mobilePhone(resp.getPhone()));
                if(StrUtil.isNotEmpty(resp.getParentPhone())){
                    resp.setParentPhone(DesensitizedUtil.mobilePhone(resp.getParentPhone()));
                }
            }
            return resp;
        } catch (Exception e) {
            log.error("查询学生详细信息失败", e);
            throw new RuntimeException("查询学生详细信息失败: " + e.getMessage());
        }
    }

    @Override
    public String createStudent(StudentDto.CreateReq req) {
        try {


            if (userService.isPhoneNumberExists(req.getPhone().trim())) {
                throw new RuntimeException("手机号已被使用");
            }

            // 1. 创建系统用户账号
            SysUser sysUser = new SysUser();
            sysUser.setUserName(req.getPhone()); // 使用手机号作为用户名
            sysUser.setNickName(req.getName()); // 使用姓名作为昵称
            sysUser.setPassword(SecurityUtils.encryptPassword(userService.getDefaultPassword())); // 默认密码
            sysUser.setPhonenumber(req.getPhone());
            sysUser.setSex(req.getGender()); // 直接使用前端传入的值：0、1、2
            sysUser.setStatus("0"); // 正常状态
            sysUser.setDeptId(systemDataQueryUtil.getStudentDept().getDeptId());

            // 获取学生角色
            SysRole studentRole = systemDataQueryUtil.getStudentRole();
            Long[] roleIds = {studentRole.getRoleId()};
            sysUser.setRoleIds(roleIds);

            userService.save(sysUser);

            Long userId = sysUser.getUserId();
            if(userId == null){
                userId = sysUserService.selectUserByPhone(req.getPhone()).getUserId();
            }
            log.info("创建学生用户账号成功: userId={}, userName={}", userId, req.getPhone());

            // 2. 创建学生扩展信息记录
            UserStudentExt student = new UserStudentExt();
            student.setStudentId(String.valueOf(userId)); // 使用用户ID作为学生ID
            student.setName(req.getName());
            student.setPhone(req.getPhone());
            student.setGender(req.getGender());
            student.setGrade(req.getGrade());
            student.setSchool(req.getSchool());
            student.setClassName(req.getClassName());
            student.setParentName(req.getParentName());
            student.setParentPhone(req.getParentPhone());
            student.setLearningGoals(req.getLearningGoals());
            student.setRemarks(req.getRemarks());
            student.setStatus(req.getStatus() != null ? req.getStatus() : "active");

            // 使用 MyBatis-Plus 的 insert 方法，自动处理 id 字段
            int result = studentManagementMapper.insert(student);
            if (result <= 0) {
                throw new RuntimeException("创建学生扩展信息失败");
            }

            log.info("创建学生成功: userId={}, name={}", userId, req.getName());
            return userId.toString();
        } catch (Exception e) {
            log.error("创建学生失败", e);
            throw new RuntimeException("创建学生失败: " + e.getMessage());
        }
    }

    @Override
    public boolean updateStudent(StudentDto.UpdateReq req) {
        try {
            String userId = req.getId();

            // 检查手机号是否被其他用户使用（排除当前用户）
            if (userService.isPhoneNumberUsedByOtherUser(req.getPhone(), Long.valueOf(userId))) {
                throw new RuntimeException("手机号已被其他用户使用");
            }

            // 1. 更新系统用户信息
            userService.lambdaUpdate()
                    .set(SysUser::getUserName, req.getPhone())
                    .set(SysUser::getNickName, req.getName())
                    .set(SysUser::getPhonenumber, req.getPhone())
                    .set(SysUser::getSex, req.getGender())
                    .eq(SysUser::getUserId, Long.valueOf(userId))
                    .update();

            // 2. 更新学生扩展信息

            // 先查询现有记录
            UserStudentExt existingStudent = userStudentExtService.lambdaQuery()
                    .eq(UserStudentExt::getStudentId, userId)
                    .eq(UserStudentExt::getDeleted, false)
                    .one();

            int result;
            if (existingStudent != null) {
                // 更新现有记录
                existingStudent.setName(req.getName());
                existingStudent.setPhone(req.getPhone());
                existingStudent.setGender(req.getGender());
                existingStudent.setGrade(req.getGrade());
                existingStudent.setSchool(req.getSchool());
                existingStudent.setClassName(req.getClassName());
                existingStudent.setParentName(req.getParentName());
                existingStudent.setParentPhone(req.getParentPhone());
                // 课时数据现在从课时管理表获取，不再更新user_student_ext表中的课时字段
                existingStudent.setLearningGoals(req.getLearningGoals());
                existingStudent.setRemarks(req.getRemarks());
                existingStudent.setStatus(req.getStatus());

                result = studentManagementMapper.updateById(existingStudent);
            } else {
                // 创建新记录
                UserStudentExt student = new UserStudentExt();
                student.setStudentId(String.valueOf(userId));
                student.setName(req.getName());
                student.setPhone(req.getPhone());
                student.setGender(req.getGender());
                student.setGrade(req.getGrade());
                student.setSchool(req.getSchool());
                student.setClassName(req.getClassName());
                student.setParentName(req.getParentName());
                student.setParentPhone(req.getParentPhone());
                student.setLearningGoals(req.getLearningGoals());
                student.setRemarks(req.getRemarks());
                student.setStatus(req.getStatus());

                result = studentManagementMapper.insert(student);
            }

            log.info("更新学生信息: studentId={}, name={}, result={}", req.getId(), req.getName(), result);
            return result > 0;
        } catch (Exception e) {
            log.error("更新学生信息失败", e);
            throw new RuntimeException("更新学生信息失败: " + e.getMessage());
        }
    }

    @Override
    public boolean deleteStudent(String studentId) {
        try {
            // 1. 软删除学生记录
            userStudentExtService.lambdaUpdate()
                    .set(UserStudentExt::getDeleted, true)
                    .set(UserStudentExt::getStatus, "inactive")
                    .eq(UserStudentExt::getStudentId, studentId)
                    .update();

            sysUserService.deleteUserById(Long.valueOf(studentId));

            // 2. 清理师生关系
            teacherStudentRelationService.lambdaUpdate()
                    .set(TeacherStudentRelation::getDeleted, true)
                    .set(TeacherStudentRelation::getStatus, "inactive")
                    .eq(TeacherStudentRelation::getStudentId, studentId)
                    .update();

            log.info("删除学生: studentId={}", studentId);
            return true;
        } catch (Exception e) {
            log.error("删除学生失败", e);
            throw new RuntimeException("删除学生失败: " + e.getMessage());
        }
    }

    @Override
    public boolean deleteStudents(List<String> studentIds) {
        try {
            // 批量软删除学生记录
            studentManagementMapper.deleteStudents(studentIds);

            userStudentExtService.lambdaUpdate()
                    .set(UserStudentExt::getDeleted, true)
                    .set(UserStudentExt::getStatus, "inactive")
                    .in(UserStudentExt::getStudentId, studentIds)
                    .update();

            sysUserService.deleteUserByIds(studentIds.stream().map(Long::valueOf).toList().toArray(new Long[]{}));

            // 批量清理师生关系

            teacherStudentRelationService.lambdaUpdate()
                    .set(TeacherStudentRelation::getDeleted, true)
                    .set(TeacherStudentRelation::getStatus, "inactive")
                    .in(TeacherStudentRelation::getStudentId, studentIds)
                    .update();


            return true;
        } catch (Exception e) {
            log.error("批量删除学生失败", e);
            throw new RuntimeException("批量删除学生失败: " + e.getMessage());
        }
    }

    @Override
    public StudentDto.StatsResp getStudentStats() {
        try {
            return studentManagementMapper.selectStudentStats();
        } catch (Exception e) {
            log.error("获取学生统计信息失败", e);
            throw new RuntimeException("获取学生统计信息失败: " + e.getMessage());
        }
    }

    @Override
    public List<StudentDto.ScheduleResp> getStudentSchedule(String studentId, String startDate, String endDate) {
        try {
            return studentManagementMapper.selectStudentSchedule(studentId, startDate, endDate);
        } catch (Exception e) {
            log.error("获取学生课表失败", e);
            throw new RuntimeException("获取学生课表失败: " + e.getMessage());
        }
    }

    @Override
    public List<StudentDto.AvailableResp> getAvailableStudents() {
        try {
            return studentManagementMapper.selectAvailableStudents();
        } catch (Exception e) {
            log.error("获取可分配学生列表失败", e);
            throw new RuntimeException("获取可分配学生列表失败: " + e.getMessage());
        }
    }

    @Override
    public StudentDto.CourseStatsResp getStudentCourseStats(String studentId) {
        try {
            return studentManagementMapper.selectStudentCourseStats(studentId);
        } catch (Exception e) {
            log.error("获取学生课程统计失败", e);
            throw new RuntimeException("获取学生课程统计失败: " + e.getMessage());
        }
    }

    @Override
    public List<StudentDto.RecentCourseResp> getStudentRecentCourses(String studentId, Integer limit) {
        try {
            return studentManagementMapper.selectStudentRecentCourses(studentId, limit);
        } catch (Exception e) {
            log.error("获取学生最近课程失败", e);
            throw new RuntimeException("获取学生最近课程失败: " + e.getMessage());
        }
    }

    @Override
    public List<StudentDto.BasicResp> getStudentsByTeacher(String teacherId) {
        try {
            return studentManagementMapper.selectStudentsByTeacher(teacherId);
        } catch (Exception e) {
            log.error("根据教师获取学生列表失败", e);
            throw new RuntimeException("根据教师获取学生列表失败: " + e.getMessage());
        }
    }

    @Override
    public boolean assignTeacherToStudent(String studentId, String teacherId) {
        try {

            String subject = "英语";
            String specification = "单词课";

            boolean exists = teacherStudentRelationService.lambdaQuery().eq(TeacherStudentRelation::getTeacherId, teacherId)
                    .eq(TeacherStudentRelation::getStudentId, studentId)
                    .eq(TeacherStudentRelation::getSubject, subject)
                    .eq(TeacherStudentRelation::getSpecification, specification)
                    .eq(TeacherStudentRelation::getRelationType, "teaching")
                    .eq(TeacherStudentRelation::getStatus, "active")
                    .exists();

            if(exists){
                log.info("教师已分配给学生，无需在处理: studentId={}, teacherId={}", studentId, teacherId);
                return true;
            }

            TeacherStudentRelation tsr = new TeacherStudentRelation();
            tsr.setRelationType("teaching");
            tsr.setStatus("active");
            tsr.setSubject(subject);
            tsr.setSpecification(specification);
            tsr.setId(IdUtil.getSnowflakeNextIdStr());
            tsr.setTeacherId(teacherId);
            tsr.setStudentId(studentId);
            tsr.setStartDate(WssContext.now());

            boolean success = teacherStudentRelationService.save(tsr);


            log.info("分配教师给学生: studentId={}, teacherId={}, result={}", studentId, teacherId, success);
            return success;
        } catch (Exception e) {
            log.error("分配教师给学生失败", e);
            throw new RuntimeException("分配教师给学生失败: " + e.getMessage());
        }
    }

    @Override
    public boolean unassignTeacherFromStudent(String studentId) {
        try {
            int result = teacherStudentRelationMapper.removeAllTeachersByStudent(studentId);

            log.info("取消学生教师分配: studentId={}, result={}", studentId, result);
            return result > 0;
        } catch (Exception e) {
            log.error("取消学生教师分配失败", e);
            throw new RuntimeException("取消学生教师分配失败: " + e.getMessage());
        }
    }


}
