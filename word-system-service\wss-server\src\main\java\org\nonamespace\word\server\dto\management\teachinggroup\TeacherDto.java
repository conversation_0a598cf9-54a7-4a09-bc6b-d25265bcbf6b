package org.nonamespace.word.server.dto.management.teachinggroup;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 教师相关DTO
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public class TeacherDto {

    /**
     * 获取教师列表请求参数
     */
    @Data
    public static class GetListReq {
        private Integer pageNum = 1;
        private Integer pageSize = 20;
        private String name;
        private String phone;
        private String groupId;
        private String status;
        private String teacherId;
    }

    /**
     * 教师基础信息响应
     */
    @Data
    public static class BasicResp {
        private String id;
        private String name;
        private String nickname;
        private String gender;
        private String phone;
        private String email;
        private String avatar;
        private String groupId;
        private String groupName;
        private String status;
        private Date createTime;
        private Date updateTime;

        // 统计信息
        private Integer studentCount;      // 学生数
        private Integer totalHours;       // 总课时
        private Integer completedHours;   // 已完成课时
        private Integer remainingHours;   // 剩余课时
    }

    /**
     * 教师详细信息响应
     */
    @Data
    public static class DetailResp {
        private String id;
        private String name;
        private String nickname;
        private String gender;
        private Integer age;
        private String phone;
        private String email;
        private String avatar;
        private String groupId;
        private String groupName;
        private String currentLocation;
        private String employmentType;
        private String currentStatus;

        // 教育背景
        private String education;
        private String graduateSchool;
        private String major;
        private String universityType;
        private Boolean isNormalUniversity;
        private Boolean studyAbroad;
        private String studyAbroadCountry;

        // 教学资质
        private String teachingCertificateLevel;
        private List<String> subjects;
        private List<String> trainingSubjects;
        private String englishQualification;
        private String mandarinQualification;
        private String communicationAbility;

        // 教学经历
        private String teachingExperience;
        private List<String> taughtCourses;
        private Integer teachingYears;
        private String awards;

        // 教学风格和适配
        private List<String> teachingStyle;
        private String englishPronunciation;
        private List<String> suitableGrades;
        private List<String> suitableLevels;
        private String suitablePersonality;

        private String other;
        private String introduction;

        // 暑期课上课时间
        private String summerScheduleType;

        // 新增字段
        private LocalDate formalEntryDate;
        private List<String> qualificationCertificates;
        private List<String> demoVideos;

        private String status;
        private Date createTime;
        private Date updateTime;

        // 新增统计字段
        private Double trialCoursePassRate;    // 试听课通过率（3个月内）
        private Integer totalTeachingHours;    // 系统已上课时（分钟）
        private Integer currentStudentCount;   // 老师现有学生数
    }

    /**
     * 教师时间表响应
     */
    @Data
    public static class TimeSlotResp {
        private String id;
        private String teacherId;
        private Integer weekday;
        private String startTime; // 格式: "09:15"
        private String endTime;   // 格式: "10:55"
        private String status;    // available: 可上课, scheduled: 已排课
        private String remark;
    }

    @Data
    public static class AvailableTimeSlotsDto {
        private Integer weekday;
        private List<TimeSlot> timeSlots;

        @Data
        public static class TimeSlot {
            private String startTime; // 格式: "09:15"
            private String endTime;   // 格式: "10:55"
        }
    }

    /**
     * 更新教师时间表请求
     */
    @Data
    public static class UpdateTimeSlotsReq {
//        @NotBlank(message = "教师ID不能为空")
        private String teacherId;
        
        private List<TimeSlotResp> timeSlots;
    }

    /**
     * 教师带教信息响应
     */
    @Data
    public static class TeachingInfoResp {
        private String teacherId;
        private String teacherName;
        private Integer currentStudents;
        private Integer unconsumedHours;
        private Integer consumedHours;
        private Integer totalHours;
    }

    /**
     * 教师时间表更新检查响应
     */
    @Data
    public static class TimeSlotUpdateCheckResp {
        private String teacherId;
        private Date lastUpdateTime;
        private Integer daysSinceLastUpdate;
        private Boolean needsUpdate;
        private String message;
    }

    /**
     * 可分配教师响应
     */
    @Data
    public static class AvailableResp {
        private String id;
        private String name;
        private String phone;
        private String email;
        private String status;
    }

    /**
     * 用户角色响应（用于选择组长、教务）
     */
    @Data
    public static class UserRoleResp {
        private String id;
        private String name;
        private String phone;
        private String email;
        private String avatar;
    }

    /**
     * 教师选择选项响应（用于下拉选择）
     */
    @Data
    public static class SelectOption {
        private String id;
        private String name;
    }

    /**
     * 更新教师信息请求
     */
    @Data
    public static class UpdateTeacherReq {
        @NotBlank(message = "教师ID不能为空")
        private String teacherId;

        @NotBlank(message = "姓名不能为空")
        private String name;

        private String nickname;
        private String gender;
        private Integer age;

        @NotBlank(message = "手机号码不能为空")
        private String phone;

        private String email;
        private String currentLocation;
        private String employmentType;
        private String currentStatus;

        // 教育背景
        private String education;
        private String graduateSchool;
        private String major;
        private String universityType;
        private Boolean isNormalUniversity;
        private Boolean studyAbroad;
        private String studyAbroadCountry;

        // 教学资质
        private String teachingCertificateLevel;
        private List<String> subjects;
        private List<String> trainingSubjects;
        private String englishQualification;
        private String mandarinQualification;
        private String communicationAbility;

        // 教学经历
        private String teachingExperience;
        private List<String> taughtCourses;
        private Integer teachingYears;
        private String awards;

        // 教学风格和适配
        private List<String> teachingStyle;
        private String englishPronunciation;
        private List<String> suitableGrades;
        private List<String> suitableLevels;
        private String suitablePersonality;

        // 暑期课上课时间
        private String summerScheduleType;

        // 新增字段
        private LocalDate formalEntryDate;
        private List<String> qualificationCertificates;
        private List<String> demoVideos;

        private String introduction;
        private String other;
    }

    /**
     * 教师导入数据
     */
    @Data
    public static class ImportTeacherData {
        @NotBlank(message = "姓名不能为空")
        private String name;

        @NotBlank(message = "昵称不能为空")
        private String nickname;

        @NotBlank(message = "性别不能为空")
        private String gender;

        @NotBlank(message = "电话不能为空")
        private String phone;
    }

    /**
     * 教师导入结果
     */
    @Data
    public static class ImportResult {
        private int totalCount;
        private int successCount;
        private int failureCount;
        private List<String> errorMessages;
        private List<ImportTeacherData> failedRecords;
    }

    /**
     * 教师导出数据
     */
    @Data
    public static class ExportData {
        private String name;
        private String gender;
        private String phone;
        private String education;
        private String major;
        private String universityType;
        private String teachingCertificateLevel;
        private String englishQualification;
    }

    /**
     * 创建教师请求
     */
    @Data
    public static class CreateTeacherReq {
        @NotBlank(message = "姓名不能为空")
        private String name;

        @NotBlank(message = "昵称不能为空")
        private String nickname;

        @NotBlank(message = "手机号码不能为空")
        private String phone;

        @NotBlank(message = "性别不能为空")
        private String gender;

        private Integer age;
        private String email;
        private String groupId;
        private String currentLocation;
        private String employmentType;
        private String currentStatus;

        // 教育背景
        private String education;
        private String graduateSchool;
        private String major;
        private String universityType;
        private Boolean isNormalUniversity;
        private Boolean studyAbroad;
        private String studyAbroadCountry;

        // 教学资质
        private String teachingCertificateLevel;
        private List<String> subjects;
        private List<String> trainingSubjects;
        private String englishQualification;
        private String mandarinQualification;
        private String communicationAbility;

        // 教学经历
        private String teachingExperience;
        private List<String> taughtCourses;
        private Integer teachingYears;
        private String awards;

        // 教学风格和适配
        private List<String> teachingStyle;
        private String englishPronunciation;
        private List<String> suitableGrades;
        private List<String> suitableLevels;
        private String suitablePersonality;

        // 暑期课上课时间
        private String summerScheduleType;

        // 新增字段
        private LocalDate formalEntryDate;
        private List<String> qualificationCertificates;
        private List<String> demoVideos;

        private String introduction;
        private String password; // 默认密码
    }

    /**
     * 更新教师暑期课上课时间请求
     */
    @Data
    public static class UpdateSummerScheduleReq {
        private String teacherId;

        @NotBlank(message = "暑期课上课时间不能为空")
        private String summerScheduleType; // full: 全满档, golden: 黄金档, other: 其他档
    }

    /**
     * 教师课表响应
     */
    @Data
    public static class ScheduleResp {
        private String id;
        private String teacherId;
        private String studentId;
        private String studentName;
        private String subject;
        private String courseType;
        private Date startTime;
        private Date endTime;
        private String status;
        private String remarks;
    }

    /**
     * 分配学生给教师请求
     */
    @Data
    public static class AssignStudentsReq {
        @NotBlank(message = "教师ID不能为空")
        private String teacherId;

        private List<StudentAssignment> studentAssignments;
    }

    /**
     * 学生分配信息
     */
    @Data
    public static class StudentAssignment {
        @NotBlank(message = "学生ID不能为空")
        private String studentId;

        @NotBlank(message = "学科不能为空")
        private String subject;

        @NotBlank(message = "课型不能为空")
        private String courseType;
    }

    /**
     * 教师的学生响应
     */
    @Data
    public static class StudentResp {
        private String id;
        private String name;
        private String phone;
        private String grade;
        private String school;
        private String subject;
        private String courseType;
        private Integer totalHours;
        private Integer consumedHours;
        private Integer remainingHours;
        private String status;
        private Date createTime;
    }
}
