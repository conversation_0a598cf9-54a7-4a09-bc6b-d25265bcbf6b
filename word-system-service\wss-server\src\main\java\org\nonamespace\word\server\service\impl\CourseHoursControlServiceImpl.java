package org.nonamespace.word.server.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.service.ICourseHoursControlService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 课时控制服务实现类
 * 
 * 使用Redis存储课时检查和课消的开关配置
 * 
 * <AUTHOR>
 * @since 2025-06-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class
CourseHoursControlServiceImpl implements ICourseHoursControlService {

    private final StringRedisTemplate stringRedisTemplate;

    // Redis键名常量
    private static final String REDIS_KEY_HOURS_BALANCE_CHECK = "course:hours:balance_check_enabled";
    private static final String REDIS_KEY_HOURS_CONSUMPTION = "course:hours:consumption_enabled";
    private static final String REDIS_KEY_LAST_UPDATE_TIME = "course:hours:last_update_time";
    private static final String REDIS_KEY_LAST_UPDATE_BY = "course:hours:last_update_by";

    // 默认值：不检查、不课消
    private static final boolean DEFAULT_BALANCE_CHECK_ENABLED = false;
    private static final boolean DEFAULT_CONSUMPTION_ENABLED = false;

    @Override
    public boolean isHoursBalanceCheckEnabled() {
        try {
            String value = stringRedisTemplate.opsForValue().get(REDIS_KEY_HOURS_BALANCE_CHECK);
            boolean enabled = value != null ? Boolean.parseBoolean(value) : DEFAULT_BALANCE_CHECK_ENABLED;
            log.debug("课时余额检查开关状态: {}", enabled);
            return enabled;
        } catch (Exception e) {
            log.warn("获取课时余额检查开关失败，使用默认值: {}", DEFAULT_BALANCE_CHECK_ENABLED, e);
            return DEFAULT_BALANCE_CHECK_ENABLED;
        }
    }

    @Override
    public boolean isCourseHoursConsumptionEnabled() {
        try {
            String value = stringRedisTemplate.opsForValue().get(REDIS_KEY_HOURS_CONSUMPTION);
            boolean enabled = value != null ? Boolean.parseBoolean(value) : DEFAULT_CONSUMPTION_ENABLED;
            log.debug("课消功能开关状态: {}", enabled);
            return enabled;
        } catch (Exception e) {
            log.warn("获取课消功能开关失败，使用默认值: {}", DEFAULT_CONSUMPTION_ENABLED, e);
            return DEFAULT_CONSUMPTION_ENABLED;
        }
    }

    @Override
    public void setHoursBalanceCheckEnabled(boolean enabled) {
        try {
            stringRedisTemplate.opsForValue().set(REDIS_KEY_HOURS_BALANCE_CHECK, String.valueOf(enabled));
            updateLastModifyInfo("setHoursBalanceCheckEnabled");
            log.info("设置课时余额检查开关: {}", enabled);
        } catch (Exception e) {
            log.error("设置课时余额检查开关失败: enabled={}", enabled, e);
            throw new RuntimeException("设置课时余额检查开关失败", e);
        }
    }

    @Override
    public void setCourseHoursConsumptionEnabled(boolean enabled) {
        try {
            stringRedisTemplate.opsForValue().set(REDIS_KEY_HOURS_CONSUMPTION, String.valueOf(enabled));
            updateLastModifyInfo("setCourseHoursConsumptionEnabled");
            log.info("设置课消功能开关: {}", enabled);
        } catch (Exception e) {
            log.error("设置课消功能开关失败: enabled={}", enabled, e);
            throw new RuntimeException("设置课消功能开关失败", e);
        }
    }

    @Override
    public CourseHoursControlConfig getConfig() {
        try {
            CourseHoursControlConfig config = new CourseHoursControlConfig();
            config.setHoursBalanceCheckEnabled(isHoursBalanceCheckEnabled());
            config.setCourseHoursConsumptionEnabled(isCourseHoursConsumptionEnabled());
            
            String lastUpdateTime = stringRedisTemplate.opsForValue().get(REDIS_KEY_LAST_UPDATE_TIME);
            String lastUpdateBy = stringRedisTemplate.opsForValue().get(REDIS_KEY_LAST_UPDATE_BY);
            
            config.setLastUpdateTime(lastUpdateTime);
            config.setLastUpdateBy(lastUpdateBy);
            
            log.debug("获取课时控制配置: {}", config);
            return config;
        } catch (Exception e) {
            log.warn("获取课时控制配置失败，返回默认配置", e);
            return new CourseHoursControlConfig(DEFAULT_BALANCE_CHECK_ENABLED, DEFAULT_CONSUMPTION_ENABLED);
        }
    }

    @Override
    public void resetToDefault() {
        try {
            stringRedisTemplate.opsForValue().set(REDIS_KEY_HOURS_BALANCE_CHECK, String.valueOf(DEFAULT_BALANCE_CHECK_ENABLED));
            stringRedisTemplate.opsForValue().set(REDIS_KEY_HOURS_CONSUMPTION, String.valueOf(DEFAULT_CONSUMPTION_ENABLED));
            updateLastModifyInfo("resetToDefault");
            log.info("重置课时控制开关为默认值: 余额检查={}, 课消={}", DEFAULT_BALANCE_CHECK_ENABLED, DEFAULT_CONSUMPTION_ENABLED);
        } catch (Exception e) {
            log.error("重置课时控制开关失败", e);
            throw new RuntimeException("重置课时控制开关失败", e);
        }
    }

    /**
     * 更新最后修改信息
     * 
     * @param operation 操作名称
     */
    private void updateLastModifyInfo(String operation) {
        try {
            String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            stringRedisTemplate.opsForValue().set(REDIS_KEY_LAST_UPDATE_TIME, currentTime);
            stringRedisTemplate.opsForValue().set(REDIS_KEY_LAST_UPDATE_BY, operation);
        } catch (Exception e) {
            log.warn("更新最后修改信息失败", e);
        }
    }

    /**
     * 批量设置开关
     * 
     * @param balanceCheckEnabled 课时余额检查开关
     * @param consumptionEnabled 课消功能开关
     */
    public void setBothSwitches(boolean balanceCheckEnabled, boolean consumptionEnabled) {
        try {
            stringRedisTemplate.opsForValue().set(REDIS_KEY_HOURS_BALANCE_CHECK, String.valueOf(balanceCheckEnabled));
            stringRedisTemplate.opsForValue().set(REDIS_KEY_HOURS_CONSUMPTION, String.valueOf(consumptionEnabled));
            updateLastModifyInfo("setBothSwitches");
            log.info("批量设置课时控制开关: 余额检查={}, 课消={}", balanceCheckEnabled, consumptionEnabled);
        } catch (Exception e) {
            log.error("批量设置课时控制开关失败: balanceCheck={}, consumption={}", balanceCheckEnabled, consumptionEnabled, e);
            throw new RuntimeException("批量设置课时控制开关失败", e);
        }
    }

    /**
     * 检查Redis连接状态
     * 
     * @return true-连接正常，false-连接异常
     */
    public boolean checkRedisConnection() {
        try {
            stringRedisTemplate.opsForValue().get("test");
            return true;
        } catch (Exception e) {
            log.error("Redis连接检查失败", e);
            return false;
        }
    }

    /**
     * 获取所有相关的Redis键值
     * 
     * @return 键值对信息
     */
    public String getAllRedisKeys() {
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("课时控制Redis配置:\n");
            sb.append("- ").append(REDIS_KEY_HOURS_BALANCE_CHECK).append(": ")
              .append(stringRedisTemplate.opsForValue().get(REDIS_KEY_HOURS_BALANCE_CHECK)).append("\n");
            sb.append("- ").append(REDIS_KEY_HOURS_CONSUMPTION).append(": ")
              .append(stringRedisTemplate.opsForValue().get(REDIS_KEY_HOURS_CONSUMPTION)).append("\n");
            sb.append("- ").append(REDIS_KEY_LAST_UPDATE_TIME).append(": ")
              .append(stringRedisTemplate.opsForValue().get(REDIS_KEY_LAST_UPDATE_TIME)).append("\n");
            sb.append("- ").append(REDIS_KEY_LAST_UPDATE_BY).append(": ")
              .append(stringRedisTemplate.opsForValue().get(REDIS_KEY_LAST_UPDATE_BY));
            return sb.toString();
        } catch (Exception e) {
            log.error("获取Redis键值失败", e);
            return "获取Redis键值失败: " + e.getMessage();
        }
    }

    /**
     * 清除所有相关的Redis键
     */
    public void clearAllRedisKeys() {
        try {
            stringRedisTemplate.delete(REDIS_KEY_HOURS_BALANCE_CHECK);
            stringRedisTemplate.delete(REDIS_KEY_HOURS_CONSUMPTION);
            stringRedisTemplate.delete(REDIS_KEY_LAST_UPDATE_TIME);
            stringRedisTemplate.delete(REDIS_KEY_LAST_UPDATE_BY);
            log.info("清除所有课时控制Redis键");
        } catch (Exception e) {
            log.error("清除Redis键失败", e);
            throw new RuntimeException("清除Redis键失败", e);
        }
    }
}
