package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nonamespace.word.server.entity.DataEntity;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("user_student_ext")
public class UserStudentExt extends DataEntity {

    private String studentId;

    private String school;

    private String grade;

    private Integer semester;

    private String englishPublisher;

    // 新增字段
    private String name;

    private String phone;

    private String gender;

    private String className;

    private String parentName;

    private String parentPhone;

    private Integer totalHours;

    private Integer consumedHours;

    private String learningGoals;

    private String remarks;

    private String status;

    // 销售相关字段
    @TableField("sales_id")
    private String salesId;

    @TableField("assign_time")
    private Date assignTime;
}
