package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nonamespace.word.server.entity.DataEntity;

/**
 * 教学组实体类
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "teaching_group", autoResultMap = true)
public class TeachingGroup extends DataEntity {

    /**
     * 教学组名称
     */
    @TableField("name")
    private String name;

    /**
     * 教学组描述
     */
    @TableField("description")
    private String description;

    /**
     * 关联的部门ID (sys_dept表)
     */
    @TableField(value = "dept_id", insertStrategy = com.baomidou.mybatisplus.annotation.FieldStrategy.NOT_EMPTY)
    private Long deptId;

    /**
     * 组长ID
     */
    @TableField("leader_id")
    private String leaderId;

    /**
     * 教务ID
     */
    @TableField("admin_id")
    private String adminId;

    /**
     * 状态 (active: 活跃, inactive: 停用)
     */
    @TableField("status")
    private String status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 成员数量（冗余字段，用于快速查询）
     */
    @TableField("member_count")
    private Integer memberCount;
}
