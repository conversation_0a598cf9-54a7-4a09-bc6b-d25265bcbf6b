package org.nonamespace.word.server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.nonamespace.word.server.domain.CourseBookingApplicationReview;
import org.nonamespace.word.server.mapper.CourseBookingApplicationReviewMapper;
import org.nonamespace.word.server.service.ICourseBookingApplicationReviewService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 预约课申请审核记录Service实现
 * 
 * 注意：Service定位为数据层服务，不在这边做业务耦合
 * 业务逻辑在Facade层实现
 *
 * <AUTHOR> Assistant
 * @date 2025-01-12
 */
@Service
public class CourseBookingApplicationReviewServiceImpl 
    extends ServiceImpl<CourseBookingApplicationReviewMapper, CourseBookingApplicationReview> 
    implements ICourseBookingApplicationReviewService {

    @Override
    public List<CourseBookingApplicationReview> getReviewsByApplicationId(String applicationId) {
        return lambdaQuery()
                .eq(CourseBookingApplicationReview::getApplicationId, applicationId)
                .eq(CourseBookingApplicationReview::getDeleted, false)
                .orderByDesc(CourseBookingApplicationReview::getReviewTime)
                .list();
    }

    @Override
    public CourseBookingApplicationReview getReviewByApplicationAndGroup(String applicationId, String teachingGroupId) {
        return lambdaQuery()
                .eq(CourseBookingApplicationReview::getApplicationId, applicationId)
                .eq(CourseBookingApplicationReview::getTeachingGroupId, teachingGroupId)
                .eq(CourseBookingApplicationReview::getDeleted, false)
                .one();
    }

    @Override
    public boolean hasReviewed(String applicationId, String teachingGroupId) {
        return lambdaQuery()
                .eq(CourseBookingApplicationReview::getApplicationId, applicationId)
                .eq(CourseBookingApplicationReview::getTeachingGroupId, teachingGroupId)
                .eq(CourseBookingApplicationReview::getDeleted, false)
                .exists();
    }

    @Override
    public List<CourseBookingApplicationReview> getApprovedReviews(String applicationId) {
        return lambdaQuery()
                .eq(CourseBookingApplicationReview::getApplicationId, applicationId)
                .eq(CourseBookingApplicationReview::getReviewResult, CourseBookingApplicationReview.ReviewResult.APPROVED.getCode())
                .eq(CourseBookingApplicationReview::getDeleted, false)
                .orderByDesc(CourseBookingApplicationReview::getReviewTime)
                .list();
    }

    @Override
    public List<CourseBookingApplicationReview> getRejectedReviews(String applicationId) {
        return lambdaQuery()
                .eq(CourseBookingApplicationReview::getApplicationId, applicationId)
                .eq(CourseBookingApplicationReview::getReviewResult, CourseBookingApplicationReview.ReviewResult.REJECTED.getCode())
                .eq(CourseBookingApplicationReview::getDeleted, false)
                .orderByDesc(CourseBookingApplicationReview::getReviewTime)
                .list();
    }
}
