package org.nonamespace.word.server.enums;

import lombok.Getter;

/**
 * <AUTHOR> Created on 2025/06/02 22:46
 */
@Getter
public enum SemesterEnum {
    SEMESTER_UP("上学期", 1),
    SEMESTER_DOWN("下学期", 2),
    SEMESTER_ALL("全年", 3),
    NULL(null, null);

    private final String text;
    private final Integer value;

    SemesterEnum(String text, Integer value) {
        this.text = text;
        this.value = value;
    }

    public static SemesterEnum fromText(String text) {
        for (SemesterEnum semesterEnum : SemesterEnum.values()) {
            if (semesterEnum.getText().equals(text)) {
                return semesterEnum;
            }
        }
        return NULL;
    }

    public static SemesterEnum fromValue(Integer value) {
        for (SemesterEnum semesterEnum : SemesterEnum.values()) {
            if (semesterEnum.getValue().equals(value)) {
                return semesterEnum;
            }
        }
        return NULL;
    }
}
