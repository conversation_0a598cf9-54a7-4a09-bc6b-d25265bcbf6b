package org.nonamespace.word.server.facade.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.misc.WssContext;
import org.nonamespace.word.server.domain.SaleProfile;
import org.nonamespace.word.server.domain.SalesGroup;
import org.nonamespace.word.server.domain.SalesGroupMember;
import org.nonamespace.word.server.dto.sales.SalesGroupDto;
import org.nonamespace.word.server.facade.SalesGroupFacade;
import org.nonamespace.word.server.service.*;
import org.nonamespace.word.server.util.SystemDataQueryUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 销售组业务门面实现类
 *
 * <AUTHOR>
 * @date 2025-06-28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SalesGroupFacadeImpl implements SalesGroupFacade {

    private final ISalesGroupService salesGroupService;
    private final ISalesGroupMemberService salesGroupMemberService;
    private final IUserService userService;
    private final IDeptService deptService;
    private final ISaleProfileService saleProfileService;
    private final SystemDataQueryUtil systemDataQueryUtil;
    private final ISysUserService sysUserService;

    @Override
    public IPage<SalesGroupDto.Resp> getOptions(SalesGroupDto.GetListReq req) {
        List<SalesGroupDto.Resp> groups = salesGroupService.lambdaQuery()
                .select(SalesGroup::getId, SalesGroup::getName, SalesGroup::getMemberCount)
                .like(StrUtil.isNotEmpty(req.getName()), SalesGroup::getName, req.getName())
                .list()
                .stream().map(x -> {
                    SalesGroupDto.Resp resp = new SalesGroupDto.Resp();
                    resp.setId(x.getId());
                    resp.setName(x.getName());
                    resp.setMemberCount(x.getMemberCount());
                    return resp;
                }).collect(Collectors.toList());

        IPage<SalesGroupDto.Resp> page = new Page<>(req.getPageNum(), req.getPageSize(), groups.size());
        page.setRecords(groups);
        return page;
    }

    @Override
    public IPage<SalesGroupDto.Resp> getSalesGroupPage(SalesGroupDto.GetListReq req) {
        log.info("分页查询销售组列表: req={}", req);

        // 应用数据权限（已修复sys_dept查询问题）
        applyDataPermissions(req);

        // 使用简单的分页查询，然后手动关联数据
        Page<SalesGroup> page = new Page<>(req.getPageNum(), req.getPageSize());

        // 构建基础查询条件
        IPage<SalesGroup> salesGroupPage = salesGroupService.lambdaQuery()
                .eq(SalesGroup::getDeleted, false)
                .like(StrUtil.isNotEmpty(req.getName()), SalesGroup::getName, req.getName())
                .eq(StrUtil.isNotEmpty(req.getStatus()), SalesGroup::getStatus, req.getStatus())
                .eq(StrUtil.isNotEmpty(req.getLeaderId()), SalesGroup::getLeaderId, req.getLeaderId())
                .orderByDesc(SalesGroup::getCreateTime)
                .page(page);

        // 转换为响应DTO并关联数据
        List<SalesGroupDto.Resp> respList = salesGroupPage.getRecords().stream().map(group -> {
            SalesGroupDto.Resp resp = new SalesGroupDto.Resp();
            resp.setId(group.getId());
            resp.setName(group.getName());
            resp.setDescription(group.getDescription());
            resp.setLeaderId(group.getLeaderId());
            resp.setDeptId(group.getDeptId());
            resp.setMemberCount(group.getMemberCount());
            resp.setStatus(group.getStatus());
            resp.setCreateTime(group.getCreateTime());
            resp.setUpdateTime(group.getUpdateTime());
            return resp;
        }).collect(Collectors.toList());

        // 按需查询组长信息，不使用冗余字段
        enrichWithLeaderInfo(respList);

        // 构建结果
        IPage<SalesGroupDto.Resp> result = new Page<>(salesGroupPage.getCurrent(), salesGroupPage.getSize(), salesGroupPage.getTotal());
        result.setRecords(respList);

        log.info("查询销售组列表成功: total={}, size={}", result.getTotal(), result.getRecords().size());
        return result;
    }

    @Override
    public SalesGroupDto.Resp getSalesGroupById(String id) {
        log.info("查询销售组详情: id={}", id);

        if (StrUtil.isEmpty(id)) {
            throw new IllegalArgumentException("销售组ID不能为空");
        }

        // 查询销售组基本信息
        SalesGroup salesGroup = salesGroupService.getById(id);
        if (salesGroup == null || salesGroup.getDeleted()) {
            throw new RuntimeException("销售组不存在");
        }

        // 转换为响应DTO
        SalesGroupDto.Resp result = new SalesGroupDto.Resp();
        result.setId(salesGroup.getId());
        result.setName(salesGroup.getName());
        result.setDescription(salesGroup.getDescription());
        result.setLeaderId(salesGroup.getLeaderId());
        result.setDeptId(salesGroup.getDeptId());
        result.setMemberCount(salesGroup.getMemberCount());
        result.setStatus(salesGroup.getStatus());
        result.setCreateTime(salesGroup.getCreateTime());
        result.setUpdateTime(salesGroup.getUpdateTime());

        // 关联组长信息 - 从sale_profile表查询
        if (StrUtil.isNotEmpty(salesGroup.getLeaderId())) {
            SaleProfile leader = saleProfileService.lambdaQuery()
                    .eq(SaleProfile::getSalesId, salesGroup.getLeaderId())
                    .eq(SaleProfile::getDeleted, false)
                    .one();
            if (leader != null) {
                result.setLeaderName(leader.getSalesName());
                result.setLeaderPhone(leader.getPhone());
            }
        }

        if (salesGroup.getDeptId() != null) {
            SysDept dept = deptService.lambdaQuery().select(SysDept::getDeptId, SysDept::getDeptName)
                    .eq(SysDept::getDeptId, salesGroup.getDeptId())
                    .eq(SysDept::getDelFlag, "0")
                    .one();
            if (dept != null && "0".equals(dept.getDelFlag())) {
                result.setDeptName(dept.getDeptName());
            }
        }

        // 检查数据权限
        checkDataPermission(result);

        log.info("查询销售组详情成功: name={}", result.getName());
        return result;
    }

    @Override
    public String createSalesGroup(SalesGroupDto.CreateReq req) {
        log.info("创建销售组: req={}", req);

        // 检查权限
        checkCreatePermission();

        // 检查名称是否重复
        boolean nameExists = salesGroupService.lambdaQuery()
                .eq(SalesGroup::getName, req.getName())
                .eq(SalesGroup::getDeleted, false)
                .exists();
        if (nameExists) {
            throw new RuntimeException("销售组名称已存在");
        }

        // 验证组长信息
        if (StrUtil.isNotEmpty(req.getLeaderId())) {
            validateLeader(req.getLeaderId());
        }

        SysDept dept = new SysDept();
        dept.setDeptName(req.getName());
        dept.setParentId(systemDataQueryUtil.getSalesDept().getDeptId());
        deptService.save(dept);


        // 创建销售组
        SalesGroup salesGroup = new SalesGroup();
        salesGroup.setId(IdUtil.getSnowflakeNextIdStr());
        salesGroup.setName(req.getName());
        salesGroup.setDescription(req.getDescription());
        salesGroup.setLeaderId(req.getLeaderId());
        // 销售组不依赖sys_dept，部门信息从销售相关表获取
        // 如果没有指定部门ID，可以设置为null或者从销售部门表获取
        salesGroup.setDeptId(req.getDeptId());
        salesGroup.setStatus(StrUtil.isNotEmpty(req.getStatus()) ? req.getStatus() : "active");
        salesGroup.setMemberCount(0);
        salesGroup.setCreateBy(WssContext.userId());
        salesGroup.setCreateTime(new Date());

        boolean success = salesGroupService.save(salesGroup);
        if (!success) {
            throw new RuntimeException("创建销售组失败");
        }

        // 销售组创建完成，不需要同步冗余信息

        // 如果指定了组长，将组长添加为成员
        if (StrUtil.isNotEmpty(req.getLeaderId())) {
            addLeaderAsMember(salesGroup.getId(), req.getLeaderId());
        }

        log.info("创建销售组成功: id={}, name={}", salesGroup.getId(), salesGroup.getName());
        return salesGroup.getId();
    }

    @Override
    public boolean updateSalesGroup(SalesGroupDto.UpdateReq req) {
        log.info("更新销售组: req={}", req);

        if (StrUtil.isEmpty(req.getId())) {
            throw new IllegalArgumentException("销售组ID不能为空");
        }

        // 检查权限
        checkUpdatePermission(req.getId());

        // 检查销售组是否存在
        SalesGroup existingSalesGroup = salesGroupService.getById(req.getId());
        if (existingSalesGroup == null || existingSalesGroup.getDeleted()) {
            throw new RuntimeException("销售组不存在");
        }

        // 检查名称是否重复（排除自己）
        boolean nameExists = salesGroupService.lambdaQuery()
                .eq(SalesGroup::getName, req.getName())
                .ne(SalesGroup::getId, req.getId())
                .eq(SalesGroup::getDeleted, false)
                .exists();
        if (nameExists) {
            throw new RuntimeException("销售组名称已存在");
        }

        // 验证组长信息
        if (StrUtil.isNotEmpty(req.getLeaderId())) {
            validateLeader(req.getLeaderId());
        }

        // 更新销售组
        SalesGroup salesGroup = new SalesGroup();
        salesGroup.setId(req.getId());
        salesGroup.setName(req.getName());
        salesGroup.setDescription(req.getDescription());
        salesGroup.setLeaderId(req.getLeaderId());
        salesGroup.setDeptId(req.getDeptId());
        salesGroup.setStatus(req.getStatus());
        salesGroup.setUpdateBy(WssContext.userId());
        salesGroup.setUpdateTime(new Date());

        boolean success = salesGroupService.updateById(salesGroup);
        if (!success) {
            throw new RuntimeException("更新销售组失败");
        }

        // 处理组长变更
        handleLeaderChange(req.getId(), existingSalesGroup.getLeaderId(), req.getLeaderId());

        log.info("更新销售组成功: id={}, name={}", req.getId(), req.getName());
        return true;
    }

    @Override
    public boolean deleteSalesGroup(String id) {
        log.info("删除销售组: id={}", id);

        if (StrUtil.isEmpty(id)) {
            throw new IllegalArgumentException("销售组ID不能为空");
        }

        // 检查权限
        checkDeletePermission(id);

        // 检查销售组是否存在
        SalesGroup salesGroup = salesGroupService.getById(id);
        if (salesGroup == null || salesGroup.getDeleted()) {
            throw new RuntimeException("销售组不存在");
        }

        // 检查是否有成员
        long memberCount = salesGroupMemberService.lambdaQuery()
                .eq(SalesGroupMember::getGroupId, id)
                .eq(SalesGroupMember::getDeleted, false)
                .count();
        if (memberCount > 0) {
            throw new RuntimeException("销售组还有成员，无法删除");
        }

        // 软删除销售组
        salesGroup.setDeleted(true);
        salesGroup.setUpdateBy(WssContext.userId());
        salesGroup.setUpdateTime(new Date());

        boolean success = salesGroupService.updateById(salesGroup);
        if (!success) {
            throw new RuntimeException("删除销售组失败");
        }

        log.info("删除销售组成功: id={}, name={}", id, salesGroup.getName());
        return true;
    }

    @Override
    public boolean deleteSalesGroups(List<String> ids) {
        log.info("批量删除销售组: ids={}", ids);

        if (CollUtil.isEmpty(ids)) {
            throw new IllegalArgumentException("销售组ID列表不能为空");
        }

        for (String id : ids) {
            deleteSalesGroup(id);
        }

        log.info("批量删除销售组成功: count={}", ids.size());
        return true;
    }

    @Override
    public SalesGroupDto.StatsResp getSalesGroupStats() {
        log.info("获取销售组统计信息");

        SalesGroupDto.StatsResp result = new SalesGroupDto.StatsResp();

        // 统计销售组总数
        long totalGroups = salesGroupService.lambdaQuery()
                .eq(SalesGroup::getDeleted, false)
                .count();
        result.setTotalGroups((int) totalGroups);

        // 统计活跃销售组数
        long activeGroups = salesGroupService.lambdaQuery()
                .eq(SalesGroup::getDeleted, false)
                .eq(SalesGroup::getStatus, "active")
                .count();
        result.setActiveGroups((int) activeGroups);

        // 统计总销售人员数（已分配到组的）
        long totalSales = salesGroupMemberService.lambdaQuery()
                .eq(SalesGroupMember::getDeleted, false)
                .count();
        result.setTotalSales((int) totalSales);

        // 统计未分配的销售人员数 - 从sale_profile表查询
        // 查询所有销售人员
        List<SaleProfile> allSalesProfiles = saleProfileService.lambdaQuery()
                .eq(SaleProfile::getDeleted, false)
                .eq(SaleProfile::getStatus, "active")
                .list();

        // 查询已分配的销售人员ID
        List<String> assignedSalesIds = salesGroupMemberService.lambdaQuery()
                .eq(SalesGroupMember::getDeleted, false)
                .list()
                .stream()
                .map(SalesGroupMember::getSalesId)
                .distinct()
                .collect(Collectors.toList());

        // 计算未分配的销售人员数
        long unassignedSales = allSalesProfiles.stream()
                .filter(profile -> !assignedSalesIds.contains(profile.getSalesId()))
                .count();
        result.setUnassignedSales((int) unassignedSales);

        log.info("获取销售组统计信息成功: {}", result);
        return result;
    }

    /**
     * 应用数据权限
     */
    private void applyDataPermissions(SalesGroupDto.GetListReq req) {
        // 根据用户角色应用数据权限
        if (systemDataQueryUtil.isAdminOrHr() || systemDataQueryUtil.isSalesDirector()) {
            // 管理员、HR、销售总监可以查看所有销售组
            return;
        }

        if (systemDataQueryUtil.isSalesGroupLeader()) {
            // 销售组长只能查看自己管理的销售组
            String currentUserId = SecurityUtils.getUserId().toString();
            req.setLeaderId(currentUserId);
            return;
        }

        // 其他角色没有权限查看销售组
        throw new RuntimeException("没有权限查看销售组");
    }

    /**
     * 检查数据权限
     */
    private void checkDataPermission(SalesGroupDto.Resp salesGroup) {
        if (systemDataQueryUtil.isAdminOrHr() || systemDataQueryUtil.isSalesDirector()) {
            return;
        }

        if (systemDataQueryUtil.isSalesGroupLeader()) {
            String currentUserId = SecurityUtils.getUserId().toString();
            if (!currentUserId.equals(salesGroup.getLeaderId())) {
                throw new RuntimeException("没有权限查看该销售组");
            }
            return;
        }

        throw new RuntimeException("没有权限查看销售组");
    }

    /**
     * 检查创建权限
     */
    private void checkCreatePermission() {
        if (!systemDataQueryUtil.isAdminOrHr() && !systemDataQueryUtil.isSalesDirector()) {
            throw new RuntimeException("没有权限创建销售组");
        }
    }

    /**
     * 检查更新权限
     */
    private void checkUpdatePermission(String groupId) {
        if (systemDataQueryUtil.isAdminOrHr() || systemDataQueryUtil.isSalesDirector()) {
            return;
        }

        if (systemDataQueryUtil.isSalesGroupLeader()) {
            SalesGroup salesGroup = salesGroupService.getById(groupId);
            if (salesGroup == null || salesGroup.getDeleted()) {
                throw new RuntimeException("销售组不存在");
            }

            String currentUserId = SecurityUtils.getUserId().toString();
            if (!currentUserId.equals(salesGroup.getLeaderId())) {
                throw new RuntimeException("没有权限修改该销售组");
            }
            return;
        }

        throw new RuntimeException("没有权限修改销售组");
    }

    /**
     * 检查删除权限
     */
    private void checkDeletePermission(String groupId) {
        if (!systemDataQueryUtil.isAdminOrHr() && !systemDataQueryUtil.isSalesDirector()) {
            throw new RuntimeException("没有权限删除销售组");
        }
    }

    /**
     * 验证组长信息
     */
    private void validateLeader(String leaderId) {
        // 检查用户是否存在
        boolean exists = userService.lambdaQuery()
                .eq(com.ruoyi.common.core.domain.entity.SysUser::getUserId, leaderId)
                .eq(com.ruoyi.common.core.domain.entity.SysUser::getDelFlag, "0")
                .exists();
        if (!exists) {
            throw new RuntimeException("指定的组长不存在");
        }

        // 检查用户是否具有销售相关角色
        boolean hasSalesRole = userService.lambdaQuery()
                .eq(SysUser::getUserId, leaderId)
                .eq(SysUser::getDelFlag, "0")
                .inSql(SysUser::getUserId, "select user_id from sys_user_role sur where sur.role_id in (select role_id from sys_role sr where sr.role_key in ('sales', 'sales_group_leader', 'sales_director') and del_flag='0')")
                .exists();

        if (!hasSalesRole) {
            // 如果用户没有销售相关角色，自动分配销售组长角色
            log.info("用户{}没有销售相关角色，自动分配销售组长角色", leaderId);
            assignSalesGroupLeaderRole(leaderId);
        }
    }

    /**
     * 将组长添加为成员
     */
    private void addLeaderAsMember(String groupId, String leaderId) {

        SalesGroupMember leader = salesGroupMemberService.lambdaQuery()
                .eq(SalesGroupMember::getGroupId, groupId)
                .eq(SalesGroupMember::getSalesId, leaderId)
                .one();

        if(leader!=null){
            if(leader.getRoleType().equals("leader")){
                return;
            }

            // 如果组长已存在但角色不是leader，更新角色为leader
            updateMemberRoleType(groupId, leaderId, "leader");

            return;
        }


        SalesGroupMember member = new SalesGroupMember();
        member.setId(IdUtil.getSnowflakeNextIdStr());
        member.setGroupId(groupId);
        member.setSalesId(leaderId);
        member.setRoleType("leader");
        member.setJoinTime(new Date());
        member.setStatus("active");
        member.setCreateBy(WssContext.userId());
        member.setCreateTime(new Date());

        salesGroupMemberService.save(member);

        // 更新成员数量
        updateMemberCount(groupId);
    }

    /**
     * 处理组长变更
     */
    private void handleLeaderChange(String groupId, String oldLeaderId, String newLeaderId) {
        if (StrUtil.equals(oldLeaderId, newLeaderId)) {
            return;
        }

        // 将原组长角色改为普通成员
        if (StrUtil.isNotEmpty(oldLeaderId)) {
            updateMemberRoleType(groupId, oldLeaderId, "member");
        }

        // 将新组长设置为组长角色
        if (StrUtil.isNotEmpty(newLeaderId)) {
            // 检查新组长是否已是成员
            boolean isMember = salesGroupMemberService.lambdaQuery()
                    .eq(SalesGroupMember::getGroupId, groupId)
                    .eq(SalesGroupMember::getSalesId, newLeaderId)
                    .eq(SalesGroupMember::getDeleted, false)
                    .exists();

            if (isMember) {
                updateMemberRoleType(groupId, newLeaderId, "leader");
            } else {
                addLeaderAsMember(groupId, newLeaderId);
            }
        }
    }

    @Override
    public IPage<SalesGroupDto.MemberResp> getSalesGroupMembersPage(SalesGroupDto.GetMembersReq req) {
        log.info("分页查询销售组成员列表: req={}", req);

        if (StrUtil.isEmpty(req.getGroupId())) {
            throw new IllegalArgumentException("销售组ID不能为空");
        }

        // 检查权限
        checkMemberViewPermission(req.getGroupId());

        // 使用Service层进行分页查询
        Page<SalesGroupMember> page = new Page<>(req.getPageNum(), req.getPageSize());

        IPage<SalesGroupMember> memberPage = salesGroupMemberService.lambdaQuery()
                .eq(SalesGroupMember::getGroupId, req.getGroupId())
                .eq(SalesGroupMember::getDeleted, false)
                .like(StrUtil.isNotEmpty(req.getSalesName()), SalesGroupMember::getSalesId, req.getSalesName()) // 这里需要关联查询
                .eq(StrUtil.isNotEmpty(req.getRoleType()), SalesGroupMember::getRoleType, req.getRoleType())
                .eq(StrUtil.isNotEmpty(req.getStatus()), SalesGroupMember::getStatus, req.getStatus())
                .orderByDesc(SalesGroupMember::getRoleType)
                .orderByAsc(SalesGroupMember::getJoinTime)
                .page(page);

        // 转换为响应DTO并关联销售人员信息
        List<SalesGroupDto.MemberResp> respList = memberPage.getRecords().stream().map(member -> {
            SalesGroupDto.MemberResp resp = new SalesGroupDto.MemberResp();
            resp.setId(member.getId());
            resp.setGroupId(member.getGroupId());
            resp.setSalesId(member.getSalesId());
            resp.setRoleType(member.getRoleType());
            resp.setStatus(member.getStatus());
            resp.setJoinTime(member.getJoinTime());
            resp.setCreateTime(member.getCreateTime());
            return resp;
        }).collect(Collectors.toList());

        // 批量查询销售人员信息
        enrichMembersWithSalesInfo(respList);

        // 构建结果
        IPage<SalesGroupDto.MemberResp> result = new Page<>(memberPage.getCurrent(), memberPage.getSize(), memberPage.getTotal());
        result.setRecords(respList);

        log.info("查询销售组成员列表成功: total={}, size={}", result.getTotal(), result.getRecords().size());
        return result;
    }

    @Override
    public List<SalesGroupDto.MemberResp> getSalesGroupMembers(String groupId) {
        log.info("查询销售组所有成员: groupId={}", groupId);

        if (StrUtil.isEmpty(groupId)) {
            throw new IllegalArgumentException("销售组ID不能为空");
        }

        // 检查权限
        checkMemberViewPermission(groupId);

        // 查询销售组成员
        List<SalesGroupMember> members = salesGroupMemberService.lambdaQuery()
                .eq(SalesGroupMember::getGroupId, groupId)
                .eq(SalesGroupMember::getDeleted, false)
                .orderByDesc(SalesGroupMember::getRoleType)
                .orderByAsc(SalesGroupMember::getJoinTime)
                .list();

        // 转换为响应DTO
        List<SalesGroupDto.MemberResp> result = members.stream().map(member -> {
            SalesGroupDto.MemberResp resp = new SalesGroupDto.MemberResp();
            resp.setId(member.getId());
            resp.setGroupId(member.getGroupId());
            resp.setSalesId(member.getSalesId());
            resp.setRoleType(member.getRoleType());
            resp.setStatus(member.getStatus());
            resp.setJoinTime(member.getJoinTime());
            resp.setCreateTime(member.getCreateTime());
            return resp;
        }).collect(Collectors.toList());

        // 批量查询销售人员信息
        enrichMembersWithSalesInfo(result);

        log.info("查询销售组成员成功: count={}", result.size());
        return result;
    }

    @Override
    public boolean addSalesGroupMembers(SalesGroupDto.AddMembersReq req) {
        log.info("添加销售组成员: req={}", req);

            if (StrUtil.isEmpty(req.getGroupId())) {
                throw new IllegalArgumentException("销售组ID不能为空");
            }

            if (CollUtil.isEmpty(req.getSalesIds())) {
                throw new IllegalArgumentException("销售人员ID列表不能为空");
            }

            // 检查权限
            checkMemberManagePermission(req.getGroupId());

            // 检查销售组是否存在
            SalesGroup salesGroup = salesGroupService.getById(req.getGroupId());
            if (salesGroup == null || salesGroup.getDeleted()) {
                throw new RuntimeException("销售组不存在");
            }

            // 检查销售人员是否已在其他组
            List<String> assignedSales = salesGroupMemberService.lambdaQuery()
                    .in(SalesGroupMember::getSalesId, req.getSalesIds())
                    .eq(SalesGroupMember::getDeleted, false)
                    .list()
                    .stream()
                    .map(SalesGroupMember::getSalesId)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(assignedSales)) {
                throw new RuntimeException("部分销售人员已在其他销售组中");
            }

            // 检查销售人员是否已在当前组
            List<String> existingMembers = salesGroupMemberService.lambdaQuery()
                    .eq(SalesGroupMember::getGroupId, req.getGroupId())
                    .in(SalesGroupMember::getSalesId, req.getSalesIds())
                    .eq(SalesGroupMember::getDeleted, false)
                    .list()
                    .stream()
                    .map(SalesGroupMember::getSalesId)
                    .toList();

            if (CollUtil.isNotEmpty(existingMembers)) {
                throw new RuntimeException("部分销售人员已在当前销售组中");
            }

            // 批量添加成员
            List<SalesGroupMember> members = new ArrayList<>();
            Date now = new Date();
            String currentUser = WssContext.userId();

            for (String salesId : req.getSalesIds()) {
                SalesGroupMember member = new SalesGroupMember();
                member.setId(IdUtil.getSnowflakeNextIdStr());
                member.setGroupId(req.getGroupId());
                member.setSalesId(salesId);
                member.setRoleType("member");
                member.setJoinTime(now);
                member.setStatus("active");
                member.setCreateBy(currentUser);
                member.setCreateTime(now);
                members.add(member);
            }

            // 批量保存成员
            boolean success = salesGroupMemberService.saveBatch(members);
            if (!success) {
                throw new RuntimeException("添加成员失败");
            }

            // 更新成员数量
            updateMemberCount(req.getGroupId());

            log.info("添加销售组成员成功: groupId={}, count={}", req.getGroupId(), req.getSalesIds().size());
            return true;

    }

    @Override
    public boolean removeSalesGroupMember(SalesGroupDto.RemoveMemberReq req) {
        log.info("移除销售组成员: req={}", req);

        if (StrUtil.isEmpty(req.getGroupId())) {
            throw new IllegalArgumentException("销售组ID不能为空");
        }

        if (StrUtil.isEmpty(req.getSalesId())) {
            throw new IllegalArgumentException("销售人员ID不能为空");
        }

        // 检查权限
        checkMemberManagePermission(req.getGroupId());

        // 检查成员是否存在
        SalesGroupMember member = salesGroupMemberService.lambdaQuery()
                .eq(SalesGroupMember::getGroupId, req.getGroupId())
                .eq(SalesGroupMember::getSalesId, req.getSalesId())
                .eq(SalesGroupMember::getDeleted, false)
                .one();

        if (member == null) {
            throw new RuntimeException("成员不存在");
        }

        // 如果是组长，需要先取消组长身份
        if ("leader".equals(member.getRoleType())) {
            // 更新销售组的组长为空
            SalesGroup salesGroup = new SalesGroup();
            salesGroup.setId(req.getGroupId());
            salesGroup.setLeaderId(null);
            salesGroup.setUpdateBy(WssContext.userId());
            salesGroup.setUpdateTime(new Date());
            salesGroupService.updateById(salesGroup);
        }

        // 软删除成员
        member.setDeleted(true);
        member.setUpdateBy(WssContext.userId());
        member.setUpdateTime(new Date());

        boolean success = salesGroupMemberService.updateById(member);
        if (!success) {
            throw new RuntimeException("移除成员失败");
        }

        // 更新成员数量
        updateMemberCount(req.getGroupId());

        log.info("移除销售组成员成功: groupId={}, salesId={}", req.getGroupId(), req.getSalesId());
        return true;
    }

    @Override
    public boolean setSalesGroupLeader(SalesGroupDto.SetLeaderReq req) {
        log.info("设置销售组组长: req={}", req);

        if (StrUtil.isEmpty(req.getGroupId())) {
            throw new IllegalArgumentException("销售组ID不能为空");
        }

        if (StrUtil.isEmpty(req.getLeaderId())) {
            throw new IllegalArgumentException("组长ID不能为空");
        }

        // 检查权限
        checkMemberManagePermission(req.getGroupId());

        // 检查销售组是否存在
        SalesGroup salesGroup = salesGroupService.getById(req.getGroupId());
        if (salesGroup == null || salesGroup.getDeleted()) {
            throw new RuntimeException("销售组不存在");
        }

        // 验证新组长信息
        validateLeader(req.getLeaderId());

        // 检查新组长是否是组内成员
        boolean isMember = salesGroupMemberService.lambdaQuery()
                .eq(SalesGroupMember::getGroupId, req.getGroupId())
                .eq(SalesGroupMember::getSalesId, req.getLeaderId())
                .eq(SalesGroupMember::getDeleted, false)
                .exists();

        if (!isMember) {
            throw new RuntimeException("指定的组长不是组内成员");
        }

        // 更新销售组组长
        String oldLeaderId = salesGroup.getLeaderId();
        salesGroup.setLeaderId(req.getLeaderId());
        salesGroup.setUpdateBy(WssContext.userId());
        salesGroup.setUpdateTime(new Date());

        boolean success = salesGroupService.updateById(salesGroup);
        if (!success) {
            throw new RuntimeException("设置组长失败");
        }

        // 处理组长变更
        handleLeaderChange(req.getGroupId(), oldLeaderId, req.getLeaderId());

        log.info("设置销售组组长成功: groupId={}, leaderId={}", req.getGroupId(), req.getLeaderId());
        return true;
    }

    /**
     * 检查成员查看权限
     */
    private void checkMemberViewPermission(String groupId) {
        if (systemDataQueryUtil.isAdminOrHr() || systemDataQueryUtil.isSalesDirector()) {
            return;
        }

        if (systemDataQueryUtil.isSalesGroupLeader()) {
            SalesGroup salesGroup = salesGroupService.getById(groupId);
            if (salesGroup == null || salesGroup.getDeleted()) {
                throw new RuntimeException("销售组不存在");
            }

            String currentUserId = SecurityUtils.getUserId().toString();
            if (!currentUserId.equals(salesGroup.getLeaderId())) {
                throw new RuntimeException("没有权限查看该销售组成员");
            }
            return;
        }

        throw new RuntimeException("没有权限查看销售组成员");
    }

    /**
     * 检查成员管理权限
     */
    private void checkMemberManagePermission(String groupId) {
        if (systemDataQueryUtil.isAdminOrHr() || systemDataQueryUtil.isSalesDirector()) {
            return;
        }

        if (systemDataQueryUtil.isSalesGroupLeader()) {
            SalesGroup salesGroup = salesGroupService.getById(groupId);
            if (salesGroup == null || salesGroup.getDeleted()) {
                throw new RuntimeException("销售组不存在");
            }

            String currentUserId = SecurityUtils.getUserId().toString();
            if (!currentUserId.equals(salesGroup.getLeaderId())) {
                throw new RuntimeException("没有权限管理该销售组成员");
            }
            return;
        }

        throw new RuntimeException("没有权限管理销售组成员");
    }

    /**
     * 批量查询组长信息
     */
    private void enrichWithLeaderInfo(List<SalesGroupDto.Resp> respList) {
        if (CollUtil.isEmpty(respList)) {
            return;
        }

        List<String> leaderIds = respList.stream()
                .map(SalesGroupDto.Resp::getLeaderId)
                .filter(StrUtil::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(leaderIds)) {
            return;
        }

        // 批量查询组长信息 - 只查询需要的字段
        List<SysUser> leaders = userService.lambdaQuery()
                .select(SysUser::getUserId, SysUser::getNickName, SysUser::getPhonenumber)
                .in(SysUser::getUserId, leaderIds)
                .eq(SysUser::getDelFlag, "0")
                .list();

        // 设置组长信息
        for (SalesGroupDto.Resp resp : respList) {
            if (StrUtil.isNotEmpty(resp.getLeaderId())) {
                leaders.stream()
                        .filter(leader -> resp.getLeaderId().equals(leader.getUserId().toString()))
                        .findFirst()
                        .ifPresent(leader -> {
                            resp.setLeaderName(leader.getNickName());
                            resp.setLeaderPhone(leader.getPhonenumber());
                        });
            }
        }
    }

    /**
     * 批量查询部门信息
     */
    private void enrichWithDeptInfo(List<SalesGroupDto.Resp> respList) {
        if (CollUtil.isEmpty(respList)) {
            return;
        }

        List<Long> deptIds = respList.stream()
                .map(SalesGroupDto.Resp::getDeptId)
                .filter(deptId -> deptId != null)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(deptIds)) {
            return;
        }

        // 暂时禁用部门信息查询，避免sys_dept表字段问题
        // TODO: 需要修复sys_dept表字段映射问题后重新启用
        Map<Long, String> deptNameMap = new HashMap<>();
        log.warn("部门信息查询已暂时禁用，需要修复sys_dept表字段映射问题: deptIds={}", deptIds);

        // 设置部门信息
        for (SalesGroupDto.Resp resp : respList) {
            if (resp.getDeptId() != null) {
                String deptName = deptNameMap.get(resp.getDeptId());
                if (deptName != null) {
                    resp.setDeptName(deptName);
                }
            }
        }
    }

    /**
     * 更新销售组成员数量
     */
    private void updateMemberCount(String groupId) {
        long memberCount = salesGroupMemberService.lambdaQuery()
                .eq(SalesGroupMember::getGroupId, groupId)
                .eq(SalesGroupMember::getDeleted, false)
                .count();

        SalesGroup salesGroup = new SalesGroup();
        salesGroup.setId(groupId);
        salesGroup.setMemberCount((int) memberCount);
        salesGroup.setUpdateTime(new Date());
        salesGroupService.updateById(salesGroup);
    }

    /**
     * 更新成员角色类型
     */
    private void updateMemberRoleType(String groupId, String salesId, String roleType) {
        SalesGroupMember member = salesGroupMemberService.lambdaQuery()
                .eq(SalesGroupMember::getGroupId, groupId)
                .eq(SalesGroupMember::getSalesId, salesId)
                .eq(SalesGroupMember::getDeleted, false)
                .one();

        if (member != null) {
            member.setRoleType(roleType);
            member.setUpdateTime(new Date());
            member.setUpdateBy(systemDataQueryUtil.getCurrentUserId());
            salesGroupMemberService.updateById(member);
        }
    }

    /**
     * 批量查询销售人员信息
     */
    private void enrichMembersWithSalesInfo(List<SalesGroupDto.MemberResp> respList) {
        if (CollUtil.isEmpty(respList)) {
            return;
        }

        List<String> salesIds = respList.stream()
                .map(SalesGroupDto.MemberResp::getSalesId)
                .filter(StrUtil::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(salesIds)) {
            return;
        }

        // 批量查询销售人员档案信息 - 从sale_profile表查询
        List<SaleProfile> salesProfiles = saleProfileService.lambdaQuery()
                .in(SaleProfile::getSalesId, salesIds)
                .eq(SaleProfile::getDeleted, false)
                .list();

        // 设置销售人员信息
        for (SalesGroupDto.MemberResp resp : respList) {
            if (StrUtil.isNotEmpty(resp.getSalesId())) {
                salesProfiles.stream()
                        .filter(profile -> resp.getSalesId().equals(profile.getSalesId()))
                        .findFirst()
                        .ifPresent(profile -> {
                            resp.setSalesName(profile.getSalesName());
                            resp.setSalesPhone(profile.getPhone());
                        });
            }
        }
    }

    /**
     * 为用户分配销售组长角色
     */
    private void assignSalesGroupLeaderRole(String userId) {
        log.info("为用户分配销售组长角色: userId={}", userId);

        // 获取销售组长角色
        SysRole salesGroupLeaderRole = systemDataQueryUtil.getSalesGroupLeaderRole();
        if (salesGroupLeaderRole == null) {
            throw new RuntimeException("销售组长角色不存在");
        }

        // 检查用户是否已有该角色
        SysUser user = sysUserService.selectUserById(Long.valueOf(userId));
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        boolean hasRole = false;
        if (user.getRoles() != null) {
            hasRole = user.getRoles().stream()
                    .anyMatch(role -> salesGroupLeaderRole.getRoleId().equals(role.getRoleId()));
        }

        if (!hasRole) {
            // 获取用户当前的所有角色ID
            List<Long> currentRoleIds = new ArrayList<>();
            if (user.getRoles() != null && !user.getRoles().isEmpty()) {
                currentRoleIds = user.getRoles().stream()
                        .map(SysRole::getRoleId)
                        .collect(Collectors.toList());
            }

            // 添加销售组长角色到现有角色列表中
            currentRoleIds.add(salesGroupLeaderRole.getRoleId());

            // 使用所有角色ID（包括现有的和新的）重新分配角色
            Long[] allRoleIds = currentRoleIds.toArray(new Long[0]);
            sysUserService.insertUserAuth(Long.valueOf(userId), allRoleIds);

            log.info("为用户 {} 添加销售组长角色成功，当前角色列表: {}", userId, currentRoleIds);
        } else {
            log.debug("用户 {} 已拥有销售组长角色，无需重复分配", userId);
        }
    }
}
