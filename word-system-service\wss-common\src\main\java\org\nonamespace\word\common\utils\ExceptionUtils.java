package org.nonamespace.word.common.utils;

public class ExceptionUtils {
    public static String getErrorMessage(Exception e) {
        String msg = e.getMessage() != null ? e.getMessage() : "未知错误";
        if (msg.contains("Error querying database")){
            msg = "数据库查询错误";
        }else if(msg.contains("java.lang.NullPointerException")) {
            msg = "发生空指针错误";
        } else if(msg.contains("org.springframework.dao.DataIntegrityViolationException")) {
            msg = "数据完整性异常";
        } else if(msg.contains("org.springframework.transaction.TransactionSystemException")) {
            msg = "数据库事务处理错误";
        }else if(msg.contains("DuplicateKeyException")) {
            msg = "数据主键重复错误";
        }else {
            msg = msg.substring(0, Math.min(50, e.getMessage().length()));
        }
        return msg;
    }
}
