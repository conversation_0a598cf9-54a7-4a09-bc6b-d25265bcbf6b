package org.nonamespace.word.server.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.nonamespace.word.server.domain.TeachingGroup;
import org.nonamespace.word.server.dto.management.teachinggroup.TeachingGroupDto;

import java.util.List;

/**
 * 教学组Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface ITeachingGroupService extends IService<TeachingGroup> {

    /**
     * 分页查询教学组列表
     * 
     * @param req 查询条件
     * @return 教学组列表
     */
    IPage<TeachingGroupDto.Resp> selectTeachingGroupPage(TeachingGroupDto.GetListReq req);

    /**
     * 根据ID查询教学组详情
     * 
     * @param id 教学组ID
     * @return 教学组详情
     */
    TeachingGroupDto.Resp selectTeachingGroupById(String id);

    /**
     * 创建教学组
     * 
     * @param req 创建请求
     * @return 是否成功
     */
    boolean createTeachingGroup(TeachingGroupDto.CreateReq req);

    /**
     * 更新教学组
     * 
     * @param req 更新请求
     * @return 是否成功
     */
    boolean updateTeachingGroup(TeachingGroupDto.UpdateReq req);

    /**
     * 删除教学组
     * 
     * @param id 教学组ID
     * @return 是否成功
     */
    boolean deleteTeachingGroup(String id);

    /**
     * 批量删除教学组
     * 
     * @param ids 教学组ID列表
     * @return 是否成功
     */
    boolean deleteTeachingGroups(List<String> ids);

    /**
     * 获取教学组统计信息
     * 
     * @return 统计信息
     */
    TeachingGroupDto.StatsResp getTeachingGroupStats();

    /**
     * 分配教师到教学组
     * 
     * @param req 分配请求
     * @return 是否成功
     */
    boolean assignTeachers(TeachingGroupDto.AssignTeachersReq req);

    /**
     * 从教学组移除教师
     * 
     * @param req 移除请求
     * @return 是否成功
     */
    boolean removeTeachers(TeachingGroupDto.RemoveTeachersReq req);

    /**
     * 更新教学组成员数量
     * 
     * @param groupId 教学组ID
     */
    void updateMemberCount(String groupId);
}
