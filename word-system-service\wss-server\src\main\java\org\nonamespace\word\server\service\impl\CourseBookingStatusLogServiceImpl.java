package org.nonamespace.word.server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.nonamespace.word.server.domain.CourseBookingStatusLog;
import org.nonamespace.word.server.mapper.CourseBookingStatusLogMapper;
import org.nonamespace.word.server.service.ICourseBookingStatusLogService;
import org.springframework.stereotype.Service;

/**
 * 预约课申请状态变更记录Service实现
 * 
 * 注意：Service定位为数据层服务，不在这边做业务耦合
 * 业务逻辑在Facade层实现
 *
 * <AUTHOR> Assistant
 * @date 2025-01-01
 */
@Service
public class CourseBookingStatusLogServiceImpl extends ServiceImpl<CourseBookingStatusLogMapper, CourseBookingStatusLog> implements ICourseBookingStatusLogService {

    // 基础的CRUD操作由ServiceImpl提供
    // 如需自定义数据层方法，可在此添加
}
