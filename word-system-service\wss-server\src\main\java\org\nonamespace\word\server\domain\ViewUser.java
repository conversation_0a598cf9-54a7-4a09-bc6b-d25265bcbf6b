package org.nonamespace.word.server.domain;

import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("view_user")
public class ViewUser {
    private String userId;
    private String realName;
    private String nickName;
    private String phone;
    private String orgId;

    public String getDsensitizedPhone(){
        if (StrUtil.isNotEmpty(phone)) {
            return DesensitizedUtil.mobilePhone(phone);
        }
        return phone;
    }
}
