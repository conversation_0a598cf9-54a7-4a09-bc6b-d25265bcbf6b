package org.nonamespace.word.server.dto.curriculum;

import lombok.Data;

/**
 * 消课DTO
 */
public class CurriculumConsumeCourseDto {

    @Data
    public static class Req {
        /** 课程ID */
        private String courseId;

        /** 上课日期 */
        private String courseDate;

        /** 开始时间 */
        private String startTime;

        /** 结束时间 */
        private String endTime;

        /** 录屏链接 */
        private String recordingUrl;

        /** 上传的图片链接（多个用逗号分隔） */
        private String images;

        /** 说明 */
        private String description;
    }

    @Data
    public static class Resp {
        /** 操作结果 */
        private Boolean success;
        
        /** 消息 */
        private String message;
    }
}
