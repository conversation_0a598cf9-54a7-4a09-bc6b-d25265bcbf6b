package org.nonamespace.word.rest.controller;

import cn.hutool.core.util.StrUtil;
import com.itextpdf.io.source.ByteArrayOutputStream;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.domain.Course;
import org.nonamespace.word.server.domain.StudentWordTest;
import org.nonamespace.word.server.dto.CourseStartLearningDto;
import org.nonamespace.word.server.dto.CourseStepSubmitDto;
import org.nonamespace.word.server.dto.ReviewScheduleQueryDto;
import org.nonamespace.word.server.dto.ReviewScheduleUploadDto;
import org.nonamespace.word.server.dto.course.CourseMaterialDownloadDto;
import org.nonamespace.word.server.dto.course.CourseWordTestDto;
import org.nonamespace.word.server.service.ICourseService;
import org.nonamespace.word.server.service.IReviewScheduleService;
import org.nonamespace.word.server.service.IStudentWordTestService;
import org.nonamespace.word.common.utils.OssService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 课程Controller
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@RestController
@RequestMapping("/course")
//@Anonymous
@Slf4j
public class CourseController extends BaseController {

    @Autowired
    private ICourseService courseService;

    @Autowired
    private IReviewScheduleService reviewScheduleService;

    @Autowired
    private IStudentWordTestService studentWordTestService;

    @Autowired
    private OssService ossService;


    /**
     * 获取课程信息接口 (Get Course Information)
     */
//    @PreAuthorize("@ss.hasPermi('word:course:query')")
    @Log(title = "查询课程信息", businessType = BusinessType.UPDATE)
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id, @RequestParam(value = "detail", required = false, defaultValue = "true") Boolean detail) {
        return success(courseService.courseInfo(id, detail));
    }


    /**
     * 开始上课接口 (Start Class)
     * @param id
     * @return
     */
    @PostMapping("/start/{id}")
    @Log(title = "开始上课", businessType = BusinessType.UPDATE)
    public AjaxResult start(@PathVariable("id") String id) {
        courseService.startCourse(id);
        return success();
    }

    /**
     * 开始上课预检接口 (Start Class)
     * @param id
     * @return
     */
    @Log(title = "上课检查", businessType = BusinessType.UPDATE)
    @PostMapping("/preflight/{id}")
    public AjaxResult preflight(@PathVariable("id") String id) {
        courseService.preflightCourse(id);
        return success();
    }

    @PostMapping("/download-pdf/{courseId}")
    public AjaxResult downloadPdf(@PathVariable("courseId") String courseId,@RequestBody List<String> textbookItemIds) {
        LocalDate today = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M月d日");
        String formatted = today.format(formatter);
        String PDFName = "【北大军哥名师团神奇英语】" + formatted + "单词讲义";
        ByteArrayOutputStream outputStream = courseService.downloadPdf(textbookItemIds, false,courseId,PDFName);
        byte[] pdfBytes = outputStream.toByteArray();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("attachment", PDFName + ".pdf");
        headers.setContentLength(pdfBytes.length);

        return success(ResponseEntity
                .ok()
                .headers(headers)
                .body(pdfBytes));
    }


    /**
     * 开始新课程学习接口 (Start New Course Learning)
     * @param id
     * @param req
     * @return
     */
    @Log(title = "开始新课程学习", businessType = BusinessType.UPDATE)
    @PostMapping("/start-learning/{id}")
    public AjaxResult startLearning(@PathVariable("id") String id, @RequestBody CourseStartLearningDto.Req req) throws IOException {
        return success(courseService.startLearning(id, req));
    }


    /**
     * 开始抗遗忘复习接口 (Start Anti-Forgetting Review)
     * @param id
     * @param reviewId
     * @return
     */
    @Log(title = "开始抗遗忘复习", businessType = BusinessType.UPDATE)
    @PostMapping("/start-review/{id}/review/{reviewId}")
    public AjaxResult startLearning(@PathVariable("id") String id, @PathVariable("reviewId") String reviewId) throws IOException {
        return success(courseService.startReview(id, reviewId));
    }

    /**
     * 开始单词测验
     * @param studentId
     * @param courseId
     * @return
     */
    @Log(title = "开始单词测验", businessType = BusinessType.UPDATE)
    @PostMapping("/start-test/{studentId}/{courseId}")
    public AjaxResult startTest(@PathVariable("studentId") String studentId, @PathVariable("courseId") String courseId, @RequestBody CourseWordTestDto wordTestDto) throws IOException {
        return success(courseService.startTest(studentId, courseId,wordTestDto));
    }

    /**
     * 获取单词测验列表
     * @param studentId
     * @return
     */
    @GetMapping("/word/test/list/{studentId}")
    public AjaxResult wordTestList(@PathVariable("studentId") String studentId) {
        return success(courseService.getWordTestList(studentId));
    }

    /**
     * 获取单词测验列表
     * @param courseId
     * @return
     */
    @GetMapping("/word/test/{courseId}")
    public AjaxResult getWorTestByCourseId(@PathVariable("courseId") String courseId) {
        return success(courseService.getWordByCourseId(courseId));
    }


    /**
     * 开始下课前复习接口 (Start Pre-End-of-Class Review)
     * @param id
     * @return
     */
    @Log(title = "开始下课前复习", businessType = BusinessType.UPDATE)
    @PostMapping("/start-end-review/{id}")
    public AjaxResult startEndReview(@PathVariable("id") String id) throws IOException {
        return success(courseService.startEndReview(id));
    }

    /**
     * 下课接口 (End Class)
     * @param id
     * @return
     */
    @Log(title = "下课", businessType = BusinessType.UPDATE)
    @PostMapping("end/{id}")
    public AjaxResult endCourse(@PathVariable("id") String id) {
        courseService.endCourse(id);
        return success();
    }


    /**
     * 课程学习步骤结果提交
     * @param stepId
     * @param req
     * @return
     */
    @PostMapping("/step/submit/{stepId}")
    public AjaxResult stepSubmit(
                                 @PathVariable("stepId") String stepId,
                                 @RequestBody CourseStepSubmitDto.Req req) {
        courseService.courseStepSubmit(stepId, req);
        return success();
    }

    @PostMapping("end-section/{sectionId}")
    public AjaxResult endSection(@PathVariable("sectionId") String sectionId) {
        courseService.endSection(sectionId);
        return success();
    }


    /**
     * 获取抗遗忘复习列表接口 (Get Anti-Forgetting Review List)
     * @param studentId
     * @return
     */
    @GetMapping("/review/list/{studentId}")
    public AjaxResult getReviewList(@PathVariable("studentId") String studentId) {
        return success(courseService.getReviewList(studentId));
    }

    /**
     * 查询抗遗忘复习计划列表（支持多条件查询）
     * @return
     */
    @GetMapping("/review/list")
    public AjaxResult getReviewListWithConditions(
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam(value = "teacherId", required = false) String teacherId,
            @RequestParam(value = "studentId", required = false) String studentId,
            @RequestParam(value = "reviewTypes", required = false) List<String> reviewTypes,
            @RequestParam(value = "statuses", required = false) List<String> statuses,
            @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam(value = "orderBy", required = false, defaultValue = "scheduled_time") String orderBy,
            @RequestParam(value = "orderDirection", required = false, defaultValue = "ASC") String orderDirection) {

        ReviewScheduleQueryDto.Req req = new ReviewScheduleQueryDto.Req();
        req.setKeyword(keyword);
        req.setTeacherId(teacherId);
        req.setStudentId(studentId);
        req.setReviewTypes(reviewTypes);
        req.setStatuses(statuses);
        req.setStartTime(startTime);
        req.setEndTime(endTime);
        req.setOrderBy(orderBy);
        req.setOrderDirection(orderDirection);

        return success(reviewScheduleService.queryList(req));
    }

    /**
     * 分页查询抗遗忘复习计划（支持多条件查询）
     * @return
     */
    @GetMapping("/review/page")
    public AjaxResult getReviewPage(
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam(value = "teacherId", required = false) String teacherId,
            @RequestParam(value = "studentId", required = false) String studentId,
            @RequestParam(value = "reviewTypes", required = false) List<String> reviewTypes,
            @RequestParam(value = "statuses", required = false) List<String> statuses,
            @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
            @RequestParam(value = "orderBy", required = false, defaultValue = "scheduled_time") String orderBy,
            @RequestParam(value = "orderDirection", required = false, defaultValue = "ASC") String orderDirection) {

        ReviewScheduleQueryDto.Req req = new ReviewScheduleQueryDto.Req();
        req.setKeyword(keyword);
        req.setTeacherId(teacherId);
        req.setStudentId(studentId);
        req.setReviewTypes(reviewTypes);
        req.setStatuses(statuses);
        req.setStartTime(startTime);
        req.setEndTime(endTime);
        req.setPageNum(pageNum);
        req.setPageSize(pageSize);
        req.setOrderBy(orderBy);
        req.setOrderDirection(orderDirection);

        return success(reviewScheduleService.queryPage(req));
    }

    @GetMapping("material/download")
    public AjaxResult downloadMaterial(CourseMaterialDownloadDto.Req req) {
        Course course = courseService.getById(req.getCourseId());
        if(course == null) {
            return error("课程不存在");
        }

        if(!Objects.equals(course.getCourseStatus(), "已完成")){
            return error("课程未完成，无法下载资料");
        }

        CourseMaterialDownloadDto.DownloadInfo downloadInfo = new CourseMaterialDownloadDto.DownloadInfo();

        // 处理错误内容下载
        if(Objects.equals(req.getType(),"error_handout")){
            // 优先使用已保存的下载地址
            if(StrUtil.isNotEmpty(course.getErrorHandoutPdfUrl())){
                downloadInfo.setDownloadUrl(course.getErrorHandoutPdfUrl());
//                downloadInfo.setFileName("错词讲义.pdf");
            } else {
                return error("课程错词讲义未生成，无法下载");

                // 如果没有保存的地址，实时生成
//                try {
//                    downloadInfo = courseService.generateAndUploadCourseErrorMaterial(req.getCourseId(), req.getType());
//                } catch (Exception e) {
//                    log.error("生成课程错词讲义失败", e);
//                    return error(e.getMessage());
//                }
            }
        } else if(Objects.equals(req.getType(),"error_exercise")){
            // 优先使用已保存的下载地址
            if(StrUtil.isNotEmpty(course.getErrorExercisePdfUrl())){
                downloadInfo.setDownloadUrl(course.getErrorExercisePdfUrl());
//                downloadInfo.setFileName("错题练习.pdf");
            } else {
                return error("课程错题练习未生成，无法下载");

                // 如果没有保存的地址，实时生成
//                try {
//                    downloadInfo = courseService.generateAndUploadCourseErrorMaterial(req.getCourseId(), req.getType());
//                } catch (Exception e) {
//                    log.error("生成课程错题练习失败", e);
//                    return error(e.getMessage());
//                }
            }
        }else if(Objects.equals(req.getType(),"handout")){
            if(StrUtil.isEmpty(course.getWordPdfUrl())){
                return error("讲义未生成，无法下载");
            }
            downloadInfo.setDownloadUrl(course.getWordPdfUrl());
        }else if(Objects.equals(req.getType(),"exercise1")){
            if(StrUtil.isEmpty(course.getPracticesPdfUrl())){
                return error("练习未生成，无法下载");
            }
            downloadInfo.setDownloadUrl(course.getPracticesPdfUrl());
        }else if (Objects.equals(req.getType(),"exercise2")) {
            if(StrUtil.isEmpty(course.getPracticesPdfUrl1())){
                return error("练习2未生成，无法下载");
            }
            downloadInfo.setDownloadUrl(course.getPracticesPdfUrl1());
        }else {
            return error("错误的资料类型");
        }

        return success(downloadInfo);
    }

    /**
     * 下载词汇测试错误内容
     * @param courseId 课程ID
     * @param type 下载类型 (error_handout: 错词讲义, error_exercise: 错题练习)
     * @return 下载信息
     */
    @GetMapping("word-test/error-material/download")
    public AjaxResult downloadWordTestErrorMaterial(@RequestParam String courseId, @RequestParam String type) {
        try {
            CourseMaterialDownloadDto.DownloadInfo downloadInfo = new CourseMaterialDownloadDto.DownloadInfo();

            // 查找最新的已完成的单词测验记录
            StudentWordTest wordTest = studentWordTestService.lambdaQuery()
                .eq(StudentWordTest::getCourseId, courseId)
                .eq(StudentWordTest::getStatus, "已完成")
                .orderByDesc(StudentWordTest::getCreateTime)
                .last("limit 1")
                .one();

            if (wordTest != null) {
                // 优先使用已保存的下载地址
                if ("error_handout".equals(type)) {
                    if( StrUtil.isNotEmpty(wordTest.getErrorHandoutPdfUrl())) {
                        downloadInfo.setDownloadUrl(wordTest.getErrorHandoutPdfUrl());
                    }else{
                        return error("测试错词讲义未生成，无法下载");
                    }
                    return success(downloadInfo);
                } else if ("error_exercise".equals(type)) {
                    if(StrUtil.isNotEmpty(wordTest.getErrorExercisePdfUrl())) {
                        downloadInfo.setDownloadUrl(wordTest.getErrorExercisePdfUrl());
                    }else{
                        return error("测试错题练习未生成，无法下载");
                    }
                    return success(downloadInfo);
                }
            }

//            // 如果没有保存的地址，实时生成
//            downloadInfo = courseService.generateAndUploadWordTestErrorMaterial(courseId, type);
            return success(downloadInfo);
        } catch (Exception e) {
            log.error("下载词汇测试错误内容失败", e);
            return error(e.getMessage());
        }
    }

    /**
     * 上传抗遗忘复习完成情况（图片和说明）
     * @param reviewScheduleId 复习计划ID
     * @param description 说明文字
     * @param images 图片文件
     * @return 上传结果
     */
    @PostMapping("/review/upload")
    public AjaxResult uploadReviewCompletion(
            @RequestParam("reviewScheduleId") String reviewScheduleId,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam("images") MultipartFile[] images) {
        try {
            ReviewScheduleUploadDto.UploadReq req = new ReviewScheduleUploadDto.UploadReq();
            req.setReviewScheduleId(reviewScheduleId);
            req.setDescription(description);
            req.setImages(images);

            ReviewScheduleUploadDto.UploadResp result = reviewScheduleService.uploadReviewCompletion(req);
            return success(result);
        } catch (Exception e) {
            log.error("上传抗遗忘复习完成情况失败", e);
            return error("上传失败: " + e.getMessage());
        }
    }

    /**
     * 查看抗遗忘复习上传内容
     * @param reviewScheduleId 复习计划ID
     * @return 上传内容详情
     */
    @GetMapping("/review/upload/{reviewScheduleId}")
    public AjaxResult viewUploadedContent(@PathVariable("reviewScheduleId") String reviewScheduleId) {
        try {
            ReviewScheduleUploadDto.ViewReq req = new ReviewScheduleUploadDto.ViewReq();
            req.setReviewScheduleId(reviewScheduleId);
            ReviewScheduleUploadDto.ViewResp result = reviewScheduleService.viewUploadedContent(req);
            return success(result);
        } catch (Exception e) {
            log.error("查看抗遗忘复习上传内容失败", e);
            return error("查看失败: " + e.getMessage());
        }
    }
}
