package org.nonamespace.word.server.facade;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.nonamespace.word.server.dto.management.booking.CourseBookingDto;
import org.nonamespace.word.server.dto.management.teachingleader.TeachingGroupLeaderDto;

import java.util.List;

/**
 * 教学组长业务门面接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ITeachingGroupLeaderFacade {

    /**
     * 获取教学组长待审核的预约课申请列表
     * 
     * @param req 查询请求参数
     * @return 分页结果
     */
    IPage<CourseBookingDto.BasicResp> getPendingApplicationsForLeader(TeachingGroupLeaderDto.GetPendingApplicationsReq req);

    /**
     * 教学组长审核预约课申请
     * 
     * @param req 审核请求参数
     * @return 是否成功
     */
    boolean reviewCourseBookingApplication(TeachingGroupLeaderDto.ReviewApplicationReq req);

    /**
     * 批量审核预约课申请
     * 
     * @param req 批量审核请求参数
     * @return 批量审核结果
     */
    TeachingGroupLeaderDto.BatchReviewResp batchReviewApplications(TeachingGroupLeaderDto.BatchReviewReq req);

    /**
     * 获取教学组长审核统计信息
     * 
     * @return 统计信息
     */
    TeachingGroupLeaderDto.ReviewStatsResp getReviewStats();

    /**
     * 获取教学组下的教师列表
     *
     * @param groupId 教学组ID（可选，不传则获取当前用户所在教学组）
     * @return 教师列表
     */
    List<TeachingGroupLeaderDto.GroupTeacherResp> getGroupTeachers(String groupId);

    /**
     * 获取指定申请中本组内的候选教师列表（用于通过申请页面）
     *
     * @param applicationId 申请记录ID
     * @param groupId 教学组ID（可选，不传则获取当前用户所在教学组）
     * @return 指定申请中本组内的候选教师列表
     */
    List<TeachingGroupLeaderDto.GroupTeacherResp> getAppliedTeachers(String applicationId, String groupId);

    /**
     * 获取教师在指定申请试听课时间的可选时间段
     *
     * @param teacherId 教师ID
     * @param applicationId 申请ID
     * @return 可选的试听课时间段列表
     */
    List<TeachingGroupLeaderDto.TrialTimeSlotResp> getTeacherAvailableSlots(String teacherId, String applicationId);

    /**
     * 分配教师和时间段
     * 
     * @param req 分配请求参数
     * @return 是否成功
     */
    boolean assignTeacherToApplication(TeachingGroupLeaderDto.AssignTeacherReq req);

    /**
     * 获取审核历史记录
     * 
     * @param req 查询请求参数
     * @return 分页结果
     */
    IPage<TeachingGroupLeaderDto.ReviewHistoryResp> getReviewHistory(TeachingGroupLeaderDto.GetReviewHistoryReq req);
}
