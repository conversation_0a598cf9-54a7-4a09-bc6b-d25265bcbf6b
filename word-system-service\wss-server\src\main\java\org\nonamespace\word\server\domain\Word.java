package org.nonamespace.word.server.domain;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.ruoyi.common.annotation.Excel;
import lombok.*;
import org.nonamespace.word.server.entity.DataEntity;
import org.nonamespace.word.server.misc.mybatis.ListStringTypeHandler;

import java.util.List;
import java.util.Map;

/**
 * 单词: 核心词库，存储所有单词的基本信息及例句对象 word
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@TableName(value = "word", autoResultMap = true)
//@TableName(value = "word", autoResultMap = true)
public class Word extends DataEntity {

    /** 单词文本 (英文), 唯一且不能为空 */
    private String word;

    /** 音节 */
    @Excel(name = "音节")
    private String syllables;

    /** 英式音标 */
    @Excel(name = "英式音标")
    private String phoneticUk;

    /** 美式音标 */
    @Excel(name = "美式音标")
    private String phoneticUs;

    /** 英式发音音频文件URL */
    @Excel(name = "英式发音音频文件URL")
    private String audioUkUrl;

    /** 美式发音音频文件URL */
    @Excel(name = "美式发音音频文件URL")
    private String audioUsUrl;

    /** 词义(可包含多个词性及对应中文解释,如{pos: [{"pos": "n.", "def": "猫"}, {"pos": "v.", "def": "抓"}], practices:["", ""]) */
    @Excel(name = "词义(可包含多个词性及对应中文解释)")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Meanings> meanings;

    /** 例句 ([{"sentence_en": "...", "sentence_cn": "...", "audio_uk_url": "...", "audio_us_url": "...", "structure_parts_en": [["...", "..."]]}]) */
    @Excel(name = "例句")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, List<Sentences>> sentences;

    /** 标签 (数组类型, 例如: "高频"、"名词"、"动词") */
    @Excel(name = "标签")
    @TableField(typeHandler = ListStringTypeHandler.class)
    private List<String> tags;

    /** 难度等级 (1-5) */
    @Excel(name = "难度等级 (1-5)")
    private Long difficulty;

    /** 标准讲解视频URL (通用) */
    @Excel(name = "标准讲解视频URL (通用)")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, String> videoUrl;

    /** 单词测验标记 */
    @Excel(name = "单词测验标记")
    private Boolean flagPracticeWord;

    /** 句子翻译标记 */
    @Excel(name = "句子翻译标记")
    private Boolean flagPracticeTranslate;

    /** 句子排序标记 */
    @Excel(name = "句子排序标记")
    private Boolean flagPracticeOrder;

    /** 版本号, 每次修改递增 */
    @Excel(name = "版本号, 每次修改递增")
    @TableField(
            fill = FieldFill.INSERT_UPDATE,
            value = "version"
    )
    private Long version;


    public Long getVersion() {
        return version == null ? 1 : version;
    }

    private String changeDescription;



    /**
     * 词义
     */
    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Meanings {
        private List<Pos> pos;
        private List<String> practices;


        @Getter
        @Setter
        @AllArgsConstructor
        @NoArgsConstructor
        public static class Pos {
            private String pos;
            private String def;

            public void setPos(String pos) {
                // 名词 n.  动词 v.  形容词 adj.  副词 adv.  介词 prep.
                if(pos == null) {
                    this.pos = null;
                } else if (pos.startsWith("n")) {
                    this.pos = "n";
                } else if (pos.startsWith("v")) {
                    this.pos = "v";
                } else if (pos.startsWith("adj")) {
                    this.pos = "adj";
                } else if (pos.startsWith("adv")) {
                    this.pos = "adv";
                } else if (pos.startsWith("prep")) {
                    this.pos = "prep";
                } else {
                    this.pos = pos;
                }
            }
        }
    }

    /**
     * 例句
     */
    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Sentences {
        private String sentenceEn;
        private String sentenceCn;
        private String syllables;
        private String stage;
        private String audioUkUrl;
        private String audioUsUrl;
        private List<String> structurePartsEn;
        private List<String> practices;

        public Sentences(String stage) {
            this.stage = stage;
        }

        public void setStructurePartsEn(List<String> structurePartsEn) {
            if(CollUtil.isNotEmpty(structurePartsEn)) {
                structurePartsEn = structurePartsEn.stream().map(x -> x.replaceAll("\\u00A0", " ")).toList();
            }
            this.structurePartsEn = structurePartsEn;
        }

        public void setSentenceEn(String sentenceEn) {
            if(StrUtil.isNotBlank(sentenceEn)) {
                sentenceEn = sentenceEn.replaceAll("\\u00A0", " ");
            }
            this.sentenceEn = sentenceEn;
        }

    }
}
