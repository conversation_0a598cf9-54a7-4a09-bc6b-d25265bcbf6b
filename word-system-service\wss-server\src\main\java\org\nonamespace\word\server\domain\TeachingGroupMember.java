package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nonamespace.word.server.entity.DataEntity;

/**
 * 教学组成员关系实体类
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "teaching_group_member", autoResultMap = true)
public class TeachingGroupMember extends DataEntity {

    /**
     * 教学组ID
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 教师ID
     */
    @TableField("teacher_id")
    private String teacherId;

    /**
     * 角色类型 (leader: 组长, admin: 教务, member: 普通成员)
     */
    @TableField("role_type")
    private String roleType;

    /**
     * 加入时间
     */
    @TableField("join_time")
    private java.util.Date joinTime;

    /**
     * 状态 (active: 活跃, inactive: 停用)
     */
    @TableField("status")
    private String status;
}
