package org.nonamespace.word.server.dto.curriculum;

import lombok.Data;

import java.util.List;

@Data
public class CurriculumCreateScheduleDto {
    @Data
    public static class Req {
        private String studentId;
        private String teacherId;
        private List<WeeklySchedule> weeklySchedules;
        private String[] dateRange;
        private String type;
        private String courseType; // 课程类型：正式课、试听课
        private Boolean useSystem; // 使用系统
        private String subject;
        private String specification;
        private Integer totalLessons;
        private String studentName;
        private String teacherName;

        @Data
        public static class WeeklySchedule {
            private Integer dayOfWeek;
            private String startTime;
            private String endTime;
            private Integer duration;
        }
    }
}
