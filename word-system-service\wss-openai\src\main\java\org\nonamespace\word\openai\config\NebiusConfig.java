package org.nonamespace.word.openai.config;


import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * x.ai配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "nebius")
public class NebiusConfig {

    private String baseUrl;
    private String apiKey;
    private String model;
    private String reasoningEffort;
    private Integer temperature;

    private Proxy proxy;

    @Getter
    @Setter
    public static class Proxy {
        private String host;
        private Integer port;
    }


    public boolean enableProxy() {
        return proxy != null && StrUtil.isNotBlank(proxy.getHost()) && proxy.getPort() != null;
    }

}
