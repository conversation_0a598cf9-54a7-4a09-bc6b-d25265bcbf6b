package org.nonamespace.word.rest.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.nonamespace.word.server.dto.management.course.CourseHoursImportDto;
import org.nonamespace.word.server.service.ICourseHoursImportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLEncoder;

/**
 * 课时导入控制器
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@RestController
@RequestMapping("/management/course-hours")
public class CourseHoursImportController extends BaseController {

    @Autowired
    private ICourseHoursImportService courseHoursImportService;

    /**
     * 导入课时数据
     */
    @PreAuthorize("@ss.hasPermi('management:course-hours:import')")
    @Log(title = "课时导入", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public AjaxResult importCourseHours(@RequestParam("file") MultipartFile file,
                                       @RequestParam(value = "overrideExisting", defaultValue = "false") Boolean overrideExisting) {
        long startTime = System.currentTimeMillis();
        try {
            if (file.isEmpty()) {
                return AjaxResult.error("上传文件不能为空");
            }

            // 检查文件格式
            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
                return AjaxResult.error("文件格式不正确，请上传Excel文件(.xlsx或.xls)");
            }

            log.info("开始课时导入，文件名: {}, 文件大小: {} bytes", fileName, file.getSize());

            // 构建导入请求
            CourseHoursImportDto.ImportRequest request = new CourseHoursImportDto.ImportRequest();
            request.setOverrideExisting(overrideExisting);

            // 执行导入
            CourseHoursImportDto.ImportResult result = courseHoursImportService.importCourseHours(file, request);
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            log.info("课时导入完成，耗时: {} ms, 总行数: {}, 成功: {}, 失败: {}, 创建老师: {}, 创建学生: {}, 创建关系: {}, 创建课时: {}",
                duration, result.getTotalRows(), result.getSuccessRows(), result.getFailedRows(),
                result.getCreatedTeachers(), result.getCreatedStudents(), result.getCreatedRelations(), result.getCreatedHours());

            if (result.getErrorMessage() != null) {
                return AjaxResult.error(result.getErrorMessage());
            }
            
            // 构建返回消息
            StringBuilder message = new StringBuilder();
            message.append("导入完成！\n");
            message.append("总行数: ").append(result.getTotalRows()).append("\n");
            message.append("成功: ").append(result.getSuccessRows()).append("\n");
            message.append("失败: ").append(result.getFailedRows()).append("\n");
            message.append("创建老师: ").append(result.getCreatedTeachers()).append("\n");
            message.append("创建学生: ").append(result.getCreatedStudents()).append("\n");
            message.append("创建师生关系: ").append(result.getCreatedRelations()).append("\n");
            message.append("创建课时记录: ").append(result.getCreatedHours()).append("\n");
            
            if (result.getDetailMessage() != null) {
                message.append("\n详细信息:\n").append(result.getDetailMessage());
            }
            
            return AjaxResult.success(message.toString(), result);
            
        } catch (Exception e) {
            log.error("课时导入失败", e);
            return AjaxResult.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('management:course-hours:template')")
    @PostMapping("/template")
    public void downloadTemplate(HttpServletResponse response) {
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("课时导入模板", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            // 创建Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("课时导入模板");

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "教学组", "老师姓名", "老师手机号", "学生姓名", "学生手机号",
                "学科", "课型", "课时性质", "购买课时", "剩余购买课时",
                "赠送课时", "剩余赠送课时", "单价"
            };

            // 创建标题样式
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            headerStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 设置标题
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
                sheet.autoSizeColumn(i);
            }

            // 创建示例数据行
            Row exampleRow = sheet.createRow(1);
            String[] exampleData = {
                "北大军哥名师团", "张老师", "13800138001", "李同学", "13900139001",
                "英语", "单词课", "正式课", "20", "20",
                "5", "5", "100"
            };

            for (int i = 0; i < exampleData.length; i++) {
                Cell cell = exampleRow.createCell(i);
                cell.setCellValue(exampleData[i]);
            }

            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // 写入响应
            workbook.write(response.getOutputStream());
            workbook.close();

        } catch (Exception e) {
            log.error("下载模板失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }
}
