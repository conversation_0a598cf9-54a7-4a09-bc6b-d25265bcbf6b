package org.nonamespace.word.server.service;

import org.nonamespace.word.server.dto.management.teachermatch.TeacherMatchDto;

/**
 * 教师匹配服务接口
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
public interface ITeacherMatchService {

    /**
     * 根据学生信息匹配合适的教师
     *
     * @param request 匹配请求
     * @return 匹配结果
     */
    TeacherMatchDto.MatchTeachersResp matchTeachers(TeacherMatchDto.MatchTeachersReq request);

    /**
     * 获取教师详细时间安排（周视图）
     *
     * @param teacherId 教师ID
     * @param startDate 开始日期
     * @return 教师周时间安排
     */
    TeacherMatchDto.TeacherWeeklySchedule getTeacherWeeklySchedule(String teacherId, String startDate);

    /**
     * 检查教师在指定日期时间段是否有1小时空闲时间（数据层操作）
     *
     * @param teacherId 教师ID
     * @param date 日期
     * @param startTime 开始时间 (HH:mm格式)
     * @param endTime 结束时间 (HH:mm格式)
     * @return 是否有空闲时间
     */
    boolean hasOneHourFreeTime(String teacherId, java.util.Date date, String startTime, String endTime);

    /**
     * 批量检查教师试听课时间可用性（数据层操作）
     *
     * @param teacherIds 教师ID列表
     * @param date 试听课日期
     * @param startTime 开始时间 (HH:mm格式)
     * @param endTime 结束时间 (HH:mm格式)
     * @return 教师ID -> 是否可用的映射
     */
    java.util.Map<String, Boolean> batchCheckTrialTimeAvailability(java.util.List<String> teacherIds,
                                                                   java.util.Date date,
                                                                   String startTime,
                                                                   String endTime);
}
