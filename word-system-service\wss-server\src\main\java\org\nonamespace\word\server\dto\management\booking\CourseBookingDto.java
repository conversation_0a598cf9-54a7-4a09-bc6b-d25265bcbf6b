package org.nonamespace.word.server.dto.management.booking;

import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 预约课申请相关DTO
 * 
 * <AUTHOR>
 * @date 2025-06-29
 */
public class CourseBookingDto {

    /**
     * 首选时间段
     */
    @Data
    public static class PreferredTimeSlot {
        @NotNull(message = "星期几不能为空")
        private Integer weekday; // 星期几 (1-7, 1=周一, 7=周日)

        @NotBlank(message = "开始时间不能为空")
        private String startTime; // 开始时间 (HH:mm格式)

        @NotBlank(message = "结束时间不能为空")
        private String endTime; // 结束时间 (HH:mm格式)

        // 移除优先级字段，按照用户要求取消优先级查询条件
    }

    /**
     * 首选教师信息
     */
    @Data
    public static class PreferredTeacherInfo {
        private String teacherId;
        private String teacherName;
        private String teacherPhone;
        private String groupName;
        private Boolean available;
        private String unavailableReason;
        private Integer priority;
    }

    /**
     * 预约课申请基本信息响应
     */
    @Data
    public static class BasicResp {
        private String id;
        private String studentId;
        private String studentName;
        private String studentPhone;
        private String salesId;
        private String salesName;
        private String salesGroupId;
        private String salesGroupName;
        private String subject;
        private String specification;
        private List<PreferredTeacherInfo> preferredTeacherInfos;
        private String teachingGroupId;
        private String teachingGroupName;
        private String teachingGroupLeaderName;
        private List<PreferredTimeSlot> preferredTimeSlots;
        private TrialClassTime trialClassTime; // 试听课时间
        private String applicationReason;
        private String status;
        private String statusText;
        private String approvedTeacherId;
        private String approvedTeacherName;
        private Date approvalTime;
        private String approvalBy;
        private String approvalByName;
        private String rejectionReason;
        private String courseHoursPackageId;
        private Date createTime;
        private Date updateTime;

        // 权限控制字段
        private Boolean canReview; // 是否可以审核（只有待审核状态且有权限的用户才能审核）
    }

    /**
     * 预约课申请详细信息响应
     */
    @Data
    @EqualsAndHashCode(callSuper = false)
    public static class DetailResp extends BasicResp {
        private String createBy;
        private String updateBy;
        // 可以添加处理历史等详细信息
    }

    /**
     * 查询预约课申请列表请求
     */
    @Data
    public static class GetListReq {
        private Integer pageNum = 1;
        private Integer pageSize = 10;
        private String studentId;        // 学生ID
        private String studentName;      // 学生姓名
        private String studentPhone;     // 学生手机号
        private String salesId;          // 销售ID
        private String salesGroupId;     // 销售组ID
        private String subject;          // 学科
        private String specification;    // 课型
        private List<String> statusList; // 状态列表（支持多选）
        private String status;           // 单个状态（兼容旧代码）
        private String teachingGroupId;  // 教学组ID
        private String teachingGroupManagerUserId; // 教学组管理员用户ID，用于新的权限过滤逻辑
        private String teacherName;      // 老师姓名
        private String teacherPhone;     // 老师手机号
        private String approvalResult;   // 审核结果（已通过、已拒绝等）
        private Date createTimeStart;    // 创建时间开始
        private Date createTimeEnd;      // 创建时间结束
    }

    /**
     * 创建预约课申请请求
     */
    @Data
    public static class CreateReq {
        @NotBlank(message = "学生ID不能为空")
        private String studentId;

        @NotBlank(message = "学科不能为空")
        private String subject;

        @NotBlank(message = "课型不能为空")
        private String specification;

        @NotEmpty(message = "首选教师不能为空")
        private List<String> preferredTeachers; // 教师ID列表，最少1个，最多3个

        private String teachingGroupId; // 教学组ID（可选，不再强制要求同一教学组）

        @NotNull(message = "试听课时间不能为空")
        @Valid
        private TrialClassTime trialClassTime; // 试听课时间（必填）

        private List<PreferredTimeSlot> preferredTimeSlots; // 首选时间段列表（可选）

        @NotBlank(message = "申请原因不能为空")
        private String applicationReason;
    }

    /**
     * 更新预约课申请请求
     */
    @Data
    public static class UpdateReq {
        @NotBlank(message = "申请ID不能为空")
        private String id;

        @NotBlank(message = "学科不能为空")
        private String subject;

        @NotBlank(message = "课型不能为空")
        private String specification;

        @NotEmpty(message = "首选教师不能为空")
        private List<String> preferredTeachers;

        private String teachingGroupId;

        @NotEmpty(message = "首选时间段不能为空")
        private List<PreferredTimeSlot> preferredTimeSlots;

        @NotBlank(message = "申请原因不能为空")
        private String applicationReason;
    }

    /**
     * 确认预约课申请请求
     */
    @Data
    public static class ApprovalReq {
        @NotBlank(message = "申请ID不能为空")
        private String applicationId;

        @NotBlank(message = "确认教师ID不能为空")
        private String approvedTeacherId;

        private String approvalRemark; // 确认备注
    }

    /**
     * 拒绝预约课申请请求
     */
    @Data
    public static class RejectionReq {
        @NotBlank(message = "申请ID不能为空")
        private String applicationId;

        @NotBlank(message = "拒绝原因不能为空")
        private String rejectionReason;
    }

    /**
     * 预约课申请统计响应
     */
    @Data
    public static class StatsResp {
        private Long totalApplications; // 总申请数
        private Long pendingApplications; // 待确认申请数
        private Long approvedApplications; // 已确认申请数
        private Long rejectedApplications; // 已拒绝申请数
        private Double approvalRate; // 确认率
        private Long monthlyApplications; // 本月申请数
        private Long monthlyApprovals; // 本月确认数
        private Double averageProcessingTime; // 平均处理时间(小时)
    }

    /**
     * 可选教师查询请求
     */
    @Data
    public static class AvailableTeachersReq {
        @NotBlank(message = "学科不能为空")
        private String subject;

        @NotBlank(message = "课型不能为空")
        private String specification;

        private String teachingGroupId; // 教学组ID过滤
        private String keyword; // 关键词搜索
        private String gender; // 性别过滤
    }

    /**
     * 可选教师响应
     */
    @Data
    public static class AvailableTeacherResp {
        private String teacherId;
        private String teacherName;
        private String teacherPhone;
        private String gender;
        private String teachingGroupId;
        private String teachingGroupName;
        private Boolean available;
        private String unavailableReason;
        private List<String> subjects; // 教授学科
        private List<String> specifications; // 教授课型
    }

    /**
     * 教学组选项响应
     */
    @Data
    public static class TeachingGroupOptionResp {
        private String id;
        private String name;
        private String leaderName;
        private String adminName;
        private Integer memberCount;
        private String status;
    }

    /**
     * 作废申请请求
     */
    @Data
    public static class VoidReq {
        private String applicationId; // 申请ID，由路径参数设置

        @NotBlank(message = "作废原因不能为空")
        private String voidReason; // 作废原因
    }

    /**
     * 试听课时间DTO
     */
    @Data
    public static class TrialClassTime {
        @NotNull(message = "试听课日期不能为空")
        private Date date;        // 试听课日期

        @NotBlank(message = "试听课开始时间不能为空")
        private String startTime; // 开始时间 (HH:mm格式)

        @NotBlank(message = "试听课结束时间不能为空")
        private String endTime;   // 结束时间 (HH:mm格式)
    }
}
