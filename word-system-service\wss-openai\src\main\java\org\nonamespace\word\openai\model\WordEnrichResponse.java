package org.nonamespace.word.openai.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 单词信息补充响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WordEnrichResponse {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 补充信息后的单词列表
     */
    private List<WordInfo> words;

    /**
     * 创建成功响应
     */
    public static WordEnrichResponse success(List<WordInfo> words) {
        return WordEnrichResponse.builder()
                .success(true)
                .words(words)
                .build();
    }

    /**
     * 创建失败响应
     */
    public static WordEnrichResponse error(String errorMessage) {
        return WordEnrichResponse.builder()
                .success(false)
                .errorMessage(errorMessage)
                .build();
    }
}