package org.nonamespace.word.server.service.order;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.nonamespace.word.server.domain.order.OrderRefundRecord;
import org.nonamespace.word.server.domain.order.Orders;
import org.nonamespace.word.server.domain.order.OrdersTrx;
import org.nonamespace.word.server.dto.order.RefundRecordDto;

import java.util.List;

/**
 * 退款记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
public interface IOrderRefundRecordService extends IService<OrderRefundRecord> {

    /**
     * 创建退款记录
     * 
     * @param orders 订单信息
     * @param originalTrx 原交易流水
     * @param refundTrx 退款交易流水
     * @param refundType 退款类型
     * @param refundAmount 退款金额
     * @param refundReason 退款原因
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 退款记录
     */
    OrderRefundRecord createRefundRecord(Orders orders, OrdersTrx originalTrx, OrdersTrx refundTrx,
                                       String refundType, Long refundAmount, String refundReason,
                                       String operatorId, String operatorName);

    /**
     * 更新退款记录状态
     * 
     * @param refundRecordId 退款记录ID
     * @param refundStatus 退款状态
     * @param errorMessage 错误信息（可选）
     */
    void updateRefundStatus(String refundRecordId, String refundStatus, String errorMessage);

    /**
     * 更新退款记录的平台信息
     * 
     * @param refundRecordId 退款记录ID
     * @param platformRefundId 平台退款ID
     * @param platformResponse 平台响应信息
     */
    void updatePlatformInfo(String refundRecordId, String platformRefundId, String platformResponse);

    /**
     * 分页查询退款记录
     * 
     * @param req 查询请求
     * @return 分页结果
     */
    IPage<RefundRecordDto.Resp> selectRefundRecordsByParam(RefundRecordDto.QueryReq req);

    /**
     * 查询退款记录详情
     * 
     * @param refundRecordId 退款记录ID
     * @return 退款记录详情
     */
    RefundRecordDto.DetailResp getRefundRecordDetail(String refundRecordId);

    /**
     * 审批退款记录
     * 
     * @param refundRecordId 退款记录ID
     * @param approvalResult 审批结果
     * @param approvalRemark 审批备注
     * @param approverId 审批人ID
     * @param approverName 审批人姓名
     */
    void approveRefundRecord(String refundRecordId, String approvalResult, String approvalRemark,
                           String approverId, String approverName);

    /**
     * 查询退款统计数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据
     */
    RefundRecordDto.StatisticsResp getRefundStatistics(String startDate, String endDate);

    /**
     * 查询订单的退款记录
     * 
     * @param orderId 订单ID
     * @return 退款记录列表
     */
    List<OrderRefundRecord> getRefundRecordsByOrderId(String orderId);

    /**
     * 查询待审批的退款记录数量
     * 
     * @return 待审批数量
     */
    Integer getPendingApprovalCount();

    /**
     * 导出退款记录
     * 
     * @param req 查询条件
     * @return 导出数据
     */
    List<RefundRecordDto.Resp> exportRefundRecords(RefundRecordDto.QueryReq req);

    /**
     * 检查退款记录是否存在
     * 
     * @param orderId 订单ID
     * @param refundType 退款类型
     * @return 是否存在
     */
    boolean existsRefundRecord(String orderId, String refundType);

    /**
     * 获取订单的总退款金额
     * 
     * @param orderId 订单ID
     * @return 总退款金额（分）
     */
    Long getTotalRefundAmountByOrderId(String orderId);

    /**
     * 批量更新退款记录状态
     *
     * @param refundRecordIds 退款记录ID列表
     * @param refundStatus 退款状态
     */
    void batchUpdateRefundStatus(List<String> refundRecordIds, String refundStatus);
}
